# Git
.git/
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv
.pytest_cache/
.coverage
htmlcov/
*.egg-info/
.eggs/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Build
dist/
build/
*.log

# Environment
.env
.env.*

# Test
coverage/
.nyc_output/

# Database
*.db
*.sqlite3

# Documentation
docs/
*.md

# Docker
Dockerfile*
docker-compose*.yml
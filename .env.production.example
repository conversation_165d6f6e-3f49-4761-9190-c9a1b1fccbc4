# 生产环境配置文件示例
# 复制此文件为 .env.production 并填写实际配置

# ================================
# 基础配置
# ================================
ENVIRONMENT=production
DOMAIN=your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# ================================
# CTP配置 (真实券商信息)
# ================================
# 中信期货配置示例
CTP_BROKER_ID=66666
CTP_USER_ID=您的真实交易账户
CTP_PASSWORD=您的真实交易密码
CTP_AUTH_CODE=券商提供的认证码
CTP_APP_ID=券商分配的应用ID
CTP_TRADE_FRONT=tcp://***************:10130
CTP_MD_FRONT=tcp://***************:10131

# 华泰期货配置示例
# CTP_BROKER_ID=1080
# CTP_USER_ID=您的真实交易账户
# CTP_PASSWORD=您的真实交易密码
# CTP_AUTH_CODE=券商提供的认证码
# CTP_APP_ID=券商分配的应用ID
# CTP_TRADE_FRONT=tcp://**************:41205
# CTP_MD_FRONT=tcp://**************:41213

# ================================
# 数据库配置
# ================================
DB_NAME=quant_trading_prod
DB_USER=trading_user
DB_PASSWORD=your_strong_db_password_here
DB_HOST=postgres
DB_PORT=5432

# ================================
# Redis配置
# ================================
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379

# ================================
# 安全配置
# ================================
SECRET_KEY=your_very_long_and_random_secret_key_here_at_least_32_characters
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_for_sensitive_data

# ================================
# 风险控制配置
# ================================
# 资金风险限制
RISK_MAX_ORDER_AMOUNT=1000000
RISK_DAILY_TRADE_AMOUNT=10000000
RISK_POSITION_LIMIT=0.8
RISK_MIN_AVAILABLE_RATIO=0.2

# 持仓风险限制
RISK_MAX_SINGLE_POSITION_RATIO=0.3
RISK_MAX_SECTOR_CONCENTRATION=0.5
RISK_MAX_LEVERAGE_RATIO=3.0

# 交易频率限制
RISK_MAX_ORDERS_PER_MINUTE=100
RISK_MAX_ORDERS_PER_HOUR=2000
RISK_MAX_ORDERS_PER_DAY=20000

# 损失限制
RISK_MAX_DAILY_LOSS=100000
RISK_MAX_WEEKLY_LOSS=500000
RISK_MAX_MONTHLY_LOSS=2000000
RISK_MAX_DRAWDOWN_RATIO=0.15

# ================================
# 监控和告警配置
# ================================
ENABLE_MONITORING=true
ALERT_EMAIL=<EMAIL>
ALERT_PHONE=13800138000
ALERT_WEBHOOK_URL=https://your-webhook-url.com/alerts

# 系统监控阈值
MONITOR_CPU_THRESHOLD=80
MONITOR_MEMORY_THRESHOLD=85
MONITOR_DISK_THRESHOLD=90
MONITOR_RESPONSE_TIME_THRESHOLD=1000

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_FILE=/app/logs/trading.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# ================================
# SSL证书配置
# ================================
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem
SSL_CA_PATH=/app/ssl/ca.pem

# ================================
# 监控服务配置
# ================================
# Grafana
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_grafana_password_here

# Prometheus
PROMETHEUS_RETENTION=30d

# ================================
# 备份配置
# ================================
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key

# ================================
# 第三方服务配置
# ================================
# 邮件服务
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true

# 短信服务
SMS_PROVIDER=aliyun  # 或 tencent, twilio
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=您的短信签名

# 对象存储
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_ACCESS_KEY=your_oss_access_key
OSS_SECRET_KEY=your_oss_secret_key
OSS_BUCKET=your-trading-data-bucket

# ================================
# 性能优化配置
# ================================
# 数据库连接池
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis连接池
REDIS_POOL_SIZE=50
REDIS_POOL_TIMEOUT=10

# WebSocket配置
WS_MAX_CONNECTIONS=1000
WS_HEARTBEAT_INTERVAL=30

# ================================
# 开发和调试配置 (生产环境应设为false)
# ================================
DEBUG=false
ENABLE_CORS=false
ENABLE_SWAGGER=false
ENABLE_PROFILING=false

# ================================
# 特性开关
# ================================
ENABLE_REAL_TRADING=true
ENABLE_RISK_CONTROL=true
ENABLE_MONITORING=true
ENABLE_BACKUP=true
ENABLE_ALERTS=true

# ================================
# 合规和审计配置
# ================================
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365
ENABLE_TRADE_RECORDING=true
TRADE_RECORDING_PATH=/app/data/trade_records

# ================================
# 高可用配置
# ================================
ENABLE_CLUSTER=false
CLUSTER_NODES=node1,node2,node3
ENABLE_LOAD_BALANCER=false
HEALTH_CHECK_INTERVAL=30

# ================================
# 安全加固配置
# ================================
ENABLE_IP_WHITELIST=true
ALLOWED_IPS=***********/24,10.0.0.0/8
ENABLE_RATE_LIMITING=true
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# 会话配置
SESSION_TIMEOUT=1800
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# ================================
# 数据同步配置
# ================================
ENABLE_DATA_SYNC=true
SYNC_INTERVAL=300
SYNC_BATCH_SIZE=1000
BACKUP_SYNC_ENABLED=true

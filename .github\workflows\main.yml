name: 量化投资平台 - 主 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
  pull_request:
    branches: [ main ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production
      skip_tests:
        description: '跳过测试'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: quant-platform
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # 变更检测
  detect-changes:
    name: 检测代码变更
    runs-on: ubuntu-latest
    outputs:
      backend-changed: ${{ steps.changes.outputs.backend }}
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      infra-changed: ${{ steps.changes.outputs.infra }}
      force-deploy: ${{ github.event_name == 'workflow_dispatch' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: 检测文件变更
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            backend:
              - 'backend/**'
              - 'requirements*.txt'
              - 'Dockerfile*'
              - 'docker-compose*.yml'
            frontend:
              - 'frontend/**'
              - 'package*.json'
              - 'Dockerfile*'
              - 'docker-compose*.yml'
            infra:
              - 'k8s/**'
              - 'config/**'
              - 'scripts/**'
              - '.github/workflows/**'

  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.backend-changed == 'true' || needs.detect-changes.outputs.frontend-changed == 'true'
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: SonarCloud 扫描
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

      - name: 安全扫描 - Secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified

  # 后端构建和测试
  backend-build-test:
    name: 后端构建测试
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.backend-changed == 'true' || needs.detect-changes.outputs.force-deploy == 'true'
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: quant_user
          POSTGRES_PASSWORD: quant_password
          POSTGRES_DB: quant_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: 安装依赖
        run: |
          cd backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio flake8 mypy safety bandit

      - name: 代码格式检查
        run: |
          cd backend
          flake8 app --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 app --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: 类型检查
        run: |
          cd backend
          mypy app --ignore-missing-imports || echo "Type check completed with warnings"

      - name: 安全检查
        run: |
          cd backend
          safety check --json || echo "Safety check completed"
          bandit -r app -f json || echo "Bandit security check completed"

      - name: 运行测试
        if: github.event.inputs.skip_tests != 'true'
        env:
          DATABASE_URL: postgresql://quant_user:quant_password@localhost:5432/quant_test
          REDIS_URL: redis://localhost:6379/1
          SECRET_KEY: test-secret-key
          ENVIRONMENT: test
        run: |
          cd backend
          pytest tests/ -v --cov=app --cov-report=xml --cov-report=html --junitxml=test-results.xml

      - name: 上传测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: backend-test-results
          path: |
            backend/coverage.xml
            backend/htmlcov/
            backend/test-results.xml

      - name: 构建 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./backend/Dockerfile
          push: false
          tags: ${{ env.REGISTRY }}/backend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 前端构建和测试
  frontend-build-test:
    name: 前端构建测试
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.frontend-changed == 'true' || needs.detect-changes.outputs.force-deploy == 'true'

    outputs:
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: frontend/pnpm-lock.yaml

      - name: 安装 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: 安装依赖
        run: |
          cd frontend
          pnpm install --frozen-lockfile

      - name: 代码格式检查
        run: |
          cd frontend
          pnpm run lint:check || echo "Lint completed with warnings"

      - name: 类型检查
        run: |
          cd frontend
          pnpm run type-check

      - name: 单元测试
        if: github.event.inputs.skip_tests != 'true'
        run: |
          cd frontend
          pnpm run test:run

      - name: 构建生产版本
        run: |
          cd frontend
          pnpm run build

      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist

      - name: 构建 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./frontend/Dockerfile
          push: false
          tags: ${{ env.REGISTRY }}/frontend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 集成测试
  integration-test:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: [backend-build-test, frontend-build-test]
    if: |
      always() &&
      (needs.backend-build-test.result == 'success' || needs.backend-build-test.result == 'skipped') &&
      (needs.frontend-build-test.result == 'success' || needs.frontend-build-test.result == 'skipped') &&
      github.event.inputs.skip_tests != 'true'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 启动集成测试环境
        run: |
          echo "启动集成测试环境..."
          docker compose -f docker/compose/ci/docker-compose.yml --env-file docker/compose/ci/.env.ci up -d --build
          echo "等待服务启动..."
          sleep 60

      - name: 等待服务就绪
        run: |
          echo "等待数据库就绪..."
          timeout 90s bash -c 'until docker compose -f docker/compose/ci/docker-compose.yml exec -T postgres pg_isready -U test_user -d test_db; do sleep 3; done'

          echo "等待后端服务就绪..."
          timeout 120s bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'

      - name: 运行数据库迁移
        run: |
          echo "运行数据库迁移..."
          docker compose -f docker/compose/ci/docker-compose.yml exec -T backend alembic upgrade head

      - name: 运行集成测试
        run: |
          echo "运行集成测试..."
          docker compose -f docker/compose/ci/docker-compose.yml exec -T backend pytest tests/integration/ -v --cov=app --cov-report=xml --junitxml=integration-test-results.xml

      - name: 运行 API 测试
        run: |
          echo "运行 API 测试..."
          docker compose -f docker/compose/ci/docker-compose.yml exec -T backend pytest tests/api/ -v --junitxml=api-test-results.xml

      - name: 上传集成测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: |
            backend/integration-test-results.xml
            backend/api-test-results.xml
            backend/coverage.xml

      - name: 清理集成测试环境
        if: always()
        run: |
          docker compose -f docker/compose/ci/docker-compose.yml down -v --remove-orphans

  # E2E 测试
  e2e-test:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: |
      always() &&
      needs.integration-test.result == 'success' &&
      github.event.inputs.skip_tests != 'true'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 启动测试环境
        run: |
          # 使用 CI 专用的 docker-compose 启动测试环境
          docker compose -f docker/compose/ci/docker-compose.yml --env-file docker/compose/ci/.env.ci up -d --build
          echo "等待服务启动..."
          sleep 45

      - name: 等待服务就绪
        run: |
          # 健康检查 - 等待数据库就绪
          echo "等待 PostgreSQL 就绪..."
          timeout 60s bash -c 'until docker compose -f docker/compose/ci/docker-compose.yml exec -T postgres pg_isready -U test_user -d test_db; do sleep 2; done'

          # 等待 Redis 就绪
          echo "等待 Redis 就绪..."
          timeout 30s bash -c 'until docker compose -f docker/compose/ci/docker-compose.yml exec -T redis redis-cli ping; do sleep 2; done'

          # 等待后端服务就绪
          echo "等待后端服务就绪..."
          timeout 120s bash -c 'until curl -f http://localhost:8000/health; do sleep 3; done'

          # 等待前端服务就绪
          echo "等待前端服务就绪..."
          timeout 90s bash -c 'until curl -f http://localhost:5173; do sleep 3; done'

          # 等待 Nginx 代理就绪
          echo "等待 Nginx 代理就绪..."
          timeout 60s bash -c 'until curl -f http://localhost:80/health; do sleep 2; done'

      - name: 运行 E2E 测试
        run: |
          cd frontend
          pnpm install --frozen-lockfile
          pnpm run test:e2e

      - name: 上传 E2E 测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            frontend/test-results/
            frontend/playwright-report/

      - name: 收集测试环境日志
        if: failure()
        run: |
          echo "收集容器日志..."
          mkdir -p logs/ci
          docker compose -f docker/compose/ci/docker-compose.yml logs backend > logs/ci/backend.log 2>&1 || true
          docker compose -f docker/compose/ci/docker-compose.yml logs frontend > logs/ci/frontend.log 2>&1 || true
          docker compose -f docker/compose/ci/docker-compose.yml logs postgres > logs/ci/postgres.log 2>&1 || true
          docker compose -f docker/compose/ci/docker-compose.yml logs redis > logs/ci/redis.log 2>&1 || true
          docker compose -f docker/compose/ci/docker-compose.yml logs nginx > logs/ci/nginx.log 2>&1 || true

      - name: 上传故障日志
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: ci-environment-logs
          path: logs/ci/

      - name: 清理测试环境
        if: always()
        run: |
          echo "清理 CI 测试环境..."
          docker compose -f docker/compose/ci/docker-compose.yml down -v --remove-orphans
          docker system prune -f

  # 发布镜像
  publish-images:
    name: 发布 Docker 镜像
    runs-on: ubuntu-latest
    needs: [code-quality, backend-build-test, frontend-build-test, e2e-test]
    if: |
      always() &&
      github.ref == 'refs/heads/main' &&
      (needs.code-quality.result == 'success' || needs.code-quality.result == 'skipped') &&
      (needs.backend-build-test.result == 'success' || needs.backend-build-test.result == 'skipped') &&
      (needs.frontend-build-test.result == 'success' || needs.frontend-build-test.result == 'skipped') &&
      (needs.e2e-test.result == 'success' || needs.e2e-test.result == 'skipped')

    outputs:
      backend-image: ${{ steps.meta-backend.outputs.tags }}
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取后端元数据
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送后端镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 提取前端元数据
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送前端镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./frontend/Dockerfile
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [detect-changes, publish-images]
    if: |
      always() &&
      github.ref == 'refs/heads/develop' &&
      (needs.publish-images.result == 'success' || needs.detect-changes.outputs.force-deploy == 'true')
    environment: development

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 部署到开发环境
        run: |
          echo "部署到开发环境"
          # 这里可以添加实际的部署逻辑
          # 例如更新 Kubernetes 配置，或者调用部署脚本

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [detect-changes, publish-images]
    if: |
      always() &&
      github.ref == 'refs/heads/main' &&
      (needs.publish-images.result == 'success' || needs.detect-changes.outputs.force-deploy == 'true')
    environment: production

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 备份当前部署
        run: |
          echo "备份当前生产环境配置"
          # 实际的备份逻辑

      - name: 部署到生产环境
        run: |
          echo "部署到生产环境"
          # 实际的部署逻辑

      - name: 部署后验证
        run: |
          echo "验证生产环境部署"
          # 健康检查和烟雾测试

      - name: 回滚（如果失败）
        if: failure()
        run: |
          echo "部署失败，执行回滚"
          # 回滚逻辑

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: [deploy-prod]
    if: success() && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 运行性能测试
        run: |
          echo "运行性能测试"
          # 使用 k6 或其他工具进行性能测试

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-prod, performance-test]
    if: always()

    steps:
      - name: 发送通知
        run: |
          echo "发送部署结果通知"
          # 发送到 Slack, 钉钉或其他通知渠道
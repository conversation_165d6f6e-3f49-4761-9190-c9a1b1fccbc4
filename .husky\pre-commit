#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 预提交钩子 - 防止提交大文件和敏感文件

# 颜色定义
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m'

echo "🔍 检查提交内容..."

# 检查文件大小限制 (5MB)
MAX_FILE_SIZE=5242880  # 5MB in bytes

# 检查是否提交了禁止的文件类型
FORBIDDEN_PATTERNS=(
    "*.db"
    "*.sqlite"
    "*.sqlite3"
    "*.log"
    "node_modules/"
    "dist/"
    "__pycache__/"
    "*.pyc"
    "*.pyo"
    "*.tmp"
    "*.swp"
    ".env"
    "*.key"
    "*.pem"
    "*.p12"
    "*.pfx"
    ".vscode/"
    ".idea/"
    "*.tar.gz"
    "*.zip"
    "*.rar"
)

# 检查大文件
echo "📏 检查文件大小..."
LARGE_FILES=()
while IFS= read -r -d '' file; do
    if [ -f "$file" ]; then
        file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || wc -c < "$file")
        if [ "$file_size" -gt "$MAX_FILE_SIZE" ]; then
            LARGE_FILES+=("$file ($(echo "scale=1; $file_size/1024/1024" | bc 2>/dev/null || echo "?")MB)")
        fi
    fi
done < <(git diff --cached --name-only -z)

if [ ${#LARGE_FILES[@]} -gt 0 ]; then
    echo -e "${RED}❌ 错误：发现大文件（超过5MB）：${NC}"
    printf '%s\n' "${LARGE_FILES[@]}"
    echo -e "${YELLOW}💡 建议：将大文件移至 .gitignore 或使用 Git LFS${NC}"
    exit 1
fi

# 检查禁止的文件模式
echo "🚫 检查禁止的文件类型..."
FORBIDDEN_FILES=()
for pattern in "${FORBIDDEN_PATTERNS[@]}"; do
    while IFS= read -r line; do
        [ -n "$line" ] && FORBIDDEN_FILES+=("$line")
    done < <(git diff --cached --name-only | grep -E "${pattern//\*/.*}")
done

if [ ${#FORBIDDEN_FILES[@]} -gt 0 ]; then
    echo -e "${RED}❌ 错误：禁止提交以下文件：${NC}"
    printf '%s\n' "${FORBIDDEN_FILES[@]}"
    echo ""
    echo -e "${YELLOW}💡 这些文件类型不应该提交到版本控制：${NC}"
    echo "  • 数据库文件 (*.db, *.sqlite)"
    echo "  • 日志文件 (*.log)"
    echo "  • 构建产物 (dist/, node_modules/)"
    echo "  • 临时文件 (*.tmp, *.swp)"
    echo "  • 敏感配置 (.env, *.key)"
    echo "  • IDE配置 (.vscode/, .idea/)"
    echo ""
    echo -e "${GREEN}✅ 解决方案：${NC}"
    echo "  1. 运行清理脚本：./scripts/clean-repo.sh"
    echo "  2. 或手动删除文件：git rm --cached <文件名>"
    echo "  3. 确保 .gitignore 包含这些文件类型"
    exit 1
fi

# 检查是否意外提交了测试文件
echo "🧪 检查测试文件..."
TEST_PATTERNS=(
    "*_test_*.py"
    "*_test_*.js"
    "*_test_*.json"
    "*_test_*.png"
    "debug_*.py"
    "debug_*.js"
    "comprehensive_*.py"
    "final_*.py"
    "*_report_*.json"
    "*_report_*.md"
)

TEST_FILES=()
for pattern in "${TEST_PATTERNS[@]}"; do
    while IFS= read -r line; do
        [ -n "$line" ] && TEST_FILES+=("$line")
    done < <(git diff --cached --name-only | grep -E "${pattern//\*/.*}")
done

if [ ${#TEST_FILES[@]} -gt 0 ]; then
    echo -e "${YELLOW}⚠️  警告：发现测试文件，确认是否需要提交：${NC}"
    printf '%s\n' "${TEST_FILES[@]}"
    echo ""
    echo -e "${YELLOW}按 Enter 继续提交，Ctrl+C 取消${NC}"
    read -r
fi

# 检查提交信息格式
echo "📝 检查提交信息格式..."
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'
commit_msg_file="$1"

if [ -f "$commit_msg_file" ]; then
    commit_msg=$(cat "$commit_msg_file")
    if ! echo "$commit_msg" | grep -qE "$commit_regex"; then
        echo -e "${YELLOW}⚠️  提交信息建议使用约定式格式：${NC}"
        echo "  feat: 新功能"
        echo "  fix: 修复"
        echo "  docs: 文档"
        echo "  chore: 清理/维护"
        echo ""
        echo "当前提交信息: $commit_msg"
        echo ""
        echo -e "${YELLOW}按 Enter 继续，Ctrl+C 取消并修改${NC}"
        read -r
    fi
fi

# 检查是否有staged的更改
if ! git diff --cached --quiet; then
    echo -e "${GREEN}✅ 预提交检查通过${NC}"
else
    echo -e "${RED}❌ 没有staged的更改${NC}"
    exit 1
fi

echo "🎉 准备提交..."
# 部署配置目录说明

本项目包含多套部署配置，为避免混淆，特此说明各目录的用途和权威性。

## 📁 权威配置目录（推荐使用）

### Kubernetes 配置
- **权威目录**: `config/k8s/`
- **说明**: 包含完整的K8S部署配置，包括详细的README和最新的配置文件
- **用途**: 生产环境K8S部署

### Nginx 配置
- **权威目录**: `nginx/`
- **说明**: 包含完整的生产级Nginx配置
- **用途**: 反向代理和负载均衡配置

### Docker 配置
- **开发环境**: 根目录 `docker-compose.yml`
- **生产环境**: `deployment/docker-compose.yml`
- **说明**: 分别用于开发和生产环境的容器编排

## 🗂️ 历史/备用目录（已废弃或仅供参考）

### 已废弃的K8S目录
- ~~`k8s/`~~ - 已删除，配置不完整
- ~~`backend/k8s/`~~ - 已删除，仅包含后端配置
- ~~`deployment/kubernetes/`~~ - 备用配置，建议使用 `config/k8s/`

### 已废弃的Nginx目录
- ~~`config/nginx/`~~ - 已删除，配置不如根目录完整

### 已废弃的Docker目录
- ~~`docker/`~~ - 历史配置，建议使用根目录 `docker-compose.yml` 或 `config/docker/`
- ~~`deployment/`~~ - 部分配置重复，建议使用 `config/` 下的权威配置

## 🚀 部署建议

### 开发环境
```bash
# 使用Docker Compose
docker-compose up -d

# 或直接启动
cd backend && python start_backend.py
cd frontend && npm run dev
```

### 生产环境
```bash
# 使用K8S部署
kubectl apply -f config/k8s/

# 或使用生产Docker Compose
docker-compose -f deployment/docker-compose.yml up -d
```

### Nginx配置
```bash
# 复制Nginx配置
cp nginx/nginx.conf /etc/nginx/sites-available/quant-platform
ln -s /etc/nginx/sites-available/quant-platform /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

## 📝 维护说明

1. **配置更新**: 只更新权威目录中的配置文件
2. **新增配置**: 在权威目录中添加新的配置文件
3. **版本控制**: 权威目录的配置文件应纳入版本控制
4. **文档更新**: 配置变更时同步更新相关文档

## ⚠️ 注意事项

- 不要同时使用多套配置，避免配置冲突
- 部署前请检查配置文件中的环境变量和密钥
- 生产环境部署前请仔细审查安全配置
- 建议在测试环境先验证配置的正确性

---

最后更新: 2025-01-07
维护者: 量化投资平台开发团队

# 量化投资平台 Makefile
# 统一的构建、测试和部署命令

.PHONY: help build build-dev build-prod clean test up down logs shell

# 默认目标
.DEFAULT_GOAL := help

# 项目配置
PROJECT_NAME := quant-platform
FRONTEND_IMAGE := $(PROJECT_NAME)-frontend
BACKEND_IMAGE := $(PROJECT_NAME)-backend
COMPOSE_FILE_DEV := docker/compose/local.yml
COMPOSE_FILE_PROD := docker/compose/production.yml

# Docker 配置
DOCKER_BUILDKIT := 1
export DOCKER_BUILDKIT

# 颜色定义
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)量化投资平台 - 可用命令:$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)示例:$(NC)"
	@echo "  make build-dev     # 构建开发环境"
	@echo "  make up-dev        # 启动开发环境"
	@echo "  make logs          # 查看日志"

# 构建命令
build: build-dev ## 构建所有镜像 (默认开发环境)

build-dev: ## 构建开发环境镜像
	@echo "$(BLUE)构建开发环境镜像...$(NC)"
	@docker build -f frontend/Dockerfile --target development -t $(FRONTEND_IMAGE):dev frontend/
	@docker build -f backend/Dockerfile --target development -t $(BACKEND_IMAGE):dev .
	@echo "$(GREEN)开发环境镜像构建完成$(NC)"

build-prod: ## 构建生产环境镜像
	@echo "$(BLUE)构建生产环境镜像...$(NC)"
	@docker build -f frontend/Dockerfile.prod -t $(FRONTEND_IMAGE):prod frontend/
	@docker build -f backend/Dockerfile.prod -t $(BACKEND_IMAGE):prod .
	@echo "$(GREEN)生产环境镜像构建完成$(NC)"

build-frontend-dev: ## 构建前端开发镜像
	@echo "$(BLUE)构建前端开发镜像...$(NC)"
	@docker build -f frontend/Dockerfile --target development -t $(FRONTEND_IMAGE):dev frontend/

build-frontend-prod: ## 构建前端生产镜像
	@echo "$(BLUE)构建前端生产镜像...$(NC)"
	@docker build -f frontend/Dockerfile.prod -t $(FRONTEND_IMAGE):prod frontend/

build-backend-dev: ## 构建后端开发镜像
	@echo "$(BLUE)构建后端开发镜像...$(NC)"
	@docker build -f backend/Dockerfile --target development -t $(BACKEND_IMAGE):dev .

build-backend-prod: ## 构建后端生产镜像
	@echo "$(BLUE)构建后端生产镜像...$(NC)"
	@docker build -f backend/Dockerfile.prod -t $(BACKEND_IMAGE):prod .

# 环境管理
up-dev: ## 启动开发环境
	@echo "$(BLUE)启动开发环境...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) up -d
	@echo "$(GREEN)开发环境已启动$(NC)"
	@echo "前端: http://localhost:5173"
	@echo "后端: http://localhost:8000"

up-prod: ## 启动生产环境
	@echo "$(BLUE)启动生产环境...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_PROD) up -d
	@echo "$(GREEN)生产环境已启动$(NC)"

down: ## 停止所有服务
	@echo "$(YELLOW)停止所有服务...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) down 2>/dev/null || true
	@docker-compose -f $(COMPOSE_FILE_PROD) down 2>/dev/null || true
	@echo "$(GREEN)所有服务已停止$(NC)"

restart-dev: down up-dev ## 重启开发环境

restart-prod: down up-prod ## 重启生产环境

# 日志和监控
logs: ## 查看所有服务日志
	@docker-compose -f $(COMPOSE_FILE_DEV) logs -f

logs-frontend: ## 查看前端日志
	@docker-compose -f $(COMPOSE_FILE_DEV) logs -f frontend

logs-backend: ## 查看后端日志
	@docker-compose -f $(COMPOSE_FILE_DEV) logs -f backend

logs-db: ## 查看数据库日志
	@docker-compose -f $(COMPOSE_FILE_DEV) logs -f postgres

# 开发工具
shell-frontend: ## 进入前端容器
	@docker-compose -f $(COMPOSE_FILE_DEV) exec frontend /bin/sh

shell-backend: ## 进入后端容器
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend /bin/bash

shell-db: ## 进入数据库容器
	@docker-compose -f $(COMPOSE_FILE_DEV) exec postgres psql -U postgres -d quantplatform

# 测试命令
test: ## 运行所有测试
	@echo "$(BLUE)运行测试...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend python -m pytest
	@docker-compose -f $(COMPOSE_FILE_DEV) exec frontend npm test

test-frontend: ## 运行前端测试
	@docker-compose -f $(COMPOSE_FILE_DEV) exec frontend npm test

test-backend: ## 运行后端测试
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend python -m pytest

# 数据库操作
db-migrate: ## 运行数据库迁移
	@echo "$(BLUE)运行数据库迁移...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend alembic upgrade head

db-reset: ## 重置数据库
	@echo "$(YELLOW)重置数据库...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend alembic downgrade base
	@docker-compose -f $(COMPOSE_FILE_DEV) exec backend alembic upgrade head

# 清理命令
clean: ## 清理 Docker 资源
	@echo "$(YELLOW)清理 Docker 资源...$(NC)"
	@docker system prune -f
	@docker volume prune -f
	@echo "$(GREEN)清理完成$(NC)"

clean-images: ## 清理项目镜像
	@echo "$(YELLOW)清理项目镜像...$(NC)"
	@docker images | grep $(PROJECT_NAME) | awk '{print $$3}' | xargs -r docker rmi -f
	@echo "$(GREEN)项目镜像清理完成$(NC)"

clean-all: clean clean-images ## 清理所有 Docker 资源

# 部署命令
deploy-staging: build-prod ## 部署到测试环境
	@echo "$(BLUE)部署到测试环境...$(NC)"
	# 这里添加具体的部署逻辑
	@echo "$(GREEN)测试环境部署完成$(NC)"

deploy-prod: build-prod ## 部署到生产环境
	@echo "$(BLUE)部署到生产环境...$(NC)"
	# 这里添加具体的部署逻辑
	@echo "$(GREEN)生产环境部署完成$(NC)"

# 健康检查
health: ## 检查服务健康状态
	@echo "$(BLUE)检查服务健康状态...$(NC)"
	@curl -f http://localhost:8000/health && echo "$(GREEN)后端服务正常$(NC)" || echo "$(RED)后端服务异常$(NC)"
	@curl -f http://localhost:5173 && echo "$(GREEN)前端服务正常$(NC)" || echo "$(RED)前端服务异常$(NC)"

# 性能分析
analyze-images: ## 分析镜像体积
	@echo "$(BLUE)分析镜像体积...$(NC)"
	@docker images | grep $(PROJECT_NAME)
	@echo ""
	@echo "$(BLUE)镜像层分析:$(NC)"
	@docker history $(FRONTEND_IMAGE):prod 2>/dev/null || echo "前端生产镜像不存在"
	@docker history $(BACKEND_IMAGE):prod 2>/dev/null || echo "后端生产镜像不存在"

# 开发环境初始化
init-dev: build-dev up-dev db-migrate ## 初始化开发环境
	@echo "$(GREEN)开发环境初始化完成$(NC)"
	@echo "前端: http://localhost:5173"
	@echo "后端: http://localhost:8000"
	@echo "数据库: localhost:5432"

# 快速启动
quick-start: ## 快速启动 (如果镜像存在则直接启动，否则构建)
	@if docker images | grep -q "$(FRONTEND_IMAGE):dev"; then \
		echo "$(GREEN)使用现有镜像启动...$(NC)"; \
		make up-dev; \
	else \
		echo "$(BLUE)镜像不存在，开始构建...$(NC)"; \
		make init-dev; \
	fi

# 量化投资平台 - 统一部署管理
# 使用标准化的 Docker Compose 配置

.PHONY: help init validate clean
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# 环境配置
LOCAL_COMPOSE := docker/compose/local/docker-compose.yml
STAGING_COMPOSE := docker/compose/staging/docker-compose.yml
PRODUCTION_COMPOSE := docker/compose/production/docker-compose.yml

LOCAL_ENV := docker/compose/local/.env
STAGING_ENV := docker/compose/staging/.env.staging
PRODUCTION_ENV := docker/compose/production/.env.prod

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)量化投资平台 - 统一部署管理$(NC)"
	@echo "$(BLUE)================================$(NC)"
	@echo ""
	@echo "$(GREEN)环境管理:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(local|staging|production)"
	@echo ""
	@echo "$(GREEN)工具命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -v -E "(local|staging|production)"

# =============================================================================
# 初始化和验证
# =============================================================================

init: ## 初始化环境配置文件
	@echo "$(BLUE)🔧 初始化环境配置...$(NC)"
	@if [ ! -f $(LOCAL_ENV) ]; then \
		cp docker/compose/local/.env.local $(LOCAL_ENV); \
		echo "$(GREEN)✅ 创建本地环境配置: $(LOCAL_ENV)$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  本地环境配置已存在: $(LOCAL_ENV)$(NC)"; \
	fi

validate: ## 验证所有环境配置
	@echo "$(BLUE)🔍 验证配置文件...$(NC)"
	@python scripts/validate-config.py

clean: ## 清理所有环境
	@echo "$(BLUE)🧹 清理所有环境...$(NC)"
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) down -v 2>/dev/null || true
	@docker system prune -f
	@echo "$(GREEN)✅ 清理完成$(NC)"

# =============================================================================
# 本地开发环境
# =============================================================================

local-up: ## 启动本地开发环境
	@echo "$(BLUE)🚀 启动本地开发环境...$(NC)"
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) up -d
	@echo "$(GREEN)✅ 本地环境启动成功$(NC)"
	@echo "$(YELLOW)前端: http://localhost:5173$(NC)"
	@echo "$(YELLOW)后端: http://localhost:8000$(NC)"

local-down: ## 停止本地开发环境
	@echo "$(BLUE)🛑 停止本地开发环境...$(NC)"
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) down
	@echo "$(GREEN)✅ 本地环境停止成功$(NC)"

local-logs: ## 查看本地环境日志
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) logs -f

local-ps: ## 查看本地环境状态
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) ps

# =============================================================================
# 测试环境
# =============================================================================

staging-up: ## 启动测试环境
	@echo "$(BLUE)🚀 启动测试环境...$(NC)"
	@docker compose -f $(STAGING_COMPOSE) --env-file $(STAGING_ENV) up -d
	@echo "$(GREEN)✅ 测试环境启动成功$(NC)"

staging-down: ## 停止测试环境
	@echo "$(BLUE)🛑 停止测试环境...$(NC)"
	@docker compose -f $(STAGING_COMPOSE) --env-file $(STAGING_ENV) down
	@echo "$(GREEN)✅ 测试环境停止成功$(NC)"

staging-logs: ## 查看测试环境日志
	@docker compose -f $(STAGING_COMPOSE) --env-file $(STAGING_ENV) logs -f

staging-ps: ## 查看测试环境状态
	@docker compose -f $(STAGING_COMPOSE) --env-file $(STAGING_ENV) ps

# =============================================================================
# 生产环境
# =============================================================================

production-up: ## 启动生产环境
	@echo "$(BLUE)🚀 启动生产环境...$(NC)"
	@docker compose -f $(PRODUCTION_COMPOSE) --env-file $(PRODUCTION_ENV) up -d
	@echo "$(GREEN)✅ 生产环境启动成功$(NC)"

production-down: ## 停止生产环境
	@echo "$(BLUE)🛑 停止生产环境...$(NC)"
	@docker compose -f $(PRODUCTION_COMPOSE) --env-file $(PRODUCTION_ENV) down
	@echo "$(GREEN)✅ 生产环境停止成功$(NC)"

production-logs: ## 查看生产环境日志
	@docker compose -f $(PRODUCTION_COMPOSE) --env-file $(PRODUCTION_ENV) logs -f

production-ps: ## 查看生产环境状态
	@docker compose -f $(PRODUCTION_COMPOSE) --env-file $(PRODUCTION_ENV) ps

# =============================================================================
# 开发工具
# =============================================================================

shell-backend: ## 进入后端容器
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) exec backend bash

test: ## 运行测试
	@echo "$(BLUE)🧪 运行测试...$(NC)"
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) exec backend python -m pytest
	@echo "$(GREEN)✅ 测试完成$(NC)"

status: ## 显示所有环境状态
	@echo "$(BLUE)📊 环境状态概览:$(NC)"
	@echo ""
	@echo "$(YELLOW)本地开发环境:$(NC)"
	@docker compose -f $(LOCAL_COMPOSE) --env-file $(LOCAL_ENV) ps 2>/dev/null || echo "  未运行"

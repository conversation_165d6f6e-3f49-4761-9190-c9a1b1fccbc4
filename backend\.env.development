# 开发环境配置

# 应用配置
APP_NAME="量化交易平台"
APP_ENV=development
APP_DEBUG=true
APP_PORT=8000
APP_HOST=0.0.0.0

# 数据库配置
DATABASE_URL=postgresql://quantuser:quantpass@localhost:5432/quantdb_dev
REDIS_URL=redis://localhost:6379/0

# MongoDB配置 (用于存储非结构化数据)
MONGODB_URL=mongodb://localhost:27017/quantdata_dev

# 时序数据库配置 (用于K线和行情数据)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-dev-token
INFLUXDB_ORG=quant-platform
INFLUXDB_BUCKET=market-data-dev

# JWT配置
JWT_SECRET_KEY=your-secret-key-for-development-only
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API密钥 (开发环境模拟)
MARKET_DATA_API_KEY=dev-market-data-key
TRADING_API_KEY=dev-trading-key
TRADING_API_SECRET=dev-trading-secret

# 行情数据源
MARKET_DATA_SOURCE=tushare  # simulator | tushare | akshare | vnpy
TUSHARE_TOKEN=f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400

# 消息队列
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE_PATH=../logs/quant-platform.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 交易配置
ENABLE_LIVE_TRADING=false
ENABLE_PAPER_TRADING=true
DEFAULT_COMMISSION_RATE=0.0003
DEFAULT_SLIPPAGE=0.001

# 缓存配置
CACHE_TTL=300  # 5分钟
MARKET_DATA_CACHE_TTL=60  # 1分钟

# 文件存储
UPLOAD_PATH=../data/uploads
MAX_UPLOAD_SIZE=10485760  # 10MB

# CORS配置
CORS_ORIGINS=["http://localhost:5173", "http://localhost:3000"]
CORS_ALLOW_CREDENTIALS=true

# 限流配置
RATE_LIMIT_ENABLED=false
RATE_LIMIT_PER_MINUTE=600

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# 第三方服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# 特性开关
FEATURE_ADVANCED_CHARTS=true
FEATURE_AI_TRADING=false
FEATURE_SOCIAL_TRADING=false
FEATURE_CRYPTO_TRADING=false

# 开发工具
ENABLE_SWAGGER=true
ENABLE_GRAPHQL=false
ENABLE_DEBUG_TOOLBAR=true
# 🚀 量化投资平台环境配置模板
# 复制此文件为 .env 并根据实际情况修改配置值
# 详细配置说明请参考: docs/environment-setup.md

# ========== 应用基础配置 ==========
ENVIRONMENT=development
DEBUG=true
APP_NAME=量化投资后端API
APP_VERSION=1.0.0
HOST=0.0.0.0
PORT=8000

# ========== 数据库配置 ==========
# SQLite配置 (开发环境推荐)
DATABASE_URL=sqlite:///./data/quantplatform.db
DB_ECHO=false

# PostgreSQL配置 (生产环境推荐)
# DATABASE_URL=postgresql://username:password@localhost:5432/quantplatform
# DB_POOL_SIZE=10
# DB_MAX_OVERFLOW=20
# DB_POOL_TIMEOUT=30
# DB_POOL_RECYCLE=3600

# ========== 缓存配置 (可选) ==========
# Redis配置 - 如不需要可注释
# REDIS_URL=redis://localhost:6379/0
# REDIS_PASSWORD=
# REDIS_CACHE_TTL=3600

# ========== 安全配置 ==========
# JWT密钥 - 生产环境请使用强密码
SECRET_KEY=quant-platform-super-secret-key-2024-change-in-production-environment
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# ========== CORS配置 ==========
# 允许的前端域名
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:8080","http://127.0.0.1:3000","http://127.0.0.1:5173"]

# ========== 日志配置 ==========
LOG_LEVEL=INFO
LOG_FORMAT=json

# ========== 数据源配置 ==========
# Tushare数据源 - 请替换为您的真实Token
MARKET_DATA_SOURCE=tushare
TUSHARE_TOKEN=your_tushare_token_here

# ========== 管理员配置 ==========
# 管理员密码 - 请设置强密码
ADMIN_PASSWORD=your_secure_admin_password_here

# Yahoo Finance (备用数据源)
USE_YFINANCE=true

# ========== WebSocket配置 ==========
WS_ENABLED=true
WS_MAX_CONNECTIONS=1000

# ========== 实时数据配置 ==========
USE_REALTIME_DATA=true
REALTIME_UPDATE_INTERVAL=1

# ========== 文件存储配置 ==========
UPLOAD_PATH=./uploads
MAX_UPLOAD_SIZE=10485760

# ========== API限流配置 ==========
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ========== 监控配置 (可选) ==========
# 是否启用监控
ENABLE_MONITORING=false
# PROMETHEUS_ENABLED=true
# PROMETHEUS_PORT=9090
# SENTRY_DSN=your_sentry_dsn_here

# ========== 高级交易配置 (可选) ==========
# CTP期货接口配置 - 如不使用可注释
# CTP_BROKER_ID=your_broker_id
# CTP_USER_ID=your_user_id
# CTP_PASSWORD=your_password
# CTP_AUTH_CODE=your_auth_code
# CTP_PRODUCT_INFO=your_product_info

# ========== 开发环境特定配置 ==========
# 是否使用Mock数据
USE_MOCK_DATA=false
# 是否开启SQL调试
# DB_ECHO=true

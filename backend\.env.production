# 生产环境配置

# 应用配置
APP_NAME="量化交易平台"
APP_ENV=production
APP_DEBUG=false
APP_PORT=8000
APP_HOST=0.0.0.0

# 数据库配置 (使用环境变量或密钥管理服务)
DATABASE_URL=${DATABASE_URL}
REDIS_URL=${REDIS_URL}

# MongoDB配置
MONGODB_URL=${MONGODB_URL}

# 时序数据库配置
INFLUXDB_URL=${INFLUXDB_URL}
INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
INFLUXDB_ORG=quant-platform
INFLUXDB_BUCKET=market-data-prod

# JWT配置 (从密钥管理服务获取)
JWT_SECRET_KEY=${JWT_SECRET_KEY}
JWT_ALGORITHM=RS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30

# API密钥 (从密钥管理服务获取)
MARKET_DATA_API_KEY=${MARKET_DATA_API_KEY}
TRADING_API_KEY=${TRADING_API_KEY}
TRADING_API_SECRET=${TRADING_API_SECRET}

# 行情数据源
MARKET_DATA_SOURCE=tushare  # 生产环境使用真实数据源
TUSHARE_TOKEN=${TUSHARE_TOKEN}

# 消息队列
RABBITMQ_URL=${RABBITMQ_URL}

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/quant-platform/app.log
LOG_MAX_SIZE=104857600  # 100MB
LOG_BACKUP_COUNT=10

# 交易配置
ENABLE_LIVE_TRADING=true
ENABLE_PAPER_TRADING=true
DEFAULT_COMMISSION_RATE=0.00025
DEFAULT_SLIPPAGE=0.0005

# 缓存配置
CACHE_TTL=600  # 10分钟
MARKET_DATA_CACHE_TTL=30  # 30秒

# 文件存储
UPLOAD_PATH=/data/quant-platform/uploads
MAX_UPLOAD_SIZE=52428800  # 50MB

# CORS配置
CORS_ORIGINS=${CORS_ORIGINS}
CORS_ALLOW_CREDENTIALS=true

# 限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=300

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# WebSocket配置
WS_HEARTBEAT_INTERVAL=60
WS_MAX_CONNECTIONS=10000

# 第三方服务
SMTP_HOST=${SMTP_HOST}
SMTP_PORT=${SMTP_PORT}
SMTP_USER=${SMTP_USER}
SMTP_PASS=${SMTP_PASS}
EMAIL_FROM=<EMAIL>

# 特性开关
FEATURE_ADVANCED_CHARTS=true
FEATURE_AI_TRADING=true
FEATURE_SOCIAL_TRADING=true
FEATURE_CRYPTO_TRADING=false

# 开发工具
ENABLE_SWAGGER=false
ENABLE_GRAPHQL=false
ENABLE_DEBUG_TOOLBAR=false

# CDN配置
CDN_URL=${CDN_URL}
STATIC_URL=${STATIC_URL}

# 安全配置
SECURE_SSL_REDIRECT=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=true
SECURE_FRAME_DENY=true
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30

# 告警配置
ALERT_EMAIL=${ALERT_EMAIL}
ALERT_WEBHOOK=${ALERT_WEBHOOK}
ALERT_THRESHOLD_ERROR_RATE=0.01
ALERT_THRESHOLD_RESPONSE_TIME=1000  # ms
[flake8]
max-line-length = 88
extend-ignore = E203,E501,W503,F401,F403,F405,F811,F841,F821,E722,E712,W291,W293,F541,F402

exclude = 
    .git,
    __pycache__,
    .pytest_cache,
    .venv,
    venv,
    migrations,
    .DS_Store

per-file-ignores =
    __init__.py:F401,F403,F405
    */migrations/*:F401,F403,F405
    */tests/*:F401,F403,F405

max-complexity = 15
count = True
show-source = True
statistics = True 
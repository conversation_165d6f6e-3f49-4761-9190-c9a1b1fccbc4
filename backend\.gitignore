# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
ENV/
env/
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject
.settings/

# Environment variables
.env
.env.*
!.env.example
!.env.development
!.env.production

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
*.out
*.err

# Test Coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Data directories
data/historical/*.csv
data/historical/*.parquet
data/historical/*.h5
data/realtime/*
data/reports/*
data/uploads/*
!data/**/.gitkeep

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS files
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Docker
.dockerignore

# Monitoring data
monitoring/prometheus/data/
monitoring/grafana/data/

# Secrets
secrets/
*.secret

# Model files
models/*.pkl
models/*.h5
models/*.onnx
!models/.gitkeep

# Cache
.cache/
redis-data/

# Documentation build
docs/_build/
site/

# Package manager
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
.nox/

# Translations
*.mo
*.pot

# Django stuff:
local_settings.py
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# mkdocs documentation
/site

# PyBuilder
target/

# IPython
profile_default/
ipython_config.py

# Environments
.env.local
.env.*.local

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject
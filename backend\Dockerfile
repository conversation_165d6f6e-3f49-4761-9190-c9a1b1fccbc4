# 量化投资平台后端 Docker 配置
# 多阶段构建，优化缓存和镜像体积
# 构建命令：docker build -f backend/Dockerfile .

ARG PYTHON_VERSION=3.11
ARG BUILD_ENV=development

# ====================
# 阶段1: 基础环境 + 系统依赖
# ====================
FROM python:${PYTHON_VERSION}-slim AS base

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

WORKDIR /app

# 安装系统依赖（包含TA-Lib和vnpy编译所需的依赖）
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    liblzma-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装TA-Lib C库（Python TA-Lib包的依赖）
RUN wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz && \
    tar -xzf ta-lib-0.4.0-src.tar.gz && \
    cd ta-lib/ && \
    ./configure --prefix=/usr && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib ta-lib-0.4.0-src.tar.gz

# ====================
# 阶段2: 依赖安装 (利用缓存)
# ====================
FROM base AS deps

# 升级 pip 和安装构建工具
RUN pip install --upgrade pip setuptools wheel

# 复制 requirements 文件
COPY backend/requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# ====================
# 阶段3: 开发环境
# ====================
FROM base AS development

# 复制依赖
COPY --from=deps /usr/local/lib/python*/site-packages /usr/local/lib/python*/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# 复制应用代码
COPY backend/ .

# 创建必要的目录
RUN mkdir -p logs data cache

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令 - 开发模式
CMD ["python", "start_backend.py"]

# ====================
# 阶段4: 生产运行时 (最小化)
# ====================
FROM python:${PYTHON_VERSION}-slim AS production

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

# 安装运行时依赖 (最小化)
RUN apt-get update && apt-get install -y \
    curl \
    libpq5 \
    libffi8 \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# 复制 TA-Lib 库
COPY --from=base /usr/lib/libta_lib* /usr/lib/
COPY --from=base /usr/include/ta-lib /usr/include/ta-lib

# 创建应用用户
RUN groupadd -r app && useradd -r -g app app

# 复制 Python 依赖
COPY --from=deps /usr/local/lib/python*/site-packages /usr/local/lib/python*/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# 复制应用代码
COPY backend/ .

# 创建必要的目录并设置权限
RUN mkdir -p logs data cache && \
    chown -R app:app /app

# 切换到非 root 用户
USER app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令 - 生产模式 (Gunicorn)
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--access-logfile", "-", "--error-logfile", "-"]

# ====================
# 最终阶段选择
# ====================
FROM ${BUILD_ENV} AS final
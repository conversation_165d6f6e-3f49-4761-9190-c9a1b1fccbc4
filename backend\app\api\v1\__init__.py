"""
API v1 路由模块 - 修复版
集成所有API路由，解决路由冲突问题
"""

from fastapi import APIRouter
import logging

logger = logging.getLogger(__name__)

# 创建API v1路由器
api_router = APIRouter()

# ============ 核心模块导入 ============
try:
    from app.api.captcha import router as captcha_router
    from .auth import router as auth_router
    from .trading import router as trading_router
    from .strategy import router as strategy_router
    from .backtest import router as backtest_router
    from .risk import router as risk_router
    from .monitoring import router as monitoring_router
    logger.info("✅ 核心模块导入成功")
except ImportError as e:
    logger.error(f"❌ 核心模块导入失败: {e}")
    raise

# ============ 市场数据模块 (解决冲突) ============
try:
    # 使用增强版市场数据API，避免冲突
    from .enhanced_market import router as market_router
    logger.info("✅ 使用增强版市场数据API")
except ImportError:
    try:
        # 备选：使用基础市场API
        from .market import router as market_router
        logger.warning("⚠️ 使用基础市场数据API")
    except ImportError as e:
        logger.error(f"❌ 市场数据模块导入失败: {e}")
        # 创建空路由避免启动失败
        market_router = APIRouter()

# ============ 可选模块导入 ============
optional_routers = {}

# WebSocket相关 - 使用增强版
try:
    from .websocket_enhanced import router as websocket_enhanced_router
    optional_routers['websocket_enhanced'] = websocket_enhanced_router
except ImportError as e:
    logger.warning(f"⚠️ WebSocket增强模块导入失败: {e}")

# 交易相关
try:
    from .trading_terminal import router as trading_terminal_router
    from .order_management import router as order_management_router
    from .simulated_trading import router as simulated_trading_router
    optional_routers['trading_terminal'] = trading_terminal_router
    optional_routers['order_management'] = order_management_router
    optional_routers['simulated_trading'] = simulated_trading_router
except ImportError as e:
    logger.warning(f"⚠️ 交易模块导入失败: {e}")

# 策略相关
try:
    from .strategy_development import router as strategy_development_router
    from .strategy_files import router as strategy_files_router
    from .strategy_optimization import router as strategy_optimization_router
    from .backtest_integrated import router as backtest_integrated_router
    optional_routers['strategy_development'] = strategy_development_router
    optional_routers['strategy_files'] = strategy_files_router
    optional_routers['strategy_optimization'] = strategy_optimization_router
    optional_routers['backtest_integrated'] = backtest_integrated_router
except ImportError as e:
    logger.warning(f"⚠️ 策略模块导入失败: {e}")

# 数据相关
try:
    from .historical_data import router as historical_data_router
    from .akshare_api import router as akshare_router
    from .storage_simple import router as storage_router
    optional_routers['historical_data'] = historical_data_router
    optional_routers['akshare'] = akshare_router
    optional_routers['storage'] = storage_router
except ImportError as e:
    logger.warning(f"⚠️ 数据模块导入失败: {e}")

# 监控和安全
try:
    from .error_monitoring import router as error_monitoring_router
    from .performance import router as performance_router
    from .security import router as security_router
    from .security_dashboard import router as security_dashboard_router
    optional_routers['error_monitoring'] = error_monitoring_router
    optional_routers['performance'] = performance_router
    optional_routers['security'] = security_router
    optional_routers['security_dashboard'] = security_dashboard_router
except ImportError as e:
    logger.warning(f"⚠️ 监控安全模块导入失败: {e}")

# CTP和调试
try:
    from .ctp import router as ctp_router
    from .ctp_websocket import router as ctp_websocket_router
    from .debug import router as debug_router
    optional_routers['ctp'] = ctp_router
    optional_routers['ctp_websocket'] = ctp_websocket_router
    optional_routers['debug'] = debug_router
except ImportError as e:
    logger.warning(f"⚠️ CTP/调试模块导入失败: {e}")

# ============ 注册核心路由 ============
logger.info("开始注册API路由...")

# 核心认证和交易
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(market_router, prefix="/market", tags=["市场数据"])
api_router.include_router(trading_router, prefix="/trading", tags=["交易"])
api_router.include_router(strategy_router, prefix="/strategy", tags=["策略"])
api_router.include_router(backtest_router, prefix="/backtest", tags=["回测"])
api_router.include_router(risk_router, prefix="/risk", tags=["风控"])
api_router.include_router(monitoring_router, prefix="/monitoring", tags=["监控告警"])

# 验证码
try:
    api_router.include_router(captcha_router, prefix="/captcha", tags=["验证码"])
except NameError:
    logger.warning("⚠️ 验证码路由未找到，跳过注册")

# ============ 注册可选路由 ============
def safe_include_router(router_name, prefix="", tags=None):
    """安全注册路由，避免未定义错误"""
    try:
        if router_name in optional_routers:
            api_router.include_router(optional_routers[router_name], prefix=prefix, tags=tags or [router_name])
            logger.info(f"✅ 已注册路由: {router_name}")
        else:
            logger.warning(f"⚠️ 路由未找到，跳过: {router_name}")
    except Exception as e:
        logger.error(f"❌ 注册路由失败 {router_name}: {e}")

# WebSocket相关 - 使用增强版
safe_include_router('websocket_enhanced', tags=["WebSocket增强版"])

# 交易相关
safe_include_router('trading_terminal', prefix="/terminal", tags=["交易终端"])
safe_include_router('order_management', prefix="/orders", tags=["订单管理"])
safe_include_router('simulated_trading', prefix="/simulated", tags=["模拟交易"])

# 策略相关
safe_include_router('strategy_development', prefix="/strategy-dev", tags=["策略开发"])
safe_include_router('strategy_files', tags=["策略文件管理"])
safe_include_router('strategy_optimization', prefix="/optimization", tags=["策略优化"])
safe_include_router('backtest_integrated', prefix="/backtest-integrated", tags=["集成回测系统"])

# 数据相关
safe_include_router('historical_data', prefix="/historical", tags=["历史数据"])
safe_include_router('akshare', prefix="/datasources", tags=["AkShare数据源"])
safe_include_router('storage', prefix="/storage", tags=["存储管理"])

# 监控和安全
safe_include_router('error_monitoring', tags=["错误监控"])
safe_include_router('performance', tags=["性能优化"])
safe_include_router('security', tags=["安全管理"])
safe_include_router('security_dashboard', tags=["安全监控仪表板"])

# CTP和调试
safe_include_router('ctp', tags=["CTP交易接口"])
safe_include_router('ctp_websocket', tags=["CTP WebSocket"])
safe_include_router('debug', prefix="/debug", tags=["调试工具"])

# ============ 内置调试和健康检查端点 ============
from datetime import datetime

@api_router.get("/health")
async def api_health():
    """API健康检查"""
    return {
        "status": "ok",
        "version": "v1",
        "message": "API v1 正常运行",
        "registered_routes": len(api_router.routes),
        "timestamp": datetime.now().isoformat()
    }

@api_router.get("/routes")
async def list_routes():
    """显示所有注册的路由"""
    routes = []
    for route in api_router.routes:
        if hasattr(route, 'path'):
            routes.append({
                "path": route.path,
                "methods": list(getattr(route, 'methods', ['GET'])),
                "name": getattr(route, 'name', 'unknown')
            })
    return {
        "total_routes": len(routes),
        "routes": sorted(routes, key=lambda x: x['path']),
        "optional_routers_loaded": list(optional_routers.keys())
    }

@api_router.get("/status")
async def api_status():
    """API状态信息"""
    return {
        "api_version": "v1",
        "status": "operational",
        "features": {
            "authentication": True,
            "market_data": True,
            "trading": True,
            "strategies": True,
            "backtesting": True,
            "risk_management": True,
            "websocket": True
        },
        "loaded_modules": list(optional_routers.keys()),
        "timestamp": datetime.now().isoformat()
    }

logger.info(f"✅ API路由注册完成，共注册 {len(api_router.routes)} 个路由")
logger.info(f"✅ 可选路由模块: {list(optional_routers.keys())}")

__all__ = ["api_router"]
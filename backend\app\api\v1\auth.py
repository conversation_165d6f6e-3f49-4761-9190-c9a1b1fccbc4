"""
修复后的认证API
提供完整的用户认证功能
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.security import create_access_token, verify_password, get_password_hash
from app.core.dependencies import get_current_active_user
from app.db.models.user import User
from app.schemas.auth import Token, UserCreate, UserResponse, LoginRequest

router = APIRouter()

# OAuth2 配置
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token")


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """验证用户"""
    # 查询用户
    result = await db.execute(
        select(User).where(User.username == username)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        return None
    
    # 验证密码
    if not verify_password(password, user.hashed_password):
        return None
    
    return user


@router.post("/register", response_model=UserResponse, status_code=201)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    用户注册
    
    - **username**: 用户名（唯一）
    - **email**: 邮箱地址（唯一）
    - **password**: 密码（至少6位）
    """
    # 检查用户名是否存在
    existing_user = await db.execute(
        select(User).where(User.username == user_data.username)
    )
    if existing_user.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否存在
    existing_email = await db.execute(
        select(User).where(User.email == user_data.email)
    )
    if existing_email.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="邮箱已被注册"
        )
    
    # 创建新用户
    user = User(
        username=user_data.username,
        email=user_data.email,
        hashed_password=get_password_hash(user_data.password),
        full_name=user_data.full_name,
        is_active=True,
        is_superuser=False,
        created_at=datetime.utcnow()
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        is_active=user.is_active,
        created_at=user.created_at
    )


@router.post("/login", response_model=Token)
async def login(
    credentials: LoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    用户登录 (JSON格式)

    - **username**: 用户名
    - **password**: 密码
    """
    # 验证用户
    user = await authenticate_user(db, credentials.username, credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login_at = datetime.utcnow()
    await db.commit()
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(user.id), "username": user.username}, 
        expires_delta=access_token_expires
    )
    
    # 构建用户信息
    user_info = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "role": getattr(user, 'role', 'user'),
        "permissions": getattr(user, 'permissions', []),
        "is_active": user.is_active
    }

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 1800,  # 30分钟
        "user": user_info
    }


@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    OAuth2兼容的登录端点 (Form格式)
    
    用于支持form-data格式的登录请求
    - **username**: 用户名
    - **password**: 密码
    """
    # 验证用户
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    
    # 更新最后登录时间
    user.last_login_at = datetime.utcnow()
    await db.commit()
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(user.id), "username": user.username}, 
        expires_delta=access_token_expires
    )
    
    # 构建用户信息
    user_info = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "role": getattr(user, 'role', 'user'),
        "permissions": getattr(user, 'permissions', []),
        "is_active": user.is_active
    }

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 1800,  # 30分钟
        "user": user_info
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户信息
    
    需要有效的访问令牌
    """
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        last_login_at=current_user.last_login_at
    )


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    用户登出
    
    在实际应用中，这里可以将token加入黑名单
    """
    try:
        # Extract token from authorization header
        auth_header = request.headers.get("authorization", "")
        token = auth_header.replace("Bearer ", "") if auth_header.startswith("Bearer ") else ""
        if token:
            # Add token to blacklist (using Redis or database)
            # For now, we'll log the logout action
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"User {current_user.username} logged out, token should be blacklisted")
            
        return {"message": "登出成功"}
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"登出处理失败: {str(e)}")
        return {"message": "登出成功"}  # Still return success to client


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新当前用户信息
    
    - **full_name**: 全名
    - **email**: 邮箱地址
    - **phone**: 电话号码
    """
    # 更新允许的字段
    allowed_fields = ["full_name", "email", "phone"]
    
    for field, value in user_update.items():
        if field in allowed_fields and value is not None:
            setattr(current_user, field, value)
    
    current_user.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(current_user)
    
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )


@router.post("/change-password")
async def change_password(
    old_password: str,
    new_password: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    修改密码
    
    - **old_password**: 旧密码
    - **new_password**: 新密码（至少6位）
    """
    # 验证旧密码
    if not verify_password(old_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="旧密码错误"
        )
    
    # 检查新密码长度
    if len(new_password) < 6:
        raise HTTPException(
            status_code=400,
            detail="新密码长度至少为6位"
        )
    
    # 更新密码
    current_user.hashed_password = get_password_hash(new_password)
    current_user.password_changed_at = datetime.utcnow()
    
    await db.commit()
    
    return {"message": "密码修改成功"}


@router.get("/check")
async def check_auth():
    """
    认证系统健康检查
    """
    return {
        "status": "healthy",
        "module": "auth",
        "timestamp": datetime.now().isoformat()
    }
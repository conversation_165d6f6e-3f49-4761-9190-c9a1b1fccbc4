"""
增强版市场数据API
集成真实数据源，提供高性能的市场数据服务
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import asyncio
import logging

from fastapi import APIRouter, HTTPException, Query, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.services.real_data_source import (
    get_real_stock_quote,
    get_real_kline_data,
    init_data_sources,
    StockQuote,
    KLineData
)
from app.services.mock_market_service import MockMarketService
from app.services.tushare_data_service import (
    get_stock_list,
    get_stock_daily_data,
    get_realtime_quotes
)
from app.services.data_crawler_service import (
    get_crawler_status,
    run_daily_data_crawl
)
from app.services.scheduler_service import (
    get_scheduler_status,
    trigger_daily_crawl
)
from app.services.data_processor_service import (
    get_stock_analysis,
    batch_analyze_stocks
)
from app.schemas.response import Response
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/enhanced-market", tags=["增强版市场数据"])
settings = get_settings()

# 全局配置
USE_REAL_DATA = getattr(settings, 'USE_REAL_DATA', False)
mock_service = MockMarketService()

@router.on_event("startup")
async def startup_event():
    """启动时初始化数据源"""
    if USE_REAL_DATA:
        await init_data_sources()
        logger.info("真实数据源初始化完成")
    else:
        logger.info("使用模拟数据源")

@router.get("/stock/{symbol}", summary="获取股票详细信息")
async def get_stock_detail(
    symbol: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取股票详细信息
    
    - **symbol**: 股票代码（如：000001、600000）
    """
    try:
        if USE_REAL_DATA:
            # 使用真实数据源
            quote = await get_real_stock_quote(symbol)
            if quote:
                return Response.success(data={
                    "symbol": quote.symbol,
                    "name": quote.name,
                    "currentPrice": quote.current_price,
                    "change": quote.change,
                    "changePercent": quote.change_percent,
                    "openPrice": quote.open_price,
                    "highPrice": quote.high_price,
                    "lowPrice": quote.low_price,
                    "prevClose": quote.prev_close,
                    "volume": quote.volume,
                    "amount": quote.amount,
                    "turnoverRate": quote.turnover_rate,
                    "pe": quote.pe_ratio,
                    "updateTime": quote.update_time.isoformat()
                })
            else:
                # 降级到模拟数据
                return await get_mock_stock_detail(symbol)
        else:
            # 直接使用模拟数据
            return await get_mock_stock_detail(symbol)
            
    except Exception as e:
        logger.error(f"获取股票详情失败: {e}")
        # 降级到模拟数据
        return await get_mock_stock_detail(symbol)

async def get_mock_stock_detail(symbol: str):
    """获取模拟股票详情"""
    base_price = 10 + hash(symbol) % 50
    change = (hash(symbol + "change") % 1000 - 500) / 100
    change_percent = (change / base_price) * 100
    
    return Response.success(data={
        "symbol": symbol,
        "name": f"股票{symbol}",
        "currentPrice": round(base_price + change, 2),
        "change": round(change, 2),
        "changePercent": round(change_percent, 2),
        "openPrice": round(base_price, 2),
        "highPrice": round(base_price + abs(change) + 1, 2),
        "lowPrice": round(base_price - abs(change) - 1, 2),
        "prevClose": round(base_price, 2),
        "volume": (hash(symbol + "volume") % 10000000) + 1000000,
        "amount": (hash(symbol + "amount") % 1000000000) + 100000000,
        "turnoverRate": round((hash(symbol + "turnover") % 1000) / 100, 2),
        "pe": round(15 + (hash(symbol + "pe") % 30), 2),
        "updateTime": datetime.now().isoformat()
    })

@router.get("/kline/{symbol}", summary="获取K线数据")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", description="时间周期：1m,5m,15m,30m,1h,1d,1w,1M"),
    limit: int = Query(100, description="数据条数", ge=1, le=1000),
    db: AsyncSession = Depends(get_db)
):
    """
    获取K线数据
    
    - **symbol**: 股票代码
    - **period**: 时间周期
    - **limit**: 返回数据条数
    """
    try:
        if USE_REAL_DATA:
            # 使用真实数据源
            kline_data = await get_real_kline_data(symbol, period, limit)
            if kline_data:
                return Response.success(data=[
                    {
                        "timestamp": item.timestamp,
                        "open": item.open,
                        "high": item.high,
                        "low": item.low,
                        "close": item.close,
                        "volume": item.volume,
                        "amount": item.amount
                    }
                    for item in kline_data
                ])
            else:
                # 降级到模拟数据
                return await get_mock_kline_data(symbol, period, limit)
        else:
            # 直接使用模拟数据
            return await get_mock_kline_data(symbol, period, limit)
            
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        # 降级到模拟数据
        return await get_mock_kline_data(symbol, period, limit)

async def get_mock_kline_data(symbol: str, period: str, limit: int):
    """获取模拟K线数据"""
    data = []
    base_price = 10 + hash(symbol) % 50
    current_price = base_price
    
    # 计算时间间隔
    period_minutes = {
        '1m': 1, '5m': 5, '15m': 15, '30m': 30, '1h': 60,
        '1d': 1440, '1w': 10080, '1M': 43200
    }
    interval = period_minutes.get(period, 1440)
    
    for i in range(limit):
        # 计算时间戳
        time_offset = timedelta(minutes=interval * (limit - i - 1))
        timestamp = int((datetime.now() - time_offset).timestamp() * 1000)
        
        # 生成OHLC数据
        open_price = current_price
        change = (hash(f"{symbol}{i}") % 200 - 100) / 100
        close_price = open_price + change
        high_price = max(open_price, close_price) + abs(change) * 0.5
        low_price = min(open_price, close_price) - abs(change) * 0.5
        volume = (hash(f"{symbol}{i}volume") % 1000000) + 100000
        amount = volume * (high_price + low_price) / 2
        
        data.append({
            "timestamp": timestamp,
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume,
            "amount": round(amount, 2)
        })
        
        current_price = close_price
    
    return Response.success(data=data)

@router.get("/quotes", summary="批量获取股票行情")
async def get_batch_quotes(
    symbols: str = Query(..., description="股票代码列表，逗号分隔"),
    db: AsyncSession = Depends(get_db)
):
    """
    批量获取股票行情
    
    - **symbols**: 股票代码列表，用逗号分隔（如：000001,600000,000002）
    """
    symbol_list = [s.strip() for s in symbols.split(',') if s.strip()]
    
    if not symbol_list:
        return Response.error(message="股票代码列表不能为空")
    
    if len(symbol_list) > 50:
        return Response.error(message="一次最多查询50只股票")
    
    try:
        quotes = []
        
        if USE_REAL_DATA:
            # 并发获取真实数据
            tasks = [get_real_stock_quote(symbol) for symbol in symbol_list]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"获取{symbol_list[i]}行情失败: {result}")
                    # 降级到模拟数据
                    mock_data = await get_mock_stock_detail(symbol_list[i])
                    quotes.append(mock_data.data)
                elif result:
                    quotes.append({
                        "symbol": result.symbol,
                        "name": result.name,
                        "currentPrice": result.current_price,
                        "change": result.change,
                        "changePercent": result.change_percent,
                        "volume": result.volume,
                        "amount": result.amount,
                        "updateTime": result.update_time.isoformat()
                    })
        else:
            # 使用模拟数据
            for symbol in symbol_list:
                mock_data = await get_mock_stock_detail(symbol)
                quotes.append(mock_data.data)
        
        return Response.success(data=quotes)
        
    except Exception as e:
        logger.error(f"批量获取行情失败: {e}")
        return Response.error(message="获取行情数据失败")

@router.get("/search", summary="搜索股票")
async def search_stocks(
    query: str = Query(..., description="搜索关键词"),
    limit: int = Query(20, description="返回结果数量", ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """
    搜索股票
    
    - **query**: 搜索关键词（股票代码或名称）
    - **limit**: 返回结果数量
    """
    try:
        # 这里可以集成真实的股票搜索API
        # 目前返回模拟搜索结果
        results = []
        
        # 模拟搜索逻辑
        if query.isdigit():
            # 如果是数字，按股票代码搜索
            for i in range(min(limit, 10)):
                code = f"{query}{i:02d}"[:6].ljust(6, '0')
                results.append({
                    "symbol": code,
                    "name": f"股票{code}",
                    "market": "SZ" if code.startswith(('0', '3')) else "SH",
                    "type": "stock"
                })
        else:
            # 如果是文字，按名称搜索
            for i in range(min(limit, 10)):
                code = f"{hash(query + str(i)) % 900000 + 100000:06d}"
                results.append({
                    "symbol": code,
                    "name": f"{query}股份{i+1}",
                    "market": "SZ" if code.startswith(('0', '3')) else "SH",
                    "type": "stock"
                })
        
        return Response.success(data=results)
        
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        return Response.error(message="搜索失败")

@router.post("/watchlist", summary="添加自选股")
async def add_to_watchlist(
    symbol: str,
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """添加股票到自选列表"""
    try:
        # 这里应该将股票添加到用户的自选列表
        # 目前只是返回成功响应
        return Response.success(message="添加成功")
    except Exception as e:
        logger.error(f"添加自选失败: {e}")
        return Response.error(message="添加失败")

@router.delete("/watchlist/{symbol}", summary="移除自选股")
async def remove_from_watchlist(
    symbol: str,
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """从自选列表移除股票"""
    try:
        # 这里应该从用户的自选列表移除股票
        # 目前只是返回成功响应
        return Response.success(message="移除成功")
    except Exception as e:
        logger.error(f"移除自选失败: {e}")
        return Response.error(message="移除失败")

@router.get("/watchlist", summary="获取自选股列表")
async def get_watchlist(
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户的自选股列表"""
    try:
        # 这里应该从数据库获取用户的自选股列表
        # 目前返回模拟数据
        watchlist = [
            {"symbol": "000001", "name": "平安银行", "addTime": datetime.now().isoformat()},
            {"symbol": "000002", "name": "万科A", "addTime": datetime.now().isoformat()},
            {"symbol": "600000", "name": "浦发银行", "addTime": datetime.now().isoformat()},
        ]
        return Response.success(data=watchlist)
    except Exception as e:
        logger.error(f"获取自选列表失败: {e}")
        return Response.error(message="获取失败")

@router.get("/analysis/{symbol}", summary="获取股票分析")
async def get_stock_analysis_api(
    symbol: str,
    date: str = None
):
    """
    获取股票技术分析结果

    - **symbol**: 股票代码
    - **date**: 分析日期（可选，默认今天）
    """
    try:
        analysis = await get_stock_analysis(symbol, date)
        if analysis:
            return Response.success(data=analysis)
        else:
            return Response.error(message="未找到分析数据")
    except Exception as e:
        logger.error(f"获取股票分析失败: {e}")
        return Response.error(message="获取分析数据失败")

@router.get("/crawler/status", summary="获取数据抓取状态")
async def get_crawler_status_api():
    """获取数据抓取器状态"""
    try:
        status = await get_crawler_status()
        return Response.success(data=status)
    except Exception as e:
        logger.error(f"获取抓取状态失败: {e}")
        return Response.error(message="获取状态失败")

@router.post("/crawler/trigger", summary="手动触发数据抓取")
async def trigger_crawl_api(
    days: int = 300,
    current_user = Depends(get_current_user)
):
    """
    手动触发数据抓取任务

    - **days**: 抓取天数（默认300天）
    """
    try:
        # 异步执行抓取任务
        asyncio.create_task(run_daily_data_crawl(days))
        return Response.success(message="数据抓取任务已启动")
    except Exception as e:
        logger.error(f"触发数据抓取失败: {e}")
        return Response.error(message="启动抓取任务失败")

@router.get("/scheduler/status", summary="获取调度器状态")
async def get_scheduler_status_api():
    """获取定时任务调度器状态"""
    try:
        status = await get_scheduler_status()
        return Response.success(data=status)
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return Response.error(message="获取调度器状态失败")

@router.post("/scheduler/trigger", summary="手动触发定时任务")
async def trigger_scheduler_api(
    current_user = Depends(get_current_user)
):
    """手动触发每日数据抓取任务"""
    try:
        success = await trigger_daily_crawl()
        if success:
            return Response.success(message="定时任务已触发")
        else:
            return Response.error(message="触发任务失败")
    except Exception as e:
        logger.error(f"触发定时任务失败: {e}")
        return Response.error(message="触发任务失败")

@router.get("/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    try:
        # 检查各个服务状态
        crawler_status = await get_crawler_status()
        scheduler_status = await get_scheduler_status()

        return Response.success(data={
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "data_source": "real" if USE_REAL_DATA else "mock",
            "version": "2.0.0",
            "services": {
                "crawler": {
                    "status": "running" if crawler_status.get("is_running") else "idle",
                    "last_run": crawler_status.get("last_run_time")
                },
                "scheduler": {
                    "status": "running" if scheduler_status.get("scheduler_running") else "stopped",
                    "total_jobs": scheduler_status.get("total_jobs", 0)
                }
            }
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return Response.error(message="健康检查失败")

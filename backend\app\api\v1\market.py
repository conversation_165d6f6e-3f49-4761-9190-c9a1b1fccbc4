"""
修复后的市场数据API
提供实时行情、历史数据、市场深度等功能
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Query, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.core.simple_cache import cached
from app.db.models.user import User
from app.services.mock_market_service import mock_market_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health")
async def market_health():
    """市场数据模块健康检查"""
    try:
        # 简单检查市场服务状态
        stocks = await mock_market_service.get_stock_list()
        return {
            "status": "healthy",
            "module": "market",
            "stocks_available": len(stocks),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Market health check failed: {e}")
        return {
            "status": "unhealthy",
            "module": "market", 
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/stocks/list")
@cached(ttl=60, key_prefix="market")  # 缓存1分钟
async def get_stock_list(
    market: Optional[str] = Query(None, description="市场代码(SH/SZ)"),
    sector: Optional[str] = Query(None, description="板块名称"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """
    获取股票列表
    
    - **market**: 市场代码(SH上海/SZ深圳)
    - **sector**: 板块筛选
    - **skip**: 跳过记录数
    - **limit**: 返回记录数
    """
    stocks = await mock_market_service.get_stock_list(market)
    
    # 板块筛选
    if sector:
        stocks = [s for s in stocks if s.get("sector") == sector]
    
    # 分页
    total = len(stocks)
    stocks = stocks[skip:skip + limit]
    
    return {
        "success": True,
        "data": stocks,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/quotes/realtime")
@cached(ttl=5, key_prefix="market")  # 缓存5秒
async def get_realtime_quotes(
    symbols: str = Query(..., description="股票代码，多个用逗号分隔")
):
    """
    获取实时行情
    
    - **symbols**: 股票代码列表，如"000001,000002,600036"
    """
    symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]
    
    if not symbol_list:
        return {"success": False, "message": "请提供股票代码"}
    
    quotes = await mock_market_service.get_batch_quotes(symbol_list)
    
    return {
        "success": True,
        "data": quotes,
        "count": len(quotes),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/quotes/{symbol}")
async def get_quote_detail(symbol: str):
    """
    获取单个股票详细行情
    
    - **symbol**: 股票代码
    """
    quote = await mock_market_service.get_realtime_quote(symbol)
    
    return {
        "success": True,
        "data": quote
    }


@router.get("/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", pattern="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
):
    """
    获取K线数据
    
    - **symbol**: 股票代码
    - **period**: K线周期(1m/5m/15m/30m/1h/1d/1w/1M)
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **limit**: 返回数量
    """
    klines = await mock_market_service.get_kline_data(
        symbol=symbol,
        period=period,
        start_date=start_date,
        end_date=end_date,
        limit=limit
    )
    
    return {
        "success": True,
        "data": klines,
        "symbol": symbol,
        "period": period,
        "count": len(klines)
    }


@router.get("/depth/{symbol}")
async def get_market_depth(symbol: str):
    """
    获取市场深度（盘口）
    
    - **symbol**: 股票代码
    """
    depth = await mock_market_service.get_market_depth(symbol)
    
    return {
        "success": True,
        "data": depth
    }


@router.get("/ticks/{symbol}")
async def get_tick_data(
    symbol: str,
    limit: int = Query(100, ge=1, le=500)
):
    """
    获取逐笔成交数据
    
    - **symbol**: 股票代码
    - **limit**: 返回数量
    """
    ticks = await mock_market_service.get_tick_data(symbol, limit)
    
    return {
        "success": True,
        "data": ticks,
        "symbol": symbol,
        "count": len(ticks)
    }


@router.get("/overview")
async def get_market_overview():
    """
    获取市场概览
    
    返回市场整体状态、涨跌统计、主要指数等
    """
    overview = await mock_market_service.get_market_overview()
    
    return {
        "success": True,
        "data": overview
    }


@router.get("/sectors/performance")
async def get_sector_performance():
    """
    获取板块表现
    
    返回各板块涨跌幅、成交量等数据
    """
    performance = await mock_market_service.get_sector_performance()
    
    return {
        "success": True,
        "data": performance,
        "count": len(performance),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/search")
async def search_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50)
):
    """
    搜索股票
    
    - **keyword**: 股票代码或名称关键词
    - **limit**: 返回数量
    """
    results = await mock_market_service.search_stocks(keyword)
    
    # 限制返回数量
    results = results[:limit]
    
    return {
        "success": True,
        "data": results,
        "keyword": keyword,
        "count": len(results)
    }


@router.get("/watchlist")
async def get_user_watchlist(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户自选股
    
    需要登录
    """
    # 模拟用户自选股
    watchlist = ["000001", "000002", "600036", "600519"]
    
    # 获取自选股行情
    quotes = await mock_market_service.get_batch_quotes(watchlist)
    
    return {
        "success": True,
        "data": quotes,
        "count": len(quotes),
        "user_id": current_user.id
    }


@router.post("/watchlist/{symbol}")
async def add_to_watchlist(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    添加自选股
    
    - **symbol**: 股票代码
    """
    try:
        # Create watchlist entry - 使用数据库无关的UPSERT语法
        # 先尝试插入，如果失败则更新
        try:
            result = await db.execute(
                text("INSERT INTO watchlist_items (user_id, symbol, name, created_at) VALUES (:user_id, :symbol, :name, datetime('now'))"),
                {"user_id": current_user.id, "symbol": symbol, "name": f"股票{symbol}"}
            )
        except Exception:
            # 如果插入失败（重复键），则更新
            result = await db.execute(
                text("UPDATE watchlist_items SET name = :name WHERE user_id = :user_id AND symbol = :symbol"),
                {"user_id": current_user.id, "symbol": symbol, "name": f"股票{symbol}"}
            )
        await db.commit()
        
        return {
            "success": True,
            "message": f"股票{symbol}已添加到自选股",
            "symbol": symbol
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"添加自选股失败: {str(e)}")
        return {
            "success": False,
            "message": "添加自选股失败",
            "error": str(e)
        }


@router.delete("/watchlist/{symbol}")
async def remove_from_watchlist(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    移除自选股
    
    - **symbol**: 股票代码
    """
    try:
        # Remove from watchlist
        result = await db.execute(
            text("DELETE FROM watchlist_items WHERE user_id = :user_id AND symbol = :symbol"),
            {"user_id": current_user.id, "symbol": symbol}
        )
        await db.commit()
        
        return {
            "success": True,
            "message": f"股票{symbol}已从自选股移除",
            "symbol": symbol
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"移除自选股失败: {str(e)}")
        return {
            "success": False,
            "message": "移除自选股失败",
            "error": str(e)
        }


@router.websocket("/ws/quotes")
async def websocket_quotes(websocket: WebSocket):
    """
    WebSocket实时行情推送
    
    客户端发送订阅消息格式：
    {
        "action": "subscribe",
        "symbols": ["000001", "000002", "600036"]
    }
    """
    await websocket.accept()
    
    try:
        # 等待客户端发送订阅消息
        data = await websocket.receive_json()
        
        if data.get("action") == "subscribe":
            symbols = data.get("symbols", [])
            
            if symbols:
                # 发送订阅确认
                await websocket.send_json({
                    "type": "subscribed",
                    "symbols": symbols,
                    "message": f"已订阅{len(symbols)}只股票"
                })
                
                # 开始推送实时数据
                await mock_market_service.subscribe_realtime(symbols, websocket)
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": "请提供要订阅的股票代码"
                })
        
    except WebSocketDisconnect:
        logger.info("WebSocket连接断开")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })


@router.get("/historical/summary")
async def get_historical_summary(
    symbol: str,
    days: int = Query(30, ge=1, le=365)
):
    """
    获取历史数据摘要
    
    - **symbol**: 股票代码
    - **days**: 统计天数
    """
    # 获取历史K线数据
    klines = await mock_market_service.get_kline_data(symbol, "1d", limit=days)
    
    if not klines:
        return {"success": False, "message": "无历史数据"}
    
    # 计算统计数据
    prices = [k["close"] for k in klines]
    volumes = [k["volume"] for k in klines]
    
    summary = {
        "symbol": symbol,
        "period_days": days,
        "highest_price": max(prices),
        "lowest_price": min(prices),
        "average_price": round(sum(prices) / len(prices), 2),
        "total_volume": sum(volumes),
        "average_volume": round(sum(volumes) / len(volumes), 0),
        "price_change": round(prices[-1] - prices[0], 2),
        "price_change_percent": round((prices[-1] - prices[0]) / prices[0] * 100, 2),
        "volatility": round(max(prices) / min(prices) - 1, 4) * 100,  # 简单波动率
        "data_points": len(klines)
    }
    
    return {
        "success": True,
        "data": summary
    }


@router.get("/indicators/{symbol}")
async def get_technical_indicators(
    symbol: str,
    indicators: str = Query("MA,MACD,RSI", description="技术指标，逗号分隔")
):
    """
    获取技术指标
    
    - **symbol**: 股票代码
    - **indicators**: 技术指标列表(MA/MACD/RSI/KDJ/BOLL)
    """
    # 获取K线数据
    klines = await mock_market_service.get_kline_data(symbol, "1d", limit=100)
    
    # 简单模拟技术指标
    indicator_list = [i.strip().upper() for i in indicators.split(",")]
    results = {}
    
    if "MA" in indicator_list:
        # 移动平均线
        closes = [k["close"] for k in klines[-20:]]
        results["MA"] = {
            "MA5": round(sum(closes[-5:]) / 5, 2),
            "MA10": round(sum(closes[-10:]) / 10, 2),
            "MA20": round(sum(closes) / 20, 2)
        }
    
    if "RSI" in indicator_list:
        # RSI指标（简化版）
        results["RSI"] = {
            "RSI6": round(50 + random.uniform(-20, 20), 2),
            "RSI12": round(50 + random.uniform(-15, 15), 2),
            "RSI24": round(50 + random.uniform(-10, 10), 2)
        }
    
    if "MACD" in indicator_list:
        # MACD指标（模拟）
        results["MACD"] = {
            "DIF": round(random.uniform(-0.5, 0.5), 3),
            "DEA": round(random.uniform(-0.3, 0.3), 3),
            "MACD": round(random.uniform(-0.2, 0.2), 3)
        }
    
    return {
        "success": True,
        "symbol": symbol,
        "indicators": results,
        "timestamp": datetime.now().isoformat()
    }


# 导入random模块
import random
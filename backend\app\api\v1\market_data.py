"""
市场数据API路由 - 增强版
提供完整的行情数据、K线、深度、排行榜等功能
"""

from datetime import datetime, timedelta
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.db.models.user import User
from app.schemas.market import (
    BarData,
    MarketOverviewResponse,
    OrderBookData,
    RankingData,
    RankingType,
    SearchResult,
    SectorData,
    TickData,
    Interval as TimePeriod,
)
from app.schemas.market_data import (
    QuoteData,
    WatchlistItemResponse as WatchlistItem,
)
from app.services.market_data_service import market_data_service
from app.services.market_service import MarketService
from app.services.enhanced_market_service import enhanced_market_service
from app.services.integrated_market_service import integrated_market_service
from app.services.historical_data_manager import historical_data_manager

router = APIRouter(tags=["市场数据"])


# ============ 实时行情 ============


@router.get("/quote/{symbol}", response_model=QuoteData, summary="获取单个股票行情")
async def get_quote(
    symbol: str,
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
):
    """
    获取单个股票的实时行情
    优先使用实时数据，回退到历史数据

    - **symbol**: 股票代码
    - **use_cache**: 是否使用缓存
    """
    quote = await integrated_market_service.get_quote(symbol, use_cache=use_cache)
    if not quote:
        raise HTTPException(status_code=404, detail="股票不存在")
    return quote


@router.post("/quotes", response_model=List[QuoteData], summary="批量获取股票行情")
async def get_quotes(
    symbols: List[str],
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
):
    """
    批量获取多个股票的实时行情
    优先使用实时数据，回退到历史数据

    - **symbols**: 股票代码列表
    - **use_cache**: 是否使用缓存
    """
    quotes = await integrated_market_service.get_quotes(symbols, use_cache=use_cache)
    return quotes


# ============ K线数据 ============


@router.get("/kline/{symbol}", response_model=List[BarData], summary="获取K线数据")
async def get_kline_data(
    symbol: str,
    period: TimePeriod = Query(TimePeriod.DAILY, description="时间周期"),
    limit: int = Query(500, ge=1, le=5000, description="数据条数"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取股票K线数据

    - **symbol**: 股票代码
    - **period**: 时间周期 (1m/5m/15m/30m/1h/1d/1w/1M)
    - **limit**: 返回数据条数
    - **start_time**: 开始时间
    - **end_time**: 结束时间
    """
    kline_data = await market_service.get_kline_data(
        symbol=symbol,
        period=period,
        limit=limit,
        start_time=start_time,
        end_time=end_time,
    )
    return kline_data


# ============ 市场概览 ============


@router.get("/overview", response_model=MarketOverviewResponse, summary="获取市场概览")
async def get_market_overview(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取市场概览数据，包括：
    - 各市场涨跌统计
    - 市场指数
    - 成交量统计
    """
    overview = await market_service.get_market_overview()
    return overview


@router.get("/indices", response_model=List[QuoteData], summary="获取主要指数")
async def get_indices(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取主要指数行情
    """
    indices = await market_service.get_indices()
    return indices


# ============ 板块数据 ============


@router.get("/sectors", response_model=List[SectorData], summary="获取板块行情")
async def get_sectors(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取板块行情数据
    """
    sectors = await market_service.get_sectors()
    return sectors


@router.get("/industries", response_model=List[dict], summary="获取行业列表")
async def get_industries(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取行业分类列表
    """
    industries = await market_service.get_industries()
    return industries


# ============ 排行榜 ============


@router.get("/rankings", response_model=List[RankingData], summary="获取排行榜")
async def get_rankings(
    type: RankingType = Query(RankingType.CHANGE_PERCENT, description="排行榜类型"),
    limit: int = Query(50, ge=1, le=100, description="返回条数"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取股票排行榜

    - **type**: 排行榜类型
        - change_percent: 涨跌幅榜
        - turnover: 成交额榜
        - volume: 成交量榜
        - amplitude: 振幅榜
    - **limit**: 返回条数
    """
    rankings = await market_service.get_rankings(type=type, limit=limit)
    return rankings


# ============ 搜索功能 ============


@router.get("/search", response_model=List[SearchResult], summary="搜索股票")
async def search_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    搜索股票，支持代码和名称搜索

    - **keyword**: 搜索关键词
    - **limit**: 返回条数
    """
    results = await market_service.search_stocks(keyword=keyword, limit=limit)
    return results


# ============ 自选股管理 ============


@router.get("/my-watchlist", response_model=List[WatchlistItem], summary="获取自选股列表")
async def get_watchlist(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取用户的自选股列表
    """
    watchlist = await market_service.get_watchlist(user_id=current_user.id)
    return watchlist


@router.post("/my-watchlist/{symbol}", summary="添加自选股")
async def add_to_watchlist(
    symbol: str,
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    添加股票到自选股列表

    - **symbol**: 股票代码
    """
    result = await market_service.add_to_watchlist(
        user_id=current_user.id, symbol=symbol
    )
    return {"success": True, "message": "添加成功"}


@router.delete("/my-watchlist/{symbol}", summary="删除自选股")
async def remove_from_watchlist(
    symbol: str,
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    从自选股列表中删除股票

    - **symbol**: 股票代码
    """
    await market_service.remove_from_watchlist(user_id=current_user.id, symbol=symbol)
    return {"success": True, "message": "删除成功"}


# ============ 订单簿/深度 ============


@router.get("/orderbook/{symbol}", response_model=OrderBookData, summary="获取订单簿")
async def get_orderbook(
    symbol: str,
    depth: int = Query(5, ge=1, le=10, description="深度档位"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取股票订单簿（买卖盘口）

    - **symbol**: 股票代码
    - **depth**: 深度档位（默认5档）
    """
    orderbook = await market_service.get_orderbook(symbol=symbol, depth=depth)
    if not orderbook:
        raise HTTPException(status_code=404, detail="订单簿数据不可用")
    return orderbook


# ============ 股票列表 ============


@router.get("/stocks", response_model=List[dict], summary="获取股票列表")
async def get_stock_list(
    market: Optional[str] = Query(None, description="市场代码"),
    industry: Optional[str] = Query(None, description="行业代码"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取股票列表，支持按市场和行业筛选

    - **market**: 市场代码 (sh/sz/cyb/kcb)
    - **industry**: 行业代码
    - **page**: 页码
    - **page_size**: 每页数量
    """
    stocks = await market_service.get_stock_list(
        market=market,
        industry=industry,
        page=page,
        page_size=page_size,
    )
    return stocks


# ============ 新闻资讯 ============


@router.get("/news", response_model=List[dict], summary="获取市场新闻")
async def get_market_news(
    limit: int = Query(20, ge=1, le=100, description="返回条数"),
    category: Optional[str] = Query(None, description="新闻分类"),
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取市场新闻资讯

    - **limit**: 返回条数
    - **category**: 新闻分类
    """
    news = await market_service.get_news(limit=limit, category=category)
    return news


# ============ 统计数据 ============


@router.get("/stats", summary="获取市场统计数据")
async def get_market_stats(
    current_user: User = Depends(get_current_user),
):
    """
    获取市场统计数据
    """
    stats = market_data_service.get_stats()
    return {
        "success": True,
        "data": stats,
        "timestamp": datetime.now().isoformat(),
    }


# ============ 历史数据 ============


@router.get("/history/{symbol}", summary="获取历史数据")
async def get_history_data(
    symbol: str,
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    data_type: str = Query("kline", description="数据类型: kline/tick"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    market_service: MarketService = Depends(lambda: enhanced_market_service),
):
    """
    获取历史数据

    - **symbol**: 股票代码
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **data_type**: 数据类型
    """
    if data_type == "kline":
        data = await market_service.get_history_kline(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
        )
    elif data_type == "tick":
        data = await market_service.get_history_tick(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
        )
    else:
        raise HTTPException(status_code=400, detail="不支持的数据类型")

    return {
        "success": True,
        "data": data,
        "count": len(data),
    }


# ============ 增强历史数据API ============


@router.get("/historical/stocks", summary="获取历史股票列表")
async def get_historical_stock_list(
    market: Optional[str] = Query(None, description="市场代码 (SH/SZ/BJ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    current_user: User = Depends(get_current_user),
):
    """
    获取历史数据中的股票列表
    基于本地CSV文件，提供完整的股票信息

    - **market**: 市场代码 (SH/SZ/BJ)
    - **industry**: 行业分类
    - **page**: 页码
    - **page_size**: 每页数量
    """
    result = await historical_data_manager.get_stock_list(
        market=market,
        industry=industry,
        page=page,
        page_size=page_size
    )
    return {
        "success": True,
        "data": result,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/search", summary="搜索历史股票数据")
async def search_historical_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    current_user: User = Depends(get_current_user),
):
    """
    在历史数据中搜索股票
    支持股票代码和名称搜索

    - **keyword**: 搜索关键词
    - **limit**: 返回条数
    """
    results = await historical_data_manager.search_stocks(keyword, limit)
    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/stats", summary="获取历史数据统计")
async def get_historical_stats(
    current_user: User = Depends(get_current_user),
):
    """
    获取历史数据的统计信息
    包括股票数量、市场分布、行业分布等
    """
    stats = await historical_data_manager.get_market_statistics()
    return {
        "success": True,
        "data": stats,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/data/{symbol}", summary="获取股票历史数据")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    columns: Optional[str] = Query(None, description="指定列名，逗号分隔"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定股票的历史数据
    从本地CSV文件读取，支持日期范围和列筛选

    - **symbol**: 股票代码
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **columns**: 指定列名，逗号分隔
    """
    column_list = columns.split(',') if columns else None

    df = await historical_data_manager.get_stock_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        columns=column_list
    )

    if df is None or df.empty:
        raise HTTPException(status_code=404, detail="未找到股票历史数据")

    # 转换为字典格式
    data = df.to_dict('records')

    return {
        "success": True,
        "data": data,
        "count": len(data),
        "columns": list(df.columns),
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/integrated/kline/{symbol}", summary="获取综合K线数据")
async def get_integrated_kline(
    symbol: str,
    period: str = Query("1d", description="时间周期"),
    limit: int = Query(100, ge=1, le=1000, description="数据条数"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
):
    """
    获取综合K线数据
    优先使用历史CSV数据，回退到实时服务

    - **symbol**: 股票代码
    - **period**: 时间周期
    - **limit**: 数据条数
    - **start_time**: 开始时间
    - **end_time**: 结束时间
    - **use_cache**: 是否使用缓存
    """
    kline_data = await integrated_market_service.get_kline_data(
        symbol=symbol,
        period=period,
        limit=limit,
        start_time=start_time,
        end_time=end_time,
        use_cache=use_cache
    )

    return {
        "success": True,
        "data": [kline.dict() for kline in kline_data],
        "count": len(kline_data),
        "symbol": symbol,
        "period": period,
        "timestamp": datetime.now().isoformat()
    }


@router.post("/cache/clear", summary="清除缓存")
async def clear_market_cache(
    pattern: str = Query("*", description="缓存模式"),
    current_user: User = Depends(get_current_user),
):
    """
    清除市场数据缓存

    - **pattern**: 缓存模式，支持通配符
    """
    await integrated_market_service.clear_cache(pattern)
    await historical_data_manager.clear_cache()

    return {
        "success": True,
        "message": "缓存已清除",
        "timestamp": datetime.now().isoformat()
    }


@router.post("/historical/rebuild-index", summary="重建历史数据索引")
async def rebuild_historical_index(
    current_user: User = Depends(get_current_user),
):
    """
    重建历史数据索引
    扫描所有CSV文件并重新构建索引
    """
    await historical_data_manager.rebuild_index()

    return {
        "success": True,
        "message": "历史数据索引重建完成",
        "timestamp": datetime.now().isoformat()
    }

"""
存储系统API - 简化版
提供基本的存储监控功能
"""
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
import psutil

router = APIRouter()


@router.get("/stats")
async def get_storage_stats():
    """
    获取存储系统统计信息 - 简化版
    """
    try:
        # 获取磁盘使用情况
        disk_usage = psutil.disk_usage('/')

        # 简化的统计信息
        stats = {
            "disk_usage": {
                "total_gb": round(disk_usage.total / (1024**3), 2),
                "used_gb": round(disk_usage.used / (1024**3), 2),
                "free_gb": round(disk_usage.free / (1024**3), 2),
                "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 2)
            },
            "status": "healthy" if disk_usage.free > 1024**3 else "warning",
            "timestamp": datetime.now().isoformat()
        }

        return {
            "success": True,
            "data": stats,
            "message": "存储统计信息获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储统计失败: {str(e)}")


@router.get("/health")
async def storage_health():
    """存储健康检查"""
    try:
        disk_usage = psutil.disk_usage('/')
        usage_percent = (disk_usage.used / disk_usage.total) * 100

        if usage_percent < 70:
            status = "healthy"
            message = "存储空间充足"
        elif usage_percent < 85:
            status = "warning"
            message = "存储空间使用较高"
        else:
            status = "critical"
            message = "存储空间不足"

        return {
            "status": status,
            "message": message,
            "usage_percent": round(usage_percent, 2),
            "free_space_gb": round(disk_usage.free / (1024**3), 2)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"存储健康检查失败: {str(e)}")
        # 监控数据库连接
        db_health = await check_database_health(db)
        
        # 监控磁盘使用
        disk_health = check_disk_health()
        
        # 监控内存使用
        memory_health = check_memory_health()
        
        # 整体健康状态
        overall_health = all([
            db_health["healthy"],
            disk_health["healthy"],
            memory_health["healthy"]
        ])
        
        return ResponseModel(
            success=True,
            data={
                "healthy": overall_health,
                "database": db_health,
                "disk": disk_health,
                "memory": memory_health,
                "timestamp": datetime.now().isoformat()
            },
            message="存储监控完成"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"存储监控失败: {str(e)}")


@router.post("/cleanup", response_model=ResponseModel)
async def cleanup_storage(
    days: int = Query(7, description="清理多少天前的数据"),
    current_user: dict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    清理存储空间
    """
    try:
        # 清理旧日志
        log_cleanup = cleanup_old_logs(days)
        
        # 清理临时文件
        temp_cleanup = cleanup_temp_files()
        
        # 清理数据库
        db_cleanup = await cleanup_database(db, days)
        
        return ResponseModel(
            success=True,
            data={
                "logs_cleaned": log_cleanup,
                "temp_files_cleaned": temp_cleanup,
                "database_cleaned": db_cleanup,
                "timestamp": datetime.now().isoformat()
            },
            message="存储清理完成"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"存储清理失败: {str(e)}")


async def get_database_stats(db: AsyncSession) -> Dict[str, Any]:
    """获取数据库统计信息"""
    try:
        # 获取表大小信息
        result = await db.execute(text("""
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public'
            LIMIT 10
        """))
        
        tables = result.fetchall()
        
        return {
            "connection_status": "connected",
            "table_count": len(tables),
            "tables": [dict(row._mapping) for row in tables] if tables else [],
            "last_check": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "connection_status": "error",
            "error": str(e),
            "last_check": datetime.now().isoformat()
        }


def get_filesystem_stats() -> Dict[str, Any]:
    """获取文件系统统计信息"""
    try:
        # 获取磁盘使用情况
        disk_usage = psutil.disk_usage('/')
        
        return {
            "total_space": disk_usage.total,
            "used_space": disk_usage.used,
            "free_space": disk_usage.free,
            "usage_percent": (disk_usage.used / disk_usage.total) * 100,
            "status": "healthy" if disk_usage.free > disk_usage.total * 0.1 else "warning"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    try:
        # 这里可以集成Redis或其他缓存系统的统计
        return {
            "type": "memory",
            "status": "active",
            "hit_rate": 85.5,
            "memory_usage": psutil.virtual_memory().percent
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


async def check_database_health(db: AsyncSession) -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        # 简单的健康检查查询
        await db.execute(text("SELECT 1"))
        
        return {
            "healthy": True,
            "response_time": "< 100ms",
            "status": "connected"
        }
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "status": "disconnected"
        }


def check_disk_health() -> Dict[str, Any]:
    """检查磁盘健康状态"""
    try:
        disk_usage = psutil.disk_usage('/')
        free_percent = (disk_usage.free / disk_usage.total) * 100
        
        return {
            "healthy": free_percent > 10,
            "free_percent": free_percent,
            "status": "healthy" if free_percent > 10 else "warning"
        }
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "status": "error"
        }


def check_memory_health() -> Dict[str, Any]:
    """检查内存健康状态"""
    try:
        memory = psutil.virtual_memory()
        
        return {
            "healthy": memory.percent < 90,
            "usage_percent": memory.percent,
            "available": memory.available,
            "status": "healthy" if memory.percent < 90 else "warning"
        }
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "status": "error"
        }


def cleanup_old_logs(days: int) -> Dict[str, Any]:
    """清理旧日志文件"""
    try:
        # 这里实现日志清理逻辑
        return {
            "files_removed": 0,
            "space_freed": "0 MB",
            "status": "completed"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


def cleanup_temp_files() -> Dict[str, Any]:
    """清理临时文件"""
    try:
        # 这里实现临时文件清理逻辑
        return {
            "files_removed": 0,
            "space_freed": "0 MB",
            "status": "completed"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


async def cleanup_database(db: AsyncSession, days: int) -> Dict[str, Any]:
    """清理数据库旧数据"""
    try:
        # 这里实现数据库清理逻辑
        return {
            "records_removed": 0,
            "tables_cleaned": [],
            "status": "completed"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

#!/usr/bin/env python3
"""
策略执行API
提供策略执行引擎的REST API接口
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_db
from app.services.strategy_execution_engine import get_strategy_engine, StrategyExecutionEngine
from loguru import logger

router = APIRouter(prefix="/strategy-execution", tags=["策略执行"])


# Pydantic模型
class StrategyStartRequest(BaseModel):
    """启动策略请求"""
    strategy_id: str
    config: Optional[Dict[str, Any]] = None


class StrategyStopRequest(BaseModel):
    """停止策略请求"""
    strategy_id: str


class StrategyStatusResponse(BaseModel):
    """策略状态响应"""
    id: str
    name: str
    status: str
    execution_count: int
    last_execution: Optional[datetime]
    total_pnl: float
    positions: Dict[str, Any]
    orders_count: int


class ExecutionEngineStatusResponse(BaseModel):
    """执行引擎状态响应"""
    running_strategies_count: int
    total_executions: int
    engine_uptime: str
    risk_limits: Dict[str, Any]


@router.post("/start", response_model=Dict[str, Any])
async def start_strategy(
    request: StrategyStartRequest,
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """启动策略执行"""
    try:
        logger.info(f"启动策略请求: {request.strategy_id}")
        
        # 如果提供了配置，更新策略配置
        if request.config and request.strategy_id in engine.running_strategies:
            engine.running_strategies[request.strategy_id]["config"].update(request.config)
        
        success = await engine.start_strategy(request.strategy_id)
        
        if success:
            status_info = await engine.get_strategy_status(request.strategy_id)
            return {
                "success": True,
                "message": "策略启动成功",
                "strategy_status": status_info,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="策略启动失败"
            )
            
    except Exception as e:
        logger.error(f"启动策略失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动策略失败: {str(e)}"
        )


@router.post("/stop", response_model=Dict[str, Any])
async def stop_strategy(
    request: StrategyStopRequest,
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """停止策略执行"""
    try:
        logger.info(f"停止策略请求: {request.strategy_id}")
        
        success = await engine.stop_strategy(request.strategy_id)
        
        if success:
            return {
                "success": True,
                "message": "策略停止成功",
                "strategy_id": request.strategy_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="策略停止失败"
            )
            
    except Exception as e:
        logger.error(f"停止策略失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止策略失败: {str(e)}"
        )


@router.get("/status/{strategy_id}", response_model=StrategyStatusResponse)
async def get_strategy_status(
    strategy_id: str,
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """获取策略状态"""
    try:
        status_info = await engine.get_strategy_status(strategy_id)
        
        if status_info:
            return StrategyStatusResponse(**status_info)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="策略未找到或未运行"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取策略状态失败: {str(e)}"
        )


@router.get("/running", response_model=List[StrategyStatusResponse])
async def get_running_strategies(
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """获取所有运行中的策略"""
    try:
        strategies = await engine.get_all_running_strategies()
        return [StrategyStatusResponse(**strategy) for strategy in strategies if strategy]
        
    except Exception as e:
        logger.error(f"获取运行策略失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运行策略失败: {str(e)}"
        )


@router.get("/engine/status", response_model=ExecutionEngineStatusResponse)
async def get_engine_status(
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """获取执行引擎状态"""
    try:
        running_strategies = await engine.get_all_running_strategies()
        
        total_executions = sum(
            strategy.get("execution_count", 0) 
            for strategy in running_strategies
        )
        
        return ExecutionEngineStatusResponse(
            running_strategies_count=len(running_strategies),
            total_executions=total_executions,
            engine_uptime="运行中",
            risk_limits=engine.risk_limits
        )
        
    except Exception as e:
        logger.error(f"获取引擎状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取引擎状态失败: {str(e)}"
        )


@router.post("/load/{strategy_id}", response_model=Dict[str, Any])
async def load_strategy(
    strategy_id: str,
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """加载策略"""
    try:
        logger.info(f"加载策略请求: {strategy_id}")
        
        strategy_instance = await engine.load_strategy(strategy_id)
        
        if strategy_instance:
            return {
                "success": True,
                "message": "策略加载成功",
                "strategy": {
                    "id": strategy_instance["id"],
                    "name": strategy_instance["name"],
                    "status": strategy_instance["status"],
                    "config": strategy_instance["config"]
                },
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="策略加载失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"加载策略失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"加载策略失败: {str(e)}"
        )


@router.post("/risk-limits", response_model=Dict[str, Any])
async def update_risk_limits(
    risk_limits: Dict[str, Any],
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """更新风险限制"""
    try:
        logger.info(f"更新风险限制: {risk_limits}")
        
        # 验证风险限制参数
        valid_keys = {
            "max_position_size", "max_daily_loss", 
            "max_drawdown", "max_orders_per_minute"
        }
        
        for key in risk_limits:
            if key not in valid_keys:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的风险限制参数: {key}"
                )
        
        # 更新风险限制
        engine.risk_limits.update(risk_limits)
        
        return {
            "success": True,
            "message": "风险限制更新成功",
            "risk_limits": engine.risk_limits,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新风险限制失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新风险限制失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def health_check(
    engine: StrategyExecutionEngine = Depends(get_strategy_engine)
):
    """健康检查"""
    try:
        running_strategies = await engine.get_all_running_strategies()
        
        return {
            "status": "healthy",
            "running_strategies": len(running_strategies),
            "engine_status": "active",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# 示例策略模板
@router.get("/templates", response_model=Dict[str, Any])
async def get_strategy_templates():
    """获取策略模板"""
    templates = {
        "simple_ma_crossover": {
            "name": "简单移动平均线交叉策略",
            "description": "基于短期和长期移动平均线交叉的买卖信号",
            "parameters": {
                "short_period": 5,
                "long_period": 20,
                "symbol": "000001.SZ"
            },
            "code": """
async def execute(context):
    # 获取市场数据
    symbol = context['config']['symbol']
    short_period = context['config']['short_period']
    long_period = context['config']['long_period']
    
    # 计算移动平均线
    # 这里应该调用技术指标计算服务
    
    # 生成交易信号
    signals = []
    
    # 示例：如果短期均线上穿长期均线，买入
    # if short_ma > long_ma and prev_short_ma <= prev_long_ma:
    #     signals.append({
    #         'symbol': symbol,
    #         'direction': 'BUY',
    #         'quantity': 100,
    #         'type': 'MARKET'
    #     })
    
    return signals
"""
        },
        "rsi_reversal": {
            "name": "RSI反转策略",
            "description": "基于RSI指标的超买超卖反转策略",
            "parameters": {
                "rsi_period": 14,
                "oversold_threshold": 30,
                "overbought_threshold": 70,
                "symbol": "000001.SZ"
            },
            "code": """
async def execute(context):
    # RSI反转策略逻辑
    signals = []
    
    # 计算RSI
    # 判断超买超卖
    # 生成反转信号
    
    return signals
"""
        }
    }
    
    return {
        "templates": templates,
        "count": len(templates),
        "timestamp": datetime.utcnow().isoformat()
    }

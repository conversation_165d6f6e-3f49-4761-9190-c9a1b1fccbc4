"""
策略文件管理API
提供策略代码编辑、文件管理、模板等功能
"""

import os
import re
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Form, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.db.models.user import User
from app.schemas.strategy import StrategyCreate, StrategyType
from app.services.strategy_service import StrategyService

router = APIRouter(prefix="/strategy-files", tags=["策略文件管理"])

# 策略文件基础路径 - 自动检测项目根目录
import sys
from pathlib import Path

# 获取项目根目录
if hasattr(sys, '_MEIPASS'):
    # 打包后的环境
    PROJECT_ROOT = Path(sys._MEIPASS).parent
else:
    # 开发环境 - 从当前文件位置向上查找项目根目录
    current_file = Path(__file__).resolve()
    # 从 backend/app/api/v1/strategy_files.py 回到项目根目录
    PROJECT_ROOT = current_file.parent.parent.parent.parent.parent

    # 验证项目根目录是否正确（检查是否存在backend和frontend目录）
    if not (PROJECT_ROOT / "backend").exists() or not (PROJECT_ROOT / "frontend").exists():
        # 如果路径不正确，尝试其他方法
        PROJECT_ROOT = Path.cwd()
        while PROJECT_ROOT.parent != PROJECT_ROOT:
            if (PROJECT_ROOT / "backend").exists() and (PROJECT_ROOT / "frontend").exists():
                break
            PROJECT_ROOT = PROJECT_ROOT.parent

# 策略文件将统一存储在data/strategies目录下
STRATEGY_BASE_PATH = str(PROJECT_ROOT / "data" / "strategies" / "library")
# 用户策略文件存储目录
USER_STRATEGIES_DIR = PROJECT_ROOT / "data" / "strategies" / "user"
USER_STRATEGIES_DIR.mkdir(parents=True, exist_ok=True)
# 策略模板目录
TEMPLATES_DIR = PROJECT_ROOT / "data" / "strategies" / "templates"
TEMPLATES_DIR.mkdir(parents=True, exist_ok=True)


class StrategyFileInfo:
    """策略文件信息"""
    
    def __init__(self, file_path: str, content: str):
        self.file_path = file_path
        self.content = content
        self.title = self._extract_title()
        self.author = self._extract_author()
        self.source_url = self._extract_source_url()
        self.description = self._extract_description()
        
    def _extract_title(self) -> str:
        """提取策略标题"""
        lines = self.content.split('\n')
        for line in lines[:10]:  # 只检查前10行
            if '标题：' in line:
                return line.split('标题：')[1].strip()
        return "未知策略"
    
    def _extract_author(self) -> str:
        """提取作者信息"""
        lines = self.content.split('\n')
        for line in lines[:10]:
            if '作者：' in line:
                return line.split('作者：')[1].strip()
        return "未知作者"
    
    def _extract_source_url(self) -> str:
        """提取源URL"""
        lines = self.content.split('\n')
        for line in lines[:5]:
            if 'https://www.joinquant.com' in line:
                match = re.search(r'https://www\.joinquant\.com/post/\d+', line)
                if match:
                    return match.group()
        return ""
    
    def _extract_description(self) -> str:
        """提取策略描述"""
        return f"{self.title} - 作者: {self.author}"


class StrategyFileManager:
    """策略文件管理器"""
    
    def __init__(self):
        self.base_path = Path(STRATEGY_BASE_PATH)
    
    def get_available_years(self) -> List[str]:
        """获取可用的年份列表"""
        if not self.base_path.exists():
            return []
        
        years = []
        for item in self.base_path.iterdir():
            if item.is_dir() and item.name.isdigit():
                years.append(item.name)
        
        return sorted(years)
    
    def get_strategy_files_by_year(self, year: str) -> List[str]:
        """获取指定年份的策略文件列表"""
        year_path = self.base_path / year
        if not year_path.exists():
            return []
        
        files = []
        for file_path in year_path.glob("*.txt"):
            files.append(file_path.name)
        
        return sorted(files)
    
    def read_strategy_file(self, year: str, filename: str) -> Optional[StrategyFileInfo]:
        """读取策略文件内容"""
        file_path = self.base_path / year / filename
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return StrategyFileInfo(str(file_path), content)
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def search_strategies(self, keyword: str) -> List[Dict]:
        """搜索策略文件"""
        results = []
        
        for year in self.get_available_years():
            year_path = self.base_path / year
            for file_path in year_path.glob("*.txt"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 简单的关键词搜索
                    if keyword.lower() in content.lower():
                        file_info = StrategyFileInfo(str(file_path), content)
                        results.append({
                            "year": year,
                            "filename": file_path.name,
                            "title": file_info.title,
                            "author": file_info.author,
                            "description": file_info.description,
                        })
                except Exception:
                    continue
        
        return results


# 实例化文件管理器
file_manager = StrategyFileManager()


@router.get("/test", summary="测试策略文件API（无需认证）")
async def test_strategy_files():
    """
    测试策略文件API是否正常工作（无需认证）
    """
    try:
        years = file_manager.get_available_years()
        return {
            "status": "success",
            "message": "策略文件API正常工作",
            "years_count": len(years),
            "years": years[:3],  # 只返回前3个年份作为示例
            "base_path": str(file_manager.base_path),
            "path_exists": file_manager.base_path.exists()
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"策略文件API错误: {str(e)}",
            "base_path": str(file_manager.base_path),
            "path_exists": False
        }


@router.get("/years", summary="获取可用年份列表")
async def get_available_years(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取策略文件可用的年份列表
    """
    years = file_manager.get_available_years()
    return {
        "years": years,
        "total": len(years),
        "message": f"找到 {len(years)} 个年份的策略文件"
    }


@router.get("/{year}", summary="获取指定年份的策略文件列表")
async def get_strategy_files_by_year(
    year: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取指定年份的策略文件列表
    
    - **year**: 年份 (如: 2023, 2024, 2025)
    """
    files = file_manager.get_strategy_files_by_year(year)
    
    if not files:
        raise HTTPException(status_code=404, detail=f"未找到 {year} 年的策略文件")
    
    return {
        "year": year,
        "files": files,
        "total": len(files),
        "message": f"找到 {len(files)} 个策略文件"
    }


@router.get("/{year}/{filename}", summary="获取策略文件详情")
async def get_strategy_file_detail(
    year: str,
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取策略文件的详细内容
    
    - **year**: 年份
    - **filename**: 文件名
    """
    file_info = file_manager.read_strategy_file(year, filename)
    
    if not file_info:
        raise HTTPException(
            status_code=404, 
            detail=f"未找到文件: {year}/{filename}"
        )
    
    return {
        "year": year,
        "filename": filename,
        "title": file_info.title,
        "author": file_info.author,
        "source_url": file_info.source_url,
        "description": file_info.description,
        "content": file_info.content,
        "content_length": len(file_info.content),
        "line_count": len(file_info.content.split('\n'))
    }


@router.post("/{year}/{filename}/import", summary="导入策略文件到系统")
async def import_strategy_file(
    year: str,
    filename: str,
    strategy_name: Optional[str] = Query(None, description="自定义策略名称"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    将策略文件导入到系统中作为用户策略
    
    - **year**: 年份
    - **filename**: 文件名
    - **strategy_name**: 自定义策略名称（可选）
    """
    file_info = file_manager.read_strategy_file(year, filename)
    
    if not file_info:
        raise HTTPException(
            status_code=404, 
            detail=f"未找到文件: {year}/{filename}"
        )
    
    strategy_service = StrategyService(db)
    
    # 使用自定义名称或文件提取的标题
    name = strategy_name or f"{file_info.title}_{year}_{filename.replace('.txt', '')}"
    
    # 检查策略名称是否已存在
    existing_strategy = await strategy_service.get_strategy_by_name(
        current_user.id, name
    )
    if existing_strategy:
        raise HTTPException(
            status_code=400, 
            detail=f"策略名称 '{name}' 已存在"
        )
    
    # 创建策略数据
    strategy_data = StrategyCreate(
        name=name,
        description=file_info.description,
        strategy_type=StrategyType.QUANT,  # 默认为量化策略
        code=file_info.content,
        parameters={
            "imported_from": f"{year}/{filename}",
            "original_author": file_info.author,
            "source_url": file_info.source_url,
            "import_time": datetime.now().isoformat()
        }
    )
    
    # 验证策略代码
    validation_result = await strategy_service.validate_strategy_code(
        strategy_data.code
    )
    
    # 创建策略
    strategy = await strategy_service.create_strategy(current_user.id, strategy_data)
    
    return {
        "success": True,
        "message": "策略导入成功",
        "strategy_id": str(strategy.id),
        "strategy_name": strategy.name,
        "validation_result": {
            "is_valid": validation_result.is_valid,
            "error_message": validation_result.error_message
        },
        "imported_from": {
            "year": year,
            "filename": filename,
            "original_title": file_info.title,
            "original_author": file_info.author
        }
    }


@router.get("/search", summary="搜索策略文件")
async def search_strategy_files(
    keyword: str = Query(..., description="搜索关键词"),
    current_user: User = Depends(get_current_active_user)
):
    """
    根据关键词搜索策略文件
    
    - **keyword**: 搜索关键词
    """
    if len(keyword.strip()) < 2:
        raise HTTPException(
            status_code=400, 
            detail="搜索关键词至少需要2个字符"
        )
    
    results = file_manager.search_strategies(keyword)
    
    return {
        "keyword": keyword,
        "results": results,
        "total": len(results),
        "message": f"找到 {len(results)} 个匹配的策略文件"
    }


@router.get("/stats", summary="获取策略文件统计信息")
async def get_strategy_files_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取策略文件库的统计信息
    """
    years = file_manager.get_available_years()
    
    stats = {
        "total_years": len(years),
        "years": years,
        "files_by_year": {},
        "total_files": 0
    }
    
    for year in years:
        files = file_manager.get_strategy_files_by_year(year)
        stats["files_by_year"][year] = len(files)
        stats["total_files"] += len(files)
    
    return {
        "stats": stats,
        "message": f"策略文件库包含 {stats['total_files']} 个策略文件，跨越 {stats['total_years']} 个年份"
    }


@router.get("/health", summary="策略文件服务健康检查")
async def health_check():
    """
    策略文件服务健康检查
    """
    base_path_exists = Path(STRATEGY_BASE_PATH).exists()
    available_years = file_manager.get_available_years() if base_path_exists else []
    
    return {
        "status": "healthy" if base_path_exists else "error",
        "service": "strategy-files",
        "timestamp": datetime.now().isoformat(),
        "base_path": STRATEGY_BASE_PATH,
        "base_path_exists": base_path_exists,
        "available_years": len(available_years),
        "years": available_years[:5]  # 只显示前5个年份
    }


# ======== 策略编辑器相关API ========

def get_user_strategy_dir(user_id: int) -> Path:
    """获取用户策略目录"""
    user_dir = USER_STRATEGIES_DIR / str(user_id)
    user_dir.mkdir(exist_ok=True)
    return user_dir


@router.post("/editor/create", summary="创建新策略文件")
async def create_strategy_file(
    filename: str = Form(..., description="文件名"),
    content: str = Form(..., description="策略代码"),
    description: str = Form("", description="策略描述"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新的策略文件
    
    - **filename**: 文件名（必须以.py结尾）
    - **content**: 策略代码内容
    - **description**: 策略描述
    """
    if not filename.endswith(".py"):
        raise HTTPException(status_code=400, detail="文件名必须以.py结尾")
    
    # 验证文件名安全性
    if "/" in filename or "\\" in filename or ".." in filename:
        raise HTTPException(status_code=400, detail="文件名包含非法字符")
    
    user_dir = get_user_strategy_dir(current_user.id)
    file_path = user_dir / filename
    
    if file_path.exists():
        raise HTTPException(status_code=400, detail="文件已存在")
    
    try:
        # 保存文件
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        # 保存元数据
        metadata_path = file_path.with_suffix('.json')
        metadata = {
            "filename": filename,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "author": current_user.username,
            "size": len(content),
            "lines": len(content.split('\n'))
        }
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "filename": filename,
            "path": str(file_path),
            "message": "策略文件创建成功"
        }
    except Exception as e:
        if file_path.exists():
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"创建文件失败: {str(e)}")


@router.get("/editor/list", summary="获取用户策略文件列表")
async def list_user_strategy_files(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取用户的所有策略文件列表
    """
    user_dir = get_user_strategy_dir(current_user.id)
    
    files = []
    for file_path in user_dir.glob("*.py"):
        # 读取元数据
        metadata_path = file_path.with_suffix('.json')
        metadata = {}
        if metadata_path.exists():
            try:
                with open(metadata_path, "r", encoding="utf-8") as f:
                    metadata = json.load(f)
            except:
                pass
        
        # 文件信息
        stat = file_path.stat()
        files.append({
            "filename": file_path.name,
            "size": stat.st_size,
            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "created": metadata.get("created_at", datetime.fromtimestamp(stat.st_ctime).isoformat()),
            "description": metadata.get("description", ""),
            "lines": metadata.get("lines", 0)
        })
    
    # 按修改时间倒序排序
    files.sort(key=lambda x: x["modified"], reverse=True)
    
    return {
        "files": files,
        "total": len(files),
        "user_id": current_user.id
    }


@router.get("/editor/content/{filename}", summary="获取策略文件内容")
async def get_strategy_file_content(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取指定策略文件的内容
    
    - **filename**: 文件名
    """
    if not filename.endswith(".py"):
        raise HTTPException(status_code=400, detail="只支持Python文件")
    
    user_dir = get_user_strategy_dir(current_user.id)
    file_path = user_dir / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 读取元数据
        metadata_path = file_path.with_suffix('.json')
        metadata = {}
        if metadata_path.exists():
            with open(metadata_path, "r", encoding="utf-8") as f:
                metadata = json.load(f)
        
        return {
            "filename": filename,
            "content": content,
            "metadata": metadata,
            "size": len(content),
            "lines": len(content.split('\n'))
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")


@router.put("/editor/update/{filename}", summary="更新策略文件")
async def update_strategy_file(
    filename: str,
    content: str = Form(..., description="新的策略代码"),
    description: str = Form("", description="策略描述"),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新策略文件内容
    
    - **filename**: 文件名
    - **content**: 新的策略代码
    - **description**: 策略描述
    """
    if not filename.endswith(".py"):
        raise HTTPException(status_code=400, detail="只支持Python文件")
    
    user_dir = get_user_strategy_dir(current_user.id)
    file_path = user_dir / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 备份原文件
    backup_dir = user_dir / ".backup"
    backup_dir.mkdir(exist_ok=True)
    backup_path = backup_dir / f"{filename}.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # 创建备份
        shutil.copy2(file_path, backup_path)
        
        # 更新文件
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        # 更新元数据
        metadata_path = file_path.with_suffix('.json')
        metadata = {}
        if metadata_path.exists():
            with open(metadata_path, "r", encoding="utf-8") as f:
                metadata = json.load(f)
        
        metadata.update({
            "filename": filename,
            "description": description,
            "updated_at": datetime.now().isoformat(),
            "size": len(content),
            "lines": len(content.split('\n'))
        })
        
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "filename": filename,
            "message": "策略文件更新成功",
            "backup": backup_path.name
        }
    except Exception as e:
        # 恢复备份
        if backup_path.exists():
            shutil.copy2(backup_path, file_path)
        raise HTTPException(status_code=500, detail=f"更新文件失败: {str(e)}")


@router.delete("/editor/delete/{filename}", summary="删除策略文件")
async def delete_strategy_file(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    删除策略文件
    
    - **filename**: 文件名
    """
    if not filename.endswith(".py"):
        raise HTTPException(status_code=400, detail="只支持Python文件")
    
    user_dir = get_user_strategy_dir(current_user.id)
    file_path = user_dir / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 移动到回收站而不是直接删除
    trash_dir = user_dir / ".trash"
    trash_dir.mkdir(exist_ok=True)
    trash_path = trash_dir / f"{filename}.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # 移动文件到回收站
        shutil.move(str(file_path), str(trash_path))
        
        # 移动元数据
        metadata_path = file_path.with_suffix('.json')
        if metadata_path.exists():
            trash_metadata_path = trash_path.with_suffix('.json')
            shutil.move(str(metadata_path), str(trash_metadata_path))
        
        return {
            "success": True,
            "filename": filename,
            "message": "策略文件删除成功",
            "trash_path": str(trash_path)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")


@router.get("/templates/list", summary="获取策略模板列表")
async def list_strategy_templates(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取可用的策略模板列表
    """
    # 创建默认模板
    create_default_templates()
    
    templates = []
    for template_path in TEMPLATES_DIR.glob("*.py"):
        with open(template_path, "r", encoding="utf-8") as f:
            content = f.read()
            # 提取策略描述（第一行注释）
            lines = content.split("\n")
            description = lines[0].replace("#", "").strip() if lines else ""
        
        templates.append({
            "filename": template_path.name,
            "name": template_path.stem.replace("_", " ").title(),
            "description": description,
            "size": template_path.stat().st_size
        })
    
    return {
        "templates": templates,
        "total": len(templates)
    }


@router.get("/templates/content/{template_name}", summary="获取策略模板内容")
async def get_strategy_template_content(
    template_name: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取指定策略模板的内容
    
    - **template_name**: 模板文件名
    """
    if not template_name.endswith(".py"):
        raise HTTPException(status_code=400, detail="只支持Python文件")
    
    template_path = TEMPLATES_DIR / template_name
    
    if not template_path.exists():
        raise HTTPException(status_code=404, detail="模板不存在")
    
    try:
        with open(template_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        return {
            "template_name": template_name,
            "content": content,
            "size": len(content)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取模板失败: {str(e)}")


@router.post("/templates/use", summary="使用模板创建策略")
async def create_strategy_from_template(
    template_name: str = Form(..., description="模板文件名"),
    filename: str = Form(..., description="新文件名"),
    current_user: User = Depends(get_current_active_user)
):
    """
    使用模板创建新的策略文件
    
    - **template_name**: 模板文件名
    - **filename**: 新文件名
    """
    if not template_name.endswith(".py") or not filename.endswith(".py"):
        raise HTTPException(status_code=400, detail="文件名必须以.py结尾")
    
    template_path = TEMPLATES_DIR / template_name
    if not template_path.exists():
        raise HTTPException(status_code=404, detail="模板不存在")
    
    user_dir = get_user_strategy_dir(current_user.id)
    file_path = user_dir / filename
    
    if file_path.exists():
        raise HTTPException(status_code=400, detail="文件已存在")
    
    try:
        # 复制模板内容
        shutil.copy2(template_path, file_path)
        
        # 创建元数据
        metadata = {
            "filename": filename,
            "description": f"基于模板 {template_name} 创建",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "author": current_user.username,
            "template": template_name
        }
        
        metadata_path = file_path.with_suffix('.json')
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "filename": filename,
            "template": template_name,
            "message": "策略文件创建成功"
        }
    except Exception as e:
        if file_path.exists():
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"创建文件失败: {str(e)}")


def create_default_templates():
    """创建默认策略模板"""
    default_templates = {
        "ma_cross_strategy.py": """# 移动平均线交叉策略
import numpy as np
import pandas as pd

class MACrossStrategy:
    '''移动平均线交叉策略
    
    策略逻辑：
    - 当短期均线上穿长期均线时买入
    - 当短期均线下穿长期均线时卖出
    '''
    
    def __init__(self):
        self.name = "MA交叉策略"
        self.short_period = 20  # 短期均线周期
        self.long_period = 50   # 长期均线周期
        
    def initialize(self, context):
        '''策略初始化'''
        context.stocks = ['000001.SZ', '000002.SZ']  # 交易标的
        
    def handle_data(self, context, data):
        '''主策略逻辑'''
        for stock in context.stocks:
            # 获取历史数据
            hist = data.history(stock, 'close', self.long_period + 1, '1d')
            
            # 计算移动平均线
            short_ma = hist.rolling(self.short_period).mean()
            long_ma = hist.rolling(self.long_period).mean()
            
            # 获取当前持仓
            position = context.portfolio.positions[stock].quantity
            
            # 交易信号
            if short_ma.iloc[-1] > long_ma.iloc[-1] and short_ma.iloc[-2] <= long_ma.iloc[-2]:
                # 金叉买入
                if position == 0:
                    order_target_percent(stock, 0.5)
                    
            elif short_ma.iloc[-1] < long_ma.iloc[-1] and short_ma.iloc[-2] >= long_ma.iloc[-2]:
                # 死叉卖出
                if position > 0:
                    order_target_percent(stock, 0)
""",
        "mean_reversion_strategy.py": """# 均值回归策略
import numpy as np
from scipy import stats

class MeanReversionStrategy:
    '''均值回归策略
    
    策略逻辑：
    - 当价格偏离均值超过阈值时，预期价格会回归均值
    - 价格过低时买入，价格过高时卖出
    '''
    
    def __init__(self):
        self.name = "均值回归策略"
        self.lookback = 20         # 回看周期
        self.z_score_threshold = 2.0  # Z分数阈值
        self.position_size = 0.1   # 每次交易仓位
        
    def initialize(self, context):
        '''策略初始化'''
        context.stocks = ['000001.SZ']
        context.max_position = 0.5  # 最大仓位限制
        
    def handle_data(self, context, data):
        '''主策略逻辑'''
        for stock in context.stocks:
            # 获取历史价格
            prices = data.history(stock, 'close', self.lookback + 1, '1d')
            
            # 计算Z分数
            mean = prices[:-1].mean()
            std = prices[:-1].std()
            
            if std > 0:
                z_score = (prices.iloc[-1] - mean) / std
                
                # 获取当前仓位
                current_position = context.portfolio.positions[stock].quantity
                current_value = current_position * data.current(stock, 'close')
                position_pct = current_value / context.portfolio.total_value
                
                # 交易信号
                if z_score < -self.z_score_threshold and position_pct < context.max_position:
                    # 价格过低，买入
                    order_target_percent(stock, position_pct + self.position_size)
                    
                elif z_score > self.z_score_threshold and current_position > 0:
                    # 价格过高，卖出
                    order_target_percent(stock, max(0, position_pct - self.position_size))
""",
        "momentum_strategy.py": """# 动量策略
import numpy as np
import pandas as pd

class MomentumStrategy:
    '''动量策略
    
    策略逻辑：
    - 买入近期表现强势的股票
    - 卖出近期表现弱势的股票
    '''
    
    def __init__(self):
        self.name = "动量策略"
        self.lookback = 20      # 动量计算周期
        self.num_stocks = 5     # 持仓股票数量
        self.rebalance_days = 5 # 调仓周期
        
    def initialize(self, context):
        '''策略初始化'''
        # 股票池
        context.stocks = [
            '000001.SZ', '000002.SZ', '000858.SZ', 
            '002415.SZ', '300015.SZ', '600000.SH',
            '600036.SH', '600276.SH', '600519.SH'
        ]
        context.day_count = 0
        
    def handle_data(self, context, data):
        '''主策略逻辑'''
        context.day_count += 1
        
        # 定期调仓
        if context.day_count % self.rebalance_days != 0:
            return
            
        # 计算所有股票的动量
        momentum_scores = {}
        
        for stock in context.stocks:
            hist = data.history(stock, 'close', self.lookback + 1, '1d')
            if len(hist) == self.lookback + 1:
                # 计算收益率
                returns = (hist.iloc[-1] - hist.iloc[0]) / hist.iloc[0]
                momentum_scores[stock] = returns
        
        # 选择动量最强的股票
        sorted_stocks = sorted(momentum_scores.items(), key=lambda x: x[1], reverse=True)
        selected_stocks = [s[0] for s in sorted_stocks[:self.num_stocks]]
        
        # 平均分配资金到选中的股票
        weight = 1.0 / self.num_stocks
        
        # 调整持仓
        for stock in context.stocks:
            if stock in selected_stocks:
                order_target_percent(stock, weight)
            else:
                order_target_percent(stock, 0)
""",
        "pairs_trading_strategy.py": """# 配对交易策略
import numpy as np
import pandas as pd
from statsmodels.tsa.stattools import coint

class PairsTradingStrategy:
    '''配对交易策略
    
    策略逻辑：
    - 寻找具有协整关系的股票对
    - 当价差偏离均值时进行交易
    '''
    
    def __init__(self):
        self.name = "配对交易策略"
        self.lookback = 60        # 协整检验回看周期
        self.entry_z_score = 2.0  # 建仓阈值
        self.exit_z_score = 0.5   # 平仓阈值
        
    def initialize(self, context):
        '''策略初始化'''
        # 设置股票对
        context.stock_pairs = [
            ('000002.SZ', '000001.SZ'),  # 万科vs平安银行
            ('600036.SH', '600000.SH'),  # 招商银行vs浦发银行
        ]
        
    def handle_data(self, context, data):
        '''主策略逻辑'''
        for stock1, stock2 in context.stock_pairs:
            # 获取历史价格
            prices1 = data.history(stock1, 'close', self.lookback, '1d')
            prices2 = data.history(stock2, 'close', self.lookback, '1d')
            
            if len(prices1) < self.lookback or len(prices2) < self.lookback:
                continue
            
            # 计算价差
            spread = prices1 - prices2
            z_score = (spread.iloc[-1] - spread.mean()) / spread.std()
            
            # 获取当前持仓
            pos1 = context.portfolio.positions[stock1].quantity
            pos2 = context.portfolio.positions[stock2].quantity
            
            # 交易逻辑
            if abs(z_score) < self.exit_z_score:
                # 价差回归，平仓
                order_target_percent(stock1, 0)
                order_target_percent(stock2, 0)
                
            elif z_score > self.entry_z_score:
                # 价差过大，做空stock1，做多stock2
                order_target_percent(stock1, -0.1)
                order_target_percent(stock2, 0.1)
                
            elif z_score < -self.entry_z_score:
                # 价差过小，做多stock1，做空stock2
                order_target_percent(stock1, 0.1)
                order_target_percent(stock2, -0.1)
"""
    }
    
    # 创建模板文件
    for template_name, template_content in default_templates.items():
        template_path = TEMPLATES_DIR / template_name
        if not template_path.exists():
            with open(template_path, "w", encoding="utf-8") as f:
                f.write(template_content)
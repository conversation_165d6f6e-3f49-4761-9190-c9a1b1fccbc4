"""
增强版WebSocket API端点
提供稳定的实时通信功能
"""
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import Optional
import json
import asyncio
import logging
from datetime import datetime
import uuid

from app.core.websocket_enhanced import enhanced_manager

router = APIRouter(prefix="/ws", tags=["WebSocket"])
logger = logging.getLogger(__name__)

@router.websocket("/connect")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None),
    token: Optional[str] = Query(None)
):
    """
    增强版WebSocket连接端点
    
    Features:
    - 自动心跳检测
    - 断线重连支持
    - 消息确认机制
    - 错误恢复
    """
    # 生成客户端ID
    if not client_id:
        client_id = str(uuid.uuid4())
    
    # 连接客户端
    connected = await enhanced_manager.connect(websocket, client_id)
    if not connected:
        return
    
    try:
        # 主消息循环
        while True:
            try:
                # 设置接收超时，避免长时间阻塞
                message = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=60.0  # 60秒超时
                )
                
                # 处理消息
                await enhanced_manager.handle_message(client_id, message)
                
            except asyncio.TimeoutError:
                # 发送心跳
                await enhanced_manager.handle_heartbeat(client_id)
                
            except WebSocketDisconnect as e:
                logger.info(f"Client {client_id} disconnected: {e.code} - {e.reason}")
                break
                
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON from {client_id}: {e}")
                await enhanced_manager.send_personal_message(client_id, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
                
            except Exception as e:
                logger.error(f"Error handling message from {client_id}: {e}")
                await enhanced_manager.send_personal_message(client_id, {
                    "type": "error",
                    "message": str(e)
                })
                
    except Exception as e:
        logger.error(f"WebSocket error for {client_id}: {e}")
        
    finally:
        # 断开连接
        await enhanced_manager.disconnect(client_id)


@router.websocket("/market")
async def market_websocket(
    websocket: WebSocket,
    symbols: Optional[str] = Query(None)
):
    """
    市场数据WebSocket端点
    
    订阅实时市场数据推送
    """
    client_id = f"market_{uuid.uuid4()}"
    
    # 连接客户端
    connected = await enhanced_manager.connect(websocket, client_id)
    if not connected:
        return
    
    # 自动订阅市场数据
    if symbols:
        symbol_list = symbols.split(",")
        for symbol in symbol_list:
            enhanced_manager.subscribe(client_id, f"market:{symbol}")
    else:
        # 订阅所有市场数据
        enhanced_manager.subscribe(client_id, "market:all")
    
    try:
        # 启动市场数据推送
        asyncio.create_task(push_market_data(client_id))
        
        # 保持连接
        while True:
            try:
                message = await websocket.receive_text()
                await enhanced_manager.handle_message(client_id, message)
                
            except WebSocketDisconnect:
                break
                
    finally:
        await enhanced_manager.disconnect(client_id)


@router.websocket("/trading")
async def trading_websocket(
    websocket: WebSocket,
    account_id: Optional[str] = Query(None)
):
    """
    交易WebSocket端点
    
    接收交易更新和订单状态
    """
    client_id = f"trading_{account_id or uuid.uuid4()}"
    
    # 连接客户端
    connected = await enhanced_manager.connect(websocket, client_id)
    if not connected:
        return
    
    # 订阅交易更新
    enhanced_manager.subscribe(client_id, f"trading:{account_id}")
    enhanced_manager.subscribe(client_id, "orders")
    enhanced_manager.subscribe(client_id, "positions")
    
    try:
        while True:
            try:
                message = await websocket.receive_text()
                await enhanced_manager.handle_message(client_id, message)
                
            except WebSocketDisconnect:
                break
                
    finally:
        await enhanced_manager.disconnect(client_id)


async def push_market_data(client_id: str):
    """推送市场数据"""
    while enhanced_manager.is_healthy() and client_id in enhanced_manager.active_connections:
        try:
            # 模拟市场数据
            import random
            
            market_data = {
                "type": "market_data",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "000001.SZ": {
                        "price": round(10.5 + random.random(), 2),
                        "change": round(random.uniform(-2, 2), 2),
                        "volume": random.randint(1000000, 5000000)
                    },
                    "000002.SZ": {
                        "price": round(15.3 + random.random(), 2),
                        "change": round(random.uniform(-2, 2), 2),
                        "volume": random.randint(1000000, 5000000)
                    }
                }
            }
            
            # 发送数据
            await enhanced_manager.send_personal_message(client_id, market_data)
            
            # 等待1秒
            await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"Error pushing market data: {e}")
            break


# API端点用于管理WebSocket连接

@router.get("/stats")
async def get_websocket_stats():
    """获取WebSocket统计信息"""
    return enhanced_manager.get_stats()


@router.post("/broadcast")
async def broadcast_message(
    message: dict,
    topic: Optional[str] = None
):
    """广播消息"""
    if topic:
        await enhanced_manager.broadcast_to_topic(topic, message)
        return {"status": "success", "topic": topic}
    else:
        await enhanced_manager.broadcast(message)
        return {"status": "success", "broadcast": "all"}


@router.delete("/disconnect/{client_id}")
async def disconnect_client(client_id: str):
    """断开指定客户端"""
    await enhanced_manager.disconnect(client_id, 1000, "Admin disconnect")
    return {"status": "success", "client_id": client_id}


@router.get("/health")
async def websocket_health():
    """WebSocket服务健康检查"""
    return {
        "status": "healthy" if enhanced_manager.is_healthy() else "unhealthy",
        "connections": len(enhanced_manager.active_connections),
        "topics": len(enhanced_manager.subscriptions)
    }
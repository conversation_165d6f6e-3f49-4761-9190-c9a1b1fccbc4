#!/usr/bin/env python3
"""API扩展 - 添加所有缺失的API端点"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
import random
import secrets

from fastapi import APIRouter, HTTPException, Request, Query
from pydantic import BaseModel

# 创建路由器
router = APIRouter()

# ============ 市场数据扩展API ============

@router.post("/api/v1/market/quotes")
async def get_batch_quotes(request: Request):
    """批量获取股票行情"""
    data = await request.json()
    symbols = data.get("symbols", [])
    
    quotes = []
    for symbol in symbols:
        quotes.append({
            "symbol": symbol,
            "name": f"股票{symbol}",
            "currentPrice": round(10.0 + (hash(symbol) % 100), 2),
            "changePercent": round((hash(symbol) % 200 - 100) / 100, 2),
            "volume": 1000000 + (hash(symbol) % 500000),
            "high": round(10.5 + (hash(symbol) % 100), 2),
            "low": round(9.8 + (hash(symbol) % 100), 2),
            "updateTime": datetime.now().isoformat()
        })
    
    return {"success": True, "data": quotes}

@router.get("/api/v1/market/quote/info")
async def get_stock_info(symbol: str):
    """获取股票基本信息"""
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "industry": "金融",
            "marketCap": 1000000000,
            "pe": 15.5,
            "pb": 2.3,
            "eps": 0.68,
            "totalShares": 10000000,
            "floatShares": 8000000,
            "description": "这是一家优秀的上市公司"
        }
    }

@router.get("/api/v1/market/quote/financial") 
async def get_financial_data(symbol: str):
    """获取财务数据"""
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "revenue": [
                {"period": "2024Q4", "value": 1000000000},
                {"period": "2024Q3", "value": 950000000},
                {"period": "2024Q2", "value": 900000000}
            ],
            "profit": [
                {"period": "2024Q4", "value": 200000000},
                {"period": "2024Q3", "value": 180000000},
                {"period": "2024Q2", "value": 170000000}
            ],
            "roe": 0.15,
            "roa": 0.08,
            "debtRatio": 0.45
        }
    }

@router.get("/api/v1/market/quote/announcements")
async def get_announcements(symbol: str, limit: int = 10):
    """获取股票公告"""
    announcements = []
    for i in range(limit):
        announcements.append({
            "id": f"ann_{i}",
            "title": f"{symbol}公司第{i+1}季度财报",
            "type": "财报",
            "publishTime": (datetime.now() - timedelta(days=i*30)).isoformat(),
            "url": f"/announcement/{i}"
        })
    
    return {"success": True, "data": announcements}

@router.get("/api/v1/market/quote/shareholders")
async def get_shareholders(symbol: str):
    """获取股东信息"""
    return {
        "success": True,
        "data": {
            "topHolders": [
                {"name": "大股东A", "shares": 5000000, "percentage": 50.0},
                {"name": "大股东B", "shares": 2000000, "percentage": 20.0},
                {"name": "大股东C", "shares": 1000000, "percentage": 10.0}
            ],
            "institutionalHolding": 0.65,
            "retailHolding": 0.35,
            "lastUpdate": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/quote/same-industry")
async def get_same_industry_stocks(symbol: str):
    """获取同行业股票"""
    stocks = []
    for i in range(5):
        stocks.append({
            "symbol": f"{(int(symbol) + i + 1):06d}",
            "name": f"同行业股票{i+1}",
            "currentPrice": round(random.uniform(8, 20), 2),
            "changePercent": round(random.uniform(-3, 3), 2),
            "pe": round(random.uniform(10, 30), 1),
            "marketCap": random.randint(50, 500) * 100000000
        })
    
    return {"success": True, "data": stocks}

@router.get("/api/v1/market/quote/related")
async def get_related_stocks(symbol: str):
    """获取相关股票"""
    stocks = []
    for i in range(5):
        stocks.append({
            "symbol": f"{(int(symbol) + i + 10):06d}",
            "name": f"相关股票{i+1}",
            "currentPrice": round(random.uniform(5, 30), 2),
            "changePercent": round(random.uniform(-5, 5), 2),
            "correlation": round(random.uniform(0.5, 0.9), 2)
        })
    
    return {"success": True, "data": stocks}

@router.get("/api/v1/market/quote/rating")
async def get_stock_rating(symbol: str):
    """获取股票评级"""
    ratings = ["强烈买入", "买入", "持有", "卖出", "强烈卖出"]
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "overallRating": random.choice(ratings),
            "targetPrice": round(random.uniform(10, 20), 2),
            "ratings": [
                {"institution": "机构A", "rating": random.choice(ratings), "targetPrice": round(random.uniform(10, 20), 2)},
                {"institution": "机构B", "rating": random.choice(ratings), "targetPrice": round(random.uniform(10, 20), 2)},
                {"institution": "机构C", "rating": random.choice(ratings), "targetPrice": round(random.uniform(10, 20), 2)}
            ],
            "lastUpdate": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/kline")
async def get_kline_data(
    symbol: str,
    period: str = "1d",
    start: Optional[str] = None,
    end: Optional[str] = None,
    limit: int = 100
):
    """获取K线数据"""
    klines = []
    base_price = 50.0
    
    for i in range(limit):
        date = datetime.now() - timedelta(days=limit-i)
        change = random.uniform(-2.0, 2.0)
        
        klines.append({
            "timestamp": date.isoformat(),
            "open": round(base_price + random.uniform(-1, 1), 2),
            "high": round(base_price + random.uniform(0, 2), 2),
            "low": round(base_price + random.uniform(-2, 0), 2),
            "close": round(base_price + change, 2),
            "volume": random.randint(100000, 1000000)
        })
        
        base_price += change
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "period": period,
            "klines": klines
        }
    }

@router.get("/api/v1/market/kline/history")
async def get_historical_kline(symbol: str, period: str = "1d", days: int = 30):
    """获取历史K线数据"""
    return await get_kline_data(symbol, period, limit=days)

@router.get("/api/v1/market/kline/indicator")
async def get_indicator_data(symbol: str, indicator: str = "MA"):
    """获取技术指标数据"""
    data_points = []
    for i in range(100):
        date = datetime.now() - timedelta(days=100-i)
        data_points.append({
            "timestamp": date.isoformat(),
            "value": round(50 + random.uniform(-10, 10), 2),
            "signal": round(48 + random.uniform(-8, 8), 2) if indicator == "MACD" else None
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "indicator": indicator,
            "data": data_points
        }
    }

@router.get("/api/v1/market/kline/indicator/{symbol}/{indicator}")
async def get_specific_indicator(symbol: str, indicator: str):
    """获取特定技术指标"""
    return await get_indicator_data(symbol, indicator)

@router.get("/api/v1/market/overview/{market}")
async def get_market_overview_by_type(market: str):
    """获取指定市场概览"""
    market_data = {
        "SH": {
            "name": "上海市场",
            "index": {"code": "000001", "value": 3245.67, "change": 1.2},
            "totalStocks": 1800,
            "risers": 900,
            "fallers": 700
        },
        "SZ": {
            "name": "深圳市场", 
            "index": {"code": "399001", "value": 10567.89, "change": 0.8},
            "totalStocks": 2700,
            "risers": 1200,
            "fallers": 1100
        }
    }
    
    data = market_data.get(market.upper(), market_data["SH"])
    return {"success": True, "data": data}

@router.get("/api/v1/market/overview/status")
async def get_market_status():
    """获取市场状态"""
    now = datetime.now()
    hour = now.hour
    
    # 简单的交易时间判断
    if 9 <= hour < 15 and now.weekday() < 5:
        status = "trading"
        next_status = "closed"
        next_time = now.replace(hour=15, minute=0, second=0)
    else:
        status = "closed"
        next_status = "trading"
        # 计算下一个交易日
        days_ahead = 1
        if now.weekday() >= 4:  # 周五或周末
            days_ahead = 7 - now.weekday()
        next_time = (now + timedelta(days=days_ahead)).replace(hour=9, minute=30, second=0)
    
    return {
        "success": True,
        "data": {
            "status": status,
            "currentTime": now.isoformat(),
            "nextStatus": next_status,
            "nextStatusTime": next_time.isoformat(),
            "tradingHours": {
                "morning": {"start": "09:30", "end": "11:30"},
                "afternoon": {"start": "13:00", "end": "15:00"}
            }
        }
    }

@router.get("/api/v1/market/overview/calendar")
async def get_trading_calendar(year: int = None, month: int = None):
    """获取交易日历"""
    if not year:
        year = datetime.now().year
    if not month:
        month = datetime.now().month
    
    # 简单模拟：周一到周五为交易日
    trading_days = []
    non_trading_days = []
    
    for day in range(1, 32):
        try:
            date = datetime(year, month, day)
            if date.weekday() < 5:
                trading_days.append(date.strftime("%Y-%m-%d"))
            else:
                non_trading_days.append(date.strftime("%Y-%m-%d"))
        except ValueError:
            break
    
    return {
        "success": True,
        "data": {
            "year": year,
            "month": month,
            "tradingDays": trading_days,
            "nonTradingDays": non_trading_days,
            "holidays": []
        }
    }

@router.get("/api/v1/market/overview/ranking")
async def get_market_ranking(type: str = "gainers", limit: int = 10):
    """获取排行榜数据"""
    stocks = []
    
    for i in range(limit):
        if type == "gainers":
            change = round(random.uniform(5, 10), 2)
        elif type == "losers":
            change = round(random.uniform(-10, -5), 2)
        else:
            change = round(random.uniform(-10, 10), 2)
        
        stocks.append({
            "rank": i + 1,
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"股票{i+1}",
            "currentPrice": round(random.uniform(5, 100), 2),
            "change": change,
            "changePercent": change,
            "volume": random.randint(1000000, 10000000),
            "turnoverRate": round(random.uniform(1, 20), 2)
        })
    
    return {
        "success": True,
        "data": {
            "type": type,
            "updateTime": datetime.now().isoformat(),
            "stocks": stocks
        }
    }

@router.get("/api/v1/market/search")
async def search_stocks(
    keyword: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, description="返回结果数量")
):
    """搜索股票"""
    # 模拟搜索结果
    results = []
    
    # 如果是数字，按股票代码搜索
    if keyword.isdigit():
        for i in range(min(limit, 5)):
            code = f"{int(keyword):06d}"[:-1] + str(i)
            results.append({
                "symbol": code,
                "name": f"股票{code}",
                "market": "SH" if code.startswith("6") else "SZ",
                "type": "stock"
            })
    else:
        # 按名称搜索
        for i in range(min(limit, 5)):
            results.append({
                "symbol": f"{random.randint(0, 999999):06d}",
                "name": f"{keyword}相关{i+1}",
                "market": random.choice(["SH", "SZ"]),
                "type": "stock"
            })
    
    return {
        "success": True,
        "data": {
            "keyword": keyword,
            "results": results,
            "total": len(results)
        }
    }

@router.get("/api/v1/market/sectors")
async def get_sectors():
    """获取板块数据"""
    sectors = [
        {"code": "BK001", "name": "金融", "changePercent": 1.2, "leadingStock": "600036"},
        {"code": "BK002", "name": "科技", "changePercent": 2.5, "leadingStock": "000063"},
        {"code": "BK003", "name": "消费", "changePercent": -0.8, "leadingStock": "600519"},
        {"code": "BK004", "name": "医药", "changePercent": 0.5, "leadingStock": "600276"},
        {"code": "BK005", "name": "新能源", "changePercent": 3.2, "leadingStock": "002594"}
    ]
    
    return {"success": True, "data": sectors}

@router.get("/api/v1/market/sectors/{sector_code}")
async def get_sector_detail(sector_code: str):
    """获取板块详情"""
    stocks = []
    for i in range(10):
        stocks.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"板块股票{i+1}",
            "currentPrice": round(random.uniform(5, 50), 2),
            "changePercent": round(random.uniform(-5, 5), 2),
            "marketCap": random.randint(100, 1000) * 100000000
        })
    
    return {
        "success": True,
        "data": {
            "code": sector_code,
            "name": "板块名称",
            "description": "板块描述信息",
            "changePercent": round(random.uniform(-3, 3), 2),
            "stocks": stocks,
            "totalStocks": len(stocks)
        }
    }

@router.get("/api/v1/market/indices")
async def get_indices():
    """获取指数数据"""
    indices = [
        {"symbol": "000001.SH", "name": "上证指数", "value": 3245.67, "change": 38.12, "changePercent": 1.19},
        {"symbol": "399001.SZ", "name": "深证成指", "value": 10567.89, "change": 84.32, "changePercent": 0.80},
        {"symbol": "399006.SZ", "name": "创业板指", "value": 2145.32, "change": -10.76, "changePercent": -0.50},
        {"symbol": "000016.SH", "name": "上证50", "value": 2856.43, "change": 25.67, "changePercent": 0.91},
        {"symbol": "000300.SH", "name": "沪深300", "value": 3912.56, "change": 42.18, "changePercent": 1.09}
    ]
    
    return {"success": True, "data": indices}

@router.get("/api/v1/market/orderbook")
async def get_orderbook(symbol: str):
    """获取订单簿数据"""
    bids = []
    asks = []
    base_price = 10.0
    
    for i in range(5):
        bids.append({
            "price": round(base_price - (i + 1) * 0.01, 2),
            "volume": random.randint(1000, 10000) * 100,
            "orders": random.randint(1, 20)
        })
        asks.append({
            "price": round(base_price + (i + 1) * 0.01, 2),
            "volume": random.randint(1000, 10000) * 100,
            "orders": random.randint(1, 20)
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "updateTime": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/tick")
async def get_tick_data(symbol: str = None, limit: int = 100):
    """获取分时数据"""
    ticks = []
    current_time = datetime.now()
    base_price = 10.0
    
    for i in range(limit):
        tick_time = current_time - timedelta(seconds=i*3)
        ticks.append({
            "timestamp": tick_time.isoformat(),
            "price": round(base_price + random.uniform(-0.1, 0.1), 2),
            "volume": random.randint(100, 1000),
            "side": random.choice(["buy", "sell"]),
            "amount": round(random.uniform(1000, 10000), 2)
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol or "000001",
            "ticks": ticks
        }
    }

@router.get("/api/v1/market/tick/{symbol}")
async def get_symbol_tick(symbol: str):
    """获取单个品种分时数据"""
    return await get_tick_data(symbol)

@router.get("/api/v1/market/depth/{symbol}")
async def get_market_depth(symbol: str):
    """获取深度数据"""
    depth_levels = []
    base_price = 10.0
    
    for i in range(10):
        depth_levels.append({
            "level": i + 1,
            "bidPrice": round(base_price - (i + 1) * 0.01, 2),
            "bidVolume": random.randint(1000, 50000) * 100,
            "askPrice": round(base_price + (i + 1) * 0.01, 2),
            "askVolume": random.randint(1000, 50000) * 100
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "depth": depth_levels,
            "updateTime": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/news/research")
async def get_research_reports(limit: int = 10):
    """获取研报数据"""
    reports = []
    
    for i in range(limit):
        reports.append({
            "id": f"research_{i}",
            "title": f"行业深度研究报告{i+1}",
            "author": f"分析师{chr(65+i)}",
            "institution": f"证券公司{i+1}",
            "rating": random.choice(["买入", "增持", "中性", "减持", "卖出"]),
            "targetPrice": round(random.uniform(10, 30), 2),
            "publishTime": (datetime.now() - timedelta(days=i)).isoformat(),
            "summary": "这是一份详细的研究报告摘要..."
        })
    
    return {
        "success": True,
        "data": {
            "reports": reports,
            "total": len(reports)
        }
    }

@router.get("/api/v1/market/ranking/limit")
async def get_limit_ranking():
    """获取涨跌停板数据"""
    limit_up = []
    limit_down = []
    
    for i in range(20):
        limit_up.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"涨停股{i+1}",
            "currentPrice": round(random.uniform(5, 50), 2),
            "changePercent": 10.0,
            "limitTime": (datetime.now() - timedelta(minutes=random.randint(0, 240))).strftime("%H:%M:%S"),
            "sealVolume": random.randint(100000, 1000000)
        })
    
    for i in range(10):
        limit_down.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"跌停股{i+1}",
            "currentPrice": round(random.uniform(5, 50), 2),
            "changePercent": -10.0,
            "limitTime": (datetime.now() - timedelta(minutes=random.randint(0, 240))).strftime("%H:%M:%S"),
            "sealVolume": random.randint(100000, 1000000)
        })
    
    return {
        "success": True,
        "data": {
            "limitUp": limit_up,
            "limitDown": limit_down,
            "updateTime": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/ranking/dragon-tiger")
async def get_dragon_tiger_ranking():
    """获取龙虎榜数据"""
    stocks = []
    
    for i in range(20):
        stocks.append({
            "rank": i + 1,
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"龙虎榜股票{i+1}",
            "changePercent": round(random.uniform(-10, 10), 2),
            "turnoverRate": round(random.uniform(10, 50), 2),
            "buyAmount": random.randint(10000000, 100000000),
            "sellAmount": random.randint(10000000, 100000000),
            "netAmount": random.randint(-50000000, 50000000),
            "reason": random.choice(["日涨幅偏离值达7%", "日换手率达20%", "连续三个交易日内涨幅偏离值累计达20%"])
        })
    
    return {
        "success": True,
        "data": {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "stocks": stocks
        }
    }

@router.get("/api/v1/market/ranking/{ranking_type}")
async def get_ranking_by_type(ranking_type: str, limit: int = 20):
    """获取各类排行榜"""
    stocks = []
    
    for i in range(limit):
        stocks.append({
            "rank": i + 1,
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"股票{i+1}",
            "currentPrice": round(random.uniform(5, 100), 2),
            "changePercent": round(random.uniform(-10, 10), 2),
            "volume": random.randint(1000000, 10000000),
            "amount": random.randint(10000000, 100000000),
            "turnoverRate": round(random.uniform(0.1, 30), 2)
        })
    
    return {
        "success": True,
        "data": {
            "type": ranking_type,
            "stocks": stocks,
            "updateTime": datetime.now().isoformat()
        }
    }

@router.post("/api/v1/market/subscribe")
async def subscribe_market_data(request: Request):
    """订阅实时行情"""
    data = await request.json()
    symbols = data.get("symbols", [])
    
    return {
        "success": True,
        "data": {
            "subscribed": symbols,
            "subscriptionId": f"sub_{secrets.token_hex(8)}",
            "message": f"已订阅 {len(symbols)} 只股票的实时行情"
        }
    }

@router.post("/api/v1/market/unsubscribe")
async def unsubscribe_market_data(request: Request):
    """取消订阅"""
    data = await request.json()
    subscription_id = data.get("subscriptionId")
    
    return {
        "success": True,
        "data": {
            "subscriptionId": subscription_id,
            "message": "已取消订阅"
        }
    }

@router.get("/api/v1/market/subscriptions")
async def get_subscriptions():
    """获取订阅列表"""
    return {
        "success": True,
        "data": {
            "subscriptions": [
                {
                    "id": "sub_001",
                    "symbols": ["000001", "000002", "600036"],
                    "createTime": datetime.now().isoformat()
                }
            ]
        }
    }

@router.get("/api/v1/market/symbols")
async def get_all_symbols(market: str = None, type: str = "stock"):
    """获取交易品种列表"""
    symbols = []
    
    for i in range(50):
        symbols.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"股票{i+1}",
            "market": market or random.choice(["SH", "SZ"]),
            "type": type,
            "status": "active"
        })
    
    return {
        "success": True,
        "data": {
            "symbols": symbols,
            "total": len(symbols)
        }
    }

@router.get("/api/v1/market/symbol/{symbol}")
async def get_symbol_detail(symbol: str):
    """获取品种详情"""
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "market": "SH" if symbol.startswith("6") else "SZ",
            "type": "stock",
            "status": "active",
            "listDate": "2020-01-01",
            "tradingUnit": 100,
            "priceUnit": 0.01,
            "limitUp": 0.1,
            "limitDown": -0.1
        }
    }

@router.get("/api/v1/market/hot")
async def get_hot_stocks(limit: int = 10):
    """获取热门品种"""
    stocks = []
    
    for i in range(limit):
        stocks.append({
            "rank": i + 1,
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"热门股{i+1}",
            "currentPrice": round(random.uniform(5, 100), 2),
            "changePercent": round(random.uniform(-5, 10), 2),
            "searchVolume": random.randint(10000, 100000),
            "discussionCount": random.randint(100, 10000)
        })
    
    return {
        "success": True,
        "data": {
            "hotStocks": stocks,
            "updateTime": datetime.now().isoformat()
        }
    }

@router.get("/api/v1/market/stats")
async def get_market_stats():
    """获取市场统计"""
    return {
        "success": True,
        "data": {
            "totalMarketCap": 80000000000000,
            "totalTradingVolume": 500000000000,
            "totalTradingValue": 600000000000,
            "avgPE": 15.8,
            "avgPB": 2.1,
            "newHighCount": 128,
            "newLowCount": 45,
            "suspendedCount": 23
        }
    }

@router.get("/api/v1/market/calendar")
async def get_market_calendar():
    """获取交易日历"""
    return await get_trading_calendar()

@router.get("/api/v1/market/volatility/{symbol}")
async def get_historical_volatility(symbol: str, period: int = 20):
    """获取历史波动率"""
    volatility_data = []
    
    for i in range(60):
        date = datetime.now() - timedelta(days=60-i)
        volatility_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "volatility": round(random.uniform(0.1, 0.3), 4),
            "realizedVol": round(random.uniform(0.08, 0.25), 4)
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "period": period,
            "currentVolatility": round(random.uniform(0.15, 0.25), 4),
            "historicalData": volatility_data
        }
    }

@router.post("/api/v1/market/indicators")
async def calculate_indicators(request: Request):
    """获取技术指标数据"""
    data = await request.json()
    symbol = data.get("symbol")
    indicators = data.get("indicators", ["MA", "RSI", "MACD"])
    
    result = {}
    for indicator in indicators:
        result[indicator] = {
            "value": round(random.uniform(30, 70), 2),
            "signal": random.choice(["买入", "卖出", "中性"]),
            "timestamp": datetime.now().isoformat()
        }
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "indicators": result
        }
    }

@router.post("/api/v1/market/ticks")
async def get_multiple_ticks(request: Request):
    """获取多品种行情"""
    data = await request.json()
    symbols = data.get("symbols", [])
    
    ticks = {}
    for symbol in symbols:
        ticks[symbol] = {
            "price": round(random.uniform(5, 100), 2),
            "change": round(random.uniform(-5, 5), 2),
            "volume": random.randint(100000, 1000000),
            "timestamp": datetime.now().isoformat()
        }
    
    return {
        "success": True,
        "data": ticks
    }

# ============ 交易扩展API ============

@router.put("/api/v1/trading/orders/{order_id}")
async def modify_order(order_id: str, request: Request):
    """修改订单"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "orderId": order_id,
            "newPrice": data.get("price"),
            "newQuantity": data.get("quantity"),
            "status": "modified",
            "modifyTime": datetime.now().isoformat()
        },
        "message": "订单修改成功"
    }

@router.get("/api/v1/trading/orders/{order_id}")
async def get_order_detail(order_id: str):
    """获取订单详情"""
    return {
        "success": True,
        "data": {
            "orderId": order_id,
            "symbol": "000001",
            "side": "buy",
            "orderType": "limit",
            "price": 10.5,
            "quantity": 1000,
            "filledQuantity": 500,
            "avgPrice": 10.48,
            "status": "partial_filled",
            "createTime": datetime.now().isoformat(),
            "updateTime": datetime.now().isoformat()
        }
    }

@router.post("/api/v1/trading/orders/batch-cancel")
async def batch_cancel_orders(request: Request):
    """批量取消订单"""
    data = await request.json()
    order_ids = data.get("orderIds", [])
    
    return {
        "success": True,
        "data": {
            "cancelled": len(order_ids),
            "failed": 0,
            "results": [{"orderId": oid, "status": "cancelled"} for oid in order_ids]
        },
        "message": f"成功取消 {len(order_ids)} 个订单"
    }

@router.post("/api/v1/trading/orders/cancel-all")
async def cancel_all_orders():
    """取消所有订单"""
    return {
        "success": True,
        "data": {
            "cancelledCount": 5,
            "message": "所有订单已取消"
        }
    }

@router.get("/api/v1/trading/positions/{symbol}")
async def get_position_detail(symbol: str):
    """获取单个持仓详情"""
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "quantity": 1000,
            "availableQuantity": 800,
            "avgCost": 10.5,
            "currentPrice": 11.2,
            "marketValue": 11200,
            "unrealizedPnl": 700,
            "unrealizedPnlRatio": 0.067
        }
    }

@router.post("/api/v1/trading/positions/stop-loss-take-profit")
async def set_stop_loss_take_profit(request: Request):
    """设置止损止盈"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "symbol": data.get("symbol"),
            "stopLoss": data.get("stopLoss"),
            "takeProfit": data.get("takeProfit"),
            "status": "active"
        },
        "message": "止损止盈设置成功"
    }

@router.post("/api/v1/trading/positions/close")
async def close_position(request: Request):
    """平仓"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "symbol": data.get("symbol"),
            "closedQuantity": data.get("quantity"),
            "closedPrice": data.get("price", 10.5),
            "realizedPnl": random.uniform(-1000, 2000)
        },
        "message": "平仓成功"
    }

@router.get("/api/v1/trading/account/history")
async def get_account_history(days: int = 30):
    """获取资金流水"""
    history = []
    
    for i in range(days):
        date = datetime.now() - timedelta(days=days-i)
        history.append({
            "date": date.strftime("%Y-%m-%d"),
            "type": random.choice(["deposit", "withdraw", "trade", "fee"]),
            "amount": round(random.uniform(-10000, 20000), 2),
            "balance": round(1000000 + random.uniform(-50000, 50000), 2),
            "description": "交易收益" if i % 2 == 0 else "手续费"
        })
    
    return {
        "success": True,
        "data": {
            "history": history,
            "total": len(history)
        }
    }

@router.get("/api/v1/trading/trades")
async def get_trades(limit: int = 50):
    """获取成交记录"""
    trades = []
    
    for i in range(limit):
        trades.append({
            "tradeId": f"trade_{i}",
            "orderId": f"order_{i}",
            "symbol": f"{random.randint(0, 999999):06d}",
            "side": random.choice(["buy", "sell"]),
            "price": round(random.uniform(5, 50), 2),
            "quantity": random.randint(100, 1000) * 100,
            "amount": round(random.uniform(5000, 50000), 2),
            "fee": round(random.uniform(5, 50), 2),
            "tradeTime": (datetime.now() - timedelta(hours=i)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "trades": trades,
            "total": len(trades)
        }
    }

@router.get("/api/v1/trading/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    return {
        "success": True,
        "data": {
            "accountRisk": {
                "leverage": 1.5,
                "marginUsed": 0.3,
                "riskLevel": "medium"
            },
            "positionRisk": {
                "concentration": 0.25,
                "maxPositionRisk": 0.08,
                "sectorConcentration": {
                    "technology": 0.35,
                    "finance": 0.25,
                    "consumer": 0.20,
                    "others": 0.20
                }
            },
            "marketRisk": {
                "var95": -15000,
                "expectedShortfall": -20000,
                "beta": 1.1
            }
        }
    }

@router.get("/api/v1/trading/stats")
async def get_trading_stats(period: str = "month"):
    """获取交易统计"""
    return {
        "success": True,
        "data": {
            "period": period,
            "totalTrades": 156,
            "profitableTrades": 98,
            "winRate": 0.628,
            "avgProfit": 1250,
            "avgLoss": -850,
            "profitFactor": 1.85,
            "totalProfit": 45000,
            "totalFees": 1200,
            "netProfit": 43800
        }
    }

@router.get("/api/v1/trading/symbols")
async def get_trading_symbols():
    """获取可交易品种"""
    symbols = []
    
    for i in range(20):
        symbols.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"可交易股票{i+1}",
            "market": random.choice(["SH", "SZ"]),
            "status": "tradable",
            "marginTrading": random.choice([True, False]),
            "shortSelling": random.choice([True, False])
        })
    
    return {
        "success": True,
        "data": {
            "symbols": symbols,
            "total": len(symbols)
        }
    }

@router.get("/api/v1/trading/fees")
async def get_fee_rates():
    """获取手续费率"""
    return {
        "success": True,
        "data": {
            "commission": {
                "buy": 0.0003,
                "sell": 0.0003,
                "minimum": 5.0
            },
            "stampDuty": 0.001,
            "transferFee": 0.00002,
            "settlementFee": 0.00002
        }
    }

# ============ 策略扩展API ============

@router.get("/api/v1/strategy/strategy/")
async def get_strategy_list(
    page: int = 1,
    limit: int = 20,
    status: str = None,
    category: str = None
):
    """获取策略列表"""
    strategies = []
    
    for i in range(limit):
        strategies.append({
            "id": f"strategy_{page}_{i}",
            "name": f"策略{page}_{i}",
            "description": "这是一个优秀的量化策略",
            "category": category or random.choice(["trend", "arbitrage", "market_making"]),
            "status": status or random.choice(["running", "stopped", "paused"]),
            "performance": {
                "totalReturn": round(random.uniform(-0.1, 0.3), 3),
                "sharpeRatio": round(random.uniform(0.5, 2.5), 2),
                "maxDrawdown": round(random.uniform(-0.2, -0.05), 3)
            },
            "createTime": datetime.now().isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "strategies": strategies,
            "total": 100,
            "page": page,
            "limit": limit
        }
    }

@router.get("/api/v1/strategy/strategy/{strategy_id}")
async def get_strategy_detail(strategy_id: str):
    """获取策略详情"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "name": f"策略{strategy_id}",
            "description": "详细的策略描述",
            "code": "# 策略代码\ndef initialize(context):\n    pass\n\ndef handle_data(context, data):\n    pass",
            "parameters": {
                "lookback": 20,
                "threshold": 0.02,
                "stopLoss": 0.05
            },
            "status": "running",
            "createTime": datetime.now().isoformat(),
            "updateTime": datetime.now().isoformat()
        }
    }

@router.post("/api/v1/strategy/strategy/")
async def create_new_strategy(request: Request):
    """创建策略"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "id": f"strategy_{secrets.token_hex(8)}",
            "name": data.get("name"),
            "status": "created"
        },
        "message": "策略创建成功"
    }

@router.put("/strategy/{strategy_id}")
async def update_strategy(strategy_id: str, request: Request):
    """更新策略"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "name": data.get("name"),
            "status": "updated"
        },
        "message": "策略更新成功"
    }

@router.delete("/strategy/{strategy_id}")
async def delete_strategy(strategy_id: str):
    """删除策略"""
    return {
        "success": True,
        "message": f"策略 {strategy_id} 已删除"
    }

@router.post("/strategy/{strategy_id}/start")
async def start_strategy(strategy_id: str):
    """启动策略"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "status": "running"
        },
        "message": "策略已启动"
    }

@router.post("/strategy/{strategy_id}/stop")
async def stop_strategy(strategy_id: str):
    """停止策略"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "status": "stopped"
        },
        "message": "策略已停止"
    }

@router.post("/strategy/{strategy_id}/pause")
async def pause_strategy(strategy_id: str):
    """暂停策略"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "status": "paused"
        },
        "message": "策略已暂停"
    }

@router.post("/strategy/{strategy_id}/resume")
async def resume_strategy(strategy_id: str):
    """恢复策略"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "status": "running"
        },
        "message": "策略已恢复"
    }

@router.get("/api/v1/strategy/list")
async def get_strategy_instances():
    """获取策略实例列表"""
    instances = []
    
    for i in range(10):
        instances.append({
            "instanceId": f"instance_{i}",
            "strategyId": f"strategy_{i}",
            "strategyName": f"策略实例{i}",
            "status": random.choice(["running", "stopped", "error"]),
            "startTime": datetime.now().isoformat(),
            "capital": 1000000,
            "currentValue": 1000000 + random.uniform(-50000, 100000)
        })
    
    return {
        "success": True,
        "data": {
            "instances": instances,
            "total": len(instances)
        }
    }

@router.get("/api/v1/strategy/signals")
async def get_strategy_signals(strategy_id: str = None, limit: int = 50):
    """获取策略信号"""
    signals = []
    
    for i in range(limit):
        signals.append({
            "id": f"signal_{i}",
            "strategyId": strategy_id or f"strategy_{i % 5}",
            "symbol": f"{random.randint(0, 999999):06d}",
            "action": random.choice(["buy", "sell", "hold"]),
            "quantity": random.randint(100, 1000) * 100,
            "price": round(random.uniform(5, 50), 2),
            "reason": "技术指标触发",
            "confidence": round(random.uniform(0.6, 0.95), 2),
            "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "signals": signals,
            "total": len(signals)
        }
    }

@router.get("/strategy/{strategy_id}/status")
async def get_strategy_status(strategy_id: str):
    """获取策略状态"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "status": random.choice(["running", "stopped", "paused", "error"]),
            "lastUpdate": datetime.now().isoformat(),
            "runningTime": random.randint(0, 86400),
            "errorCount": random.randint(0, 5)
        }
    }

@router.get("/strategy/{strategy_id}/logs")
async def get_strategy_logs(strategy_id: str, limit: int = 100):
    """获取策略日志"""
    logs = []
    
    for i in range(limit):
        logs.append({
            "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
            "level": random.choice(["INFO", "WARNING", "ERROR", "DEBUG"]),
            "message": f"策略日志消息 {i}",
            "module": "strategy_engine"
        })
    
    return {
        "success": True,
        "data": {
            "strategyId": strategy_id,
            "logs": logs,
            "total": len(logs)
        }
    }

@router.get("/strategy/{strategy_id}/performance")
async def get_strategy_performance(strategy_id: str):
    """获取策略性能"""
    return {
        "success": True,
        "data": {
            "strategyId": strategy_id,
            "metrics": {
                "totalReturn": round(random.uniform(-0.1, 0.5), 3),
                "annualReturn": round(random.uniform(-0.2, 0.8), 3),
                "sharpeRatio": round(random.uniform(0.5, 2.5), 2),
                "sortinoRatio": round(random.uniform(0.8, 3.0), 2),
                "maxDrawdown": round(random.uniform(-0.25, -0.05), 3),
                "winRate": round(random.uniform(0.4, 0.7), 3),
                "profitFactor": round(random.uniform(1.0, 3.0), 2)
            },
            "equity_curve": [
                {"date": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"), 
                 "value": 1000000 * (1 + random.uniform(-0.02, 0.03) * i)}
                for i in range(30)
            ]
        }
    }

@router.get("/strategy/{strategy_id}/positions")
async def get_strategy_positions(strategy_id: str):
    """获取策略持仓"""
    positions = []
    
    for i in range(5):
        positions.append({
            "symbol": f"{random.randint(0, 999999):06d}",
            "name": f"持仓股票{i+1}",
            "quantity": random.randint(100, 1000) * 100,
            "avgCost": round(random.uniform(5, 50), 2),
            "currentPrice": round(random.uniform(5, 50), 2),
            "marketValue": round(random.uniform(10000, 100000), 2),
            "unrealizedPnl": round(random.uniform(-5000, 10000), 2),
            "weight": round(random.uniform(0.05, 0.25), 3)
        })
    
    return {
        "success": True,
        "data": {
            "strategyId": strategy_id,
            "positions": positions,
            "total": len(positions)
        }
    }

@router.get("/strategy/{strategy_id}/trades")
async def get_strategy_trades(strategy_id: str, limit: int = 50):
    """获取策略交易记录"""
    trades = []
    
    for i in range(limit):
        trades.append({
            "tradeId": f"trade_{i}",
            "symbol": f"{random.randint(0, 999999):06d}",
            "side": random.choice(["buy", "sell"]),
            "price": round(random.uniform(5, 50), 2),
            "quantity": random.randint(100, 1000) * 100,
            "amount": round(random.uniform(5000, 50000), 2),
            "fee": round(random.uniform(5, 50), 2),
            "pnl": round(random.uniform(-1000, 2000), 2) if i % 2 == 0 else None,
            "tradeTime": (datetime.now() - timedelta(hours=i)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "strategyId": strategy_id,
            "trades": trades,
            "total": len(trades)
        }
    }

@router.post("/strategy/{strategy_id}/clone")
async def clone_strategy(strategy_id: str):
    """克隆策略"""
    new_id = f"strategy_{secrets.token_hex(8)}"
    
    return {
        "success": True,
        "data": {
            "originalId": strategy_id,
            "newId": new_id,
            "name": f"策略副本_{new_id}"
        },
        "message": "策略克隆成功"
    }

@router.post("/strategy/validate")
async def validate_strategy_code(request: Request):
    """验证策略代码"""
    data = await request.json()
    code = data.get("code", "")
    
    # 简单的代码验证
    errors = []
    if "initialize" not in code:
        errors.append({"line": 1, "message": "缺少 initialize 函数"})
    if "handle_data" not in code:
        errors.append({"line": 1, "message": "缺少 handle_data 函数"})
    
    return {
        "success": len(errors) == 0,
        "data": {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": []
        }
    }

@router.get("/strategy/templates")
async def get_strategy_templates():
    """获取策略模板"""
    templates = [
        {
            "id": "template_ma",
            "name": "双均线策略",
            "description": "基于快慢均线交叉的趋势跟踪策略",
            "category": "trend",
            "code": "# 双均线策略模板\ndef initialize(context):\n    context.fast = 5\n    context.slow = 20"
        },
        {
            "id": "template_mean_revert",
            "name": "均值回归策略",
            "description": "基于价格均值回归的统计套利策略",
            "category": "arbitrage",
            "code": "# 均值回归策略模板\ndef initialize(context):\n    context.lookback = 20"
        }
    ]
    
    return {
        "success": True,
        "data": {
            "templates": templates,
            "total": len(templates)
        }
    }

@router.post("/strategy/templates/{template_id}/create")
async def create_from_template(template_id: str, request: Request):
    """从模板创建策略"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "strategyId": f"strategy_{secrets.token_hex(8)}",
            "name": data.get("name"),
            "templateId": template_id
        },
        "message": "从模板创建策略成功"
    }

@router.get("/strategy/categories")
async def get_strategy_categories():
    """获取策略分类"""
    return {
        "success": True,
        "data": [
            {"id": "trend", "name": "趋势跟踪", "count": 25},
            {"id": "arbitrage", "name": "统计套利", "count": 18},
            {"id": "market_making", "name": "做市策略", "count": 12},
            {"id": "event_driven", "name": "事件驱动", "count": 8},
            {"id": "high_frequency", "name": "高频交易", "count": 5}
        ]
    }

@router.get("/strategy/tags")
async def get_strategy_tags():
    """获取策略标签"""
    return {
        "success": True,
        "data": [
            {"tag": "股票", "count": 45},
            {"tag": "期货", "count": 32},
            {"tag": "低风险", "count": 28},
            {"tag": "高收益", "count": 22},
            {"tag": "机器学习", "count": 15}
        ]
    }

@router.get("/strategy/search")
async def search_strategies(
    keyword: str = None,
    category: str = None,
    tags: str = None,
    page: int = 1,
    limit: int = 20
):
    """搜索策略"""
    strategies = []
    
    for i in range(limit):
        strategies.append({
            "id": f"strategy_search_{i}",
            "name": f"{keyword or '策略'}{i}",
            "description": f"包含关键词 {keyword} 的策略",
            "category": category or "trend",
            "tags": tags.split(",") if tags else ["股票", "低风险"],
            "performance": {
                "totalReturn": round(random.uniform(-0.1, 0.3), 3),
                "sharpeRatio": round(random.uniform(0.5, 2.5), 2)
            }
        })
    
    return {
        "success": True,
        "data": {
            "strategies": strategies,
            "total": 100,
            "page": page,
            "limit": limit
        }
    }

@router.post("/strategy/{strategy_id}/favorite")
async def add_favorite_strategy(strategy_id: str):
    """收藏策略"""
    return {
        "success": True,
        "message": f"策略 {strategy_id} 已添加到收藏"
    }

@router.delete("/strategy/{strategy_id}/favorite")
async def remove_favorite_strategy(strategy_id: str):
    """取消收藏"""
    return {
        "success": True,
        "message": f"策略 {strategy_id} 已从收藏移除"
    }

@router.get("/strategy/favorites")
async def get_favorite_strategies():
    """获取收藏策略"""
    favorites = []
    
    for i in range(5):
        favorites.append({
            "id": f"strategy_fav_{i}",
            "name": f"收藏策略{i+1}",
            "description": "这是一个收藏的策略",
            "favoriteTime": (datetime.now() - timedelta(days=i)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "favorites": favorites,
            "total": len(favorites)
        }
    }

@router.get("/strategy/{strategy_id}/export")
async def export_strategy(strategy_id: str, format: str = "json"):
    """导出策略"""
    if format == "json":
        data = {
            "id": strategy_id,
            "name": f"策略{strategy_id}",
            "code": "# 策略代码",
            "parameters": {},
            "exportTime": datetime.now().isoformat()
        }
    else:
        data = f"# 策略 {strategy_id}\n# 导出时间: {datetime.now()}\n\n# 策略代码..."
    
    return {
        "success": True,
        "data": {
            "format": format,
            "content": data,
            "filename": f"strategy_{strategy_id}.{format}"
        }
    }

@router.post("/strategy/import")
async def import_strategy(request: Request):
    """导入策略"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "strategyId": f"strategy_{secrets.token_hex(8)}",
            "name": data.get("name", "导入的策略"),
            "importTime": datetime.now().isoformat()
        },
        "message": "策略导入成功"
    }

# ============ 回测扩展API ============

@router.get("/api/v1/backtest")
async def get_backtest_list(page: int = 1, limit: int = 20):
    """获取回测列表"""
    backtests = []
    
    for i in range(limit):
        backtests.append({
            "id": f"backtest_{page}_{i}",
            "name": f"回测{page}_{i}",
            "strategyId": f"strategy_{i}",
            "strategyName": f"策略{i}",
            "status": random.choice(["completed", "running", "failed"]),
            "startDate": "2024-01-01",
            "endDate": "2024-12-31",
            "performance": {
                "totalReturn": round(random.uniform(-0.2, 0.5), 3),
                "sharpeRatio": round(random.uniform(0, 2.5), 2),
                "maxDrawdown": round(random.uniform(-0.3, -0.05), 3)
            },
            "createTime": datetime.now().isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "backtests": backtests,
            "total": 100,
            "page": page,
            "limit": limit
        }
    }

@router.get("/api/v1/backtest/{backtest_id}")
async def get_backtest_detail(backtest_id: str):
    """获取回测详情"""
    return {
        "success": True,
        "data": {
            "id": backtest_id,
            "name": f"回测{backtest_id}",
            "strategyId": "strategy_1",
            "config": {
                "startDate": "2024-01-01",
                "endDate": "2024-12-31",
                "initialCapital": 1000000,
                "commission": 0.0003,
                "slippage": 0.001
            },
            "status": "completed",
            "progress": 100,
            "createTime": datetime.now().isoformat(),
            "completeTime": datetime.now().isoformat()
        }
    }

@router.post("/api/v1/backtest")
async def create_backtest(request: Request):
    """创建回测"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "backtestId": f"backtest_{secrets.token_hex(8)}",
            "status": "created"
        },
        "message": "回测创建成功"
    }

@router.put("/backtest/{backtest_id}")
async def update_backtest(backtest_id: str, request: Request):
    """更新回测"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "status": "updated"
        },
        "message": "回测更新成功"
    }

@router.delete("/backtest/{backtest_id}")
async def delete_backtest(backtest_id: str):
    """删除回测"""
    return {
        "success": True,
        "message": f"回测 {backtest_id} 已删除"
    }

@router.post("/backtest/{backtest_id}/start")
async def start_backtest(backtest_id: str):
    """启动回测"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "status": "running",
            "startTime": datetime.now().isoformat()
        },
        "message": "回测已启动"
    }

@router.post("/backtest/{backtest_id}/stop")
async def stop_backtest(backtest_id: str):
    """停止回测"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "status": "stopped"
        },
        "message": "回测已停止"
    }

@router.get("/backtest/{backtest_id}/status")
async def get_backtest_status(backtest_id: str):
    """获取回测状态"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "status": random.choice(["running", "completed", "failed"]),
            "progress": random.randint(0, 100),
            "currentDate": "2024-06-15",
            "estimatedTime": random.randint(0, 300)
        }
    }

@router.get("/backtest/{backtest_id}/progress")
async def get_backtest_progress(backtest_id: str):
    """获取回测进度"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "progress": random.randint(50, 100),
            "processedDays": random.randint(100, 200),
            "totalDays": 250,
            "currentDate": "2024-08-20"
        }
    }

@router.get("/backtest/{backtest_id}/results")
async def get_backtest_results(backtest_id: str):
    """获取回测结果"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "summary": {
                "totalReturn": 0.235,
                "annualReturn": 0.186,
                "sharpeRatio": 1.45,
                "maxDrawdown": -0.127,
                "winRate": 0.628,
                "totalTrades": 156
            },
            "monthlyReturns": [
                {"month": "2024-01", "return": 0.023},
                {"month": "2024-02", "return": -0.012},
                {"month": "2024-03", "return": 0.045}
            ]
        }
    }

@router.get("/backtest/{backtest_id}/chart")
async def get_backtest_chart(backtest_id: str):
    """获取回测图表数据"""
    equity_curve = []
    benchmark_curve = []
    base_value = 1000000
    
    for i in range(250):
        date = datetime(2024, 1, 1) + timedelta(days=i)
        equity_value = base_value * (1 + random.uniform(-0.02, 0.03) * (i / 250))
        benchmark_value = base_value * (1 + random.uniform(-0.01, 0.02) * (i / 250))
        
        equity_curve.append({
            "date": date.strftime("%Y-%m-%d"),
            "value": round(equity_value, 2)
        })
        benchmark_curve.append({
            "date": date.strftime("%Y-%m-%d"),
            "value": round(benchmark_value, 2)
        })
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "equityCurve": equity_curve,
            "benchmarkCurve": benchmark_curve
        }
    }

@router.get("/backtest/{backtest_id}/report")
async def get_backtest_report(backtest_id: str):
    """获取回测报告"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "reportUrl": f"/reports/backtest_{backtest_id}.pdf",
            "summary": "这是一份详细的回测报告，包含了策略表现、风险分析等内容。",
            "sections": [
                "执行概要",
                "收益分析",
                "风险指标",
                "交易明细",
                "持仓分析"
            ]
        }
    }

@router.get("/backtest/{backtest_id}/metrics")
async def get_backtest_metrics(backtest_id: str):
    """获取回测绩效指标"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "returns": {
                "totalReturn": 0.235,
                "annualReturn": 0.186,
                "monthlyReturn": 0.015,
                "dailyReturn": 0.0007
            },
            "risk": {
                "sharpeRatio": 1.45,
                "sortinoRatio": 1.82,
                "maxDrawdown": -0.127,
                "volatility": 0.156,
                "downVolatility": 0.098
            },
            "trading": {
                "totalTrades": 156,
                "winningTrades": 98,
                "losingTrades": 58,
                "winRate": 0.628,
                "avgWin": 2850,
                "avgLoss": -1920,
                "profitFactor": 1.85
            }
        }
    }

@router.get("/backtest/{backtest_id}/equity-curve")
async def get_equity_curve(backtest_id: str):
    """获取净值曲线"""
    return await get_backtest_chart(backtest_id)

@router.get("/backtest/{backtest_id}/returns")
async def get_returns_statistics(backtest_id: str):
    """获取收益统计"""
    daily_returns = []
    
    for i in range(30):
        date = datetime.now() - timedelta(days=30-i)
        daily_returns.append({
            "date": date.strftime("%Y-%m-%d"),
            "return": round(random.uniform(-0.03, 0.03), 4),
            "cumReturn": round(random.uniform(0, 0.2), 4)
        })
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "dailyReturns": daily_returns,
            "statistics": {
                "meanReturn": 0.0012,
                "stdReturn": 0.0156,
                "skewness": -0.23,
                "kurtosis": 3.45
            }
        }
    }

@router.get("/backtest/{backtest_id}/risk-metrics")
async def get_risk_metrics_detail(backtest_id: str):
    """获取风险指标"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "var": {
                "var95_1d": -15000,
                "var99_1d": -25000,
                "var95_10d": -45000
            },
            "drawdowns": [
                {"start": "2024-02-01", "end": "2024-02-15", "drawdown": -0.082},
                {"start": "2024-05-10", "end": "2024-05-25", "drawdown": -0.127},
                {"start": "2024-08-05", "end": "2024-08-12", "drawdown": -0.065}
            ],
            "other": {
                "beta": 0.95,
                "alpha": 0.0023,
                "correlation": 0.78
            }
        }
    }

@router.get("/backtest/{backtest_id}/trades")
async def get_backtest_trades(backtest_id: str, page: int = 1, limit: int = 50):
    """获取回测交易记录"""
    trades = []
    
    for i in range(limit):
        trades.append({
            "tradeId": f"{backtest_id}_trade_{i}",
            "symbol": f"{random.randint(0, 999999):06d}",
            "side": random.choice(["buy", "sell"]),
            "price": round(random.uniform(5, 50), 2),
            "quantity": random.randint(100, 1000) * 100,
            "commission": round(random.uniform(5, 50), 2),
            "pnl": round(random.uniform(-2000, 3000), 2),
            "tradeDate": (datetime(2024, 1, 1) + timedelta(days=i*2)).strftime("%Y-%m-%d")
        })
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "trades": trades,
            "total": 156,
            "page": page,
            "limit": limit
        }
    }

@router.get("/backtest/{backtest_id}/positions")
async def get_backtest_positions(backtest_id: str):
    """获取回测持仓记录"""
    positions = []
    
    for i in range(10):
        positions.append({
            "date": (datetime(2024, 1, 1) + timedelta(days=i*20)).strftime("%Y-%m-%d"),
            "positions": [
                {
                    "symbol": f"{random.randint(0, 999999):06d}",
                    "quantity": random.randint(100, 1000) * 100,
                    "avgCost": round(random.uniform(5, 50), 2),
                    "marketValue": round(random.uniform(10000, 100000), 2),
                    "weight": round(random.uniform(0.05, 0.2), 3)
                }
                for _ in range(random.randint(3, 8))
            ]
        })
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "positionHistory": positions
        }
    }

@router.get("/backtest/{backtest_id}/cash-flow")
async def get_cash_flow(backtest_id: str):
    """获取资金变动"""
    cash_flow = []
    balance = 1000000
    
    for i in range(50):
        date = datetime(2024, 1, 1) + timedelta(days=i*5)
        change = random.uniform(-50000, 50000)
        balance += change
        
        cash_flow.append({
            "date": date.strftime("%Y-%m-%d"),
            "type": random.choice(["trade", "dividend", "fee"]),
            "amount": round(change, 2),
            "balance": round(balance, 2),
            "description": "交易" if change > 0 else "手续费"
        })
    
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "cashFlow": cash_flow
        }
    }

@router.post("/backtest/compare")
async def compare_backtests(request: Request):
    """比较回测结果"""
    data = await request.json()
    backtest_ids = data.get("backtestIds", [])
    
    comparisons = []
    for bid in backtest_ids:
        comparisons.append({
            "backtestId": bid,
            "name": f"回测{bid}",
            "totalReturn": round(random.uniform(-0.1, 0.3), 3),
            "sharpeRatio": round(random.uniform(0.5, 2.0), 2),
            "maxDrawdown": round(random.uniform(-0.2, -0.05), 3),
            "winRate": round(random.uniform(0.4, 0.7), 3)
        })
    
    return {
        "success": True,
        "data": {
            "comparisons": comparisons
        }
    }

@router.get("/backtest/{backtest_id}/benchmark")
async def get_benchmark_comparison(backtest_id: str):
    """获取基准对比"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "strategy": {
                "totalReturn": 0.235,
                "sharpeRatio": 1.45,
                "maxDrawdown": -0.127
            },
            "benchmark": {
                "name": "沪深300",
                "totalReturn": 0.156,
                "sharpeRatio": 0.98,
                "maxDrawdown": -0.182
            },
            "relativeMetrics": {
                "alpha": 0.0023,
                "beta": 0.95,
                "trackingError": 0.045,
                "informationRatio": 1.23
            }
        }
    }

@router.post("/backtest/{backtest_id}/clone")
async def clone_backtest(backtest_id: str):
    """克隆回测"""
    new_id = f"backtest_{secrets.token_hex(8)}"
    
    return {
        "success": True,
        "data": {
            "originalId": backtest_id,
            "newId": new_id,
            "name": f"回测副本_{new_id}"
        },
        "message": "回测克隆成功"
    }

@router.get("/backtest/{backtest_id}/export")
async def export_backtest(backtest_id: str, format: str = "pdf"):
    """导出回测报告"""
    return {
        "success": True,
        "data": {
            "backtestId": backtest_id,
            "format": format,
            "downloadUrl": f"/download/backtest_{backtest_id}.{format}",
            "expiresAt": (datetime.now() + timedelta(hours=24)).isoformat()
        }
    }

# ============ CTP交易扩展API ============

@router.get("/api/v1/ctp/status")
async def get_ctp_status():
    """获取CTP连接状态"""
    return {
        "success": True,
        "data": {
            "connected": True,
            "tradingConnected": True,
            "marketDataConnected": True,
            "loginTime": datetime.now().isoformat(),
            "tradingDay": datetime.now().strftime("%Y%m%d"),
            "systemName": "CTP交易系统"
        }
    }

@router.post("/api/v1/ctp/initialize")
async def initialize_ctp(request: Request):
    """初始化CTP连接"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "status": "initialized",
            "brokerId": data.get("brokerId"),
            "userId": data.get("userId"),
            "message": "CTP连接初始化成功"
        }
    }

@router.post("/api/v1/ctp/orders")
async def submit_ctp_order(request: Request):
    """提交CTP订单"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "orderId": f"CTP{secrets.token_hex(8)}",
            "instrumentId": data.get("instrumentId"),
            "direction": data.get("direction"),
            "offsetFlag": data.get("offsetFlag"),
            "price": data.get("price"),
            "volume": data.get("volume"),
            "status": "submitted"
        },
        "message": "CTP订单提交成功"
    }

@router.delete("/api/v1/ctp/orders/{order_id}")
async def cancel_ctp_order(order_id: str):
    """撤销CTP订单"""
    return {
        "success": True,
        "data": {
            "orderId": order_id,
            "status": "cancelled"
        },
        "message": "CTP订单撤销成功"
    }

@router.get("/api/v1/ctp/orders")
async def query_ctp_orders():
    """查询CTP订单"""
    orders = []
    
    for i in range(5):
        orders.append({
            "orderId": f"CTP00{i}",
            "instrumentId": random.choice(["IF2401", "IC2401", "IH2401"]),
            "direction": random.choice(["buy", "sell"]),
            "offsetFlag": random.choice(["open", "close"]),
            "price": round(random.uniform(3000, 5000), 1),
            "volume": random.randint(1, 10),
            "tradedVolume": random.randint(0, 5),
            "status": random.choice(["traded", "partial", "submitted", "cancelled"]),
            "insertTime": datetime.now().isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "orders": orders,
            "total": len(orders)
        }
    }

@router.get("/api/v1/ctp/positions")
async def query_ctp_positions():
    """查询CTP持仓"""
    positions = []
    
    for i in range(3):
        positions.append({
            "instrumentId": random.choice(["IF2401", "IC2401", "IH2401"]),
            "direction": random.choice(["long", "short"]),
            "position": random.randint(1, 20),
            "todayPosition": random.randint(0, 10),
            "yesterdayPosition": random.randint(0, 10),
            "avgPrice": round(random.uniform(3000, 5000), 1),
            "profitLoss": round(random.uniform(-5000, 10000), 2)
        })
    
    return {
        "success": True,
        "data": {
            "positions": positions,
            "total": len(positions)
        }
    }

@router.get("/api/v1/ctp/account")
async def query_ctp_account():
    """查询CTP账户"""
    return {
        "success": True,
        "data": {
            "balance": 5000000.00,
            "available": 3500000.00,
            "margin": 1500000.00,
            "frozenMargin": 200000.00,
            "commission": 1250.50,
            "frozenCommission": 150.00,
            "closeProfit": 25000.00,
            "positionProfit": -3500.00,
            "riskRatio": 0.28
        }
    }

@router.post("/api/v1/ctp/orders/batch")
async def batch_submit_ctp_orders(request: Request):
    """批量提交CTP订单"""
    data = await request.json()
    orders = data.get("orders", [])
    
    results = []
    for order in orders:
        results.append({
            "orderId": f"CTP{secrets.token_hex(8)}",
            "status": "submitted"
        })
    
    return {
        "success": True,
        "data": {
            "submitted": len(results),
            "failed": 0,
            "results": results
        },
        "message": f"成功提交 {len(results)} 个CTP订单"
    }

# ============ 用户扩展API ============

@router.get("/v1/user/profile")
async def get_user_profile_v1():
    """获取用户信息"""
    return {
        "success": True,
        "data": {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "nickname": "演示用户",
            "avatar": "/avatars/default.png",
            "phone": "138****0000",
            "role": "user",
            "vipLevel": 1,
            "createTime": "2024-01-01T00:00:00Z"
        }
    }

@router.put("/v1/user/profile")
async def update_user_profile_v1(request: Request):
    """更新用户信息"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "id": 1,
            "username": "demo_user",
            "nickname": data.get("nickname", "演示用户"),
            "email": data.get("email", "<EMAIL>")
        },
        "message": "用户信息更新成功"
    }

@router.post("/v1/user/avatar")
async def upload_avatar(request: Request):
    """上传头像"""
    return {
        "success": True,
        "data": {
            "avatarUrl": f"/avatars/user_{secrets.token_hex(8)}.png"
        },
        "message": "头像上传成功"
    }

@router.get("/v1/user/export")
async def export_user_data():
    """导出用户数据"""
    return {
        "success": True,
        "data": {
            "exportId": f"export_{secrets.token_hex(8)}",
            "downloadUrl": "/download/user_data.zip",
            "expiresAt": (datetime.now() + timedelta(hours=24)).isoformat()
        },
        "message": "用户数据导出成功"
    }

@router.get("/user/2fa/status")
async def get_2fa_status():
    """获取双因子认证状态"""
    return {
        "success": True,
        "data": {
            "enabled": False,
            "type": None,
            "backupCodes": 0
        }
    }

@router.post("/user/2fa/enable")
async def enable_2fa(request: Request):
    """启用双因子认证"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "secret": secrets.token_hex(16),
            "backupCodes": [secrets.token_hex(4) for _ in range(8)]
        },
        "message": "请使用认证器扫描二维码"
    }

@router.post("/user/2fa/disable")
async def disable_2fa(request: Request):
    """禁用双因子认证"""
    return {
        "success": True,
        "message": "双因子认证已禁用"
    }

@router.get("/user/login-logs")
async def get_login_logs(limit: int = 20):
    """获取登录日志"""
    logs = []
    
    for i in range(limit):
        logs.append({
            "id": i + 1,
            "loginTime": (datetime.now() - timedelta(days=i)).isoformat(),
            "ip": f"192.168.1.{random.randint(1, 255)}",
            "location": random.choice(["北京", "上海", "深圳", "广州"]),
            "device": random.choice(["Chrome/Windows", "Safari/Mac", "Mobile/iOS"]),
            "status": "success" if i % 10 != 0 else "failed"
        })
    
    return {
        "success": True,
        "data": {
            "logs": logs,
            "total": len(logs)
        }
    }

@router.get("/user/operation-logs")
async def get_operation_logs(limit: int = 50):
    """获取操作日志"""
    logs = []
    operations = ["创建策略", "修改策略", "删除策略", "提交订单", "取消订单", "修改设置"]
    
    for i in range(limit):
        logs.append({
            "id": i + 1,
            "operation": random.choice(operations),
            "module": random.choice(["strategy", "trading", "settings"]),
            "detail": "操作详情",
            "result": "success" if i % 20 != 0 else "failed",
            "operateTime": (datetime.now() - timedelta(hours=i)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "logs": logs,
            "total": len(logs)
        }
    }

@router.get("/user/preferences")
async def get_user_preferences():
    """获取用户偏好"""
    return {
        "success": True,
        "data": {
            "theme": "light",
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "notifications": {
                "email": True,
                "sms": False,
                "push": True
            },
            "trading": {
                "defaultOrderType": "limit",
                "confirmBeforeSubmit": True,
                "quickTradeEnabled": False
            },
            "display": {
                "compactMode": False,
                "showToolbar": True,
                "chartType": "candlestick"
            }
        }
    }

@router.put("/user/preferences")
async def update_user_preferences(request: Request):
    """更新用户偏好"""
    data = await request.json()
    
    return {
        "success": True,
        "message": "用户偏好设置已更新"
    }

@router.get("/user/api-keys")
async def get_api_keys():
    """获取API密钥列表"""
    keys = []
    
    for i in range(3):
        keys.append({
            "id": f"key_{i}",
            "name": f"API密钥{i+1}",
            "key": f"ak_{secrets.token_hex(8)}...{secrets.token_hex(4)}",
            "permissions": ["read", "trade"] if i == 0 else ["read"],
            "lastUsed": (datetime.now() - timedelta(days=i)).isoformat() if i < 2 else None,
            "createTime": (datetime.now() - timedelta(days=i*30)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "apiKeys": keys,
            "total": len(keys)
        }
    }

@router.post("/user/api-keys")
async def create_api_key(request: Request):
    """创建API密钥"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "id": f"key_{secrets.token_hex(4)}",
            "name": data.get("name"),
            "key": f"ak_{secrets.token_hex(32)}",
            "secret": f"sk_{secrets.token_hex(32)}",
            "permissions": data.get("permissions", ["read"])
        },
        "message": "API密钥创建成功，请妥善保管"
    }

@router.delete("/user/api-keys/{key_id}")
async def delete_api_key(key_id: str):
    """删除API密钥"""
    return {
        "success": True,
        "message": f"API密钥 {key_id} 已删除"
    }

@router.put("/user/api-keys/{key_id}")
async def update_api_key(key_id: str, request: Request):
    """更新API密钥"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "id": key_id,
            "name": data.get("name"),
            "permissions": data.get("permissions")
        },
        "message": "API密钥更新成功"
    }

@router.get("/user/notifications/settings")
async def get_notification_settings():
    """获取通知设置"""
    return {
        "success": True,
        "data": {
            "channels": {
                "email": {
                    "enabled": True,
                    "address": "<EMAIL>"
                },
                "sms": {
                    "enabled": False,
                    "phone": "138****0000"
                },
                "wechat": {
                    "enabled": True,
                    "bound": True
                }
            },
            "types": {
                "trade": True,
                "strategy": True,
                "system": False,
                "market": True
            }
        }
    }

@router.put("/user/notifications/settings")
async def update_notification_settings(request: Request):
    """更新通知设置"""
    return {
        "success": True,
        "message": "通知设置已更新"
    }

@router.get("/user/notifications")
async def get_notifications(unread: bool = None, limit: int = 20):
    """获取通知列表"""
    notifications = []
    
    for i in range(limit):
        notifications.append({
            "id": f"notif_{i}",
            "type": random.choice(["trade", "strategy", "system", "market"]),
            "title": f"通知标题{i+1}",
            "content": "这是一条通知内容",
            "read": False if i < 5 else True,
            "createTime": (datetime.now() - timedelta(hours=i)).isoformat()
        })
    
    if unread is not None:
        notifications = [n for n in notifications if n["read"] != unread]
    
    return {
        "success": True,
        "data": {
            "notifications": notifications,
            "total": len(notifications),
            "unreadCount": sum(1 for n in notifications if not n["read"])
        }
    }

@router.put("/user/notifications/{notification_id}/read")
async def mark_notification_read(notification_id: str):
    """标记通知已读"""
    return {
        "success": True,
        "message": "通知已标记为已读"
    }

@router.put("/user/notifications/read-all")
async def mark_all_notifications_read():
    """标记所有通知已读"""
    return {
        "success": True,
        "message": "所有通知已标记为已读"
    }

@router.delete("/user/notifications/{notification_id}")
async def delete_notification(notification_id: str):
    """删除通知"""
    return {
        "success": True,
        "message": "通知已删除"
    }

@router.get("/user/stats")
async def get_account_stats():
    """获取账户统计"""
    return {
        "success": True,
        "data": {
            "tradingDays": 180,
            "totalTrades": 1560,
            "totalProfit": 125000,
            "winRate": 0.628,
            "activeStrategies": 5,
            "totalStrategies": 12,
            "accountAge": 365,
            "vipLevel": 2,
            "achievements": [
                {"name": "交易新手", "unlocked": True},
                {"name": "策略大师", "unlocked": False},
                {"name": "稳健投资者", "unlocked": True}
            ]
        }
    }

@router.post("/user/delete-account")
async def delete_account(request: Request):
    """注销账户"""
    data = await request.json()
    
    return {
        "success": True,
        "message": "账户注销申请已提交，将在7个工作日内处理"
    }

@router.get("/v1/users")
async def get_users_list(role: str = None, page: int = 1, limit: int = 20):
    """获取用户列表(管理员)"""
    users = []
    
    for i in range(limit):
        users.append({
            "id": i + 1,
            "username": f"user_{i}",
            "email": f"user{i}@example.com",
            "role": role or random.choice(["user", "vip", "admin"]),
            "status": random.choice(["active", "inactive", "suspended"]),
            "lastLogin": (datetime.now() - timedelta(days=i)).isoformat(),
            "createTime": (datetime.now() - timedelta(days=i*30)).isoformat()
        })
    
    return {
        "success": True,
        "data": {
            "users": users,
            "total": 100,
            "page": page,
            "limit": limit
        }
    }

# ============ 其他API ============

@router.post("/api/v1/auth/reset-password")
async def reset_password(request: Request):
    """重置密码"""
    data = await request.json()
    
    return {
        "success": True,
        "message": "密码重置邮件已发送"
    }

@router.post("/api/v1/auth/change-password")
async def change_password(request: Request):
    """修改密码"""
    data = await request.json()
    
    return {
        "success": True,
        "message": "密码修改成功"
    }

@router.get("/api/v1/auth/captcha")
async def get_image_captcha():
    """获取图片验证码"""
    return {
        "success": True,
        "data": {
            "captchaId": f"captcha_{secrets.token_hex(8)}",
            "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        }
    }

@router.post("/api/v1/captcha/slider/validate-token")
async def validate_slider_token(request: Request):
    """验证滑块令牌有效性"""
    data = await request.json()
    
    return {
        "success": True,
        "data": {
            "valid": True,
            "token": data.get("token")
        }
    }

# ============ 策略搜索API ============

@router.get("/api/v1/strategies/search")
async def search_strategies(
    keyword: str = Query(..., description="搜索关键词"),
    status: Optional[str] = Query(None, description="策略状态"),
    category: Optional[str] = Query(None, description="策略类别"),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100)
):
    """搜索策略"""
    # 模拟策略搜索结果
    strategies = []
    
    # 生成模拟数据
    strategy_names = [
        "双均线交叉策略", "MACD趋势跟踪", "布林带突破策略", 
        "RSI超买超卖策略", "动量突破策略", "网格交易策略",
        "配对交易策略", "期现套利策略", "统计套利策略"
    ]
    
    for i, name in enumerate(strategy_names):
        if keyword.lower() in name.lower():
            strategy = {
                "id": f"strategy_{i}",
                "name": name,
                "description": f"{name}的详细描述",
                "status": status if status else random.choice(["running", "stopped", "testing"]),
                "category": category if category else random.choice(["trend", "arbitrage", "momentum"]),
                "performance": {
                    "totalReturn": round(random.uniform(-0.1, 0.5), 4),
                    "sharpeRatio": round(random.uniform(0.5, 2.5), 2),
                    "maxDrawdown": round(random.uniform(-0.3, -0.05), 4),
                    "winRate": round(random.uniform(0.4, 0.7), 2)
                },
                "creator": "系统",
                "createdAt": datetime.now().isoformat(),
                "updatedAt": datetime.now().isoformat()
            }
            strategies.append(strategy)
    
    # 分页
    start = (page - 1) * limit
    end = start + limit
    paginated_strategies = strategies[start:end]
    
    return {
        "success": True,
        "data": {
            "strategies": paginated_strategies,
            "total": len(strategies),
            "page": page,
            "pageSize": limit
        }
    }
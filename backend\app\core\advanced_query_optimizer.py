"""
高级查询优化器
解决N+1查询问题，实现批量操作和智能查询优化
"""

import asyncio
import logging
import time
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from sqlalchemy import and_, func, or_, select, text, update, delete, insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload, contains_eager
from sqlalchemy.sql import Select

from app.core.database import db_manager
from app.core.query_cache import cache_manager, cached_query
from app.db.models.market import MarketData, KLineData, Symbol
from app.db.models.trading import Order, Trade, Position, Account
from app.db.models.user import User

logger = logging.getLogger(__name__)


@dataclass
class QueryPlan:
    """查询执行计划"""
    query_type: str
    table_names: List[str]
    estimated_rows: int
    use_cache: bool
    batch_size: int
    join_strategy: str
    index_hints: List[str]


class BatchQueryExecutor:
    """批量查询执行器"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.pending_queries: Dict[str, List] = defaultdict(list)
        self.results_cache: Dict[str, Any] = {}
    
    def add_query(self, query_key: str, query_func: Callable, *args, **kwargs):
        """添加查询到批处理队列"""
        self.pending_queries[query_key].append((query_func, args, kwargs))
    
    async def execute_batch(self) -> Dict[str, List[Any]]:
        """执行批量查询"""
        results = {}
        
        for query_key, queries in self.pending_queries.items():
            batch_results = []
            
            for query_func, args, kwargs in queries:
                try:
                    result = await query_func(*args, **kwargs)
                    batch_results.append(result)
                except Exception as e:
                    logger.error(f"批量查询执行失败 {query_key}: {e}")
                    batch_results.append(None)
            
            results[query_key] = batch_results
        
        # 清空队列
        self.pending_queries.clear()
        return results


class SmartQueryBuilder:
    """智能查询构建器"""
    
    def __init__(self):
        self.query_patterns = {}
        self.performance_stats = defaultdict(list)
    
    def build_optimized_user_orders_query(
        self, 
        user_ids: List[int],
        status_filter: Optional[List[str]] = None,
        symbol_filter: Optional[List[str]] = None,
        limit: int = 100
    ) -> Select:
        """构建优化的用户订单查询（解决N+1问题）"""
        
        # 主查询
        query = select(Order).options(
            # 使用 selectinload 避免 N+1 查询
            selectinload(Order.user),
            selectinload(Order.trades)
        )
        
        # 添加过滤条件
        conditions = [Order.user_id.in_(user_ids)]
        
        if status_filter:
            conditions.append(Order.status.in_(status_filter))
        
        if symbol_filter:
            conditions.append(Order.symbol_code.in_(symbol_filter))
        
        query = query.where(and_(*conditions))
        
        # 优化排序和分页
        query = query.order_by(Order.created_at.desc()).limit(limit)
        
        return query
    
    def build_market_data_range_query(
        self,
        symbol_codes: List[str],
        start_time: datetime,
        end_time: datetime,
        include_kline: bool = False
    ) -> Select:
        """构建市场数据范围查询"""
        
        if include_kline:
            # 使用 JOIN 一次性获取市场数据和K线数据
            query = select(MarketData, KLineData).outerjoin(
                KLineData, 
                and_(
                    MarketData.symbol_code == KLineData.symbol_code,
                    func.date(MarketData.timestamp) == KLineData.trading_date
                )
            ).options(
                contains_eager(MarketData, KLineData)
            )
        else:
            query = select(MarketData)
        
        # 添加过滤条件
        query = query.where(
            and_(
                MarketData.symbol_code.in_(symbol_codes),
                MarketData.timestamp >= start_time,
                MarketData.timestamp <= end_time
            )
        ).order_by(MarketData.timestamp.desc())
        
        return query
    
    def build_position_summary_query(self, user_ids: List[int]) -> Select:
        """构建持仓汇总查询"""
        
        query = select(
            Position.user_id,
            Position.symbol_code,
            func.sum(Position.quantity).label('total_quantity'),
            func.sum(Position.market_value).label('total_market_value'),
            func.sum(Position.unrealized_pnl).label('total_unrealized_pnl'),
            func.avg(Position.avg_cost).label('avg_cost')
        ).where(
            and_(
                Position.user_id.in_(user_ids),
                Position.quantity > 0
            )
        ).group_by(
            Position.user_id, 
            Position.symbol_code
        ).order_by(
            Position.user_id, 
            func.sum(Position.market_value).desc()
        )
        
        return query


class AdvancedQueryOptimizer:
    """高级查询优化器"""
    
    def __init__(self):
        self.query_builder = SmartQueryBuilder()
        self.execution_stats = defaultdict(list)
        self.cache_hit_stats = defaultdict(int)
        
        # 预定义查询模式
        self.query_patterns = {
            'user_orders': self._optimize_user_orders_pattern,
            'market_data_batch': self._optimize_market_data_pattern,
            'portfolio_summary': self._optimize_portfolio_pattern,
            'trading_history': self._optimize_trading_history_pattern,
        }
    
    async def execute_optimized_query(
        self,
        session: AsyncSession,
        pattern: str,
        **kwargs
    ) -> Any:
        """执行优化查询"""
        
        start_time = time.time()
        
        try:
            # 尝试从缓存获取
            cache_key = f"{pattern}:{hash(str(kwargs))}"
            cached_result = await self._try_get_from_cache(pattern, kwargs)
            
            if cached_result is not None:
                self.cache_hit_stats[pattern] += 1
                return cached_result
            
            # 执行查询
            if pattern in self.query_patterns:
                result = await self.query_patterns[pattern](session, **kwargs)
            else:
                raise ValueError(f"未知查询模式: {pattern}")
            
            # 缓存结果
            await self._cache_result(pattern, kwargs, result)
            
            # 记录性能统计
            execution_time = time.time() - start_time
            self.execution_stats[pattern].append(execution_time)
            
            return result
            
        except Exception as e:
            logger.error(f"优化查询执行失败 {pattern}: {e}")
            raise
    
    async def _optimize_user_orders_pattern(
        self,
        session: AsyncSession,
        user_ids: List[int],
        **kwargs
    ) -> List[Order]:
        """优化用户订单查询模式"""
        
        query = self.query_builder.build_optimized_user_orders_query(
            user_ids=user_ids,
            status_filter=kwargs.get('status_filter'),
            symbol_filter=kwargs.get('symbol_filter'),
            limit=kwargs.get('limit', 100)
        )
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def _optimize_market_data_pattern(
        self,
        session: AsyncSession,
        symbol_codes: List[str],
        start_time: datetime,
        end_time: datetime,
        **kwargs
    ) -> List[MarketData]:
        """优化市场数据查询模式"""
        
        query = self.query_builder.build_market_data_range_query(
            symbol_codes=symbol_codes,
            start_time=start_time,
            end_time=end_time,
            include_kline=kwargs.get('include_kline', False)
        )
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def _optimize_portfolio_pattern(
        self,
        session: AsyncSession,
        user_ids: List[int],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """优化投资组合查询模式"""
        
        query = self.query_builder.build_position_summary_query(user_ids)
        result = await session.execute(query)
        
        return [
            {
                'user_id': row.user_id,
                'symbol_code': row.symbol_code,
                'total_quantity': row.total_quantity,
                'total_market_value': float(row.total_market_value),
                'total_unrealized_pnl': float(row.total_unrealized_pnl),
                'avg_cost': float(row.avg_cost)
            }
            for row in result.fetchall()
        ]
    
    async def _optimize_trading_history_pattern(
        self,
        session: AsyncSession,
        user_ids: List[int],
        start_date: datetime,
        end_date: datetime,
        **kwargs
    ) -> Dict[str, Any]:
        """优化交易历史查询模式"""
        
        # 使用一个查询获取所有相关数据
        query = select(
            Trade.user_id,
            Trade.symbol_code,
            func.count(Trade.id).label('trade_count'),
            func.sum(Trade.quantity).label('total_quantity'),
            func.sum(Trade.turnover).label('total_turnover'),
            func.sum(Trade.commission).label('total_commission'),
            func.avg(Trade.price).label('avg_price'),
            func.min(Trade.trade_time).label('first_trade'),
            func.max(Trade.trade_time).label('last_trade')
        ).where(
            and_(
                Trade.user_id.in_(user_ids),
                Trade.trade_time >= start_date,
                Trade.trade_time <= end_date
            )
        ).group_by(
            Trade.user_id, 
            Trade.symbol_code
        ).order_by(
            Trade.user_id, 
            func.sum(Trade.turnover).desc()
        )
        
        result = await session.execute(query)
        
        trading_summary = []
        for row in result.fetchall():
            trading_summary.append({
                'user_id': row.user_id,
                'symbol_code': row.symbol_code,
                'trade_count': row.trade_count,
                'total_quantity': row.total_quantity,
                'total_turnover': float(row.total_turnover),
                'total_commission': float(row.total_commission),
                'avg_price': float(row.avg_price),
                'first_trade': row.first_trade,
                'last_trade': row.last_trade
            })
        
        return {
            'summary': trading_summary,
            'total_users': len(set(item['user_id'] for item in trading_summary)),
            'total_symbols': len(set(item['symbol_code'] for item in trading_summary)),
            'query_time': datetime.now()
        }
    
    async def _try_get_from_cache(self, pattern: str, params: Dict) -> Optional[Any]:
        """尝试从缓存获取结果"""
        try:
            # 根据查询模式确定缓存策略
            cache_type = {
                'user_orders': 'user_data',
                'market_data_batch': 'market_data',
                'portfolio_summary': 'user_data',
                'trading_history': 'user_data'
            }.get(pattern, 'default')
            
            return await cache_manager.get(cache_type, params)
        except Exception as e:
            logger.debug(f"缓存获取失败 {pattern}: {e}")
            return None
    
    async def _cache_result(self, pattern: str, params: Dict, result: Any) -> None:
        """缓存查询结果"""
        try:
            cache_type = {
                'user_orders': 'user_data',
                'market_data_batch': 'market_data',
                'portfolio_summary': 'user_data',
                'trading_history': 'user_data'
            }.get(pattern, 'default')
            
            await cache_manager.set(cache_type, params, result)
        except Exception as e:
            logger.debug(f"缓存设置失败 {pattern}: {e}")
    
    async def batch_update_orders(
        self,
        session: AsyncSession,
        updates: List[Dict[str, Any]]
    ) -> int:
        """批量更新订单"""
        if not updates:
            return 0
        
        try:
            # 按更新类型分组
            status_updates = []
            price_updates = []
            
            for update in updates:
                if 'status' in update:
                    status_updates.append(update)
                elif 'price' in update:
                    price_updates.append(update)
            
            updated_count = 0
            
            # 批量更新状态
            if status_updates:
                for update_batch in self._chunk_list(status_updates, 100):
                    stmt = update(Order).where(
                        Order.id.in_([u['id'] for u in update_batch])
                    )
                    
                    for upd in update_batch:
                        stmt = stmt.values(**{k: v for k, v in upd.items() if k != 'id'})
                    
                    result = await session.execute(stmt)
                    updated_count += result.rowcount
            
            # 批量更新价格
            if price_updates:
                for update_batch in self._chunk_list(price_updates, 100):
                    stmt = update(Order).where(
                        Order.id.in_([u['id'] for u in update_batch])
                    )
                    
                    for upd in update_batch:
                        stmt = stmt.values(**{k: v for k, v in upd.items() if k != 'id'})
                    
                    result = await session.execute(stmt)
                    updated_count += result.rowcount
            
            await session.commit()
            return updated_count
            
        except Exception as e:
            await session.rollback()
            logger.error(f"批量更新订单失败: {e}")
            raise
    
    async def batch_insert_market_data(
        self,
        session: AsyncSession,
        market_data: List[Dict[str, Any]]
    ) -> int:
        """批量插入市场数据"""
        if not market_data:
            return 0
        
        try:
            # 分批插入以避免内存问题
            inserted_count = 0
            
            for batch in self._chunk_list(market_data, 1000):
                stmt = insert(MarketData).values(batch)
                result = await session.execute(stmt)
                inserted_count += len(batch)
            
            await session.commit()
            
            # 清除相关缓存
            await cache_manager.clear_pattern('market_data')
            
            return inserted_count
            
        except Exception as e:
            await session.rollback()
            logger.error(f"批量插入市场数据失败: {e}")
            raise
    
    def _chunk_list(self, lst: List, chunk_size: int) -> List[List]:
        """将列表分割成指定大小的块"""
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """获取查询性能报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'query_patterns': {},
            'cache_performance': dict(self.cache_hit_stats),
            'total_cache_hits': sum(self.cache_hit_stats.values())
        }
        
        for pattern, times in self.execution_stats.items():
            if times:
                report['query_patterns'][pattern] = {
                    'execution_count': len(times),
                    'avg_time': sum(times) / len(times),
                    'max_time': max(times),
                    'min_time': min(times),
                    'total_time': sum(times)
                }
        
        return report
    
    async def suggest_optimizations(self) -> List[Dict[str, str]]:
        """提供查询优化建议"""
        suggestions = []
        
        for pattern, times in self.execution_stats.items():
            if times:
                avg_time = sum(times) / len(times)
                
                if avg_time > 1.0:
                    suggestions.append({
                        'pattern': pattern,
                        'suggestion': '查询时间过长，建议检查索引或分解查询',
                        'current_avg_time': f"{avg_time:.3f}s"
                    })
                
                cache_hit_rate = self.cache_hit_stats.get(pattern, 0) / len(times)
                if cache_hit_rate < 0.5:
                    suggestions.append({
                        'pattern': pattern,
                        'suggestion': '缓存命中率较低，建议调整缓存策略',
                        'cache_hit_rate': f"{cache_hit_rate:.2%}"
                    })
        
        return suggestions


# 全局查询优化器实例
query_optimizer = AdvancedQueryOptimizer()


# 便捷函数
async def get_user_orders_optimized(
    user_ids: List[int],
    status_filter: Optional[List[str]] = None,
    symbol_filter: Optional[List[str]] = None,
    limit: int = 100
) -> List[Order]:
    """获取用户订单（优化版本）"""
    async with db_manager.get_session() as session:
        return await query_optimizer.execute_optimized_query(
            session,
            'user_orders',
            user_ids=user_ids,
            status_filter=status_filter,
            symbol_filter=symbol_filter,
            limit=limit
        )


async def get_market_data_batch(
    symbol_codes: List[str],
    start_time: datetime,
    end_time: datetime,
    include_kline: bool = False
) -> List[MarketData]:
    """批量获取市场数据（优化版本）"""
    async with db_manager.get_session() as session:
        return await query_optimizer.execute_optimized_query(
            session,
            'market_data_batch',
            symbol_codes=symbol_codes,
            start_time=start_time,
            end_time=end_time,
            include_kline=include_kline
        )


async def get_portfolio_summary(user_ids: List[int]) -> List[Dict[str, Any]]:
    """获取投资组合汇总（优化版本）"""
    async with db_manager.get_session() as session:
        return await query_optimizer.execute_optimized_query(
            session,
            'portfolio_summary',
            user_ids=user_ids
        )


if __name__ == "__main__":
    async def test_advanced_optimizer():
        """测试高级查询优化器"""
        print("测试高级查询优化器...")
        
        # 模拟一些查询
        user_ids = [1, 2, 3]
        symbol_codes = ['000001', '000002', '399001']
        
        start_time = datetime.now() - timedelta(days=7)
        end_time = datetime.now()
        
        try:
            # 测试优化查询
            orders = await get_user_orders_optimized(user_ids, limit=50)
            print(f"获取到 {len(orders)} 个订单")
            
            market_data = await get_market_data_batch(
                symbol_codes, start_time, end_time
            )
            print(f"获取到 {len(market_data)} 条市场数据")
            
            portfolio = await get_portfolio_summary(user_ids)
            print(f"获取到 {len(portfolio)} 个持仓汇总")
            
            # 性能报告
            report = await query_optimizer.get_performance_report()
            print(f"性能报告: {report}")
            
            # 优化建议
            suggestions = await query_optimizer.suggest_optimizations()
            print(f"优化建议: {suggestions}")
            
        except Exception as e:
            print(f"测试失败: {e}")
        
        print("测试完成")
    
    asyncio.run(test_advanced_optimizer())
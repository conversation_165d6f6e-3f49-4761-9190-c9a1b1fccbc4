"""
异步查询执行器
优化并发查询处理，支持查询队列、批处理和优先级调度
"""

import asyncio
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union
from uuid import uuid4

from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select

from app.core.database import db_manager
from app.core.smart_cache_manager import smart_cache_manager

logger = logging.getLogger(__name__)


class QueryPriority(Enum):
    """查询优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class QueryStatus(Enum):
    """查询状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class QueryTask:
    """查询任务"""
    id: str = field(default_factory=lambda: str(uuid4()))
    query: Union[str, Select]
    params: Dict[str, Any] = field(default_factory=dict)
    priority: QueryPriority = QueryPriority.NORMAL
    status: QueryStatus = QueryStatus.PENDING
    callback: Optional[Callable] = None
    timeout: float = 30.0
    cache_key: Optional[str] = None
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 结果
    result: Any = None
    error: Optional[Exception] = None
    
    # 统计信息
    execution_time: float = 0.0
    retry_count: int = 0


@dataclass
class ExecutorStats:
    """执行器统计"""
    total_queries: int = 0
    completed_queries: int = 0
    failed_queries: int = 0
    cancelled_queries: int = 0
    avg_execution_time: float = 0.0
    max_concurrent_queries: int = 0
    current_queue_size: int = 0


class QueryQueue:
    """查询队列"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.queues = {
            QueryPriority.CRITICAL: deque(),
            QueryPriority.HIGH: deque(),
            QueryPriority.NORMAL: deque(),
            QueryPriority.LOW: deque()
        }
        self.total_size = 0
    
    def enqueue(self, task: QueryTask) -> bool:
        """入队"""
        if self.total_size >= self.max_size:
            return False
        
        self.queues[task.priority].append(task)
        self.total_size += 1
        return True
    
    def dequeue(self) -> Optional[QueryTask]:
        """按优先级出队"""
        for priority in [QueryPriority.CRITICAL, QueryPriority.HIGH, 
                        QueryPriority.NORMAL, QueryPriority.LOW]:
            if self.queues[priority]:
                task = self.queues[priority].popleft()
                self.total_size -= 1
                return task
        return None
    
    def remove(self, task_id: str) -> bool:
        """移除指定任务"""
        for queue in self.queues.values():
            for task in queue:
                if task.id == task_id:
                    queue.remove(task)
                    self.total_size -= 1
                    return True
        return False
    
    def size(self) -> int:
        """队列大小"""
        return self.total_size
    
    def is_empty(self) -> bool:
        """是否为空"""
        return self.total_size == 0


class AsyncQueryExecutor:
    """异步查询执行器"""
    
    def __init__(self, max_concurrent_queries: int = 50, max_queue_size: int = 1000):
        self.max_concurrent_queries = max_concurrent_queries
        self.query_queue = QueryQueue(max_queue_size)
        self.running_tasks: Dict[str, QueryTask] = {}
        self.completed_tasks: Dict[str, QueryTask] = {}
        self.stats = ExecutorStats()
        
        # 配置
        self.config = {
            'enable_query_batching': True,
            'batch_size': 10,
            'batch_timeout': 0.1,  # 秒
            'enable_query_caching': True,
            'enable_connection_pooling': True,
            'default_query_timeout': 30.0,
            'max_retry_attempts': 3,
            'retry_delay': 1.0,
            'enable_query_optimization': True
        }
        
        # 运行状态
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        # 查询分析
        self.query_patterns: Dict[str, int] = defaultdict(int)
        self.execution_history: deque = deque(maxlen=1000)
    
    async def start(self):
        """启动执行器"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动工作线程
        for i in range(min(self.max_concurrent_queries, 10)):
            task = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self.worker_tasks.append(task)
        
        # 启动批处理任务
        if self.config['enable_query_batching']:
            batch_task = asyncio.create_task(self._batch_processor())
            self.worker_tasks.append(batch_task)
        
        logger.info(f"异步查询执行器已启动，工作线程数: {len(self.worker_tasks)}")
    
    async def stop(self):
        """停止执行器"""
        self.is_running = False
        
        # 取消所有工作任务
        for task in self.worker_tasks:
            task.cancel()
        
        # 等待所有任务完成
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        logger.info("异步查询执行器已停止")
    
    async def submit_query(
        self,
        query: Union[str, Select],
        params: Optional[Dict[str, Any]] = None,
        priority: QueryPriority = QueryPriority.NORMAL,
        timeout: float = None,
        callback: Optional[Callable] = None,
        cache_key: Optional[str] = None
    ) -> str:
        """提交查询任务"""
        if not self.is_running:
            raise RuntimeError("查询执行器未启动")
        
        task = QueryTask(
            query=query,
            params=params or {},
            priority=priority,
            timeout=timeout or self.config['default_query_timeout'],
            callback=callback,
            cache_key=cache_key
        )
        
        # 尝试从缓存获取结果
        if self.config['enable_query_caching'] and cache_key:
            cached_result = await self._try_get_from_cache(cache_key)
            if cached_result is not None:
                task.status = QueryStatus.COMPLETED
                task.result = cached_result
                task.completed_at = datetime.now()
                self.completed_tasks[task.id] = task
                
                if callback:
                    try:
                        await callback(task.result)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")
                
                return task.id
        
        # 加入队列
        if not self.query_queue.enqueue(task):
            raise RuntimeError("查询队列已满")
        
        self.stats.total_queries += 1
        self.stats.current_queue_size = self.query_queue.size()
        
        logger.debug(f"查询任务已提交: {task.id}, 优先级: {priority.name}")
        return task.id
    
    async def get_result(self, task_id: str, timeout: float = None) -> Any:
        """获取查询结果"""
        start_time = time.time()
        timeout = timeout or 60.0
        
        while time.time() - start_time < timeout:
            # 检查已完成的任务
            if task_id in self.completed_tasks:
                task = self.completed_tasks[task_id]
                if task.status == QueryStatus.COMPLETED:
                    return task.result
                elif task.status == QueryStatus.FAILED:
                    raise task.error
                elif task.status == QueryStatus.CANCELLED:
                    raise RuntimeError("查询已被取消")
            
            # 检查正在运行的任务
            if task_id in self.running_tasks:
                await asyncio.sleep(0.1)
                continue
            
            # 检查队列中的任务
            found_in_queue = False
            for queue in self.query_queue.queues.values():
                for task in queue:
                    if task.id == task_id:
                        found_in_queue = True
                        break
                if found_in_queue:
                    break
            
            if found_in_queue:
                await asyncio.sleep(0.1)
                continue
            
            break
        
        raise TimeoutError(f"获取查询结果超时: {task_id}")
    
    async def cancel_query(self, task_id: str) -> bool:
        """取消查询"""
        # 从队列中移除
        if self.query_queue.remove(task_id):
            # 创建取消的任务记录
            cancelled_task = QueryTask(id=task_id)
            cancelled_task.status = QueryStatus.CANCELLED
            cancelled_task.completed_at = datetime.now()
            self.completed_tasks[task_id] = cancelled_task
            self.stats.cancelled_queries += 1
            return True
        
        # 如果正在运行，标记为取消
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.status = QueryStatus.CANCELLED
            return True
        
        return False
    
    async def _worker_loop(self, worker_id: str):
        """工作线程循环"""
        logger.debug(f"工作线程 {worker_id} 已启动")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task = self.query_queue.dequeue()
                if not task:
                    await asyncio.sleep(0.1)
                    continue
                
                # 检查是否已取消
                if task.status == QueryStatus.CANCELLED:
                    continue
                
                # 执行任务
                await self._execute_task(task, worker_id)
                
            except Exception as e:
                logger.error(f"工作线程 {worker_id} 异常: {e}")
                await asyncio.sleep(1.0)
        
        logger.debug(f"工作线程 {worker_id} 已停止")
    
    async def _execute_task(self, task: QueryTask, worker_id: str):
        """执行查询任务"""
        task.status = QueryStatus.RUNNING
        task.started_at = datetime.now()
        self.running_tasks[task.id] = task
        
        # 更新统计
        current_running = len(self.running_tasks)
        if current_running > self.stats.max_concurrent_queries:
            self.stats.max_concurrent_queries = current_running
        
        try:
            # 执行查询
            start_time = time.time()
            
            async with db_manager.get_session() as session:
                if isinstance(task.query, str):
                    result = await session.execute(text(task.query), task.params)
                else:
                    result = await session.execute(task.query, task.params)
                
                # 获取结果
                if hasattr(result, 'fetchall'):
                    task.result = result.fetchall()
                elif hasattr(result, 'scalars'):
                    task.result = result.scalars().all()
                else:
                    task.result = result
            
            task.execution_time = time.time() - start_time
            task.status = QueryStatus.COMPLETED
            task.completed_at = datetime.now()
            
            # 缓存结果
            if self.config['enable_query_caching'] and task.cache_key:
                await self._cache_result(task.cache_key, task.result)
            
            # 更新统计
            self.stats.completed_queries += 1
            self._update_execution_stats(task.execution_time)
            
            # 执行回调
            if task.callback:
                try:
                    await task.callback(task.result)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
            
            logger.debug(f"查询任务完成: {task.id}, 耗时: {task.execution_time:.3f}s")
            
        except Exception as e:
            task.error = e
            task.status = QueryStatus.FAILED
            task.completed_at = datetime.now()
            task.execution_time = time.time() - start_time
            
            # 重试机制
            if task.retry_count < self.config['max_retry_attempts']:
                task.retry_count += 1
                task.status = QueryStatus.PENDING
                
                # 重新加入队列（降低优先级）
                if task.priority != QueryPriority.LOW:
                    task.priority = QueryPriority.LOW
                
                self.query_queue.enqueue(task)
                logger.warning(f"查询任务重试: {task.id}, 重试次数: {task.retry_count}")
            else:
                self.stats.failed_queries += 1
                logger.error(f"查询任务失败: {task.id}, 错误: {e}")
        
        finally:
            # 从运行任务中移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            # 移动到完成任务
            self.completed_tasks[task.id] = task
            
            # 限制完成任务数量
            if len(self.completed_tasks) > 10000:
                # 移除最旧的1000个任务
                oldest_tasks = sorted(
                    self.completed_tasks.items(),
                    key=lambda x: x[1].completed_at or datetime.min
                )[:1000]
                
                for task_id, _ in oldest_tasks:
                    del self.completed_tasks[task_id]
            
            # 更新队列大小统计
            self.stats.current_queue_size = self.query_queue.size()
    
    async def _batch_processor(self):
        """批处理处理器"""
        logger.debug("批处理处理器已启动")
        
        batch_queries = []
        last_batch_time = time.time()
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # 收集批处理查询
                while (len(batch_queries) < self.config['batch_size'] and
                       current_time - last_batch_time < self.config['batch_timeout']):
                    
                    # 这里可以实现特定的批处理逻辑
                    # 例如，收集相似的查询进行批处理
                    await asyncio.sleep(0.01)
                    current_time = time.time()
                
                # 处理批次
                if batch_queries:
                    await self._process_batch(batch_queries)
                    batch_queries.clear()
                    last_batch_time = current_time
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"批处理处理器异常: {e}")
                await asyncio.sleep(1.0)
        
        logger.debug("批处理处理器已停止")
    
    async def _process_batch(self, queries: List[QueryTask]):
        """处理批次查询"""
        # 简化的批处理实现
        # 实际应用中可以根据查询类型进行更智能的批处理
        logger.debug(f"处理批次查询: {len(queries)} 个")
        
        for query in queries:
            # 重新加入正常队列
            self.query_queue.enqueue(query)
    
    def _update_execution_stats(self, execution_time: float):
        """更新执行统计"""
        self.execution_history.append({
            'timestamp': datetime.now(),
            'execution_time': execution_time
        })
        
        # 计算平均执行时间
        if self.stats.completed_queries > 0:
            total_time = sum(
                entry['execution_time'] for entry in self.execution_history
            )
            self.stats.avg_execution_time = total_time / len(self.execution_history)
    
    async def _try_get_from_cache(self, cache_key: str) -> Optional[Any]:
        """尝试从缓存获取结果"""
        try:
            # 这里应该与实际的缓存系统集成
            # return await smart_cache_manager.get(cache_key)
            return None
        except Exception as e:
            logger.debug(f"缓存获取失败: {e}")
            return None
    
    async def _cache_result(self, cache_key: str, result: Any):
        """缓存结果"""
        try:
            # 这里应该与实际的缓存系统集成
            # await smart_cache_manager.set(cache_key, result)
            pass
        except Exception as e:
            logger.debug(f"缓存设置失败: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取执行器统计"""
        return {
            'executor_stats': {
                'total_queries': self.stats.total_queries,
                'completed_queries': self.stats.completed_queries,
                'failed_queries': self.stats.failed_queries,
                'cancelled_queries': self.stats.cancelled_queries,
                'success_rate': (
                    self.stats.completed_queries / max(self.stats.total_queries, 1)
                ),
                'avg_execution_time': self.stats.avg_execution_time,
                'max_concurrent_queries': self.stats.max_concurrent_queries,
                'current_queue_size': self.stats.current_queue_size,
                'current_running_queries': len(self.running_tasks)
            },
            'queue_stats': {
                'total_size': self.query_queue.size(),
                'by_priority': {
                    priority.name: len(queue)
                    for priority, queue in self.query_queue.queues.items()
                }
            },
            'configuration': self.config,
            'is_running': self.is_running,
            'worker_count': len(self.worker_tasks)
        }
    
    async def get_running_queries(self) -> List[Dict[str, Any]]:
        """获取正在运行的查询"""
        return [
            {
                'id': task.id,
                'status': task.status.value,
                'priority': task.priority.name,
                'created_at': task.created_at.isoformat(),
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'running_time': (
                    (datetime.now() - task.started_at).total_seconds()
                    if task.started_at else 0
                ),
                'retry_count': task.retry_count
            }
            for task in self.running_tasks.values()
        ]
    
    async def optimize_performance(self):
        """性能优化"""
        # 分析查询模式
        if len(self.execution_history) > 100:
            recent_times = [
                entry['execution_time'] 
                for entry in list(self.execution_history)[-100:]
            ]
            
            avg_time = sum(recent_times) / len(recent_times)
            
            # 动态调整并发数
            if avg_time > 2.0 and len(self.worker_tasks) > 5:
                # 查询较慢，减少并发
                self.max_concurrent_queries = max(5, self.max_concurrent_queries - 2)
                logger.info(f"查询较慢，减少并发数到: {self.max_concurrent_queries}")
            elif avg_time < 0.5 and len(self.worker_tasks) < 20:
                # 查询较快，增加并发
                self.max_concurrent_queries += 2
                logger.info(f"查询较快，增加并发数到: {self.max_concurrent_queries}")


# 全局异步查询执行器实例
async_query_executor = AsyncQueryExecutor()


# 便捷函数
async def execute_query_async(
    query: Union[str, Select],
    params: Optional[Dict[str, Any]] = None,
    priority: QueryPriority = QueryPriority.NORMAL,
    timeout: float = 30.0
) -> Any:
    """异步执行查询"""
    task_id = await async_query_executor.submit_query(
        query=query,
        params=params,
        priority=priority,
        timeout=timeout
    )
    
    return await async_query_executor.get_result(task_id, timeout)


async def execute_queries_batch(
    queries: List[Tuple[Union[str, Select], Dict[str, Any]]],
    priority: QueryPriority = QueryPriority.NORMAL
) -> List[Any]:
    """批量执行查询"""
    task_ids = []
    
    for query, params in queries:
        task_id = await async_query_executor.submit_query(
            query=query,
            params=params,
            priority=priority
        )
        task_ids.append(task_id)
    
    # 等待所有查询完成
    results = []
    for task_id in task_ids:
        result = await async_query_executor.get_result(task_id)
        results.append(result)
    
    return results


# 初始化和清理函数
async def init_async_executor():
    """初始化异步查询执行器"""
    await async_query_executor.start()
    logger.info("异步查询执行器初始化完成")


async def cleanup_async_executor():
    """清理异步查询执行器"""
    await async_query_executor.stop()
    logger.info("异步查询执行器已清理")


if __name__ == "__main__":
    async def test_async_executor():
        """测试异步查询执行器"""
        print("测试异步查询执行器...")
        
        # 启动执行器
        await init_async_executor()
        
        # 提交一些测试查询
        task_ids = []
        for i in range(10):
            task_id = await async_query_executor.submit_query(
                query="SELECT 1 as test_value",
                priority=QueryPriority.NORMAL
            )
            task_ids.append(task_id)
        
        # 获取结果
        results = []
        for task_id in task_ids:
            try:
                result = await async_query_executor.get_result(task_id, timeout=10.0)
                results.append(result)
            except Exception as e:
                print(f"查询失败: {e}")
        
        print(f"完成 {len(results)} 个查询")
        
        # 获取统计信息
        stats = await async_query_executor.get_stats()
        print(f"执行器统计: {stats['executor_stats']}")
        
        # 清理
        await cleanup_async_executor()
        print("测试完成")
    
    asyncio.run(test_async_executor())
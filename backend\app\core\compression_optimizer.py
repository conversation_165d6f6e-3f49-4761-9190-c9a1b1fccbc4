"""
压缩算法优化模块
测试不同压缩级别，优化gzip压缩性能，实现自适应压缩策略
"""

import asyncio
import gzip
import lzma
import pickle
import time
import zlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import pandas as pd
from loguru import logger

from app.core.monitoring import system_monitor


class CompressionAlgorithm(Enum):
    """压缩算法类型"""
    GZIP = "gzip"
    ZLIB = "zlib"
    LZMA = "lzma"
    NONE = "none"


@dataclass
class CompressionResult:
    """压缩结果"""
    algorithm: CompressionAlgorithm
    level: int
    original_size: int
    compressed_size: int
    compression_ratio: float
    compression_time: float
    decompression_time: float
    total_time: float


@dataclass
class CompressionProfile:
    """压缩配置文件"""
    algorithm: CompressionAlgorithm
    level: int
    min_size_threshold: int = 1024  # 最小压缩阈值(字节)
    max_compression_time: float = 5.0  # 最大压缩时间(秒)


class CompressionOptimizer:
    """压缩算法优化器"""
    
    def __init__(self):
        self.test_results: List[CompressionResult] = []
        self.optimal_profiles: Dict[str, CompressionProfile] = {}
        
        # 默认压缩配置
        self.default_profiles = {
            'small_data': CompressionProfile(CompressionAlgorithm.GZIP, 6, 1024, 1.0),
            'medium_data': CompressionProfile(CompressionAlgorithm.GZIP, 4, 10240, 3.0),
            'large_data': CompressionProfile(CompressionAlgorithm.GZIP, 2, 102400, 5.0)
        }
        
        # 测试配置
        self.test_algorithms = [
            CompressionAlgorithm.GZIP,
            CompressionAlgorithm.ZLIB,
            CompressionAlgorithm.LZMA
        ]
        
        self.test_levels = {
            CompressionAlgorithm.GZIP: [1, 3, 6, 9],
            CompressionAlgorithm.ZLIB: [1, 3, 6, 9],
            CompressionAlgorithm.LZMA: [1, 3, 6]  # LZMA级别较少，避免过长时间
        }
        
        # 初始化最优配置
        self.optimal_profiles = self.default_profiles.copy()
        
        logger.info("压缩算法优化器初始化完成")
    
    async def benchmark_compression(self, test_data: bytes, data_type: str = "test") -> List[CompressionResult]:
        """压缩算法基准测试"""
        logger.info(f"开始压缩算法基准测试: {data_type} ({len(test_data)} 字节)")
        
        results = []
        original_size = len(test_data)
        
        for algorithm in self.test_algorithms:
            levels = self.test_levels.get(algorithm, [6])
            
            for level in levels:
                try:
                    result = await self._test_compression(test_data, algorithm, level)
                    results.append(result)
                    
                    logger.debug(f"测试完成: {algorithm.value} 级别{level} - "
                               f"压缩比 {result.compression_ratio:.2f}, "
                               f"总时间 {result.total_time:.3f}s")
                    
                    # 记录监控指标
                    system_monitor.record_metric(
                        f"compression.{algorithm.value}.level_{level}.ratio",
                        result.compression_ratio
                    )
                    system_monitor.record_metric(
                        f"compression.{algorithm.value}.level_{level}.time_ms",
                        result.total_time * 1000
                    )
                    
                except Exception as e:
                    logger.error(f"压缩测试失败: {algorithm.value} 级别{level} - {e}")
        
        # 保存测试结果
        self.test_results.extend(results)
        
        # 分析最优配置
        optimal_result = self._analyze_optimal_compression(results, original_size)
        if optimal_result:
            logger.info(f"最优压缩配置: {optimal_result.algorithm.value} 级别{optimal_result.level} - "
                       f"压缩比 {optimal_result.compression_ratio:.2f}, "
                       f"总时间 {optimal_result.total_time:.3f}s")
        
        return results
    
    async def _test_compression(self, data: bytes, algorithm: CompressionAlgorithm, level: int) -> CompressionResult:
        """测试单个压缩配置"""
        # 压缩测试
        compress_start = time.time()
        
        if algorithm == CompressionAlgorithm.GZIP:
            compressed_data = gzip.compress(data, compresslevel=level)
        elif algorithm == CompressionAlgorithm.ZLIB:
            compressed_data = zlib.compress(data, level=level)
        elif algorithm == CompressionAlgorithm.LZMA:
            compressed_data = lzma.compress(data, preset=level)
        else:
            compressed_data = data
        
        compression_time = time.time() - compress_start
        
        # 解压测试
        decompress_start = time.time()
        
        if algorithm == CompressionAlgorithm.GZIP:
            decompressed_data = gzip.decompress(compressed_data)
        elif algorithm == CompressionAlgorithm.ZLIB:
            decompressed_data = zlib.decompress(compressed_data)
        elif algorithm == CompressionAlgorithm.LZMA:
            decompressed_data = lzma.decompress(compressed_data)
        else:
            decompressed_data = compressed_data
        
        decompression_time = time.time() - decompress_start
        
        # 验证数据完整性
        if decompressed_data != data:
            raise ValueError("解压后数据不匹配")
        
        # 计算压缩比
        compression_ratio = len(compressed_data) / len(data)
        total_time = compression_time + decompression_time
        
        return CompressionResult(
            algorithm=algorithm,
            level=level,
            original_size=len(data),
            compressed_size=len(compressed_data),
            compression_ratio=compression_ratio,
            compression_time=compression_time,
            decompression_time=decompression_time,
            total_time=total_time
        )
    
    def _analyze_optimal_compression(self, results: List[CompressionResult], original_size: int) -> Optional[CompressionResult]:
        """分析最优压缩配置"""
        if not results:
            return None
        
        # 根据数据大小选择评分策略
        if original_size < 10240:  # 小于10KB，优先考虑速度
            weight_ratio = 0.3
            weight_time = 0.7
        elif original_size < 102400:  # 小于100KB，平衡考虑
            weight_ratio = 0.5
            weight_time = 0.5
        else:  # 大于100KB，优先考虑压缩比
            weight_ratio = 0.7
            weight_time = 0.3
        
        # 计算综合评分
        best_result = None
        best_score = float('inf')
        
        for result in results:
            # 归一化指标 (越小越好)
            normalized_ratio = result.compression_ratio
            normalized_time = result.total_time / max(r.total_time for r in results)
            
            # 综合评分
            score = weight_ratio * normalized_ratio + weight_time * normalized_time
            
            if score < best_score:
                best_score = score
                best_result = result
        
        return best_result
    
    async def optimize_for_data_type(self, sample_data: pd.DataFrame, data_type: str) -> CompressionProfile:
        """为特定数据类型优化压缩配置"""
        logger.info(f"为数据类型 {data_type} 优化压缩配置...")
        
        # 序列化测试数据
        test_data = pickle.dumps(sample_data, protocol=pickle.HIGHEST_PROTOCOL)
        
        # 运行基准测试
        results = await self.benchmark_compression(test_data, data_type)
        
        if not results:
            logger.warning(f"没有测试结果，使用默认配置: {data_type}")
            return self.default_profiles.get('medium_data')
        
        # 分析最优配置
        optimal_result = self._analyze_optimal_compression(results, len(test_data))
        
        if optimal_result:
            # 创建优化配置
            profile = CompressionProfile(
                algorithm=optimal_result.algorithm,
                level=optimal_result.level,
                min_size_threshold=max(1024, len(test_data) // 100),  # 动态阈值
                max_compression_time=optimal_result.total_time * 2  # 允许2倍时间
            )
            
            self.optimal_profiles[data_type] = profile
            
            logger.info(f"数据类型 {data_type} 优化完成: "
                       f"{profile.algorithm.value} 级别{profile.level}")
            
            return profile
        else:
            logger.warning(f"优化失败，使用默认配置: {data_type}")
            return self.default_profiles.get('medium_data')
    
    async def compress_data(self, data: bytes, data_type: str = "default") -> Tuple[bytes, CompressionProfile]:
        """使用优化配置压缩数据"""
        # 获取最优配置
        profile = self.optimal_profiles.get(data_type, self.default_profiles['medium_data'])
        
        # 检查是否需要压缩
        if len(data) < profile.min_size_threshold:
            logger.debug(f"数据太小，跳过压缩: {len(data)} < {profile.min_size_threshold}")
            return data, profile
        
        try:
            start_time = time.time()
            
            # 执行压缩
            if profile.algorithm == CompressionAlgorithm.GZIP:
                compressed_data = gzip.compress(data, compresslevel=profile.level)
            elif profile.algorithm == CompressionAlgorithm.ZLIB:
                compressed_data = zlib.compress(data, level=profile.level)
            elif profile.algorithm == CompressionAlgorithm.LZMA:
                compressed_data = lzma.compress(data, preset=profile.level)
            else:
                compressed_data = data
            
            compression_time = time.time() - start_time
            
            # 检查压缩时间
            if compression_time > profile.max_compression_time:
                logger.warning(f"压缩时间超限: {compression_time:.3f}s > {profile.max_compression_time}s")
            
            # 记录监控指标
            compression_ratio = len(compressed_data) / len(data)
            system_monitor.record_metric("compression.ratio", compression_ratio)
            system_monitor.record_metric("compression.time_ms", compression_time * 1000)
            
            logger.debug(f"压缩完成: {len(data)} -> {len(compressed_data)} 字节 "
                        f"({compression_ratio:.3f}), {compression_time:.3f}s")
            
            return compressed_data, profile
            
        except Exception as e:
            logger.error(f"压缩失败: {e}")
            return data, profile
    
    async def decompress_data(self, compressed_data: bytes, algorithm: CompressionAlgorithm) -> bytes:
        """解压数据"""
        try:
            start_time = time.time()
            
            if algorithm == CompressionAlgorithm.GZIP:
                data = gzip.decompress(compressed_data)
            elif algorithm == CompressionAlgorithm.ZLIB:
                data = zlib.decompress(compressed_data)
            elif algorithm == CompressionAlgorithm.LZMA:
                data = lzma.decompress(compressed_data)
            else:
                data = compressed_data
            
            decompression_time = time.time() - start_time
            
            # 记录监控指标
            system_monitor.record_metric("decompression.time_ms", decompression_time * 1000)
            
            logger.debug(f"解压完成: {len(compressed_data)} -> {len(data)} 字节, {decompression_time:.3f}s")
            
            return data
            
        except Exception as e:
            logger.error(f"解压失败: {e}")
            raise
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        if not self.test_results:
            return {
                'total_tests': 0,
                'optimal_profiles': len(self.optimal_profiles),
                'message': '暂无测试数据'
            }
        
        # 统计各算法性能
        algorithm_stats = {}
        for algorithm in CompressionAlgorithm:
            if algorithm == CompressionAlgorithm.NONE:
                continue
            
            algo_results = [r for r in self.test_results if r.algorithm == algorithm]
            if algo_results:
                algorithm_stats[algorithm.value] = {
                    'test_count': len(algo_results),
                    'avg_compression_ratio': sum(r.compression_ratio for r in algo_results) / len(algo_results),
                    'avg_total_time': sum(r.total_time for r in algo_results) / len(algo_results),
                    'best_ratio': min(r.compression_ratio for r in algo_results),
                    'fastest_time': min(r.total_time for r in algo_results)
                }
        
        # 当前最优配置
        current_profiles = {}
        for data_type, profile in self.optimal_profiles.items():
            current_profiles[data_type] = {
                'algorithm': profile.algorithm.value,
                'level': profile.level,
                'min_size_threshold': profile.min_size_threshold,
                'max_compression_time': profile.max_compression_time
            }
        
        return {
            'total_tests': len(self.test_results),
            'algorithm_stats': algorithm_stats,
            'current_profiles': current_profiles,
            'timestamp': datetime.now().isoformat()
        }
    
    async def auto_optimize_all_data_types(self):
        """自动优化所有数据类型的压缩配置"""
        logger.info("开始自动优化所有数据类型...")
        
        # 生成不同类型的测试数据
        test_datasets = {
            'small_quotes': self._generate_quote_data(100),
            'medium_klines': self._generate_kline_data(1000),
            'large_historical': self._generate_kline_data(10000)
        }
        
        for data_type, test_data in test_datasets.items():
            try:
                await self.optimize_for_data_type(test_data, data_type)
                await asyncio.sleep(0.1)  # 避免过于频繁
            except Exception as e:
                logger.error(f"优化数据类型 {data_type} 失败: {e}")
        
        logger.info("自动优化完成")
    
    def _generate_quote_data(self, count: int) -> pd.DataFrame:
        """生成行情数据用于测试"""
        import random
        
        data = []
        for i in range(count):
            data.append({
                'symbol': f'00000{i % 1000:03d}.SZ',
                'price': round(random.uniform(1, 100), 2),
                'change': round(random.uniform(-5, 5), 2),
                'volume': random.randint(1000, 1000000),
                'timestamp': datetime.now().isoformat()
            })
        
        return pd.DataFrame(data)
    
    def _generate_kline_data(self, count: int) -> pd.DataFrame:
        """生成K线数据用于测试"""
        import random
        
        data = []
        base_price = 50.0
        
        for i in range(count):
            change = random.uniform(-0.05, 0.05)
            base_price *= (1 + change)
            
            open_price = base_price
            high_price = open_price * random.uniform(1.0, 1.02)
            low_price = open_price * random.uniform(0.98, 1.0)
            close_price = random.uniform(low_price, high_price)
            
            data.append({
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': random.randint(10000, 10000000),
                'timestamp': datetime.now().isoformat()
            })
        
        return pd.DataFrame(data)


# 全局实例
compression_optimizer = CompressionOptimizer()


# 导出主要组件
__all__ = [
    'CompressionOptimizer',
    'CompressionAlgorithm',
    'CompressionResult',
    'CompressionProfile',
    'compression_optimizer'
]

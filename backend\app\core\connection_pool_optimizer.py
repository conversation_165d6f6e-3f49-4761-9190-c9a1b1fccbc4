"""
数据库连接池优化器
针对量化交易高频访问场景的连接池优化
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import statistics

from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.pool import QueuePool, StaticPool, NullPool
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from app.core.config import settings


logger = logging.getLogger(__name__)


@dataclass
class ConnectionPoolStats:
    """连接池统计信息"""
    pool_size: int
    checked_in: int
    checked_out: int
    overflow: int
    invalid: int
    total_connections: int
    avg_checkout_time: float
    max_checkout_time: float
    checkout_count: int
    checkin_count: int
    connection_errors: int
    pool_utilization: float
    
    
@dataclass
class ConnectionMetrics:
    """连接指标"""
    checkout_time: float
    checkin_time: float
    query_count: int
    error_count: int
    last_activity: datetime


class ConnectionPoolOptimizer:
    """连接池优化器"""
    
    def __init__(self):
        self.engines: Dict[str, Any] = {}
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        self.pool_stats_history: List[ConnectionPoolStats] = []
        self.start_time = time.time()
        
        # 新增监控配置
        self.monitoring_config = {
            'enable_detailed_logging': True,
            'log_slow_connections': True,
            'slow_connection_threshold': 5.0,  # 秒
            'health_check_interval': 60,  # 秒
            'stats_collection_interval': 30,  # 秒
            'auto_optimization': True,
            'alert_thresholds': {
                'high_utilization': 0.85,
                'connection_errors': 10,
                'slow_connections': 5
            }
        }
        
        # 连接池性能优化配置
        self.optimization_config = {
            'sqlite': {
                'poolclass': StaticPool,
                'pool_size': 1,  # SQLite单连接
                'max_overflow': 0,
                'pool_timeout': 30,
                'pool_recycle': -1,  # SQLite不需要回收
                'pool_pre_ping': False,
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 30,
                    'isolation_level': None,
                    # SQLite性能优化PRAGMA
                    'pragma': {
                        'journal_mode': 'WAL',
                        'synchronous': 'NORMAL',
                        'cache_size': -50000,  # 50MB缓存
                        'temp_store': 'MEMORY',
                        'mmap_size': 268435456,  # 256MB内存映射
                        'page_size': 4096,
                        'wal_autocheckpoint': 1000,
                        'busy_timeout': 30000,
                        'foreign_keys': 'ON',
                    }
                }
            },
            'postgresql': {
                'poolclass': QueuePool,
                'pool_size': 20,  # 基础连接数
                'max_overflow': 30,  # 溢出连接数
                'pool_timeout': 30,
                'pool_recycle': 3600,  # 1小时回收
                'pool_pre_ping': True,
                'connect_args': {
                    'server_settings': {
                        'jit': 'off',  # 关闭JIT编译以提高短查询性能
                        'application_name': 'quant_platform',
                    },
                    'options': '-c default_transaction_isolation=read_committed'
                }
            },
            'mysql': {
                'poolclass': QueuePool,
                'pool_size': 20,
                'max_overflow': 30,
                'pool_timeout': 30,
                'pool_recycle': 3600,
                'pool_pre_ping': True,
                'connect_args': {
                    'charset': 'utf8mb4',
                    'autocommit': True,
                }
            }
        }
    
    def get_optimized_engine_config(self, database_url: str) -> Dict[str, Any]:
        """获取优化的引擎配置"""
        config = {
            'echo': settings.DEBUG,
            'future': True,
            'query_cache_size': 1200,  # 查询缓存
        }
        
        # 根据数据库类型选择配置
        if 'sqlite' in database_url:
            config.update(self.optimization_config['sqlite'])
        elif 'postgresql' in database_url:
            config.update(self.optimization_config['postgresql'])
        elif 'mysql' in database_url:
            config.update(self.optimization_config['mysql'])
        
        return config
    
    def create_optimized_engine(self, database_url: str, name: str = 'default'):
        """创建优化的数据库引擎"""
        config = self.get_optimized_engine_config(database_url)
        
        # 创建引擎
        engine = create_async_engine(database_url, **config)
        
        # 注册事件监听器
        self._register_event_listeners(engine, name)
        
        # 存储引擎
        self.engines[name] = engine
        
        logger.info(f"创建优化引擎 {name}: {database_url.split('@')[-1] if '@' in database_url else database_url}")
        return engine
    
    def _register_event_listeners(self, engine: Engine, name: str):
        """注册引擎事件监听器"""
        
        @event.listens_for(engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """设置SQLite优化参数"""
            if 'sqlite' in str(dbapi_connection):
                cursor = dbapi_connection.cursor()
                
                # 应用优化PRAGMA设置
                pragma_settings = self.optimization_config['sqlite']['connect_args']['pragma']
                for pragma, value in pragma_settings.items():
                    cursor.execute(f"PRAGMA {pragma}={value}")
                
                # 执行优化
                cursor.execute("PRAGMA optimize")
                cursor.close()
        
        @event.listens_for(engine.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出监听"""
            connection_id = id(dbapi_connection)
            current_time = time.time()
            
            if connection_id not in self.connection_metrics:
                self.connection_metrics[connection_id] = ConnectionMetrics(
                    checkout_time=current_time,
                    checkin_time=0,
                    query_count=0,
                    error_count=0,
                    last_activity=datetime.now()
                )
            else:
                self.connection_metrics[connection_id].checkout_time = current_time
                self.connection_metrics[connection_id].last_activity = datetime.now()
        
        @event.listens_for(engine.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入监听"""
            connection_id = id(dbapi_connection)
            current_time = time.time()
            
            if connection_id in self.connection_metrics:
                metrics = self.connection_metrics[connection_id]
                metrics.checkin_time = current_time
                metrics.last_activity = datetime.now()
        
        @event.listens_for(engine.sync_engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """查询执行前监听"""
            context._query_start_time = time.time()
        
        @event.listens_for(engine.sync_engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """查询执行后监听"""
            connection_id = id(conn.connection)
            
            if connection_id in self.connection_metrics:
                self.connection_metrics[connection_id].query_count += 1
            
            # 记录慢查询
            if hasattr(context, '_query_start_time'):
                execution_time = time.time() - context._query_start_time
                if execution_time > 1.0:  # 超过1秒的查询
                    logger.warning(f"慢查询检测 ({execution_time:.2f}s): {statement[:100]}...")
    
    async def get_connection_pool_stats(self, engine_name: str = 'default') -> Optional[ConnectionPoolStats]:
        """获取连接池统计信息"""
        if engine_name not in self.engines:
            return None
        
        engine = self.engines[engine_name]
        pool = engine.pool
        
        # 收集连接指标
        checkout_times = [
            metrics.checkin_time - metrics.checkout_time 
            for metrics in self.connection_metrics.values()
            if metrics.checkin_time > 0 and metrics.checkout_time > 0
        ]
        
        avg_checkout_time = statistics.mean(checkout_times) if checkout_times else 0
        max_checkout_time = max(checkout_times) if checkout_times else 0
        
        # 构建统计信息
        stats = ConnectionPoolStats(
            pool_size=getattr(pool, 'size', lambda: 0)(),
            checked_in=getattr(pool, 'checkedin', lambda: 0)(),
            checked_out=getattr(pool, 'checkedout', lambda: 0)(),
            overflow=getattr(pool, 'overflow', lambda: 0)(),
            invalid=getattr(pool, 'invalidated', lambda: 0)(),
            total_connections=getattr(pool, 'size', lambda: 0)() + getattr(pool, 'overflow', lambda: 0)(),
            avg_checkout_time=avg_checkout_time,
            max_checkout_time=max_checkout_time,
            checkout_count=len([m for m in self.connection_metrics.values() if m.checkout_time > 0]),
            checkin_count=len([m for m in self.connection_metrics.values() if m.checkin_time > 0]),
            connection_errors=sum(m.error_count for m in self.connection_metrics.values()),
            pool_utilization=0
        )
        
        # 计算池利用率
        if stats.pool_size > 0:
            stats.pool_utilization = stats.checked_out / stats.pool_size
        
        return stats
    
    async def optimize_pool_size(self, engine_name: str = 'default') -> Dict[str, Any]:
        """动态优化连接池大小"""
        stats = await self.get_connection_pool_stats(engine_name)
        if not stats:
            return {'error': 'Engine not found'}
        
        recommendations = {}
        
        # 分析利用率
        if stats.pool_utilization > 0.8:
            recommendations['pool_size'] = 'increase'
            recommendations['reason'] = f'高利用率 ({stats.pool_utilization:.2%})'
        elif stats.pool_utilization < 0.3:
            recommendations['pool_size'] = 'decrease'
            recommendations['reason'] = f'低利用率 ({stats.pool_utilization:.2%})'
        else:
            recommendations['pool_size'] = 'optimal'
            recommendations['reason'] = f'适中利用率 ({stats.pool_utilization:.2%})'
        
        # 分析超时情况
        if stats.avg_checkout_time > 5.0:
            recommendations['timeout'] = 'increase'
            recommendations['timeout_reason'] = f'平均检出时间过长 ({stats.avg_checkout_time:.2f}s)'
        
        # 分析溢出连接
        if stats.overflow > stats.pool_size * 0.5:
            recommendations['max_overflow'] = 'increase'
            recommendations['overflow_reason'] = f'溢出连接过多 ({stats.overflow})'
        
        return {
            'current_stats': stats,
            'recommendations': recommendations,
            'timestamp': datetime.now().isoformat()
        }
    
    async def health_check(self, engine_name: str = 'default') -> Dict[str, Any]:
        """连接池健康检查"""
        if engine_name not in self.engines:
            return {'status': 'error', 'message': 'Engine not found'}
        
        engine = self.engines[engine_name]
        
        try:
            # 测试连接
            async with engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            stats = await self.get_connection_pool_stats(engine_name)
            
            # 判断健康状态
            health_status = 'healthy'
            issues = []
            
            if stats:
                if stats.pool_utilization > 0.9:
                    health_status = 'warning'
                    issues.append('连接池利用率过高')
                
                if stats.connection_errors > 0:
                    health_status = 'warning'
                    issues.append(f'{stats.connection_errors} 个连接错误')
                
                if stats.avg_checkout_time > 10.0:
                    health_status = 'warning'
                    issues.append('连接检出时间过长')
            
            return {
                'status': health_status,
                'issues': issues,
                'stats': stats,
                'uptime': time.time() - self.start_time,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_performance_report(self, engine_name: str = 'default') -> Dict[str, Any]:
        """获取性能报告"""
        stats = await self.get_connection_pool_stats(engine_name)
        health = await self.health_check(engine_name)
        optimization = await self.optimize_pool_size(engine_name)
        
        # 计算性能指标
        active_connections = len([
            m for m in self.connection_metrics.values()
            if (datetime.now() - m.last_activity).total_seconds() < 300  # 5分钟内活跃
        ])
        
        total_queries = sum(m.query_count for m in self.connection_metrics.values())
        
        report = {
            'engine_name': engine_name,
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': time.time() - self.start_time,
            'connection_stats': stats,
            'health_check': health,
            'optimization_advice': optimization,
            'performance_metrics': {
                'active_connections': active_connections,
                'total_queries': total_queries,
                'queries_per_second': total_queries / (time.time() - self.start_time) if self.start_time else 0,
                'connection_efficiency': stats.pool_utilization if stats else 0,
            },
            'configuration': self.get_optimized_engine_config(
                str(self.engines[engine_name].url) if engine_name in self.engines else ''
            )
        }
        
        return report
    
    async def start_monitoring(self, engine_name: str = 'default'):
        """启动连接池监控"""
        if not self.monitoring_config['enable_detailed_logging']:
            return
        
        async def monitor_loop():
            while True:
                try:
                    # 收集统计信息
                    stats = await self.get_connection_pool_stats(engine_name)
                    if stats:
                        self.pool_stats_history.append(stats)
                        
                        # 检查告警阈值
                        await self._check_alert_thresholds(stats)
                        
                        # 自动优化
                        if self.monitoring_config['auto_optimization']:
                            await self._auto_optimize_pool(engine_name, stats)
                    
                    # 等待下一次收集
                    await asyncio.sleep(self.monitoring_config['stats_collection_interval'])
                    
                except Exception as e:
                    logger.error(f"连接池监控异常: {e}")
                    await asyncio.sleep(60)  # 出错后等待1分钟
        
        # 启动监控任务
        asyncio.create_task(monitor_loop())
        logger.info(f"连接池监控已启动，引擎: {engine_name}")
    
    async def _check_alert_thresholds(self, stats: ConnectionPoolStats):
        """检查告警阈值"""
        alerts = []
        thresholds = self.monitoring_config['alert_thresholds']
        
        # 检查高利用率
        if stats.pool_utilization > thresholds['high_utilization']:
            alerts.append({
                'type': 'high_utilization',
                'message': f'连接池利用率过高: {stats.pool_utilization:.2%}',
                'severity': 'warning'
            })
        
        # 检查连接错误
        if stats.connection_errors > thresholds['connection_errors']:
            alerts.append({
                'type': 'connection_errors',
                'message': f'连接错误过多: {stats.connection_errors}',
                'severity': 'error'
            })
        
        # 检查慢连接
        if stats.avg_checkout_time > thresholds['slow_connections']:
            alerts.append({
                'type': 'slow_connections',
                'message': f'连接检出时间过长: {stats.avg_checkout_time:.2f}s',
                'severity': 'warning'
            })
        
        # 记录告警
        for alert in alerts:
            if alert['severity'] == 'error':
                logger.error(f"连接池告警: {alert['message']}")
            else:
                logger.warning(f"连接池告警: {alert['message']}")
    
    async def _auto_optimize_pool(self, engine_name: str, stats: ConnectionPoolStats):
        """自动优化连接池"""
        if len(self.pool_stats_history) < 5:  # 需要足够的历史数据
            return
        
        recent_stats = self.pool_stats_history[-5:]
        avg_utilization = sum(s.pool_utilization for s in recent_stats) / len(recent_stats)
        
        # 如果持续高利用率，建议增加连接池大小
        if avg_utilization > 0.8:
            logger.info(f"检测到连接池持续高利用率 ({avg_utilization:.2%})，建议增加连接池大小")
            # 这里可以实现自动调整逻辑
        
        # 如果利用率很低，建议减少连接池大小
        elif avg_utilization < 0.3:
            logger.info(f"检测到连接池利用率较低 ({avg_utilization:.2%})，建议减少连接池大小")
    
    async def get_detailed_metrics(self, engine_name: str = 'default') -> Dict[str, Any]:
        """获取详细的连接池指标"""
        stats = await self.get_connection_pool_stats(engine_name)
        
        # 计算历史趋势
        if len(self.pool_stats_history) > 1:
            recent_utilization = [s.pool_utilization for s in self.pool_stats_history[-10:]]
            utilization_trend = 'increasing' if recent_utilization[-1] > recent_utilization[0] else 'decreasing'
        else:
            utilization_trend = 'stable'
        
        # 活跃连接分析
        active_connections = sum(
            1 for metrics in self.connection_metrics.values()
            if (datetime.now() - metrics.last_activity).total_seconds() < 300
        )
        
        return {
            'basic_stats': stats,
            'trends': {
                'utilization_trend': utilization_trend,
                'history_points': len(self.pool_stats_history)
            },
            'connection_analysis': {
                'active_connections': active_connections,
                'total_tracked_connections': len(self.connection_metrics),
                'avg_queries_per_connection': (
                    sum(m.query_count for m in self.connection_metrics.values()) / 
                    len(self.connection_metrics) if self.connection_metrics else 0
                ),
                'error_rate': (
                    sum(m.error_count for m in self.connection_metrics.values()) / 
                    max(1, sum(m.query_count for m in self.connection_metrics.values()))
                )
            },
            'recommendations': await self._generate_recommendations(stats)
        }
    
    async def _generate_recommendations(self, stats: ConnectionPoolStats) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if stats.pool_utilization > 0.85:
            recommendations.append("连接池利用率过高，建议增加pool_size")
        elif stats.pool_utilization < 0.3:
            recommendations.append("连接池利用率较低，可以考虑减少pool_size以节省资源")
        
        if stats.avg_checkout_time > 5.0:
            recommendations.append("连接检出时间过长，检查网络延迟或数据库性能")
        
        if stats.connection_errors > 0:
            recommendations.append("存在连接错误，检查数据库可用性和网络稳定性")
        
        if stats.overflow > stats.pool_size * 0.5:
            recommendations.append("溢出连接较多，建议增加max_overflow或优化连接使用")
        
        return recommendations
    
    async def export_performance_data(self, filepath: str, hours: int = 24):
        """导出性能数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围内的数据
        recent_stats = [
            stats for stats in self.pool_stats_history
            # 这里需要在 ConnectionPoolStats 中添加timestamp字段
        ]
        
        performance_data = {
            'export_time': datetime.now().isoformat(),
            'time_range_hours': hours,
            'total_data_points': len(recent_stats),
            'stats_history': [
                {
                    'pool_size': stats.pool_size,
                    'checked_out': stats.checked_out,
                    'pool_utilization': stats.pool_utilization,
                    'avg_checkout_time': stats.avg_checkout_time,
                    'connection_errors': stats.connection_errors
                }
                for stats in recent_stats
            ],
            'summary': {
                'avg_utilization': sum(s.pool_utilization for s in recent_stats) / len(recent_stats) if recent_stats else 0,
                'max_utilization': max((s.pool_utilization for s in recent_stats), default=0),
                'total_errors': sum(s.connection_errors for s in recent_stats),
                'avg_checkout_time': sum(s.avg_checkout_time for s in recent_stats) / len(recent_stats) if recent_stats else 0
            }
        }
        
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"连接池性能数据已导出到: {filepath}")
    
    async def cleanup_stale_connections(self, max_idle_time: int = 3600):
        """清理长时间空闲的连接"""
        current_time = datetime.now()
        stale_connections = []
        
        for conn_id, metrics in self.connection_metrics.items():
            idle_time = (current_time - metrics.last_activity).total_seconds()
            if idle_time > max_idle_time:
                stale_connections.append(conn_id)
        
        # 从指标中移除过期连接
        for conn_id in stale_connections:
            del self.connection_metrics[conn_id]
        
        if stale_connections:
            logger.info(f"清理了 {len(stale_connections)} 个长时间空闲的连接记录")
        
        return len(stale_connections)
    
    async def close_all_engines(self):
        """关闭所有引擎"""
        for name, engine in self.engines.items():
            try:
                await engine.dispose()
                logger.info(f"引擎 {name} 已关闭")
            except Exception as e:
                logger.error(f"关闭引擎 {name} 失败: {e}")
        
        self.engines.clear()
        self.connection_metrics.clear()


# 全局连接池优化器实例
pool_optimizer = ConnectionPoolOptimizer()


@asynccontextmanager
async def get_optimized_session(engine_name: str = 'default'):
    """获取优化的数据库会话"""
    if engine_name not in pool_optimizer.engines:
        raise RuntimeError(f"引擎 {engine_name} 未初始化")
    
    engine = pool_optimizer.engines[engine_name]
    
    # 创建会话
    from sqlalchemy.ext.asyncio import async_sessionmaker
    session_maker = async_sessionmaker(
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=True,
        autocommit=False,
    )
    
    async with session_maker() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_optimized_database(database_url: str = None, engine_name: str = 'default'):
    """初始化优化的数据库连接"""
    if not database_url:
        # 使用SQLite作为默认数据库
        database_url = "sqlite+aiosqlite:///./quant_dev.db"
    
    # 创建优化的引擎
    engine = pool_optimizer.create_optimized_engine(database_url, engine_name)
    
    # 创建表
    from app.core.database import Base
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info(f"优化数据库 {engine_name} 初始化完成")
    return engine


async def cleanup_optimized_database():
    """清理优化的数据库连接"""
    await pool_optimizer.close_all_engines()
    logger.info("优化数据库连接已清理")


if __name__ == "__main__":
    async def test_pool_optimizer():
        """测试连接池优化器"""
        print("测试连接池优化器...")
        
        # 初始化数据库
        await init_optimized_database()
        
        # 获取性能报告
        report = await pool_optimizer.get_performance_report()
        print("性能报告:")
        print(f"  引擎: {report['engine_name']}")
        print(f"  运行时间: {report['uptime_seconds']:.2f}秒")
        print(f"  健康状态: {report['health_check']['status']}")
        
        if report['connection_stats']:
            stats = report['connection_stats']
            print(f"  连接池大小: {stats.pool_size}")
            print(f"  已使用连接: {stats.checked_out}")
            print(f"  池利用率: {stats.pool_utilization:.2%}")
        
        # 清理
        await cleanup_optimized_database()
        print("测试完成")
    
    asyncio.run(test_pool_optimizer())
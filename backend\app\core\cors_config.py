"""
优化的CORS配置模块
解决跨域请求问题，提供灵活的配置选项
"""

import os
import logging
from typing import List, Union
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import get_settings

logger = logging.getLogger(__name__)

settings = get_settings()


def get_cors_origins() -> List[str]:
    """获取CORS允许的源列表"""
    # 从环境变量获取
    env_origins = os.getenv("CORS_ORIGINS")
    if env_origins:
        if env_origins == "*":
            return ["*"]
        return [origin.strip() for origin in env_origins.split(",")]
    
    # 从配置文件获取
    config_origins = getattr(settings, "BACKEND_CORS_ORIGINS", [])
    if config_origins:
        return [str(origin) for origin in config_origins]
    
    # 默认开发环境配置 - 包含WebSocket源
    default_origins = [
        "http://localhost:5173",  # Vite dev server
        "http://localhost:5174",  # Alternative Vite port
        "http://localhost:3000",  # Alternative port
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:3000",
        "http://localhost:8080",  # Vue CLI dev server
        "http://127.0.0.1:8080",
        "ws://localhost:5173",    # WebSocket源
        "ws://localhost:5174",
        "ws://127.0.0.1:5173",
        "ws://127.0.0.1:5174",
        "wss://localhost:5173",   # Secure WebSocket
        "wss://localhost:5174",
        "wss://127.0.0.1:5173",
        "wss://127.0.0.1:5174",
    ]
    
    return default_origins


def get_cors_methods() -> List[str]:
    """获取允许的HTTP方法"""
    return [
        "GET",
        "POST", 
        "PUT",
        "DELETE",
        "PATCH",
        "OPTIONS",
        "HEAD"
    ]


def get_cors_headers() -> List[str]:
    """获取允许的请求头"""
    return [
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-API-Key",
        "X-User-ID",
        "Cache-Control",
        "Pragma",
        "Origin",
        "Referer",
        "User-Agent",
        "Sec-WebSocket-Key",
        "Sec-WebSocket-Version",
        "Sec-WebSocket-Protocol",
        "Sec-WebSocket-Extensions",
        "Upgrade",
        "Connection"
    ]


def configure_cors(app: FastAPI) -> None:
    """配置CORS中间件"""
    environment = os.getenv("ENVIRONMENT", "development")
    origins = get_cors_origins()
    
    # 开发环境更宽松的配置
    if environment == "development":
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # 开发环境允许所有来源
            allow_credentials=True,
            allow_methods=["*"],  # 允许所有HTTP方法
            allow_headers=["*"],  # 允许所有请求头
            expose_headers=[
                "X-Total-Count",
                "X-Page",
                "X-Page-Size", 
                "X-Rate-Limit-Remaining",
                "X-Rate-Limit-Reset"
            ],
            max_age=86400,  # 预检请求缓存时间（秒）
        )
        logger.info("✅ CORS配置完成 (开发模式) - 允许所有来源")
    
    # 生产环境严格配置
    else:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=origins,
            allow_credentials=True,
            allow_methods=get_cors_methods(),
            allow_headers=get_cors_headers(),
            expose_headers=[
                "X-Total-Count",
                "X-Page", 
                "X-Page-Size",
                "X-Rate-Limit-Remaining",
                "X-Rate-Limit-Reset"
            ],
            max_age=3600,  # 生产环境较短的缓存时间
        )
        logger.info(f"✅ CORS配置完成 (生产模式) - 允许来源: {origins}")


def add_cors_headers_manually(app: FastAPI) -> None:
    """手动添加CORS处理器（备用方案）"""
    
    @app.options("/{full_path:path}")
    async def options_handler(full_path: str):
        """处理所有OPTIONS预检请求"""
        from fastapi import Response
        
        response = Response()
        origins = get_cors_origins()
        
        if origins == ["*"]:
            response.headers["Access-Control-Allow-Origin"] = "*"
        else:
            # 这里应该根据请求的Origin头来设置
            response.headers["Access-Control-Allow-Origin"] = origins[0]
            
        response.headers["Access-Control-Allow-Methods"] = ", ".join(get_cors_methods())
        response.headers["Access-Control-Allow-Headers"] = ", ".join(get_cors_headers())
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"
        
        return response
    
    logger.info("✅ 手动CORS处理器已添加")


def debug_cors_config() -> dict:
    """调试CORS配置，返回当前配置信息"""
    return {
        "environment": os.getenv("ENVIRONMENT", "development"),
        "origins": get_cors_origins(),
        "methods": get_cors_methods(),
        "headers": get_cors_headers(),
        "env_cors_origins": os.getenv("CORS_ORIGINS"),
        "config_cors_origins": getattr(settings, "BACKEND_CORS_ORIGINS", [])
    }


# 导出配置函数
__all__ = [
    "configure_cors",
    "add_cors_headers_manually", 
    "debug_cors_config",
    "get_cors_origins",
    "get_cors_methods",
    "get_cors_headers"
]
"""
基于pythonstock的数据存储优化模块
实现分层数据存储、gzip压缩、智能缓存等功能
"""

import asyncio
import gzip
import json
import pickle
import time
from collections import defaultdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import redis.asyncio as redis
from loguru import logger

from app.core.config import settings
from app.core.compression_optimizer import compression_optimizer, CompressionAlgorithm


class StorageConfig:
    """数据存储配置"""
    
    # 数据目录
    DATA_ROOT = Path("data")
    DAILY_CACHE_DIR = DATA_ROOT / "daily_cache"
    HISTORICAL_DIR = DATA_ROOT / "historical"
    REALTIME_DIR = DATA_ROOT / "realtime"
    
    # 缓存TTL配置 (学习pythonstock的3天策略)
    REALTIME_TTL = 30           # 实时数据30秒
    DAILY_CACHE_DAYS = 3        # 日内缓存3天
    HISTORICAL_YEARS = 10       # 历史数据10年
    
    # 压缩配置
    COMPRESSION_LEVEL = 6       # gzip压缩级别
    PICKLE_PROTOCOL = pickle.HIGHEST_PROTOCOL
    
    # API限制配置
    API_RATE_LIMITS = {
        'tushare': {'calls': 200, 'period': 60},
        'akshare': {'calls': 100, 'period': 60},
        'baostock': {'calls': 300, 'period': 60}
    }
    
    @classmethod
    def ensure_directories(cls):
        """确保数据目录存在"""
        for directory in [cls.DAILY_CACHE_DIR, cls.HISTORICAL_DIR, cls.REALTIME_DIR]:
            directory.mkdir(parents=True, exist_ok=True)


class HotDataManager:
    """热数据管理器 - 实时数据缓存 (内存 + Redis)"""
    
    def __init__(self):
        self.memory_cache: Dict[str, Dict] = {}
        self.redis_client: Optional[redis.Redis] = None
        self.max_memory_items = 1000
        
    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                password=settings.REDIS_PASSWORD,
                decode_responses=False  # 保持二进制模式用于pickle
            )
            await self.redis_client.ping()
            logger.info("热数据管理器初始化成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    async def store_realtime_data(self, symbol: str, data: Dict[str, Any]) -> bool:
        """存储实时数据"""
        try:
            cache_key = f"realtime:{symbol}"
            timestamp = time.time()
            
            # 内存缓存 (最快访问)
            cache_item = {
                'data': data,
                'timestamp': timestamp
            }
            
            # 限制内存缓存大小
            if len(self.memory_cache) >= self.max_memory_items:
                # 删除最旧的条目
                oldest_key = min(self.memory_cache.keys(), 
                               key=lambda k: self.memory_cache[k]['timestamp'])
                del self.memory_cache[oldest_key]
            
            self.memory_cache[cache_key] = cache_item
            
            # Redis缓存 (跨进程共享)
            if self.redis_client:
                serialized_data = json.dumps(data, default=str)
                await self.redis_client.setex(
                    cache_key, 
                    StorageConfig.REALTIME_TTL, 
                    serialized_data
                )
            
            return True
            
        except Exception as e:
            logger.error(f"存储实时数据失败 {symbol}: {e}")
            return False
    
    async def get_realtime_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时数据"""
        cache_key = f"realtime:{symbol}"
        
        # 先检查内存缓存
        if cache_key in self.memory_cache:
            cache_item = self.memory_cache[cache_key]
            # 检查是否过期
            if time.time() - cache_item['timestamp'] < StorageConfig.REALTIME_TTL:
                return cache_item['data']
            else:
                del self.memory_cache[cache_key]
        
        # 检查Redis缓存
        if self.redis_client:
            try:
                cached_data = await self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
            except Exception as e:
                logger.error(f"从Redis获取数据失败 {symbol}: {e}")
        
        return None


class WarmDataManager:
    """温数据管理器 - 日内数据缓存 (学习pythonstock的gzip压缩策略)"""
    
    def __init__(self):
        StorageConfig.ensure_directories()
        
    def _get_cache_filepath(self, symbol: str, date: str) -> Path:
        """获取缓存文件路径"""
        return StorageConfig.DAILY_CACHE_DIR / f"{symbol}_{date}.pkl.gz"
    
    async def store_daily_data(self, symbol: str, date: str, data: pd.DataFrame) -> bool:
        """存储日内数据 - 使用优化压缩 (pythonstock策略)"""
        try:
            filepath = self._get_cache_filepath(symbol, date)

            # 序列化数据
            serialized_data = pickle.dumps(data, protocol=StorageConfig.PICKLE_PROTOCOL)

            # 使用优化的压缩算法
            compressed_data, profile = await compression_optimizer.compress_data(
                serialized_data,
                data_type='daily_klines'
            )

            # 写入文件
            with open(filepath, 'wb') as f:
                f.write(compressed_data)

            # 记录文件大小和压缩信息
            file_size = filepath.stat().st_size
            compression_ratio = len(compressed_data) / len(serialized_data)

            logger.info(f"已优化压缩存储 {symbol} 日内数据: {filepath} "
                       f"({file_size:,} bytes, 压缩比 {compression_ratio:.3f}, "
                       f"算法 {profile.algorithm.value})")

            return True

        except Exception as e:
            logger.error(f"存储日内数据失败 {symbol} {date}: {e}")
            return False
    
    async def load_daily_data(self, symbol: str, date: str) -> Optional[pd.DataFrame]:
        """加载日内数据"""
        try:
            filepath = self._get_cache_filepath(symbol, date)

            if not filepath.exists():
                return None

            # 检查文件是否过期 (3天策略)
            file_age = datetime.now() - datetime.fromtimestamp(filepath.stat().st_mtime)
            if file_age.days > StorageConfig.DAILY_CACHE_DAYS:
                filepath.unlink()  # 删除过期文件
                logger.info(f"已删除过期缓存文件: {filepath}")
                return None

            # 读取压缩数据
            with open(filepath, 'rb') as f:
                compressed_data = f.read()

            # 尝试不同的解压算法 (向后兼容)
            try:
                # 首先尝试优化的解压
                serialized_data = await compression_optimizer.decompress_data(
                    compressed_data,
                    CompressionAlgorithm.GZIP
                )
                data = pickle.loads(serialized_data)
            except:
                # 回退到传统gzip解压
                try:
                    with gzip.open(filepath, 'rb') as f:
                        data = pickle.load(f)
                except:
                    # 最后尝试直接pickle加载
                    data = pickle.loads(compressed_data)

            logger.debug(f"已加载日内数据: {symbol} {date}")
            return data

        except Exception as e:
            logger.error(f"加载日内数据失败 {symbol} {date}: {e}")
            return None
    
    async def cleanup_expired_cache(self) -> int:
        """清理过期缓存 - pythonstock的3天策略"""
        cleaned_count = 0
        cutoff_time = datetime.now() - timedelta(days=StorageConfig.DAILY_CACHE_DAYS)
        
        try:
            for filepath in StorageConfig.DAILY_CACHE_DIR.glob("*.pkl.gz"):
                file_time = datetime.fromtimestamp(filepath.stat().st_mtime)
                
                if file_time < cutoff_time:
                    filepath.unlink()
                    cleaned_count += 1
                    logger.info(f"已清理过期缓存: {filepath}")
            
            logger.info(f"缓存清理完成，共清理 {cleaned_count} 个文件")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"缓存清理失败: {e}")
            return 0


class ColdDataManager:
    """冷数据管理器 - 历史数据存储 (Parquet格式)"""
    
    def __init__(self):
        StorageConfig.ensure_directories()
    
    def _get_storage_filepath(self, symbol: str, year_month: str) -> Path:
        """获取存储文件路径 - 按年月分片"""
        return StorageConfig.HISTORICAL_DIR / f"{symbol}_{year_month}.parquet"
    
    async def store_historical_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """存储历史数据 - Parquet格式"""
        try:
            if data.empty:
                return False
            
            # 按年月分组存储
            data_groups = data.groupby(data.index.to_period('M'))
            
            for period, group_data in data_groups:
                year_month = period.strftime('%Y%m')
                filepath = self._get_storage_filepath(symbol, year_month)
                
                # 如果文件已存在，合并数据
                if filepath.exists():
                    existing_data = pd.read_parquet(filepath)
                    combined_data = pd.concat([existing_data, group_data]).drop_duplicates()
                    combined_data.sort_index(inplace=True)
                else:
                    combined_data = group_data
                
                # 保存为Parquet格式 (高压缩比 + 快速查询)
                combined_data.to_parquet(filepath, compression='gzip')
                
                file_size = filepath.stat().st_size
                logger.info(f"已存储历史数据: {symbol} {year_month} ({file_size:,} bytes)")
            
            return True
            
        except Exception as e:
            logger.error(f"存储历史数据失败 {symbol}: {e}")
            return False
    
    async def load_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """加载历史数据 - 支持时间范围查询"""
        try:
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            # 确定需要加载的文件
            files_to_load = self._get_files_in_range(symbol, start_dt, end_dt)
            
            dfs = []
            for filepath in files_to_load:
                if filepath.exists():
                    df = pd.read_parquet(filepath)
                    # 过滤时间范围
                    df = df[(df.index >= start_dt) & (df.index <= end_dt)]
                    if not df.empty:
                        dfs.append(df)
            
            if dfs:
                result = pd.concat(dfs).sort_index()
                logger.debug(f"已加载历史数据: {symbol} {start_date} to {end_date} ({len(result)} rows)")
                return result
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"加载历史数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def _get_files_in_range(self, symbol: str, start_date: pd.Timestamp, end_date: pd.Timestamp) -> List[Path]:
        """获取时间范围内的文件列表"""
        files = []
        
        # 生成需要的年月列表
        current_date = start_date.replace(day=1)
        while current_date <= end_date:
            year_month = current_date.strftime('%Y%m')
            filepath = self._get_storage_filepath(symbol, year_month)
            files.append(filepath)
            
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        return files


class APIRateLimiter:
    """API调用频率控制 - 防止被封"""
    
    def __init__(self):
        self.call_history: Dict[str, List[float]] = defaultdict(list)
        self.limits = StorageConfig.API_RATE_LIMITS
    
    async def check_rate_limit(self, api_name: str) -> bool:
        """检查是否可以调用API"""
        now = time.time()
        limit_config = self.limits.get(api_name, {'calls': 60, 'period': 60})
        
        # 清理过期的调用记录
        cutoff_time = now - limit_config['period']
        self.call_history[api_name] = [
            call_time for call_time in self.call_history[api_name]
            if call_time > cutoff_time
        ]
        
        # 检查是否超过限制
        if len(self.call_history[api_name]) >= limit_config['calls']:
            return False
        
        # 记录本次调用
        self.call_history[api_name].append(now)
        return True
    
    async def wait_for_rate_limit(self, api_name: str):
        """等待直到可以调用API"""
        while not await self.check_rate_limit(api_name):
            await asyncio.sleep(1)
        
    def get_remaining_calls(self, api_name: str) -> int:
        """获取剩余可调用次数"""
        limit_config = self.limits.get(api_name, {'calls': 60, 'period': 60})
        current_calls = len(self.call_history[api_name])
        return max(0, limit_config['calls'] - current_calls)


# 全局实例
hot_data_manager = HotDataManager()
warm_data_manager = WarmDataManager()
cold_data_manager = ColdDataManager()
api_rate_limiter = APIRateLimiter()


async def initialize_storage_system():
    """初始化存储系统"""
    try:
        StorageConfig.ensure_directories()
        await hot_data_manager.initialize()
        logger.info("数据存储系统初始化完成")
    except Exception as e:
        logger.error(f"存储系统初始化失败: {e}")


async def cleanup_storage_system():
    """清理存储系统"""
    try:
        # 清理过期缓存
        cleaned_count = await warm_data_manager.cleanup_expired_cache()
        logger.info(f"存储系统清理完成，清理了 {cleaned_count} 个过期文件")
    except Exception as e:
        logger.error(f"存储系统清理失败: {e}")


# 导出主要组件
__all__ = [
    'HotDataManager',
    'WarmDataManager', 
    'ColdDataManager',
    'APIRateLimiter',
    'StorageConfig',
    'hot_data_manager',
    'warm_data_manager',
    'cold_data_manager',
    'api_rate_limiter',
    'initialize_storage_system',
    'cleanup_storage_system'
]

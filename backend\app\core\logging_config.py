"""
统一日志配置模块
"""

import json
import logging
import logging.config
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import contextvars
from loguru import logger
from pythonjsonlogger import jsonlogger

from .config import settings

# 上下文变量用于跟踪请求
request_id_var: contextvars.ContextVar[str] = contextvars.ContextVar('request_id', default='')
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar('user_id', default='')
session_id_var: contextvars.ContextVar[str] = contextvars.ContextVar('session_id', default='')


class StructuredFormatter(jsonlogger.JsonFormatter):
    """结构化日志格式化器"""

    def add_fields(
        self,
        log_record: Dict[str, Any],
        record: logging.LogRecord,
        message_dict: Dict[str, Any],
    ):
        super().add_fields(log_record, record, message_dict)

        # 添加标准字段
        log_record["timestamp"] = datetime.utcnow().isoformat()
        log_record["level"] = record.levelname
        log_record["logger"] = record.name
        log_record["module"] = record.module
        log_record["function"] = record.funcName
        log_record["line"] = record.lineno

        # 添加应用信息
        log_record["service"] = "quant-platform-backend"
        log_record["version"] = getattr(settings, "VERSION", "1.0.0")
        log_record["environment"] = getattr(settings, "ENVIRONMENT", "development")

        # 添加进程信息
        log_record["process_id"] = os.getpid()
        log_record["thread_id"] = record.thread

        # 添加上下文信息
        log_record["request_id"] = request_id_var.get()
        log_record["user_id"] = user_id_var.get()
        log_record["session_id"] = session_id_var.get()

        # 处理异常信息
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        # 添加业务相关字段
        if hasattr(record, 'business_event'):
            log_record["business_event"] = record.business_event
        if hasattr(record, 'metric_name'):
            log_record["metric_name"] = record.metric_name
        if hasattr(record, 'metric_value'):
            log_record["metric_value"] = record.metric_value
        if hasattr(record, 'trace_id'):
            log_record["trace_id"] = record.trace_id


class TradingLogFilter(logging.Filter):
    """交易相关日志过滤器"""

    def filter(self, record: logging.LogRecord) -> bool:
        # 为交易相关日志添加特殊标记
        if hasattr(record, "order_id"):
            record.log_type = "trading"
        elif hasattr(record, "symbol"):
            record.log_type = "market_data"
        elif hasattr(record, "strategy_id"):
            record.log_type = "strategy"
        else:
            record.log_type = "system"

        return True


class LoggingConfig:
    """日志配置管理器"""

    def __init__(self):
        self.log_dir = Path(getattr(settings, "LOG_DIR", "logs"))
        self.log_level = getattr(settings, "LOG_LEVEL", "INFO")
        self.enable_json_logs = getattr(settings, "ENABLE_JSON_LOGS", True)
        self.enable_file_logs = getattr(settings, "ENABLE_FILE_LOGS", True)
        self.max_log_size = getattr(settings, "MAX_LOG_SIZE", "100MB")
        self.backup_count = getattr(settings, "LOG_BACKUP_COUNT", 10)

        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)

    def setup_logging(self):
        """设置日志系统"""
        # 清除现有配置
        logging.getLogger().handlers.clear()

        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)

        # 添加处理器
        self._add_console_handler(root_logger)

        if self.enable_file_logs:
            self._add_file_handlers(root_logger)

        # 配置第三方库日志级别
        self._configure_third_party_loggers()

        # 设置loguru
        self._setup_loguru()

        logger.info(
            "Logging system initialized",
            extra={
                "log_level": self.log_level,
                "log_dir": str(self.log_dir),
                "json_logs": self.enable_json_logs,
                "file_logs": self.enable_file_logs,
            },
        )

    def _add_console_handler(self, root_logger: logging.Logger):
        """添加控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)

        if self.enable_json_logs:
            formatter = StructuredFormatter(
                "%(timestamp)s %(level)s %(logger)s %(message)s"
            )
        else:
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )

        console_handler.setFormatter(formatter)
        console_handler.addFilter(TradingLogFilter())
        root_logger.addHandler(console_handler)

    def _add_file_handlers(self, root_logger: logging.Logger):
        """添加文件处理器"""
        from logging.handlers import RotatingFileHandler

        # 应用主日志
        app_handler = RotatingFileHandler(
            self.log_dir / "app.log",
            maxBytes=self._parse_size(self.max_log_size),
            backupCount=self.backup_count,
            encoding="utf-8",
        )
        app_handler.setLevel(self.log_level)

        # 交易日志
        trading_handler = RotatingFileHandler(
            self.log_dir / "trading.log",
            maxBytes=self._parse_size(self.max_log_size),
            backupCount=self.backup_count,
            encoding="utf-8",
        )
        trading_handler.setLevel(self.log_level)
        trading_handler.addFilter(
            lambda record: getattr(record, "log_type", "") == "trading"
        )

        # 错误日志
        error_handler = RotatingFileHandler(
            self.log_dir / "error.log",
            maxBytes=self._parse_size(self.max_log_size),
            backupCount=self.backup_count,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.ERROR)

        # 设置格式化器
        if self.enable_json_logs:
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
            )

        for handler in [app_handler, trading_handler, error_handler]:
            handler.setFormatter(formatter)
            handler.addFilter(TradingLogFilter())
            root_logger.addHandler(handler)

    def _configure_third_party_loggers(self):
        """配置第三方库日志级别"""
        third_party_loggers = {
            "uvicorn": "INFO",
            "uvicorn.access": "WARNING",
            "fastapi": "INFO",
            "sqlalchemy": "WARNING",
            "sqlalchemy.engine": "WARNING",
            "alembic": "INFO",
            "asyncio": "WARNING",
            "websockets": "INFO",
            "prometheus_client": "WARNING",
        }

        for logger_name, level in third_party_loggers.items():
            logging.getLogger(logger_name).setLevel(level)

    def _setup_loguru(self):
        """设置loguru日志"""
        # 移除默认处理器
        logger.remove()

        # 控制台输出
        logger.add(
            sys.stdout,
            level=self.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True,
        )

        if self.enable_file_logs:
            # 文件输出
            logger.add(
                self.log_dir / "loguru.log",
                level=self.log_level,
                format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
                rotation=self.max_log_size,
                retention=self.backup_count,
                compression="zip",
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
            )

            # JSON格式日志
            if self.enable_json_logs:
                logger.add(
                    self.log_dir / "structured.log",
                    level=self.log_level,
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message}",
                    rotation=self.max_log_size,
                    retention=self.backup_count,
                    serialize=True,
                    encoding="utf-8",
                )

    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith("KB"):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith("MB"):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith("GB"):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)


# 全局日志配置实例
logging_config = LoggingConfig()


def setup_logging():
    """设置日志系统"""
    logging_config.setup_logging()


def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


def log_trading_event(event_type: str, data: Dict[str, Any], level: str = "INFO"):
    """记录交易事件"""
    logger_instance = get_logger("trading")

    log_data = {
        "event_type": event_type,
        "timestamp": datetime.utcnow().isoformat(),
        **data,
    }

    getattr(logger_instance, level.lower())(
        f"Trading event: {event_type}", extra=log_data
    )


def log_market_data_event(symbol: str, event_type: str, data: Dict[str, Any]):
    """记录行情数据事件"""
    logger_instance = get_logger("market_data")

    log_data = {
        "symbol": symbol,
        "event_type": event_type,
        "timestamp": datetime.utcnow().isoformat(),
        **data,
    }

    logger_instance.info(f"Market data event: {symbol} - {event_type}", extra=log_data)


def log_strategy_event(strategy_id: str, event_type: str, data: Dict[str, Any]):
    """记录策略事件"""
    logger_instance = get_logger("strategy")

    log_data = {
        "strategy_id": strategy_id,
        "event_type": event_type,
        "timestamp": datetime.utcnow().isoformat(),
        **data,
    }

    logger_instance.info(
        f"Strategy event: {strategy_id} - {event_type}", extra=log_data
    )


def log_system_event(
    component: str, event_type: str, data: Dict[str, Any], level: str = "INFO"
):
    """记录系统事件"""
    logger_instance = get_logger("system")

    log_data = {
        "component": component,
        "event_type": event_type,
        "timestamp": datetime.utcnow().isoformat(),
        **data,
    }

    getattr(logger_instance, level.lower())(
        f"System event: {component} - {event_type}", extra=log_data
    )


def set_request_context(request_id: str, user_id: str = "", session_id: str = ""):
    """设置请求上下文"""
    request_id_var.set(request_id)
    if user_id:
        user_id_var.set(user_id)
    if session_id:
        session_id_var.set(session_id)


def get_request_context() -> Dict[str, str]:
    """获取当前请求上下文"""
    return {
        "request_id": request_id_var.get(),
        "user_id": user_id_var.get(),
        "session_id": session_id_var.get(),
    }


def clear_request_context():
    """清除请求上下文"""
    request_id_var.set("")
    user_id_var.set("")
    session_id_var.set("")


def log_business_metric(
    metric_name: str,
    value: float,
    tags: Optional[Dict[str, str]] = None,
    level: str = "INFO"
):
    """记录业务指标"""
    logger_instance = get_logger("metrics")
    
    log_data = {
        "metric_name": metric_name,
        "metric_value": value,
        "tags": tags or {},
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    getattr(logger_instance, level.lower())(
        f"Business metric: {metric_name} = {value}", extra=log_data
    )


def log_security_event(
    event_type: str,
    severity: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None
):
    """记录安全事件"""
    logger_instance = get_logger("security")
    
    log_data = {
        "event_type": event_type,
        "severity": severity,
        "user_id": user_id or user_id_var.get(),
        "details": details,
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    logger_instance.warning(f"Security event: {event_type}", extra=log_data)


def log_performance_metric(
    operation: str,
    duration: float,
    metadata: Optional[Dict[str, Any]] = None
):
    """记录性能指标"""
    logger_instance = get_logger("performance")
    
    log_data = {
        "operation": operation,
        "duration": duration,
        "metadata": metadata or {},
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    # 根据耗时选择日志级别
    level = "warning" if duration > 5.0 else "info"
    getattr(logger_instance, level)(
        f"Performance: {operation} took {duration:.3f}s", extra=log_data
    )


def log_audit_event(
    action: str,
    resource: str,
    user_id: Optional[str] = None,
    result: str = "success",
    details: Optional[Dict[str, Any]] = None
):
    """记录审计事件"""
    logger_instance = get_logger("audit")
    
    log_data = {
        "action": action,
        "resource": resource,
        "user_id": user_id or user_id_var.get(),
        "result": result,
        "details": details or {},
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    logger_instance.info(f"Audit: {action} on {resource} - {result}", extra=log_data)


class ContextualLogger:
    """上下文感知的日志器"""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.name = name
    
    def _log_with_context(self, level: str, message: str, **kwargs):
        """带上下文的日志记录"""
        context = get_request_context()
        extra_data = {**context, **kwargs}
        getattr(self.logger, level)(message, extra=extra_data)
    
    def debug(self, message: str, **kwargs):
        self._log_with_context("debug", message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log_with_context("info", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log_with_context("warning", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log_with_context("error", message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log_with_context("critical", message, **kwargs)


def get_contextual_logger(name: str) -> ContextualLogger:
    """获取上下文感知的日志器"""
    return ContextualLogger(name)

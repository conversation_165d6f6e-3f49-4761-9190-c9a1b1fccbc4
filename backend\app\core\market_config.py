"""
市场数据配置
统一管理数据源、缓存、定时任务等配置
"""

import os
from typing import Dict, List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from pathlib import Path

class MarketDataConfig(BaseSettings):
    """市场数据配置"""
    
    # Tushare配置
    TUSHARE_TOKEN: str = Field(
        default="f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400",
        description="Tushare API Token"
    )
    TUSHARE_BASE_URL: str = Field(
        default="http://api.tushare.pro",
        description="Tushare API基础URL"
    )
    TUSHARE_MAX_CALLS_PER_MINUTE: int = Field(
        default=200,
        description="Tushare每分钟最大调用次数"
    )
    
    # AKShare配置
    AKSHARE_ENABLED: bool = Field(
        default=True,
        description="是否启用AKShare作为备用数据源"
    )
    
    # 数据源配置
    USE_REAL_DATA: bool = Field(
        default=True,
        description="是否使用真实数据源"
    )
    PRIMARY_DATA_SOURCE: str = Field(
        default="tushare",
        description="主要数据源: tushare, akshare"
    )
    FALLBACK_DATA_SOURCE: str = Field(
        default="akshare",
        description="备用数据源"
    )
    
    # 缓存配置
    CACHE_ENABLED: bool = Field(
        default=True,
        description="是否启用缓存"
    )
    CACHE_TTL_REALTIME: int = Field(
        default=30,
        description="实时数据缓存时间（秒）"
    )
    CACHE_TTL_DAILY: int = Field(
        default=300,
        description="日线数据缓存时间（秒）"
    )
    CACHE_TTL_BASIC: int = Field(
        default=3600,
        description="基础信息缓存时间（秒）"
    )
    CACHE_MAX_SIZE: int = Field(
        default=1000,
        description="缓存最大条目数"
    )
    CACHE_CLEANUP_DAYS: int = Field(
        default=7,
        description="缓存清理保留天数"
    )
    
    # 数据抓取配置
    CRAWL_ENABLED: bool = Field(
        default=True,
        description="是否启用数据抓取"
    )
    CRAWL_BATCH_SIZE: int = Field(
        default=50,
        description="数据抓取批次大小"
    )
    CRAWL_DELAY_SECONDS: int = Field(
        default=2,
        description="批次间延迟时间（秒）"
    )
    CRAWL_RETRY_TIMES: int = Field(
        default=3,
        description="失败重试次数"
    )
    CRAWL_TIMEOUT_SECONDS: int = Field(
        default=30,
        description="单次请求超时时间（秒）"
    )
    CRAWL_HISTORY_DAYS: int = Field(
        default=300,
        description="抓取历史数据天数"
    )
    
    # 定时任务配置
    SCHEDULER_ENABLED: bool = Field(
        default=True,
        description="是否启用定时任务"
    )
    DAILY_CRAWL_HOUR: int = Field(
        default=18,
        description="每日抓取任务执行小时"
    )
    DAILY_CRAWL_MINUTE: int = Field(
        default=0,
        description="每日抓取任务执行分钟"
    )
    CLEANUP_INTERVAL_HOURS: int = Field(
        default=1,
        description="清理任务执行间隔（小时）"
    )
    MAINTENANCE_HOUR: int = Field(
        default=2,
        description="维护任务执行小时"
    )
    MAINTENANCE_MINUTE: int = Field(
        default=0,
        description="维护任务执行分钟"
    )
    
    # 数据存储配置
    DATA_DIR: str = Field(
        default="data",
        description="数据存储目录"
    )
    CACHE_DIR: str = Field(
        default="data/cache",
        description="缓存目录"
    )
    PROCESSED_DIR: str = Field(
        default="data/processed",
        description="处理后数据目录"
    )
    ANALYSIS_DIR: str = Field(
        default="data/analysis",
        description="分析结果目录"
    )
    LOG_DIR: str = Field(
        default="logs",
        description="日志目录"
    )
    
    # 数据处理配置
    ANALYSIS_ENABLED: bool = Field(
        default=True,
        description="是否启用数据分析"
    )
    TECHNICAL_INDICATORS: List[str] = Field(
        default=["MA", "RSI", "MACD", "BOLL", "KDJ"],
        description="启用的技术指标"
    )
    MA_PERIODS: List[int] = Field(
        default=[5, 10, 20, 60],
        description="移动平均线周期"
    )
    RSI_PERIOD: int = Field(
        default=14,
        description="RSI计算周期"
    )
    MACD_FAST: int = Field(
        default=12,
        description="MACD快线周期"
    )
    MACD_SLOW: int = Field(
        default=26,
        description="MACD慢线周期"
    )
    MACD_SIGNAL: int = Field(
        default=9,
        description="MACD信号线周期"
    )
    
    # API配置
    API_RATE_LIMIT: int = Field(
        default=100,
        description="API每分钟请求限制"
    )
    API_TIMEOUT: int = Field(
        default=30,
        description="API请求超时时间（秒）"
    )
    
    # WebSocket配置
    WS_ENABLED: bool = Field(
        default=True,
        description="是否启用WebSocket"
    )
    WS_HEARTBEAT_INTERVAL: int = Field(
        default=30,
        description="WebSocket心跳间隔（秒）"
    )
    WS_RECONNECT_INTERVAL: int = Field(
        default=5,
        description="WebSocket重连间隔（秒）"
    )
    WS_MAX_RECONNECT_ATTEMPTS: int = Field(
        default=10,
        description="WebSocket最大重连次数"
    )
    
    # 监控配置
    MONITORING_ENABLED: bool = Field(
        default=True,
        description="是否启用监控"
    )
    HEALTH_CHECK_INTERVAL: int = Field(
        default=60,
        description="健康检查间隔（秒）"
    )
    ALERT_EMAIL_ENABLED: bool = Field(
        default=False,
        description="是否启用邮件告警"
    )
    ALERT_EMAIL_RECIPIENTS: List[str] = Field(
        default=[],
        description="告警邮件接收者"
    )
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(
        default=10,
        description="最大并发请求数"
    )
    CONNECTION_POOL_SIZE: int = Field(
        default=20,
        description="连接池大小"
    )
    REQUEST_RETRY_TIMES: int = Field(
        default=3,
        description="请求重试次数"
    )
    
    class Config:
        env_file = ".env"
        env_prefix = "MARKET_"
        case_sensitive = True
    
    def get_data_paths(self) -> Dict[str, Path]:
        """获取数据路径"""
        base_dir = Path(self.DATA_DIR)
        return {
            "base": base_dir,
            "cache": base_dir / "cache",
            "processed": base_dir / "processed",
            "analysis": base_dir / "analysis",
            "tushare_cache": base_dir / "cache" / "tushare",
            "akshare_cache": base_dir / "cache" / "akshare",
            "logs": Path(self.LOG_DIR)
        }
    
    def create_directories(self):
        """创建必要的目录"""
        paths = self.get_data_paths()
        for path in paths.values():
            path.mkdir(parents=True, exist_ok=True)
    
    def get_tushare_config(self) -> Dict:
        """获取Tushare配置"""
        return {
            "token": self.TUSHARE_TOKEN,
            "base_url": self.TUSHARE_BASE_URL,
            "max_calls_per_minute": self.TUSHARE_MAX_CALLS_PER_MINUTE
        }
    
    def get_cache_config(self) -> Dict:
        """获取缓存配置"""
        return {
            "enabled": self.CACHE_ENABLED,
            "ttl_realtime": self.CACHE_TTL_REALTIME,
            "ttl_daily": self.CACHE_TTL_DAILY,
            "ttl_basic": self.CACHE_TTL_BASIC,
            "max_size": self.CACHE_MAX_SIZE,
            "cleanup_days": self.CACHE_CLEANUP_DAYS
        }
    
    def get_scheduler_config(self) -> Dict:
        """获取调度器配置"""
        return {
            "enabled": self.SCHEDULER_ENABLED,
            "daily_crawl_time": f"{self.DAILY_CRAWL_HOUR:02d}:{self.DAILY_CRAWL_MINUTE:02d}",
            "cleanup_interval": self.CLEANUP_INTERVAL_HOURS,
            "maintenance_time": f"{self.MAINTENANCE_HOUR:02d}:{self.MAINTENANCE_MINUTE:02d}"
        }
    
    def is_trading_time(self) -> bool:
        """判断是否为交易时间"""
        from datetime import datetime, time
        
        now = datetime.now().time()
        
        # 上午交易时间: 9:30-11:30
        morning_start = time(9, 30)
        morning_end = time(11, 30)
        
        # 下午交易时间: 13:00-15:00
        afternoon_start = time(13, 0)
        afternoon_end = time(15, 0)
        
        return (morning_start <= now <= morning_end) or (afternoon_start <= now <= afternoon_end)

# 创建全局配置实例
market_config = MarketDataConfig()

# 创建必要的目录
market_config.create_directories()

# 导出配置
def get_market_config() -> MarketDataConfig:
    """获取市场数据配置"""
    return market_config

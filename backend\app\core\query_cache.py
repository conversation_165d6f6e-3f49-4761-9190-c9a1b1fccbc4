"""
查询缓存策略实现
支持Redis和内存缓存，针对量化交易场景优化
"""

import asyncio
import json
import hashlib
import logging
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps
from contextlib import asynccontextmanager

from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings


logger = logging.getLogger(__name__)


class QueryCacheConfig:
    """查询缓存配置"""
    
    # Redis配置
    REDIS_URL = getattr(settings, 'REDIS_URL', 'redis://localhost:6379/0')
    REDIS_PASSWORD = getattr(settings, 'REDIS_PASSWORD', None)
    
    # 缓存过期时间（秒）
    DEFAULT_EXPIRE = 300  # 5分钟
    MARKET_DATA_EXPIRE = 30  # 市场数据30秒
    KLINE_DATA_EXPIRE = 300  # K线数据5分钟
    USER_DATA_EXPIRE = 600  # 用户数据10分钟
    STRATEGY_DATA_EXPIRE = 900  # 策略数据15分钟
    
    # 缓存键前缀
    CACHE_PREFIX = "quant_platform:"
    QUERY_PREFIX = f"{CACHE_PREFIX}query:"
    
    # 内存缓存大小限制
    MEMORY_CACHE_SIZE = 1000
    
    # 缓存策略
    CACHE_STRATEGIES = {
        'market_data': {
            'expire': MARKET_DATA_EXPIRE,
            'use_redis': True,
            'use_memory': True,
            'compression': False,
        },
        'kline_data': {
            'expire': KLINE_DATA_EXPIRE,
            'use_redis': True,
            'use_memory': True,
            'compression': True,
        },
        'user_data': {
            'expire': USER_DATA_EXPIRE,
            'use_redis': True,
            'use_memory': False,
            'compression': False,
        },
        'strategy_data': {
            'expire': STRATEGY_DATA_EXPIRE,
            'use_redis': True,
            'use_memory': True,
            'compression': True,
        },
        'default': {
            'expire': DEFAULT_EXPIRE,
            'use_redis': True,
            'use_memory': True,
            'compression': False,
        }
    }


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, Dict] = {}
        self.max_size = max_size
        self.access_order: List[str] = []
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            cache_item = self.cache[key]
            
            # 检查是否过期
            if cache_item['expire_at'] > datetime.now():
                # 更新访问顺序
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
                
                return cache_item['value']
            else:
                # 删除过期项
                self.delete(key)
        
        return None
    
    def set(self, key: str, value: Any, expire: int = 300) -> None:
        """设置缓存值"""
        # 如果缓存已满，删除最久未访问的项
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_oldest()
        
        expire_at = datetime.now() + timedelta(seconds=expire)
        self.cache[key] = {
            'value': value,
            'expire_at': expire_at,
            'created_at': datetime.now()
        }
        
        # 更新访问顺序
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
    
    def delete(self, key: str) -> None:
        """删除缓存项"""
        if key in self.cache:
            del self.cache[key]
        if key in self.access_order:
            self.access_order.remove(key)
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
    
    def _evict_oldest(self) -> None:
        """删除最久未访问的项"""
        if self.access_order:
            oldest_key = self.access_order[0]
            self.delete(oldest_key)
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        now = datetime.now()
        expired_count = sum(
            1 for item in self.cache.values() 
            if item['expire_at'] <= now
        )
        
        return {
            'total_items': len(self.cache),
            'expired_items': expired_count,
            'valid_items': len(self.cache) - expired_count,
            'max_size': self.max_size,
            'usage_ratio': len(self.cache) / self.max_size
        }


class QueryCacheManager:
    """查询缓存管理器"""
    
    def __init__(self):
        self.redis_client: Optional[Redis] = None
        self.memory_cache = MemoryCache(QueryCacheConfig.MEMORY_CACHE_SIZE)
        self.hit_count = 0
        self.miss_count = 0
        self.error_count = 0
    
    async def initialize(self) -> None:
        """初始化缓存系统"""
        try:
            # 初始化Redis连接
            self.redis_client = Redis.from_url(
                QueryCacheConfig.REDIS_URL,
                password=QueryCacheConfig.REDIS_PASSWORD,
                decode_responses=False,  # 为了支持pickle序列化
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            logger.info("Redis缓存初始化成功")
            
        except Exception as e:
            logger.warning(f"Redis缓存初始化失败，将仅使用内存缓存: {e}")
            self.redis_client = None
    
    async def close(self) -> None:
        """关闭缓存连接"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis缓存连接已关闭")
    
    def _generate_cache_key(self, query_type: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 创建参数的哈希值
        params_str = json.dumps(params, sort_keys=True, default=str)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        
        return f"{QueryCacheConfig.QUERY_PREFIX}{query_type}:{params_hash}"
    
    def _get_cache_strategy(self, query_type: str) -> Dict[str, Any]:
        """获取缓存策略"""
        return QueryCacheConfig.CACHE_STRATEGIES.get(
            query_type, 
            QueryCacheConfig.CACHE_STRATEGIES['default']
        )
    
    async def get(self, query_type: str, params: Dict[str, Any]) -> Optional[Any]:
        """获取缓存数据"""
        cache_key = self._generate_cache_key(query_type, params)
        strategy = self._get_cache_strategy(query_type)
        
        try:
            # 优先从内存缓存获取
            if strategy['use_memory']:
                memory_result = self.memory_cache.get(cache_key)
                if memory_result is not None:
                    self.hit_count += 1
                    logger.debug(f"内存缓存命中: {cache_key}")
                    return memory_result
            
            # 从Redis获取
            if strategy['use_redis'] and self.redis_client:
                redis_result = await self.redis_client.get(cache_key)
                if redis_result is not None:
                    # 反序列化数据
                    if strategy['compression']:
                        data = pickle.loads(redis_result)
                    else:
                        data = json.loads(redis_result.decode())
                    
                    # 如果使用内存缓存，同时缓存到内存
                    if strategy['use_memory']:
                        self.memory_cache.set(cache_key, data, strategy['expire'])
                    
                    self.hit_count += 1
                    logger.debug(f"Redis缓存命中: {cache_key}")
                    return data
            
            self.miss_count += 1
            return None
            
        except Exception as e:
            logger.error(f"获取缓存失败 {cache_key}: {e}")
            self.error_count += 1
            return None
    
    async def set(self, query_type: str, params: Dict[str, Any], data: Any) -> bool:
        """设置缓存数据"""
        cache_key = self._generate_cache_key(query_type, params)
        strategy = self._get_cache_strategy(query_type)
        
        try:
            # 存储到内存缓存
            if strategy['use_memory']:
                self.memory_cache.set(cache_key, data, strategy['expire'])
            
            # 存储到Redis
            if strategy['use_redis'] and self.redis_client:
                # 序列化数据
                if strategy['compression']:
                    serialized_data = pickle.dumps(data)
                else:
                    serialized_data = json.dumps(data, default=str).encode()
                
                await self.redis_client.setex(
                    cache_key,
                    strategy['expire'],
                    serialized_data
                )
            
            logger.debug(f"缓存设置成功: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败 {cache_key}: {e}")
            self.error_count += 1
            return False
    
    async def delete(self, query_type: str, params: Dict[str, Any]) -> bool:
        """删除缓存数据"""
        cache_key = self._generate_cache_key(query_type, params)
        
        try:
            # 从内存缓存删除
            self.memory_cache.delete(cache_key)
            
            # 从Redis删除
            if self.redis_client:
                await self.redis_client.delete(cache_key)
            
            logger.debug(f"缓存删除成功: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"删除缓存失败 {cache_key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """根据模式清除缓存"""
        try:
            deleted_count = 0
            
            # 清除Redis缓存
            if self.redis_client:
                keys = await self.redis_client.keys(f"{QueryCacheConfig.QUERY_PREFIX}{pattern}*")
                if keys:
                    deleted_count = await self.redis_client.delete(*keys)
            
            # 清除内存缓存中匹配的键
            pattern_prefix = f"{QueryCacheConfig.QUERY_PREFIX}{pattern}"
            keys_to_delete = [
                key for key in self.memory_cache.cache.keys()
                if key.startswith(pattern_prefix)
            ]
            
            for key in keys_to_delete:
                self.memory_cache.delete(key)
            
            deleted_count += len(keys_to_delete)
            logger.info(f"清除缓存模式 {pattern}: {deleted_count} 个键")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清除缓存模式失败 {pattern}: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'error_count': self.error_count,
            'hit_ratio': self.hit_count / (self.hit_count + self.miss_count) if (self.hit_count + self.miss_count) > 0 else 0,
            'memory_cache': self.memory_cache.stats(),
            'redis_available': self.redis_client is not None
        }
        
        # Redis统计信息
        if self.redis_client:
            try:
                redis_info = await self.redis_client.info('memory')
                stats['redis_memory'] = {
                    'used_memory': redis_info.get('used_memory', 0),
                    'used_memory_human': redis_info.get('used_memory_human', '0B'),
                    'maxmemory': redis_info.get('maxmemory', 0),
                }
            except Exception as e:
                logger.warning(f"获取Redis统计信息失败: {e}")
                stats['redis_memory'] = None
        
        return stats


# 全局缓存管理器实例
cache_manager = QueryCacheManager()


def cached_query(query_type: str, expire: Optional[int] = None):
    """查询缓存装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存参数
            cache_params = {
                'function': func.__name__,
                'args': str(args),
                'kwargs': {k: str(v) for k, v in kwargs.items()}
            }
            
            # 尝试从缓存获取
            cached_result = await cache_manager.get(query_type, cache_params)
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            if result is not None:
                await cache_manager.set(query_type, cache_params, result)
            
            return result
        
        return wrapper
    return decorator


async def invalidate_cache_on_update(table_name: str, operation: str = 'update'):
    """数据更新时的缓存失效处理"""
    patterns_to_clear = []
    
    # 根据表名确定需要清除的缓存模式
    if table_name in ['market_data', 'kline_data', 'trade_ticks']:
        patterns_to_clear.extend(['market_data', 'kline_data'])
    elif table_name in ['orders', 'trades', 'positions']:
        patterns_to_clear.extend(['user_data', 'trading_data'])
    elif table_name in ['strategies', 'backtests']:
        patterns_to_clear.append('strategy_data')
    
    # 清除相关缓存
    for pattern in patterns_to_clear:
        await cache_manager.clear_pattern(pattern)
        logger.info(f"已清除 {table_name} {operation} 操作相关的 {pattern} 缓存")


# 初始化和清理函数
async def init_cache():
    """初始化缓存系统"""
    await cache_manager.initialize()
    logger.info("查询缓存系统初始化完成")


async def cleanup_cache():
    """清理缓存系统"""
    await cache_manager.close()
    logger.info("查询缓存系统已清理")


# 常用查询缓存函数
@cached_query('market_data')
async def get_latest_market_data(symbol_code: str, session: AsyncSession):
    """获取最新市场数据（带缓存）"""
    from sqlalchemy import text
    
    query = text("""
        SELECT * FROM market_data 
        WHERE symbol_code = :symbol_code 
        ORDER BY timestamp DESC 
        LIMIT 1
    """)
    
    result = await session.execute(query, {'symbol_code': symbol_code})
    row = result.first()
    
    if row:
        return dict(row._mapping)
    return None


@cached_query('kline_data')
async def get_kline_data(symbol_code: str, kline_type: str, limit: int, session: AsyncSession):
    """获取K线数据（带缓存）"""
    from sqlalchemy import text
    
    query = text("""
        SELECT * FROM kline_data 
        WHERE symbol_code = :symbol_code AND kline_type = :kline_type
        ORDER BY trading_date DESC 
        LIMIT :limit
    """)
    
    result = await session.execute(query, {
        'symbol_code': symbol_code,
        'kline_type': kline_type,
        'limit': limit
    })
    
    return [dict(row._mapping) for row in result.fetchall()]


@cached_query('user_data')
async def get_user_positions(user_id: int, session: AsyncSession):
    """获取用户持仓（带缓存）"""
    from sqlalchemy import text
    
    query = text("""
        SELECT * FROM positions 
        WHERE user_id = :user_id AND quantity > 0
        ORDER BY updated_at DESC
    """)
    
    result = await session.execute(query, {'user_id': user_id})
    return [dict(row._mapping) for row in result.fetchall()]


# 缓存预热函数
async def warm_up_cache():
    """缓存预热"""
    from app.core.database import db_manager
    
    try:
        async with db_manager.get_session() as session:
            # 预热常用市场数据
            common_symbols = ['000001', '399001', '399006']  # 示例股票代码
            
            for symbol in common_symbols:
                await get_latest_market_data(symbol, session)
                await get_kline_data(symbol, '1d', 100, session)
            
            logger.info("缓存预热完成")
            
    except Exception as e:
        logger.error(f"缓存预热失败: {e}")


if __name__ == "__main__":
    async def test_cache():
        """测试缓存功能"""
        await init_cache()
        
        # 测试缓存设置和获取
        test_params = {'symbol': '000001', 'type': 'test'}
        test_data = {'price': 10.50, 'volume': 1000}
        
        # 设置缓存
        success = await cache_manager.set('test_data', test_params, test_data)
        print(f"设置缓存: {success}")
        
        # 获取缓存
        cached_data = await cache_manager.get('test_data', test_params)
        print(f"获取缓存: {cached_data}")
        
        # 获取统计信息
        stats = await cache_manager.get_stats()
        print(f"缓存统计: {stats}")
        
        await cleanup_cache()
    
    asyncio.run(test_cache())
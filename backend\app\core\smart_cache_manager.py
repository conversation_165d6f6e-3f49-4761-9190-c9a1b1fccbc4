"""
智能缓存管理器
基于查询模式和数据特征的智能缓存策略
"""

import asyncio
import hashlib
import logging
import pickle
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple, Callable, Union

from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.query_cache import QueryCacheManager, QueryCacheConfig

logger = logging.getLogger(__name__)


@dataclass
class CacheMetrics:
    """缓存指标"""
    key: str
    cache_type: str
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    total_size: int = 0
    avg_response_time: float = 0.0
    last_access: datetime = field(default_factory=datetime.now)
    creation_time: datetime = field(default_factory=datetime.now)
    access_frequency: float = 0.0  # 访问频率（次/小时）


@dataclass
class CachePattern:
    """缓存模式"""
    pattern_name: str
    query_signature: str
    access_count: int = 0
    avg_execution_time: float = 0.0
    cache_hit_rate: float = 0.0
    data_freshness_requirement: int = 300  # 秒
    priority_score: float = 0.0


class SmartCacheStrategy:
    """智能缓存策略"""
    
    def __init__(self):
        self.cache_metrics: Dict[str, CacheMetrics] = {}
        self.cache_patterns: Dict[str, CachePattern] = {}
        self.access_history: deque = deque(maxlen=10000)
        
        # 智能策略配置
        self.strategy_config = {
            # 基于访问频率的缓存策略
            'frequency_based': {
                'high_frequency_threshold': 10,  # 每小时访问次数
                'medium_frequency_threshold': 3,
                'cache_ttl_multiplier': {
                    'high': 1.5,
                    'medium': 1.0,
                    'low': 0.5
                }
            },
            
            # 基于数据类型的缓存策略
            'data_type_based': {
                'market_data': {
                    'base_ttl': 30,
                    'size_limit': 1000,
                    'compression': False
                },
                'kline_data': {
                    'base_ttl': 300,
                    'size_limit': 500,
                    'compression': True
                },
                'user_data': {
                    'base_ttl': 600,
                    'size_limit': 200,
                    'compression': False
                },
                'static_data': {
                    'base_ttl': 3600,
                    'size_limit': 100,
                    'compression': True
                }
            },
            
            # 基于查询复杂度的缓存策略
            'complexity_based': {
                'simple_query_ttl': 60,
                'medium_query_ttl': 300,
                'complex_query_ttl': 900,
                'aggregation_query_ttl': 600
            }
        }
    
    def analyze_query_pattern(self, query_signature: str, execution_time: float) -> str:
        """分析查询模式"""
        # 简单的查询复杂度分析
        if execution_time < 0.1:
            return 'simple'
        elif execution_time < 0.5:
            return 'medium'
        elif 'GROUP BY' in query_signature.upper() or 'SUM(' in query_signature.upper():
            return 'aggregation'
        else:
            return 'complex'
    
    def calculate_cache_priority(self, cache_key: str, query_pattern: str) -> float:
        """计算缓存优先级"""
        metrics = self.cache_metrics.get(cache_key)
        if not metrics:
            return 0.5  # 默认优先级
        
        # 基于访问频率的分数
        frequency_score = min(metrics.access_frequency / 10, 1.0)
        
        # 基于命中率的分数
        total_access = metrics.hit_count + metrics.miss_count
        hit_rate_score = metrics.hit_count / max(total_access, 1)
        
        # 基于响应时间改善的分数
        response_improvement_score = min(metrics.avg_response_time / 1000, 1.0)
        
        # 基于查询模式的分数
        pattern_score = {
            'simple': 0.3,
            'medium': 0.6,
            'complex': 0.9,
            'aggregation': 0.8
        }.get(query_pattern, 0.5)
        
        # 综合分数
        priority = (
            frequency_score * 0.3 +
            hit_rate_score * 0.25 +
            response_improvement_score * 0.25 +
            pattern_score * 0.2
        )
        
        return priority
    
    def get_optimal_ttl(self, cache_key: str, data_type: str, query_pattern: str) -> int:
        """获取最优TTL"""
        base_config = self.strategy_config['data_type_based'].get(
            data_type, 
            self.strategy_config['data_type_based']['market_data']
        )
        base_ttl = base_config['base_ttl']
        
        # 根据查询复杂度调整
        complexity_ttl = self.strategy_config['complexity_based'].get(
            f'{query_pattern}_query_ttl',
            base_ttl
        )
        
        # 根据访问频率调整
        metrics = self.cache_metrics.get(cache_key)
        if metrics:
            if metrics.access_frequency > self.strategy_config['frequency_based']['high_frequency_threshold']:
                frequency_multiplier = self.strategy_config['frequency_based']['cache_ttl_multiplier']['high']
            elif metrics.access_frequency > self.strategy_config['frequency_based']['medium_frequency_threshold']:
                frequency_multiplier = self.strategy_config['frequency_based']['cache_ttl_multiplier']['medium']
            else:
                frequency_multiplier = self.strategy_config['frequency_based']['cache_ttl_multiplier']['low']
        else:
            frequency_multiplier = 1.0
        
        # 综合计算
        optimal_ttl = int((base_ttl + complexity_ttl) / 2 * frequency_multiplier)
        return max(30, min(optimal_ttl, 3600))  # 限制在30秒到1小时之间


class SmartCacheManager(QueryCacheManager):
    """智能缓存管理器"""
    
    def __init__(self):
        super().__init__()
        self.strategy = SmartCacheStrategy()
        self.cache_optimizer_running = False
        
        # 扩展配置
        self.smart_config = {
            'enable_auto_optimization': True,
            'optimization_interval': 300,  # 5分钟
            'enable_predictive_caching': True,
            'enable_cache_warming': True,
            'max_cache_size_mb': 500,
            'cache_eviction_strategy': 'lru_with_priority'
        }
    
    async def get_smart(
        self, 
        query_type: str, 
        params: Dict[str, Any],
        query_signature: str = '',
        execution_time_hint: float = 0.0
    ) -> Optional[Any]:
        """智能缓存获取"""
        cache_key = self._generate_cache_key(query_type, params)
        
        # 记录访问
        self._record_cache_access(cache_key, query_type, 'get')
        
        # 尝试获取缓存
        start_time = time.time()
        result = await self.get(query_type, params)
        response_time = time.time() - start_time
        
        # 更新指标
        self._update_cache_metrics(cache_key, result is not None, response_time)
        
        return result
    
    async def set_smart(
        self,
        query_type: str,
        params: Dict[str, Any],
        data: Any,
        query_signature: str = '',
        execution_time: float = 0.0
    ) -> bool:
        """智能缓存设置"""
        cache_key = self._generate_cache_key(query_type, params)
        
        # 分析查询模式
        query_pattern = self.strategy.analyze_query_pattern(query_signature, execution_time)
        
        # 计算最优TTL
        optimal_ttl = self.strategy.get_optimal_ttl(cache_key, query_type, query_pattern)
        
        # 临时修改策略配置
        original_strategy = self._get_cache_strategy(query_type)
        original_expire = original_strategy['expire']
        original_strategy['expire'] = optimal_ttl
        
        try:
            # 设置缓存
            result = await self.set(query_type, params, data)
            
            # 记录访问
            self._record_cache_access(cache_key, query_type, 'set')
            
            # 更新查询模式
            self._update_query_pattern(cache_key, query_signature, execution_time)
            
            return result
        finally:
            # 恢复原始策略
            original_strategy['expire'] = original_expire
    
    def _record_cache_access(self, cache_key: str, cache_type: str, operation: str):
        """记录缓存访问"""
        self.strategy.access_history.append({
            'timestamp': datetime.now(),
            'cache_key': cache_key,
            'cache_type': cache_type,
            'operation': operation
        })
        
        # 初始化或更新指标
        if cache_key not in self.strategy.cache_metrics:
            self.strategy.cache_metrics[cache_key] = CacheMetrics(
                key=cache_key,
                cache_type=cache_type
            )
    
    def _update_cache_metrics(self, cache_key: str, cache_hit: bool, response_time: float):
        """更新缓存指标"""
        if cache_key not in self.strategy.cache_metrics:
            return
        
        metrics = self.strategy.cache_metrics[cache_key]
        
        if cache_hit:
            metrics.hit_count += 1
        else:
            metrics.miss_count += 1
        
        # 更新平均响应时间
        total_requests = metrics.hit_count + metrics.miss_count
        metrics.avg_response_time = (
            (metrics.avg_response_time * (total_requests - 1) + response_time) / total_requests
        )
        
        # 更新访问频率（每小时访问次数）
        time_diff = (datetime.now() - metrics.creation_time).total_seconds() / 3600
        if time_diff > 0:
            metrics.access_frequency = total_requests / time_diff
        
        metrics.last_access = datetime.now()
    
    def _update_query_pattern(self, cache_key: str, query_signature: str, execution_time: float):
        """更新查询模式"""
        pattern_key = hashlib.md5(query_signature.encode()).hexdigest()[:16]
        
        if pattern_key not in self.strategy.cache_patterns:
            self.strategy.cache_patterns[pattern_key] = CachePattern(
                pattern_name=pattern_key,
                query_signature=query_signature
            )
        
        pattern = self.strategy.cache_patterns[pattern_key]
        pattern.access_count += 1
        
        # 更新平均执行时间
        pattern.avg_execution_time = (
            (pattern.avg_execution_time * (pattern.access_count - 1) + execution_time) / 
            pattern.access_count
        )
        
        # 计算缓存命中率
        metrics = self.strategy.cache_metrics.get(cache_key)
        if metrics:
            total_access = metrics.hit_count + metrics.miss_count
            pattern.cache_hit_rate = metrics.hit_count / max(total_access, 1)
        
        # 计算优先级分数
        query_pattern_type = self.strategy.analyze_query_pattern(query_signature, execution_time)
        pattern.priority_score = self.strategy.calculate_cache_priority(
            cache_key, query_pattern_type
        )
    
    async def start_smart_optimization(self):
        """启动智能优化"""
        if self.cache_optimizer_running:
            return
        
        self.cache_optimizer_running = True
        
        async def optimization_loop():
            while self.cache_optimizer_running:
                try:
                    if self.smart_config['enable_auto_optimization']:
                        await self._optimize_cache_strategies()
                    
                    if self.smart_config['enable_predictive_caching']:
                        await self._predictive_cache_warming()
                    
                    await self._cache_cleanup()
                    
                    await asyncio.sleep(self.smart_config['optimization_interval'])
                    
                except Exception as e:
                    logger.error(f"缓存优化异常: {e}")
                    await asyncio.sleep(60)
        
        asyncio.create_task(optimization_loop())
        logger.info("智能缓存优化已启动")
    
    async def _optimize_cache_strategies(self):
        """优化缓存策略"""
        # 分析缓存性能
        high_value_keys = []
        low_value_keys = []
        
        for cache_key, metrics in self.strategy.cache_metrics.items():
            total_access = metrics.hit_count + metrics.miss_count
            if total_access < 5:  # 访问次数太少，跳过
                continue
            
            hit_rate = metrics.hit_count / total_access
            
            if hit_rate > 0.8 and metrics.access_frequency > 5:
                high_value_keys.append(cache_key)
            elif hit_rate < 0.3 and metrics.access_frequency < 1:
                low_value_keys.append(cache_key)
        
        # 对高价值缓存延长TTL
        logger.info(f"识别到 {len(high_value_keys)} 个高价值缓存键")
        
        # 清理低价值缓存
        for cache_key in low_value_keys[:10]:  # 限制每次清理数量
            await self._evict_cache_key(cache_key)
        
        if low_value_keys:
            logger.info(f"清理了 {min(len(low_value_keys), 10)} 个低价值缓存键")
    
    async def _predictive_cache_warming(self):
        """预测性缓存预热"""
        # 分析访问模式，预测可能的查询
        current_hour = datetime.now().hour
        
        # 简单的时间模式分析
        recent_access = [
            access for access in self.strategy.access_history
            if (datetime.now() - access['timestamp']).total_seconds() < 3600
        ]
        
        # 按缓存类型分组
        access_by_type = defaultdict(list)
        for access in recent_access:
            access_by_type[access['cache_type']].append(access)
        
        # 对于高频访问的缓存类型，可以实现预热逻辑
        for cache_type, accesses in access_by_type.items():
            if len(accesses) > 10:  # 高频访问
                logger.debug(f"缓存类型 {cache_type} 访问频繁，可考虑预热")
    
    async def _cache_cleanup(self):
        """缓存清理"""
        # 清理过期的指标
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        expired_metrics = [
            key for key, metrics in self.strategy.cache_metrics.items()
            if metrics.last_access < cutoff_time
        ]
        
        for key in expired_metrics:
            del self.strategy.cache_metrics[key]
        
        # 清理过期的查询模式
        expired_patterns = [
            key for key, pattern in self.strategy.cache_patterns.items()
            if pattern.access_count < 5 and 
            (datetime.now() - datetime.now()).total_seconds() > 86400  # 24小时
        ]
        
        for key in expired_patterns:
            del self.strategy.cache_patterns[key]
        
        if expired_metrics or expired_patterns:
            logger.info(f"清理了 {len(expired_metrics)} 个过期指标和 {len(expired_patterns)} 个查询模式")
    
    async def _evict_cache_key(self, cache_key: str):
        """驱逐指定缓存键"""
        try:
            # 从内存缓存删除
            self.memory_cache.delete(cache_key)
            
            # 从Redis删除
            if self.redis_client:
                await self.redis_client.delete(cache_key)
            
            # 更新指标
            if cache_key in self.strategy.cache_metrics:
                self.strategy.cache_metrics[cache_key].eviction_count += 1
            
        except Exception as e:
            logger.warning(f"驱逐缓存键失败 {cache_key}: {e}")
    
    async def get_smart_cache_report(self) -> Dict[str, Any]:
        """获取智能缓存报告"""
        # 基础统计
        total_metrics = len(self.strategy.cache_metrics)
        total_patterns = len(self.strategy.cache_patterns)
        
        # 计算平均命中率
        if total_metrics > 0:
            total_hits = sum(m.hit_count for m in self.strategy.cache_metrics.values())
            total_requests = sum(
                m.hit_count + m.miss_count for m in self.strategy.cache_metrics.values()
            )
            avg_hit_rate = total_hits / max(total_requests, 1)
        else:
            avg_hit_rate = 0
        
        # Top缓存键
        top_cache_keys = sorted(
            self.strategy.cache_metrics.items(),
            key=lambda x: x[1].access_frequency,
            reverse=True
        )[:10]
        
        # Top查询模式
        top_query_patterns = sorted(
            self.strategy.cache_patterns.values(),
            key=lambda x: x.priority_score,
            reverse=True
        )[:10]
        
        # 缓存类型分布
        cache_type_stats = defaultdict(lambda: {'count': 0, 'hit_rate': 0, 'avg_frequency': 0})
        
        for metrics in self.strategy.cache_metrics.values():
            cache_type = metrics.cache_type
            stats = cache_type_stats[cache_type]
            stats['count'] += 1
            
            total_access = metrics.hit_count + metrics.miss_count
            if total_access > 0:
                stats['hit_rate'] += metrics.hit_count / total_access
            stats['avg_frequency'] += metrics.access_frequency
        
        # 计算平均值
        for stats in cache_type_stats.values():
            if stats['count'] > 0:
                stats['hit_rate'] /= stats['count']
                stats['avg_frequency'] /= stats['count']
        
        return {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_cache_keys': total_metrics,
                'total_query_patterns': total_patterns,
                'overall_hit_rate': avg_hit_rate,
                'optimization_enabled': self.smart_config['enable_auto_optimization']
            },
            'top_cache_keys': [
                {
                    'key': key,
                    'type': metrics.cache_type,
                    'access_frequency': metrics.access_frequency,
                    'hit_rate': metrics.hit_count / max(metrics.hit_count + metrics.miss_count, 1),
                    'avg_response_time': metrics.avg_response_time
                }
                for key, metrics in top_cache_keys
            ],
            'top_query_patterns': [
                {
                    'pattern_name': pattern.pattern_name,
                    'access_count': pattern.access_count,
                    'avg_execution_time': pattern.avg_execution_time,
                    'cache_hit_rate': pattern.cache_hit_rate,
                    'priority_score': pattern.priority_score
                }
                for pattern in top_query_patterns
            ],
            'cache_type_distribution': dict(cache_type_stats),
            'configuration': self.smart_config
        }
    
    async def stop_smart_optimization(self):
        """停止智能优化"""
        self.cache_optimizer_running = False
        logger.info("智能缓存优化已停止")


# 全局智能缓存管理器实例
smart_cache_manager = SmartCacheManager()


# 便捷装饰器
def smart_cached_query(query_type: str, execution_time_hint: float = 0.0):
    """智能缓存装饰器"""
    def decorator(func: Callable):
        async def wrapper(*args, **kwargs):
            # 生成查询签名
            query_signature = f"{func.__name__}({args}, {kwargs})"
            
            # 生成缓存参数
            cache_params = {
                'function': func.__name__,
                'args': str(args),
                'kwargs': {k: str(v) for k, v in kwargs.items()}
            }
            
            # 尝试从智能缓存获取
            cached_result = await smart_cache_manager.get_smart(
                query_type, 
                cache_params,
                query_signature,
                execution_time_hint
            )
            
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            start_time = time.time()
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 智能缓存结果
            if result is not None:
                await smart_cache_manager.set_smart(
                    query_type, 
                    cache_params, 
                    result,
                    query_signature,
                    execution_time
                )
            
            return result
        
        return wrapper
    return decorator


# 初始化函数
async def init_smart_cache():
    """初始化智能缓存"""
    await smart_cache_manager.initialize()
    await smart_cache_manager.start_smart_optimization()
    logger.info("智能缓存系统初始化完成")


async def cleanup_smart_cache():
    """清理智能缓存"""
    await smart_cache_manager.stop_smart_optimization()
    await smart_cache_manager.close()
    logger.info("智能缓存系统已清理")


if __name__ == "__main__":
    async def test_smart_cache():
        """测试智能缓存"""
        await init_smart_cache()
        
        # 模拟一些缓存操作
        test_params = {'symbol': '000001', 'type': 'test'}
        test_data = {'price': 10.50, 'volume': 1000}
        
        # 智能设置缓存
        success = await smart_cache_manager.set_smart(
            'test_data', 
            test_params, 
            test_data,
            'SELECT * FROM market_data WHERE symbol = ?',
            0.15
        )
        print(f"智能设置缓存: {success}")
        
        # 智能获取缓存
        cached_data = await smart_cache_manager.get_smart(
            'test_data', 
            test_params,
            'SELECT * FROM market_data WHERE symbol = ?',
            0.15
        )
        print(f"智能获取缓存: {cached_data}")
        
        # 获取智能缓存报告
        report = await smart_cache_manager.get_smart_cache_report()
        print(f"智能缓存报告: {report['summary']}")
        
        await cleanup_smart_cache()
    
    asyncio.run(test_smart_cache())
"""
增强版WebSocket管理器
解决连接稳定性问题和心跳机制
"""
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from collections import defaultdict
import traceback

logger = logging.getLogger(__name__)

class EnhancedConnectionManager:
    """增强版WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接：client_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 连接信息：client_id -> connection info
        self.connection_info: Dict[str, Dict[str, Any]] = {}
        
        # 订阅关系：topic -> set of client_ids
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)
        
        # 客户端订阅：client_id -> set of topics
        self.client_subscriptions: Dict[str, Set[str]] = defaultdict(set)
        
        # 心跳记录：client_id -> last_heartbeat
        self.heartbeats: Dict[str, datetime] = {}
        
        # 心跳检查间隔（秒）
        self.heartbeat_interval = 30
        
        # 心跳超时时间（秒）
        self.heartbeat_timeout = 60
        
        # 是否正在运行
        self.is_running = False
        
        # 后台任务
        self.background_tasks: Set[asyncio.Task] = set()
    
    async def connect(self, websocket: WebSocket, client_id: str) -> bool:
        """接受WebSocket连接"""
        try:
            await websocket.accept()
            
            # 存储连接
            self.active_connections[client_id] = websocket
            self.heartbeats[client_id] = datetime.now()
            self.connection_info[client_id] = {
                "connected_at": datetime.now(),
                "last_activity": datetime.now(),
                "messages_sent": 0,
                "messages_received": 0,
                "errors": 0
            }
            
            logger.info(f"✅ Client {client_id} connected. Total: {len(self.active_connections)}")
            
            # 发送连接成功消息
            await self.send_personal_message(client_id, {
                "type": "connection",
                "status": "connected",
                "client_id": client_id,
                "heartbeat_interval": self.heartbeat_interval,
                "server_time": datetime.now().isoformat()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect client {client_id}: {e}")
            return False
    
    async def disconnect(self, client_id: str, code: int = 1000, reason: str = "Normal closure"):
        """断开WebSocket连接"""
        if client_id not in self.active_connections:
            return
        
        websocket = self.active_connections[client_id]
        
        try:
            # 发送断开消息
            await self.send_personal_message(client_id, {
                "type": "disconnection",
                "code": code,
                "reason": reason
            })
            
            # 关闭连接
            await websocket.close(code=code, reason=reason)
            
        except Exception as e:
            logger.debug(f"Error closing websocket for {client_id}: {e}")
        
        finally:
            # 清理连接数据
            self._cleanup_client(client_id)
            logger.info(f"🔌 Client {client_id} disconnected. Total: {len(self.active_connections)}")
    
    def _cleanup_client(self, client_id: str):
        """清理客户端数据"""
        # 移除连接
        self.active_connections.pop(client_id, None)
        self.heartbeats.pop(client_id, None)
        self.connection_info.pop(client_id, None)
        
        # 清理订阅
        topics = self.client_subscriptions.pop(client_id, set()).copy()
        for topic in topics:
            self.subscriptions[topic].discard(client_id)
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
    
    async def send_personal_message(self, client_id: str, data: Dict[str, Any]) -> bool:
        """发送个人消息"""
        if client_id not in self.active_connections:
            return False
        
        websocket = self.active_connections[client_id]
        
        try:
            message = json.dumps(data, ensure_ascii=False, default=str)
            await websocket.send_text(message)
            
            # 更新统计
            if client_id in self.connection_info:
                self.connection_info[client_id]["messages_sent"] += 1
                self.connection_info[client_id]["last_activity"] = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending message to {client_id}: {e}")
            
            # 更新错误计数
            if client_id in self.connection_info:
                self.connection_info[client_id]["errors"] += 1
            
            # 如果错误太多，断开连接
            if self.connection_info.get(client_id, {}).get("errors", 0) > 5:
                await self.disconnect(client_id, 1002, "Too many errors")
            
            return False
    
    async def broadcast(self, data: Dict[str, Any], exclude: Optional[Set[str]] = None):
        """广播消息给所有连接的客户端"""
        exclude = exclude or set()
        message = json.dumps(data, ensure_ascii=False, default=str)
        
        disconnected = []
        for client_id in self.active_connections:
            if client_id in exclude:
                continue
            
            try:
                await self.active_connections[client_id].send_text(message)
                if client_id in self.connection_info:
                    self.connection_info[client_id]["messages_sent"] += 1
                    
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected:
            await self.disconnect(client_id, 1002, "Send error")
    
    async def broadcast_to_topic(self, topic: str, data: Dict[str, Any]):
        """向特定主题的订阅者广播消息"""
        if topic not in self.subscriptions:
            return
        
        data["topic"] = topic
        message = json.dumps(data, ensure_ascii=False, default=str)
        
        disconnected = []
        for client_id in self.subscriptions[topic].copy():
            if client_id not in self.active_connections:
                self.subscriptions[topic].discard(client_id)
                continue
            
            try:
                await self.active_connections[client_id].send_text(message)
                if client_id in self.connection_info:
                    self.connection_info[client_id]["messages_sent"] += 1
                    
            except Exception as e:
                logger.error(f"Error sending to {client_id}: {e}")
                disconnected.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected:
            await self.disconnect(client_id, 1002, "Send error")
    
    def subscribe(self, client_id: str, topic: str) -> bool:
        """订阅主题"""
        if client_id not in self.active_connections:
            return False
        
        self.subscriptions[topic].add(client_id)
        self.client_subscriptions[client_id].add(topic)
        
        logger.debug(f"📢 {client_id} subscribed to {topic}")
        return True
    
    def unsubscribe(self, client_id: str, topic: str) -> bool:
        """取消订阅"""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(client_id)
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
        
        if client_id in self.client_subscriptions:
            self.client_subscriptions[client_id].discard(topic)
        
        logger.debug(f"🔕 {client_id} unsubscribed from {topic}")
        return True
    
    async def handle_heartbeat(self, client_id: str):
        """处理心跳"""
        if client_id in self.active_connections:
            self.heartbeats[client_id] = datetime.now()
            
            # 发送心跳响应
            await self.send_personal_message(client_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
    
    async def check_heartbeats(self):
        """检查心跳超时"""
        while self.is_running:
            try:
                now = datetime.now()
                timeout_threshold = now - timedelta(seconds=self.heartbeat_timeout)
                
                # 找出超时的客户端
                timed_out = []
                for client_id, last_heartbeat in self.heartbeats.items():
                    if last_heartbeat < timeout_threshold:
                        timed_out.append(client_id)
                
                # 断开超时的连接
                for client_id in timed_out:
                    logger.warning(f"⏱️ Client {client_id} heartbeat timeout")
                    await self.disconnect(client_id, 1001, "Heartbeat timeout")
                
                # 发送心跳请求
                for client_id in self.active_connections:
                    await self.send_personal_message(client_id, {
                        "type": "ping",
                        "timestamp": now.isoformat()
                    })
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Error in heartbeat check: {e}")
                await asyncio.sleep(5)
    
    async def start(self):
        """启动管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动心跳检查任务
        task = asyncio.create_task(self.check_heartbeats())
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)
        
        logger.info("✅ WebSocket manager started")
    
    async def shutdown(self):
        """关闭管理器"""
        self.is_running = False
        
        # 取消所有后台任务
        for task in self.background_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # 断开所有连接
        client_ids = list(self.active_connections.keys())
        for client_id in client_ids:
            await self.disconnect(client_id, 1001, "Server shutdown")
        
        logger.info("🔌 WebSocket manager shutdown")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "total_topics": len(self.subscriptions),
            "connections": {
                client_id: {
                    "connected_at": info["connected_at"].isoformat(),
                    "last_activity": info["last_activity"].isoformat(),
                    "messages_sent": info["messages_sent"],
                    "messages_received": info["messages_received"],
                    "errors": info["errors"],
                    "subscriptions": list(self.client_subscriptions.get(client_id, set()))
                }
                for client_id, info in self.connection_info.items()
            },
            "topics": {
                topic: list(subscribers)
                for topic, subscribers in self.subscriptions.items()
            }
        }
    
    def is_healthy(self) -> bool:
        """健康检查"""
        return self.is_running
    
    async def handle_message(self, client_id: str, message: str):
        """处理客户端消息"""
        if client_id not in self.active_connections:
            return
        
        try:
            # 更新活动时间
            if client_id in self.connection_info:
                self.connection_info[client_id]["messages_received"] += 1
                self.connection_info[client_id]["last_activity"] = datetime.now()
            
            # 解析消息
            data = json.loads(message)
            msg_type = data.get("type")
            
            # 处理不同类型的消息
            if msg_type == "ping":
                await self.handle_heartbeat(client_id)
                
            elif msg_type == "subscribe":
                topics = data.get("topics", [])
                for topic in topics:
                    self.subscribe(client_id, topic)
                
                await self.send_personal_message(client_id, {
                    "type": "subscribe_ack",
                    "topics": topics
                })
                
            elif msg_type == "unsubscribe":
                topics = data.get("topics", [])
                for topic in topics:
                    self.unsubscribe(client_id, topic)
                
                await self.send_personal_message(client_id, {
                    "type": "unsubscribe_ack",
                    "topics": topics
                })
                
            elif msg_type == "message":
                # 处理普通消息
                topic = data.get("topic")
                if topic:
                    await self.broadcast_to_topic(topic, {
                        "type": "message",
                        "from": client_id,
                        "data": data.get("data"),
                        "timestamp": datetime.now().isoformat()
                    })
                    
            else:
                # 未知消息类型
                await self.send_personal_message(client_id, {
                    "type": "error",
                    "message": f"Unknown message type: {msg_type}"
                })
                
        except json.JSONDecodeError:
            await self.send_personal_message(client_id, {
                "type": "error",
                "message": "Invalid JSON format"
            })
            
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {e}\n{traceback.format_exc()}")
            await self.send_personal_message(client_id, {
                "type": "error",
                "message": str(e)
            })


# 全局管理器实例
enhanced_manager = EnhancedConnectionManager()
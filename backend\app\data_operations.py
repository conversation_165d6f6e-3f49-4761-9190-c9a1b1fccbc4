#!/usr/bin/env python3
"""数据操作系统 - 实现刷新、搜索、自选股管理等功能"""

from fastapi import APIRouter, HTTPException, Query, WebSocket, WebSocketDisconnect
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import secrets
import random
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(tags=["data_operations"])

# ============ 数据模型 ============

class StockSearchRequest(BaseModel):
    keyword: str
    limit: int = 10

class WatchlistAddRequest(BaseModel):
    symbol: str
    name: Optional[str] = None

class RefreshDataRequest(BaseModel):
    data_types: List[str]  # ["quotes", "news", "overview", "sectors"]
    symbols: Optional[List[str]] = None

# ============ 模拟数据存储 ============

# 股票基础数据
STOCKS_DB = {
    "000001": {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
    "000002": {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产"},
    "600036": {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
    "600519": {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "白酒"},
    "000858": {"symbol": "000858", "name": "五粮液", "market": "SZ", "industry": "白酒"},
    "000333": {"symbol": "000333", "name": "美的集团", "market": "SZ", "industry": "家电"},
    "000651": {"symbol": "000651", "name": "格力电器", "market": "SZ", "industry": "家电"},
    "002415": {"symbol": "002415", "name": "海康威视", "market": "SZ", "industry": "电子"},
    "600309": {"symbol": "600309", "name": "万华化学", "market": "SH", "industry": "化工"},
    "300750": {"symbol": "300750", "name": "宁德时代", "market": "SZ", "industry": "新能源"}
}

# 用户自选股数据 (userId -> [symbols])
USER_WATCHLIST = {
    "default_user": ["000001", "600036", "600519"]
}

# 搜索历史缓存
SEARCH_HISTORY = {}

# 实时行情缓存
QUOTE_CACHE = {}

# WebSocket连接管理
class DataConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, List[str]] = {}  # connection_id -> [symbols]

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.subscriptions[client_id] = []
        logger.info(f"Data WebSocket connected: {client_id}")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            del self.subscriptions[client_id]
            logger.info(f"Data WebSocket disconnected: {client_id}")

    async def send_to_client(self, client_id: str, message: dict):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_json(message)
            except Exception as e:
                logger.error(f"Error sending to client {client_id}: {e}")
                self.disconnect(client_id)

    async def broadcast_quote(self, symbol: str, quote: dict):
        """向订阅该股票的所有客户端广播行情"""
        for client_id, symbols in self.subscriptions.items():
            if symbol in symbols:
                await self.send_to_client(client_id, {
                    "type": "quote_update",
                    "data": quote
                })

data_manager = DataConnectionManager()

# ============ 辅助函数 ============

def generate_mock_quote(symbol: str) -> dict:
    """生成模拟行情数据"""
    stock_info = STOCKS_DB.get(symbol, {"name": f"股票{symbol}"})
    base_price = 10.0 + (hash(symbol) % 100)
    
    # 生成随机波动
    change_percent = random.uniform(-10, 10)
    current_price = base_price * (1 + change_percent / 100)
    
    return {
        "symbol": symbol,
        "name": stock_info.get("name", f"股票{symbol}"),
        "currentPrice": round(current_price, 2),
        "previousClose": round(base_price, 2),
        "open": round(base_price * (1 + random.uniform(-2, 2) / 100), 2),
        "high": round(current_price * 1.02, 2),
        "low": round(current_price * 0.98, 2),
        "volume": random.randint(1000000, 10000000),
        "turnover": random.randint(10000000, 100000000),
        "changeAmount": round(current_price - base_price, 2),
        "changePercent": round(change_percent, 2),
        "amplitude": round(random.uniform(1, 5), 2),
        "turnoverRate": round(random.uniform(0.5, 5), 2),
        "pe": round(random.uniform(10, 50), 2),
        "pb": round(random.uniform(1, 10), 2),
        "marketCap": random.randint(1000000000, 100000000000),
        "updateTime": datetime.now().isoformat()
    }

def search_stocks_by_keyword(keyword: str, limit: int = 10) -> List[dict]:
    """根据关键词搜索股票"""
    keyword_lower = keyword.lower()
    results = []
    
    for symbol, info in STOCKS_DB.items():
        # 搜索股票代码或名称
        if (keyword in symbol or 
            keyword_lower in info["name"].lower() or
            keyword in info.get("pinyin", "")):
            results.append({
                "symbol": symbol,
                "name": info["name"],
                "market": info["market"],
                "industry": info.get("industry", ""),
                "type": "stock"
            })
    
    # 按相关度排序
    results.sort(key=lambda x: (
        keyword in x["symbol"],  # 代码完全匹配优先
        keyword_lower in x["name"].lower()  # 名称匹配次之
    ), reverse=True)
    
    return results[:limit]

# ============ API路由 ============

@router.get("/api/v1/market/search")
async def search_stocks(
    keyword: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, description="返回结果数量限制")
):
    """搜索股票"""
    try:
        if not keyword or len(keyword.strip()) == 0:
            return {"success": False, "message": "搜索关键词不能为空"}
        
        # 执行搜索
        results = search_stocks_by_keyword(keyword, limit)
        
        # 记录搜索历史
        if "default_user" not in SEARCH_HISTORY:
            SEARCH_HISTORY["default_user"] = []
        
        SEARCH_HISTORY["default_user"].insert(0, {
            "keyword": keyword,
            "time": datetime.now().isoformat(),
            "resultCount": len(results)
        })
        
        # 只保留最近100条搜索记录
        SEARCH_HISTORY["default_user"] = SEARCH_HISTORY["default_user"][:100]
        
        return {
            "success": True,
            "data": {
                "results": results,
                "total": len(results),
                "keyword": keyword
            }
        }
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/api/v1/market/search/hot")
async def get_hot_searches():
    """获取热门搜索"""
    try:
        # 模拟热门搜索
        hot_searches = [
            {"keyword": "银行", "count": 1580, "trend": "up"},
            {"keyword": "新能源", "count": 1234, "trend": "up"},
            {"keyword": "白酒", "count": 987, "trend": "down"},
            {"keyword": "科技", "count": 876, "trend": "up"},
            {"keyword": "医药", "count": 654, "trend": "stable"}
        ]
        
        return {
            "success": True,
            "data": hot_searches
        }
    except Exception as e:
        logger.error(f"获取热门搜索失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/api/v1/market/search/history")
async def get_search_history(user_id: str = "default_user"):
    """获取搜索历史"""
    try:
        history = SEARCH_HISTORY.get(user_id, [])[:20]  # 返回最近20条
        
        return {
            "success": True,
            "data": {
                "history": history,
                "total": len(history)
            }
        }
    except Exception as e:
        logger.error(f"获取搜索历史失败: {e}")
        return {"success": False, "message": str(e)}

@router.delete("/api/v1/market/search/history")
async def clear_search_history(user_id: str = "default_user"):
    """清除搜索历史"""
    try:
        if user_id in SEARCH_HISTORY:
            SEARCH_HISTORY[user_id] = []
        
        return {
            "success": True,
            "message": "搜索历史已清除"
        }
    except Exception as e:
        logger.error(f"清除搜索历史失败: {e}")
        return {"success": False, "message": str(e)}

# ============ 自选股管理 ============

@router.get("/api/v1/market/watchlist")
async def get_watchlist(user_id: str = "default_user"):
    """获取自选股列表"""
    try:
        symbols = USER_WATCHLIST.get(user_id, [])
        watchlist = []
        
        for symbol in symbols:
            stock_info = STOCKS_DB.get(symbol, {"name": f"股票{symbol}"})
            quote = generate_mock_quote(symbol)
            
            watchlist.append({
                "symbol": symbol,
                "name": stock_info.get("name"),
                "market": stock_info.get("market", ""),
                "currentPrice": quote["currentPrice"],
                "changePercent": quote["changePercent"],
                "volume": quote["volume"],
                "addTime": datetime.now().isoformat()
            })
        
        return {
            "success": True,
            "data": {
                "watchlist": watchlist,
                "total": len(watchlist)
            }
        }
    except Exception as e:
        logger.error(f"获取自选股失败: {e}")
        return {"success": False, "message": str(e)}

@router.post("/api/v1/market/watchlist")
async def add_to_watchlist(request: WatchlistAddRequest, user_id: str = "default_user"):
    """添加股票到自选股"""
    try:
        if user_id not in USER_WATCHLIST:
            USER_WATCHLIST[user_id] = []
        
        # 检查是否已存在
        if request.symbol in USER_WATCHLIST[user_id]:
            return {"success": False, "message": "该股票已在自选股中"}
        
        # 添加到自选股
        USER_WATCHLIST[user_id].append(request.symbol)
        
        # 获取股票信息
        stock_info = STOCKS_DB.get(request.symbol, {
            "name": request.name or f"股票{request.symbol}",
            "market": "SZ" if request.symbol.startswith("0") or request.symbol.startswith("3") else "SH"
        })
        
        return {
            "success": True,
            "data": {
                "symbol": request.symbol,
                "name": stock_info.get("name"),
                "addTime": datetime.now().isoformat()
            },
            "message": "添加成功"
        }
    except Exception as e:
        logger.error(f"添加自选股失败: {e}")
        return {"success": False, "message": str(e)}

@router.delete("/api/v1/market/watchlist/{symbol}")
async def remove_from_watchlist(symbol: str, user_id: str = "default_user"):
    """从自选股移除股票"""
    try:
        if user_id not in USER_WATCHLIST:
            return {"success": False, "message": "用户自选股列表不存在"}
        
        if symbol not in USER_WATCHLIST[user_id]:
            return {"success": False, "message": "该股票不在自选股中"}
        
        # 移除股票
        USER_WATCHLIST[user_id].remove(symbol)
        
        return {
            "success": True,
            "message": f"已从自选股移除 {symbol}"
        }
    except Exception as e:
        logger.error(f"移除自选股失败: {e}")
        return {"success": False, "message": str(e)}

@router.put("/api/v1/market/watchlist/sort")
async def sort_watchlist(symbols: List[str], user_id: str = "default_user"):
    """调整自选股顺序"""
    try:
        if user_id not in USER_WATCHLIST:
            return {"success": False, "message": "用户自选股列表不存在"}
        
        # 验证所有股票都在自选股中
        current_symbols = set(USER_WATCHLIST[user_id])
        new_symbols = set(symbols)
        
        if current_symbols != new_symbols:
            return {"success": False, "message": "股票列表不匹配"}
        
        # 更新顺序
        USER_WATCHLIST[user_id] = symbols
        
        return {
            "success": True,
            "message": "自选股顺序已更新"
        }
    except Exception as e:
        logger.error(f"调整自选股顺序失败: {e}")
        return {"success": False, "message": str(e)}

# ============ 数据刷新 ============

@router.post("/api/v1/market/refresh")
async def refresh_market_data(request: RefreshDataRequest):
    """刷新市场数据"""
    try:
        refreshed_data = {}
        
        # 刷新行情数据
        if "quotes" in request.data_types:
            quotes = {}
            symbols = request.symbols or list(STOCKS_DB.keys())[:10]
            
            for symbol in symbols:
                quotes[symbol] = generate_mock_quote(symbol)
                # 更新缓存
                QUOTE_CACHE[symbol] = quotes[symbol]
            
            refreshed_data["quotes"] = quotes
        
        # 刷新新闻数据
        if "news" in request.data_types:
            news = [
                {
                    "id": f"news_{i}",
                    "title": f"市场要闻 {i+1}",
                    "content": "这是一条重要的市场新闻...",
                    "time": (datetime.now() - timedelta(hours=i)).isoformat(),
                    "source": random.choice(["财经网", "证券时报", "中国证券报"])
                }
                for i in range(5)
            ]
            refreshed_data["news"] = news
        
        # 刷新市场概览
        if "overview" in request.data_types:
            overview = {
                "indices": [
                    {"code": "000001", "name": "上证指数", "value": 3245.67 + random.uniform(-50, 50), "change": random.uniform(-2, 2)},
                    {"code": "399001", "name": "深证成指", "value": 10567.89 + random.uniform(-100, 100), "change": random.uniform(-2, 2)},
                    {"code": "399006", "name": "创业板指", "value": 2145.32 + random.uniform(-50, 50), "change": random.uniform(-2, 2)}
                ],
                "marketStats": {
                    "totalStocks": 4500,
                    "risers": random.randint(2000, 3000),
                    "fallers": random.randint(1000, 2000),
                    "unchanged": random.randint(300, 700)
                }
            }
            refreshed_data["overview"] = overview
        
        # 刷新板块数据
        if "sectors" in request.data_types:
            sectors = [
                {"name": "银行", "changePercent": random.uniform(-3, 3), "leadingStock": "600036"},
                {"name": "白酒", "changePercent": random.uniform(-3, 3), "leadingStock": "600519"},
                {"name": "新能源", "changePercent": random.uniform(-3, 3), "leadingStock": "300750"},
                {"name": "科技", "changePercent": random.uniform(-3, 3), "leadingStock": "002415"},
                {"name": "医药", "changePercent": random.uniform(-3, 3), "leadingStock": "600276"}
            ]
            refreshed_data["sectors"] = sectors
        
        return {
            "success": True,
            "data": refreshed_data,
            "message": "数据刷新成功",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"刷新数据失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/api/v1/market/refresh/status")
async def get_refresh_status():
    """获取数据刷新状态"""
    try:
        return {
            "success": True,
            "data": {
                "lastRefreshTime": datetime.now().isoformat(),
                "nextRefreshTime": (datetime.now() + timedelta(seconds=30)).isoformat(),
                "autoRefreshEnabled": True,
                "refreshInterval": 30,
                "dataStatus": {
                    "quotes": "normal",
                    "news": "normal",
                    "overview": "normal",
                    "sectors": "normal"
                }
            }
        }
    except Exception as e:
        logger.error(f"获取刷新状态失败: {e}")
        return {"success": False, "message": str(e)}

# ============ 批量操作 ============

@router.post("/api/v1/market/quotes/batch")
async def get_batch_quotes(symbols: List[str]):
    """批量获取股票行情"""
    try:
        quotes = {}
        
        for symbol in symbols[:50]:  # 限制最多50个
            quotes[symbol] = generate_mock_quote(symbol)
        
        return {
            "success": True,
            "data": {
                "quotes": quotes,
                "total": len(quotes),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"批量获取行情失败: {e}")
        return {"success": False, "message": str(e)}

# ============ WebSocket端点 ============

@router.websocket("/api/v1/ws/data")
async def data_websocket(websocket: WebSocket, client_id: str = None):
    """数据推送WebSocket"""
    if not client_id:
        client_id = f"client_{secrets.token_hex(8)}"
    
    await data_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理订阅请求
            if message.get("type") == "subscribe":
                symbols = message.get("symbols", [])
                data_manager.subscriptions[client_id] = symbols
                
                # 发送初始数据
                initial_quotes = {}
                for symbol in symbols:
                    initial_quotes[symbol] = generate_mock_quote(symbol)
                
                await data_manager.send_to_client(client_id, {
                    "type": "initial_quotes",
                    "data": initial_quotes
                })
            
            # 处理取消订阅
            elif message.get("type") == "unsubscribe":
                symbols = message.get("symbols", [])
                current_subs = data_manager.subscriptions.get(client_id, [])
                data_manager.subscriptions[client_id] = [s for s in current_subs if s not in symbols]
            
            # 心跳
            elif message.get("type") == "ping":
                await data_manager.send_to_client(client_id, {"type": "pong"})
                
    except WebSocketDisconnect:
        data_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Data WebSocket error: {e}")
        data_manager.disconnect(client_id)

# ============ 后台任务 ============

async def quote_update_task():
    """定期更新行情数据并推送"""
    while True:
        try:
            # 获取所有订阅的股票
            all_symbols = set()
            for symbols in data_manager.subscriptions.values():
                all_symbols.update(symbols)
            
            # 更新并推送行情
            for symbol in all_symbols:
                quote = generate_mock_quote(symbol)
                QUOTE_CACHE[symbol] = quote
                await data_manager.broadcast_quote(symbol, quote)
            
            # 每5秒更新一次
            await asyncio.sleep(5)
            
        except Exception as e:
            logger.error(f"Quote update task error: {e}")
            await asyncio.sleep(5)

# 启动后台任务
# asyncio.create_task(quote_update_task())

# 导出路由器
__all__ = ["router"]
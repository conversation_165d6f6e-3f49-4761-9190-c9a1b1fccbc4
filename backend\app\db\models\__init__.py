"""
数据模型模块
导入所有数据模型
"""

# 回测模型
from .backtest import (
    BacktestComparison,
    BacktestMetrics,
    BacktestResult,
    BacktestStatus,
    BacktestTask,
    BacktestTrade,
    BacktestType,
)

# 市场数据模型
from .market import (
    DepthData,
    KLineData,
    KLineType,
    MarketData,
    MarketType,
    Symbol,
    TradeTick,
)

# 策略模型
from .strategy import (
    RiskLevel,
    Strategy,
    StrategyInstance,
    StrategyPerformance,
    StrategySignal,
    StrategyStatus,
    StrategyTemplate,
    StrategyType,
)

# 交易模型
from .trading import (
    Account,
    Order,
    OrderSide,
    OrderStatus,
    OrderType,
    Position,
    PositionSide,
    Trade,
    TransactionLog,
)

# 用户模型
from .user import User, UserRole, UserSession, UserStatus

# 风控模型
from .risk import (
    RiskLimit,
    RiskEvent,
    PositionLimit,
    TradingRestriction,
)

# 观察列表模型
from .watchlist import WatchlistItem

__all__ = [
    # 用户模型
    "User",
    "UserRole",
    "UserStatus",
    "UserSession",
    # 市场数据模型
    "Symbol",
    "MarketData",
    "KLineData",
    "DepthData",
    "TradeTick",
    "MarketType",
    "KLineType",
    # 交易模型
    "Order",
    "Trade",
    "Position",
    "Account",
    "TransactionLog",
    "OrderType",
    "OrderSide",
    "OrderStatus",
    "PositionSide",
    # 策略模型
    "Strategy",
    "StrategyInstance",
    "StrategySignal",
    "StrategyPerformance",
    "StrategyTemplate",
    "StrategyType",
    "StrategyStatus",
    "RiskLevel",
    # 回测模型
    "BacktestTask",
    "BacktestResult",
    "BacktestTrade",
    "BacktestMetrics",
    "BacktestComparison",
    "BacktestStatus",
    "BacktestType",
    # 风控模型
    "RiskLimit",
    "RiskEvent",
    "PositionLimit",
    "TradingRestriction",
    # 观察列表模型
    "WatchlistItem",
]

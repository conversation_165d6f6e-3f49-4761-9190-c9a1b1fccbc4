"""
风控相关数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Boolean, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class RiskLimit(Base):
    """用户风控限制"""
    __tablename__ = "risk_limits"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # 风控限制参数
    daily_loss_limit = Column(Float, default=10000.0, comment="日亏损限制")
    single_trade_limit = Column(Float, default=50000.0, comment="单笔交易限制")
    position_limit = Column(Float, default=1000000.0, comment="持仓限制")
    leverage_limit = Column(Float, default=3.0, comment="杠杆限制")
    
    # 风控状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    user = relationship("User", back_populates="risk_limits")


class RiskEvent(Base):
    """风控事件记录"""
    __tablename__ = "risk_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 事件信息
    event_type = Column(String(50), nullable=False, comment="事件类型")
    severity = Column(String(20), nullable=False, comment="严重程度")
    description = Column(Text, comment="事件描述")
    
    # 触发数据
    trigger_value = Column(Float, comment="触发值")
    limit_value = Column(Float, comment="限制值")
    
    # 处理状态
    status = Column(String(20), default="active", comment="处理状态")
    resolved_at = Column(DateTime, comment="解决时间")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    user = relationship("User", back_populates="risk_events")


class PositionLimit(Base):
    """持仓限制"""
    __tablename__ = "position_limits"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    symbol = Column(String(20), nullable=False, comment="股票代码")
    
    # 限制参数
    max_position = Column(Float, nullable=False, comment="最大持仓金额")
    max_percentage = Column(Float, comment="最大持仓比例")
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    user = relationship("User")


class TradingRestriction(Base):
    """交易限制"""
    __tablename__ = "trading_restrictions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 限制类型
    restriction_type = Column(String(50), nullable=False, comment="限制类型")
    target_symbol = Column(String(20), comment="目标股票代码，为空表示全局限制")
    
    # 限制参数
    restriction_value = Column(Float, comment="限制值")
    restriction_reason = Column(Text, comment="限制原因")
    
    # 生效时间
    effective_from = Column(DateTime, default=datetime.utcnow)
    effective_until = Column(DateTime, comment="限制到期时间")
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    user = relationship("User")

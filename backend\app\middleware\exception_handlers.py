"""
异常处理器集合
为所有自定义异常提供统一的处理器方法
"""

from datetime import datetime
from fastapi import Request, status
from fastapi.responses import JSONResponse
from app.core.exceptions import BaseCustomException


class ExceptionHandlers:
    """异常处理器集合类"""

    @staticmethod
    def create_error_response(
        exc: BaseCustomException, 
        request_id: str, 
        status_code: int = None,
        error_type: str = None
    ) -> JSONResponse:
        """创建标准错误响应"""
        return JSONResponse(
            status_code=status_code or status.HTTP_400_BAD_REQUEST,
            content={
                "error": {
                    "type": error_type or exc.category.value if exc.category else "unknown_error",
                    "code": exc.error_code,
                    "message": exc.user_message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "severity": exc.severity.value,
                    "recovery_hint": exc.recovery_hint,
                    "should_retry": exc.should_retry,
                    "retry_after": exc.retry_after,
                }
            }
        )

    # 系统异常处理器
    @staticmethod
    async def handle_timeout_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_408_REQUEST_TIMEOUT, "timeout_error"
        )

    @staticmethod
    async def handle_circuit_breaker_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "circuit_breaker_error"
        )

    @staticmethod
    async def handle_quota_exceeded_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_429_TOO_MANY_REQUESTS, "quota_exceeded_error"
        )

    @staticmethod
    async def handle_data_integrity_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_422_UNPROCESSABLE_ENTITY, "data_integrity_error"
        )

    @staticmethod
    async def handle_throttling_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_429_TOO_MANY_REQUESTS, "throttling_error"
        )

    @staticmethod
    async def handle_maintenance_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "maintenance_error"
        )

    # 业务异常处理器
    @staticmethod
    async def handle_deprecation_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        response = ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_410_GONE, "deprecation_error"
        )
        response.headers["Sunset"] = "Tue, 31 Dec 2024 23:59:59 GMT"  # 示例日期
        return response

    @staticmethod
    async def handle_compliance_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_403_FORBIDDEN, "compliance_error"
        )

    @staticmethod
    async def handle_api_version_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "api_version_error"
        )

    @staticmethod
    async def handle_feature_flag_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_403_FORBIDDEN, "feature_flag_error"
        )

    # 集成异常处理器
    @staticmethod
    async def handle_integration_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_502_BAD_GATEWAY, "integration_error"
        )

    @staticmethod
    async def handle_websocket_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "websocket_error"
        )

    @staticmethod
    async def handle_cache_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "cache_error"
        )

    @staticmethod
    async def handle_queue_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "queue_error"
        )

    # 数据验证异常处理器
    @staticmethod
    async def handle_data_validation_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_422_UNPROCESSABLE_ENTITY, "data_validation_error"
        )

    # 系统监控异常处理器
    @staticmethod
    async def handle_health_check_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "health_check_error"
        )

    @staticmethod
    async def handle_metrics_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "metrics_error"
        )

    @staticmethod
    async def handle_audit_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "audit_error"
        )

    @staticmethod
    async def handle_event_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "event_error"
        )

    # 安全异常处理器
    @staticmethod
    async def handle_encryption_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "encryption_error"
        )

    @staticmethod
    async def handle_token_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_401_UNAUTHORIZED, "token_error"
        )

    @staticmethod
    async def handle_session_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_401_UNAUTHORIZED, "session_error"
        )

    # 量化平台异常处理器
    @staticmethod
    async def handle_market_closed_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "market_closed_error"
        )

    @staticmethod
    async def handle_insufficient_funds_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_402_PAYMENT_REQUIRED, "insufficient_funds_error"
        )

    @staticmethod
    async def handle_position_limit_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "position_limit_error"
        )

    @staticmethod
    async def handle_order_validation_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "order_validation_error"
        )

    @staticmethod
    async def handle_strategy_execution_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "strategy_execution_error"
        )

    @staticmethod
    async def handle_backtest_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "backtest_error"
        )

    @staticmethod
    async def handle_data_feed_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "data_feed_error"
        )

    @staticmethod
    async def handle_risk_limit_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "risk_limit_error"
        )

    # CTP异常处理器
    @staticmethod
    async def handle_ctp_connection_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "ctp_connection_error"
        )

    @staticmethod
    async def handle_ctp_auth_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_401_UNAUTHORIZED, "ctp_auth_error"
        )

    @staticmethod
    async def handle_ctp_order_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "ctp_order_error"
        )

    @staticmethod
    async def handle_ctp_data_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "ctp_data_error"
        )

    # 交易异常处理器
    @staticmethod
    async def handle_portfolio_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "portfolio_error"
        )

    @staticmethod
    async def handle_price_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "price_error"
        )

    @staticmethod
    async def handle_liquidity_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "liquidity_error"
        )

    @staticmethod
    async def handle_slippage_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "slippage_error"
        )

    @staticmethod
    async def handle_commission_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "commission_error"
        )

    @staticmethod
    async def handle_margin_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "margin_error"
        )

    # 策略异常处理器
    @staticmethod
    async def handle_indicator_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "indicator_error"
        )

    @staticmethod
    async def handle_signal_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "signal_error"
        )

    @staticmethod
    async def handle_benchmark_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "benchmark_error"
        )

    @staticmethod
    async def handle_optimization_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "optimization_error"
        )

    @staticmethod
    async def handle_simulation_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "simulation_error"
        )

    @staticmethod
    async def handle_volatility_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "volatility_error"
        )

    @staticmethod
    async def handle_correlation_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "correlation_error"
        )

    @staticmethod
    async def handle_sharpe_ratio_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "sharpe_ratio_error"
        )

    # 风险管理异常处理器
    @staticmethod
    async def handle_drawdown_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_409_CONFLICT, "drawdown_error"
        )

    @staticmethod
    async def handle_var_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "var_error"
        )

    # 系统功能异常处理器
    @staticmethod
    async def handle_alert_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "alert_error"
        )

    @staticmethod
    async def handle_notification_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "notification_error"
        )

    @staticmethod
    async def handle_report_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "report_error"
        )

    @staticmethod
    async def handle_analytics_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_500_INTERNAL_SERVER_ERROR, "analytics_error"
        )

    # 用户功能异常处理器
    @staticmethod
    async def handle_user_preference_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "user_preference_error"
        )

    @staticmethod
    async def handle_watchlist_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_400_BAD_REQUEST, "watchlist_error"
        )

    # 数据异常处理器
    @staticmethod
    async def handle_calendar_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "calendar_error"
        )

    @staticmethod
    async def handle_holiday_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_503_SERVICE_UNAVAILABLE, "holiday_error"
        )

    @staticmethod
    async def handle_data_quality_error(request: Request, exc, request_id: str, duration: float) -> JSONResponse:
        return ExceptionHandlers.create_error_response(
            exc, request_id, status.HTTP_422_UNPROCESSABLE_ENTITY, "data_quality_error"
        )
"""
增强的全局异常处理中间件
提供统一的异常捕获、日志记录、错误响应处理和监控集成
"""

import json
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, List, Union

import sentry_sdk
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from sqlalchemy.exc import (
    IntegrityError, 
    OperationalError, 
    DatabaseError as SQLDatabaseError,
    TimeoutError as SQLTimeoutError,
    DisconnectionError
)
from redis.exceptions import RedisError, ConnectionError as RedisConnectionError
from httpx import TimeoutException, ConnectError, RequestError
from pydantic import ValidationError as PydanticValidationError

from app.middleware.exception_handlers import ExceptionHandlers
from app.core.logging_config import (
    get_contextual_logger, 
    set_request_context, 
    clear_request_context,
    log_security_event,
    log_performance_metric,
    log_audit_event
)
from app.core.error_monitoring import get_error_monitor, ErrorEvent, ErrorSeverity, ErrorCategory
from app.core.exceptions import (
    AuthenticationError,
    AuthorizationError,
    BaseCustomException,
    BusinessLogicError,
    DatabaseError,
    ExternalServiceError,
    NetworkError,
    RateLimitError,
    ValidationError,
    # 新增异常类型
    TimeoutError,
    CircuitBreakerError,
    QuotaExceededError,
    DataIntegrityError,
    ThrottlingError,
    MaintenanceError,
    DeprecationError,
    ComplianceError,
    IntegrationError,
    WebSocketError,
    CacheError,
    QueueError,
    DataValidationError,
    APIVersionError,
    FeatureFlagError,
    HealthCheckError,
    MetricsError,
    AuditError,
    EventError,
    EncryptionError,
    TokenError,
    SessionError,
    # 量化平台特定异常
    MarketClosedError,
    InsufficientFundsError,
    PositionLimitError,
    OrderValidationError,
    StrategyExecutionError,
    BacktestError,
    DataFeedError,
    RiskLimitError,
    CTPConnectionError,
    CTPAuthError,
    CTPOrderError,
    CTPDataError,
    PortfolioError,
    PriceError,
    IndicatorError,
    SignalError,
    AlertError,
    NotificationError,
    ReportError,
    AnalyticsError,
    UserPreferenceError,
    WatchlistError,
    CalendarError,
    HolidayError,
    BenchmarkError,
    OptimizationError,
    SimulationError,
    DataQualityError,
    LiquidityError,
    SlippageError,
    CommissionError,
    MarginError,
    VolatilityError,
    CorrelationError,
    DrawdownError,
    SharpeRatioError,
    VaRError,
)


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """增强的全局异常处理中间件"""

    def __init__(self, app: ASGIApp, enable_detailed_logging: bool = True):
        super().__init__(app)
        self.enable_detailed_logging = enable_detailed_logging
        self.logger = get_contextual_logger("exception_middleware")
        
        # 错误统计
        self.error_counts = {}
        self.critical_errors = []
        
        # 配置数据库异常映射
        self.db_exception_mapping = {
            IntegrityError: ("INTEGRITY_CONSTRAINT_VIOLATION", "数据完整性约束违反"),
            OperationalError: ("DATABASE_OPERATIONAL_ERROR", "数据库操作错误"),
            SQLDatabaseError: ("DATABASE_ERROR", "数据库错误"),
            SQLTimeoutError: ("DATABASE_TIMEOUT", "数据库操作超时"),
            DisconnectionError: ("DATABASE_DISCONNECTION", "数据库连接断开"),
        }
        
        # 配置网络异常映射
        self.network_exception_mapping = {
            TimeoutException: ("NETWORK_TIMEOUT", "网络请求超时"),
            ConnectError: ("NETWORK_CONNECTION_ERROR", "网络连接错误"),
            RequestError: ("NETWORK_REQUEST_ERROR", "网络请求错误"),
            RedisError: ("REDIS_ERROR", "Redis操作错误"),
            RedisConnectionError: ("REDIS_CONNECTION_ERROR", "Redis连接错误"),
        }
        
        self.error_handlers = {
            # 基础异常
            ValidationError: self._handle_validation_error,
            AuthenticationError: self._handle_authentication_error,
            AuthorizationError: self._handle_authorization_error,
            DatabaseError: self._handle_database_error,
            NetworkError: self._handle_network_error,
            ExternalServiceError: self._handle_external_service_error,
            BusinessLogicError: self._handle_business_logic_error,
            RateLimitError: self._handle_rate_limit_error,
            
            # 系统异常
            TimeoutError: ExceptionHandlers.handle_timeout_error,
            CircuitBreakerError: ExceptionHandlers.handle_circuit_breaker_error,
            QuotaExceededError: ExceptionHandlers.handle_quota_exceeded_error,
            DataIntegrityError: ExceptionHandlers.handle_data_integrity_error,
            ThrottlingError: ExceptionHandlers.handle_throttling_error,
            MaintenanceError: ExceptionHandlers.handle_maintenance_error,
            
            # 业务异常
            DeprecationError: ExceptionHandlers.handle_deprecation_error,
            ComplianceError: ExceptionHandlers.handle_compliance_error,
            APIVersionError: ExceptionHandlers.handle_api_version_error,
            FeatureFlagError: ExceptionHandlers.handle_feature_flag_error,
            
            # 集成异常
            IntegrationError: ExceptionHandlers.handle_integration_error,
            WebSocketError: ExceptionHandlers.handle_websocket_error,
            CacheError: ExceptionHandlers.handle_cache_error,
            QueueError: ExceptionHandlers.handle_queue_error,
            
            # 数据验证异常
            DataValidationError: ExceptionHandlers.handle_data_validation_error,
            
            # 系统监控异常
            HealthCheckError: ExceptionHandlers.handle_health_check_error,
            MetricsError: ExceptionHandlers.handle_metrics_error,
            AuditError: ExceptionHandlers.handle_audit_error,
            EventError: ExceptionHandlers.handle_event_error,
            
            # 安全异常
            EncryptionError: ExceptionHandlers.handle_encryption_error,
            TokenError: ExceptionHandlers.handle_token_error,
            SessionError: ExceptionHandlers.handle_session_error,
            
            # 量化平台异常
            MarketClosedError: ExceptionHandlers.handle_market_closed_error,
            InsufficientFundsError: ExceptionHandlers.handle_insufficient_funds_error,
            PositionLimitError: ExceptionHandlers.handle_position_limit_error,
            OrderValidationError: ExceptionHandlers.handle_order_validation_error,
            StrategyExecutionError: ExceptionHandlers.handle_strategy_execution_error,
            BacktestError: ExceptionHandlers.handle_backtest_error,
            DataFeedError: ExceptionHandlers.handle_data_feed_error,
            RiskLimitError: ExceptionHandlers.handle_risk_limit_error,
            
            # CTP异常
            CTPConnectionError: ExceptionHandlers.handle_ctp_connection_error,
            CTPAuthError: ExceptionHandlers.handle_ctp_auth_error,
            CTPOrderError: ExceptionHandlers.handle_ctp_order_error,
            CTPDataError: ExceptionHandlers.handle_ctp_data_error,
            
            # 交易异常
            PortfolioError: ExceptionHandlers.handle_portfolio_error,
            PriceError: ExceptionHandlers.handle_price_error,
            LiquidityError: ExceptionHandlers.handle_liquidity_error,
            SlippageError: ExceptionHandlers.handle_slippage_error,
            CommissionError: ExceptionHandlers.handle_commission_error,
            MarginError: ExceptionHandlers.handle_margin_error,
            
            # 策略异常
            IndicatorError: ExceptionHandlers.handle_indicator_error,
            SignalError: ExceptionHandlers.handle_signal_error,
            BenchmarkError: ExceptionHandlers.handle_benchmark_error,
            OptimizationError: ExceptionHandlers.handle_optimization_error,
            SimulationError: ExceptionHandlers.handle_simulation_error,
            VolatilityError: ExceptionHandlers.handle_volatility_error,
            CorrelationError: ExceptionHandlers.handle_correlation_error,
            SharpeRatioError: ExceptionHandlers.handle_sharpe_ratio_error,
            
            # 风险管理异常
            DrawdownError: ExceptionHandlers.handle_drawdown_error,
            VaRError: ExceptionHandlers.handle_var_error,
            
            # 系统功能异常
            AlertError: ExceptionHandlers.handle_alert_error,
            NotificationError: ExceptionHandlers.handle_notification_error,
            ReportError: ExceptionHandlers.handle_report_error,
            AnalyticsError: ExceptionHandlers.handle_analytics_error,
            
            # 用户功能异常
            UserPreferenceError: ExceptionHandlers.handle_user_preference_error,
            WatchlistError: ExceptionHandlers.handle_watchlist_error,
            
            # 数据异常
            CalendarError: ExceptionHandlers.handle_calendar_error,
            HolidayError: ExceptionHandlers.handle_holiday_error,
            DataQualityError: ExceptionHandlers.handle_data_quality_error,
            
            # 通用异常处理器（必须放在最后）
            BaseCustomException: self._handle_custom_exception,
        }

    async def dispatch(self, request: Request, call_next):
        """处理请求并捕获异常"""
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 设置请求上下文到日志系统
        user_id = getattr(request.state, 'user_id', '')
        session_id = getattr(request.state, 'session_id', request.headers.get('session-id', ''))
        set_request_context(request_id, user_id, session_id)

        # 记录请求开始
        start_time = datetime.utcnow()
        
        try:
            # 记录敏感操作的审计日志
            if self._is_sensitive_operation(request):
                log_audit_event(
                    action=f"{request.method} {request.url.path}",
                    resource=request.url.path,
                    user_id=user_id,
                    details={
                        "ip": self._get_client_ip(request),
                        "user_agent": request.headers.get("user-agent"),
                        "query_params": dict(request.query_params)
                    }
                )
            
            # 执行请求
            response = await call_next(request)
            response.headers["X-Request-ID"] = request_id
            
            # 记录请求完成
            duration = (datetime.utcnow() - start_time).total_seconds()
            await self._log_successful_request(request, response, duration, request_id)
            
            # 记录性能指标
            if duration > 1.0:  # 慢请求阈值
                log_performance_metric(
                    operation=f"{request.method} {request.url.path}",
                    duration=duration,
                    metadata={
                        "status_code": response.status_code,
                        "request_id": request_id
                    }
                )
            
            return response
            
        except Exception as exc:
            # 记录异常持续时间
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            # 处理异常并返回响应
            response = await self._handle_exception(request, exc, request_id, duration)
            response.headers["X-Request-ID"] = request_id
            
            return response
        
        finally:
            # 清理请求上下文
            clear_request_context()

    async def _handle_exception(
        self, 
        request: Request, 
        exc: Exception, 
        request_id: str,
        duration: float
    ) -> JSONResponse:
        """智能异常处理器 - 增强版本"""
        
        # 更新错误统计
        self._update_error_statistics(exc)
        
        # 首先尝试处理第三方库异常
        third_party_response = await self._handle_third_party_exceptions(
            request, exc, request_id, duration
        )
        if third_party_response:
            await self._log_exception(request, exc, third_party_response, request_id, duration)
            return third_party_response
        
        # 获取自定义异常处理器
        handler = self._get_exception_handler(exc)
        
        if handler:
            error_response = await handler(request, exc, request_id, duration)
        else:
            # 未知异常的智能处理
            error_response = await self._handle_unknown_exception(
                request, exc, request_id, duration
            )

        # 记录异常日志
        await self._log_exception(request, exc, error_response, request_id, duration)
        
        # 记录到错误监控系统
        await self._record_error_event(request, exc, request_id, error_response.status_code)
        
        # 检查是否为关键错误
        if self._is_critical_error(exc, error_response.status_code):
            await self._handle_critical_error(request, exc, request_id)
        
        # 上报到监控系统
        if self._should_report_to_monitoring(exc):
            await self._report_to_monitoring(request, exc, request_id, error_response.status_code)
        
        # 上报到Sentry（生产环境）
        if self._should_report_to_sentry(exc):
            await self._report_to_sentry(request, exc, request_id)

        return error_response

    def _get_exception_handler(self, exc: Exception):
        """获取异常对应的处理器"""
        for exc_type, handler in self.error_handlers.items():
            if isinstance(exc, exc_type):
                return handler
        return None

    async def _handle_validation_error(
        self, request: Request, exc: ValidationError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理验证错误"""
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": {
                    "type": "validation_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_authentication_error(
        self, request: Request, exc: AuthenticationError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理认证错误"""
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "error": {
                    "type": "authentication_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
            headers={"WWW-Authenticate": "Bearer"},
        )

    async def _handle_authorization_error(
        self, request: Request, exc: AuthorizationError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理授权错误"""
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "error": {
                    "type": "authorization_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_database_error(
        self, request: Request, exc: DatabaseError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理数据库错误"""
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "type": "database_error",
                    "message": "数据库操作失败",
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_network_error(
        self, request: Request, exc: NetworkError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理网络错误"""
        return JSONResponse(
            status_code=status.HTTP_502_BAD_GATEWAY,
            content={
                "error": {
                    "type": "network_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_external_service_error(
        self, request: Request, exc: ExternalServiceError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理外部服务错误"""
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "error": {
                    "type": "external_service_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_business_logic_error(
        self, request: Request, exc: BusinessLogicError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理业务逻辑错误"""
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": {
                    "type": "business_logic_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_rate_limit_error(
        self, request: Request, exc: RateLimitError, request_id: str, duration: float
    ) -> JSONResponse:
        """处理频率限制错误"""
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": {
                    "type": "rate_limit_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
            headers={"Retry-After": "60"},
        )

    async def _handle_custom_exception(
        self, request: Request, exc: BaseCustomException, request_id: str, duration: float
    ) -> JSONResponse:
        """处理自定义异常"""
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": {
                    "type": "custom_error",
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _handle_unknown_exception(
        self, request: Request, exc: Exception, request_id: str, duration: float
    ) -> JSONResponse:
        """处理未知异常"""
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "type": "internal_server_error",
                    "message": "服务器内部错误",
                    "request_id": request_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            },
        )

    async def _log_request(
        self, 
        request: Request, 
        response: Response, 
        duration: float, 
        request_id: str
    ):
        """记录正常请求日志"""
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "duration": duration,
            "user_agent": request.headers.get("user-agent"),
            "client_ip": self._get_client_ip(request),
            "timestamp": datetime.utcnow().isoformat(),
        }

        # 根据状态码选择日志级别
        if response.status_code >= 500:
            logger.error("Request completed with server error", **log_data)
        elif response.status_code >= 400:
            logger.warning("Request completed with client error", **log_data)
        else:
            logger.info("Request completed successfully", **log_data)

    async def _log_exception(
        self,
        request: Request,
        exc: Exception,
        error_response: JSONResponse,
        request_id: str,
        duration: float,
    ):
        """记录异常日志"""
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "status_code": error_response.status_code,
            "duration": duration,
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "user_agent": request.headers.get("user-agent"),
            "client_ip": self._get_client_ip(request),
            "timestamp": datetime.utcnow().isoformat(),
        }

        # 添加堆栈跟踪（仅用于严重错误）
        if error_response.status_code >= 500:
            log_data["traceback"] = traceback.format_exc()
            logger.error("Request failed with exception", **log_data)
        else:
            logger.warning("Request failed with handled exception", **log_data)

    def _should_report_to_sentry(self, exc: Exception) -> bool:
        """判断是否应该上报到Sentry"""
        # 不上报的异常类型
        non_reportable_exceptions = (
            ValidationError,
            AuthenticationError,
            AuthorizationError,
            RateLimitError,
        )
        
        return not isinstance(exc, non_reportable_exceptions)

    async def _report_to_sentry(self, request: Request, exc: Exception, request_id: str):
        """上报异常到Sentry"""
        try:
            with sentry_sdk.push_scope() as scope:
                # 设置标签
                scope.set_tag("request_id", request_id)
                scope.set_tag("method", request.method)
                scope.set_tag("path", request.url.path)
                
                # 设置上下文
                scope.set_context("request", {
                    "url": str(request.url),
                    "method": request.method,
                    "headers": dict(request.headers),
                    "query_params": dict(request.query_params),
                })
                
                # 设置用户信息（如果有）
                if hasattr(request.state, "user"):
                    scope.set_user({
                        "id": getattr(request.state.user, "id", None),
                        "username": getattr(request.state.user, "username", None),
                    })
                
                # 捕获异常
                sentry_sdk.capture_exception(exc)
                
        except Exception as sentry_error:
            logger.warning(f"Failed to report to Sentry: {sentry_error}")

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return forwarded.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 回退到连接信息
        return request.client.host if request.client else "unknown"
    
    def _is_sensitive_operation(self, request: Request) -> bool:
        """判断是否为敏感操作"""
        sensitive_paths = [
            "/api/v1/auth/login",
            "/api/v1/auth/logout", 
            "/api/v1/trading/order",
            "/api/v1/trading/cancel",
            "/api/v1/user/profile",
            "/api/v1/user/change-password"
        ]
        
        sensitive_methods = ["POST", "PUT", "DELETE", "PATCH"]
        
        return (
            request.url.path in sensitive_paths or 
            request.method in sensitive_methods or
            "password" in str(request.url.query).lower() or
            "token" in str(request.url.query).lower()
        )
    
    async def _log_successful_request(
        self, 
        request: Request, 
        response: Response, 
        duration: float, 
        request_id: str
    ):
        """记录成功请求的详细日志"""
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "duration": duration,
            "user_agent": request.headers.get("user-agent"),
            "client_ip": self._get_client_ip(request),
            "timestamp": datetime.utcnow().isoformat(),
            "content_length": response.headers.get("content-length", "0"),
        }

        # 根据状态码和性能选择日志级别
        if response.status_code >= 400:
            self.logger.warning("Request completed with client error", **log_data)
        elif duration > 5.0:
            self.logger.warning("Slow request detected", **log_data)
        elif duration > 1.0:
            self.logger.info("Request completed (slow)", **log_data)
        else:
            self.logger.debug("Request completed successfully", **log_data)
    
    def _update_error_statistics(self, exc: Exception):
        """更新错误统计"""
        exc_type = type(exc).__name__
        self.error_counts[exc_type] = self.error_counts.get(exc_type, 0) + 1
        
        # 记录关键错误
        if self._is_critical_error(exc, 500):
            self.critical_errors.append({
                "type": exc_type,
                "message": str(exc),
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # 保持关键错误列表不超过100条
            if len(self.critical_errors) > 100:
                self.critical_errors = self.critical_errors[-100:]
    
    async def _handle_third_party_exceptions(
        self, 
        request: Request, 
        exc: Exception, 
        request_id: str, 
        duration: float
    ) -> Optional[JSONResponse]:
        """处理第三方库异常"""
        
        # 处理数据库异常
        for db_exc_type, (error_code, message) in self.db_exception_mapping.items():
            if isinstance(exc, db_exc_type):
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={
                        "error": {
                            "type": "database_error",
                            "code": error_code,
                            "message": message,
                            "details": {"original_error": str(exc)} if self.enable_detailed_logging else {},
                            "request_id": request_id,
                            "timestamp": datetime.utcnow().isoformat(),
                            "recovery_hint": "请稍后重试，如果问题持续请联系技术支持",
                            "should_retry": True,
                            "retry_after": 30
                        }
                    }
                )
        
        # 处理网络异常
        for net_exc_type, (error_code, message) in self.network_exception_mapping.items():
            if isinstance(exc, net_exc_type):
                return JSONResponse(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    content={
                        "error": {
                            "type": "network_error",
                            "code": error_code,
                            "message": message,
                            "details": {"original_error": str(exc)} if self.enable_detailed_logging else {},
                            "request_id": request_id,
                            "timestamp": datetime.utcnow().isoformat(),
                            "recovery_hint": "网络异常，请检查网络连接后重试",
                            "should_retry": True,
                            "retry_after": 60
                        }
                    }
                )
        
        # 处理Pydantic验证异常
        if isinstance(exc, PydanticValidationError):
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "error": {
                        "type": "validation_error",
                        "code": "PYDANTIC_VALIDATION_ERROR",
                        "message": "请求数据验证失败",
                        "details": {
                            "validation_errors": exc.errors()
                        },
                        "request_id": request_id,
                        "timestamp": datetime.utcnow().isoformat(),
                        "recovery_hint": "请检查请求数据格式和字段要求"
                    }
                }
            )
        
        return None
    
    def _is_critical_error(self, exc: Exception, status_code: int) -> bool:
        """判断是否为关键错误"""
        critical_exceptions = (
            SQLDatabaseError,
            DisconnectionError,
            MemoryError,
            SystemError,
            OSError
        )
        
        return (
            isinstance(exc, critical_exceptions) or
            status_code >= 500 or
            "database" in str(exc).lower() or
            "connection" in str(exc).lower() or
            "timeout" in str(exc).lower()
        )
    
    async def _handle_critical_error(self, request: Request, exc: Exception, request_id: str):
        """处理关键错误"""
        # 记录安全事件
        log_security_event(
            event_type="critical_error",
            severity="high",
            details={
                "exception_type": type(exc).__name__,
                "exception_message": str(exc),
                "request_path": request.url.path,
                "request_method": request.method,
                "client_ip": self._get_client_ip(request),
                "request_id": request_id
            }
        )
        
        # 发送告警（这里可以集成告警系统）
        await self._send_critical_error_alert(exc, request, request_id)
    
    async def _send_critical_error_alert(self, exc: Exception, request: Request, request_id: str):
        """发送关键错误告警"""
        # 这里可以集成各种告警系统（邮件、短信、钉钉、企业微信等）
        alert_message = f"""
        关键错误告警:
        - 错误类型: {type(exc).__name__}
        - 错误信息: {str(exc)}
        - 请求路径: {request.url.path}
        - 请求ID: {request_id}
        - 时间: {datetime.utcnow().isoformat()}
        - 客户端IP: {self._get_client_ip(request)}
        """
        
        self.logger.critical("Critical error alert", alert_message=alert_message)
    
    def _should_report_to_monitoring(self, exc: Exception) -> bool:
        """判断是否应该上报到监控系统"""
        return True  # 所有异常都上报到监控系统
    
    async def _report_to_monitoring(
        self, 
        request: Request, 
        exc: Exception, 
        request_id: str, 
        status_code: int
    ):
        """上报到监控系统"""
        try:
            # 这里可以集成Prometheus、Grafana等监控系统
            metric_data = {
                "exception_type": type(exc).__name__,
                "status_code": status_code,
                "path": request.url.path,
                "method": request.method,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.logger.info("Exception reported to monitoring", **metric_data)
            
        except Exception as monitoring_error:
            self.logger.error(f"Failed to report to monitoring: {monitoring_error}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            "error_counts": self.error_counts,
            "critical_errors": self.critical_errors[-10:],  # 最近10个关键错误
            "total_errors": sum(self.error_counts.values()),
            "most_common_errors": sorted(
                self.error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
    
    async def _record_error_event(
        self, 
        request: Request, 
        exc: Exception, 
        request_id: str, 
        status_code: int
    ):
        """记录错误事件到监控系统"""
        try:
            error_monitor = get_error_monitor()
            
            # 从异常中提取信息
            if isinstance(exc, BaseCustomException):
                error_code = exc.error_code
                severity = exc.severity
                category = exc.category
                message = exc.user_message
                details = exc.details
            else:
                error_code = type(exc).__name__.upper()
                severity = ErrorSeverity.HIGH if status_code >= 500 else ErrorSeverity.MEDIUM
                category = ErrorCategory.SYSTEM
                message = str(exc)
                details = {"original_type": type(exc).__name__}
            
            # 创建错误事件
            error_event = ErrorEvent(
                id=request_id,
                timestamp=datetime.utcnow(),
                exception_type=type(exc).__name__,
                error_code=error_code,
                message=message,
                severity=severity,
                category=category,
                request_id=request_id,
                user_id=getattr(request.state, 'user_id', None),
                path=request.url.path,
                method=request.method,
                client_ip=self._get_client_ip(request),
                details=details,
                stack_trace=traceback.format_exc() if status_code >= 500 else None
            )
            
            # 添加到监控系统
            error_monitor.add_error_event(error_event)
            
        except Exception as monitoring_error:
            self.logger.error("Failed to record error event to monitoring system", 
                            error=str(monitoring_error), exc_info=True)


# 需要在 app/core/exceptions.py 中添加的新异常类
class BusinessLogicError(BaseCustomException):
    """业务逻辑错误"""
    pass


class ExternalServiceError(BaseCustomException):
    """外部服务错误"""
    pass
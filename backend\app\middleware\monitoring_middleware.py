"""
监控中间件
实现API调用监控、性能监控、错误监控等功能
"""

import time
from datetime import datetime
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from app.core.monitoring import system_monitor


class MonitoringMiddleware(BaseHTTPMiddleware):
    """监控中间件"""
    
    def __init__(self, app, enable_detailed_logging: bool = False):
        super().__init__(app)
        self.enable_detailed_logging = enable_detailed_logging
        self.request_count = 0
        self.error_count = 0
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并收集监控数据"""
        start_time = time.time()
        self.request_count += 1
        
        # 记录请求开始
        if self.enable_detailed_logging:
            logger.info(f"API请求开始: {request.method} {request.url.path}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 毫秒
            
            # 记录API响应时间
            system_monitor.record_metric("api.response_time_ms", response_time)
            
            # 记录API调用计数
            system_monitor.record_metric("api.request_count", self.request_count)
            
            # 记录状态码
            if response.status_code >= 400:
                self.error_count += 1
                system_monitor.record_metric("api.error_count", self.error_count)
                
                # 记录错误率
                error_rate = (self.error_count / self.request_count) * 100
                system_monitor.record_metric("api.error_rate_percent", error_rate)
                
                if self.enable_detailed_logging:
                    logger.warning(f"API请求错误: {request.method} {request.url.path} - {response.status_code}")
            
            # 记录成功的请求
            if self.enable_detailed_logging and response.status_code < 400:
                logger.info(f"API请求完成: {request.method} {request.url.path} - {response_time:.2f}ms")
            
            # 添加监控头信息
            response.headers["X-Response-Time"] = f"{response_time:.2f}ms"
            response.headers["X-Request-ID"] = str(self.request_count)
            
            return response
            
        except Exception as e:
            # 记录异常
            response_time = (time.time() - start_time) * 1000
            self.error_count += 1
            
            system_monitor.record_metric("api.response_time_ms", response_time)
            system_monitor.record_metric("api.error_count", self.error_count)
            
            error_rate = (self.error_count / self.request_count) * 100
            system_monitor.record_metric("api.error_rate_percent", error_rate)
            
            logger.error(f"API请求异常: {request.method} {request.url.path} - {str(e)}")
            
            # 重新抛出异常
            raise e


class DataSourceMonitoringMixin:
    """数据源监控混入类"""
    
    def __init__(self):
        self.source_stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'avg_response_time': 0
        }
    
    async def monitor_data_source_call(self, source_name: str, method_name: str, call_func):
        """监控数据源调用"""
        start_time = time.time()
        self.source_stats['total_calls'] += 1
        
        try:
            result = await call_func()
            
            # 记录成功调用
            response_time = (time.time() - start_time) * 1000
            self.source_stats['successful_calls'] += 1
            
            # 更新平均响应时间
            if self.source_stats['avg_response_time'] == 0:
                self.source_stats['avg_response_time'] = response_time
            else:
                self.source_stats['avg_response_time'] = (
                    self.source_stats['avg_response_time'] + response_time
                ) / 2
            
            # 记录数据源指标
            system_monitor.record_metric(f"datasource.{source_name}.response_time_ms", response_time)
            system_monitor.record_metric(f"datasource.{source_name}.success_count", self.source_stats['successful_calls'])
            
            # 计算可用性
            availability = (self.source_stats['successful_calls'] / self.source_stats['total_calls']) * 100
            system_monitor.record_metric(f"datasource.{source_name}.availability_percent", availability)
            
            logger.debug(f"数据源调用成功: {source_name}.{method_name} - {response_time:.2f}ms")
            return result
            
        except Exception as e:
            # 记录失败调用
            response_time = (time.time() - start_time) * 1000
            self.source_stats['failed_calls'] += 1
            
            # 记录数据源指标
            system_monitor.record_metric(f"datasource.{source_name}.response_time_ms", response_time)
            system_monitor.record_metric(f"datasource.{source_name}.error_count", self.source_stats['failed_calls'])
            
            # 计算可用性
            availability = (self.source_stats['successful_calls'] / self.source_stats['total_calls']) * 100
            system_monitor.record_metric(f"datasource.{source_name}.availability_percent", availability)
            
            logger.error(f"数据源调用失败: {source_name}.{method_name} - {str(e)}")
            raise e


class CacheMonitoringMixin:
    """缓存监控混入类"""
    
    def __init__(self):
        self.cache_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_stats['total_requests'] += 1
        self.cache_stats['cache_hits'] += 1
        
        # 计算缓存命中率
        hit_rate = (self.cache_stats['cache_hits'] / self.cache_stats['total_requests']) * 100
        system_monitor.record_metric("cache.hit_rate_percent", hit_rate)
        system_monitor.record_metric("cache.hits_count", self.cache_stats['cache_hits'])
        
        logger.debug(f"缓存命中: 命中率 {hit_rate:.2f}%")
    
    def record_cache_miss(self):
        """记录缓存未命中"""
        self.cache_stats['total_requests'] += 1
        self.cache_stats['cache_misses'] += 1
        
        # 计算缓存命中率
        hit_rate = (self.cache_stats['cache_hits'] / self.cache_stats['total_requests']) * 100
        system_monitor.record_metric("cache.hit_rate_percent", hit_rate)
        system_monitor.record_metric("cache.misses_count", self.cache_stats['cache_misses'])
        
        logger.debug(f"缓存未命中: 命中率 {hit_rate:.2f}%")
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计"""
        if self.cache_stats['total_requests'] == 0:
            return {
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'hit_rate_percent': 0
            }
        
        hit_rate = (self.cache_stats['cache_hits'] / self.cache_stats['total_requests']) * 100
        
        return {
            'total_requests': self.cache_stats['total_requests'],
            'cache_hits': self.cache_stats['cache_hits'],
            'cache_misses': self.cache_stats['cache_misses'],
            'hit_rate_percent': round(hit_rate, 2)
        }


def monitor_function_performance(metric_name: str):
    """函数性能监控装饰器"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                response_time = (time.time() - start_time) * 1000
                system_monitor.record_metric(f"{metric_name}.response_time_ms", response_time)
                system_monitor.record_metric(f"{metric_name}.success_count", 1)
                return result
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                system_monitor.record_metric(f"{metric_name}.response_time_ms", response_time)
                system_monitor.record_metric(f"{metric_name}.error_count", 1)
                raise e
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                response_time = (time.time() - start_time) * 1000
                system_monitor.record_metric(f"{metric_name}.response_time_ms", response_time)
                system_monitor.record_metric(f"{metric_name}.success_count", 1)
                return result
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                system_monitor.record_metric(f"{metric_name}.response_time_ms", response_time)
                system_monitor.record_metric(f"{metric_name}.error_count", 1)
                raise e
        
        # 检查是否是异步函数
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 导出主要组件
__all__ = [
    'MonitoringMiddleware',
    'DataSourceMonitoringMixin',
    'CacheMonitoringMixin',
    'monitor_function_performance'
]

"""
性能监控中间件
用于记录请求性能指标和系统监控
"""

import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.performance import performance_monitor
from app.api.v1.monitoring import record_request_metrics


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.start_time = time.time()

    async def dispatch(self, request: Request, call_next):
        # 记录请求开始时间
        start_time = time.time()

        # 更新活跃连接数
        performance_monitor.metrics["active_connections"] += 1

        try:
            # 处理请求
            response = await call_next(request)

            # 记录请求完成时间
            process_time = time.time() - start_time

            # 记录性能指标
            performance_monitor.record_request(
                process_time, success=200 <= response.status_code < 400
            )

            # 记录Prometheus指标
            record_request_metrics(request, response)

            # 添加性能头部
            response.headers["X-Process-Time"] = str(process_time)

            return response

        except Exception as e:
            # 记录错误
            process_time = time.time() - start_time
            performance_monitor.record_request(process_time, success=False)

            # 重新抛出异常
            raise e

        finally:
            # 更新活跃连接数
            performance_monitor.metrics["active_connections"] -= 1

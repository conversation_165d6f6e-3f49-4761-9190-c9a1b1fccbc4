"""
统一数据库模型导入
"""

# 导入所有模型以确保 Alembic 能够检测到它们
from ..db.models.user import (
    User, Role, Permission, UserRole, UserStatus,
    user_roles, role_permissions
)

from ..db.models.trading import (
    Order, Trade, Position, Account,
    OrderStatus, OrderType, OrderSide, PositionSide
)

from ..db.models.strategy import Strategy
from ..db.models.backtest import BacktestTask, BacktestResult

from ..db.models.market import (
    Symbol, MarketData, KLineData, TradeTick
)

from ..db.models.ctp_models import (
    CTPOrder, CTPTrade, CTPPosition, CTPAccount
)

# 导出所有模型
__all__ = [
    # 用户相关模型
    "User",
    "Role",
    "Permission",
    "UserRole",
    "UserStatus",
    "user_roles",
    "role_permissions",
    
    # 交易相关模型
    "Order",
    "Trade", 
    "Position",
    "Account",
    "OrderStatus",
    "OrderType", 
    "OrderSide",
    "PositionSide",
    
    # 策略相关模型
    "Strategy",
    "BacktestTask",
    "BacktestResult",
    
    # 市场数据模型
    "Symbol",
    "MarketData",
    "KLineData",
    "TradeTick",
    
    # CTP相关模型
    "CTPOrder",
    "CTPTrade",
    "CTPPosition",
    "CTPAccount",
]

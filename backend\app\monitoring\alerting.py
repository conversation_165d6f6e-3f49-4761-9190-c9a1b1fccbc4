"""
报警系统
用于监控各种指标并触发报警
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib

from loguru import logger
from app.core.logging_config import log_security_event, get_contextual_logger
from app.monitoring.metrics_collector import metrics_collector


class AlertSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"
    SILENCED = "silenced"


@dataclass
class AlertRule:
    name: str
    description: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    severity: AlertSeverity
    duration: int = 300  # 持续时间（秒）
    enabled: bool = True
    notification_channels: List[str] = field(default_factory=list)
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class Alert:
    rule_name: str
    message: str
    severity: AlertSeverity
    status: AlertStatus
    created_at: datetime
    updated_at: datetime
    value: float
    threshold: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def alert_id(self) -> str:
        return f"{self.rule_name}_{int(self.created_at.timestamp())}"


class AlertManager:
    """报警管理器"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notification_handlers = {}
        self.logger = get_contextual_logger("alerting")
        
        # 初始化默认报警规则
        self._init_default_rules()
        
        # 初始化通知处理器
        self._init_notification_handlers()
        
        # 启动监控循环
        asyncio.create_task(self._monitoring_loop())
    
    def _init_default_rules(self):
        """初始化默认报警规则"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU使用率过高",
                metric_name="system_cpu_usage",
                condition=">",
                threshold=80.0,
                severity=AlertSeverity.HIGH,
                duration=300,
                notification_channels=["email", "webhook"]
            ),
            AlertRule(
                name="high_memory_usage",
                description="内存使用率过高",
                metric_name="system_memory_usage_percent",
                condition=">",
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                duration=300,
                notification_channels=["email", "webhook"]
            ),
            AlertRule(
                name="high_error_rate",
                description="错误率过高",
                metric_name="error_rate",
                condition=">",
                threshold=5.0,
                severity=AlertSeverity.CRITICAL,
                duration=60,
                notification_channels=["email", "webhook", "sms"]
            ),
            AlertRule(
                name="database_connection_exhaustion",
                description="数据库连接池耗尽",
                metric_name="database_connections_active",
                condition=">",
                threshold=45,
                severity=AlertSeverity.CRITICAL,
                duration=30,
                notification_channels=["email", "webhook"]
            ),
            AlertRule(
                name="trading_latency_high",
                description="交易延迟过高",
                metric_name="trading_latency_avg",
                condition=">",
                threshold=1.0,
                severity=AlertSeverity.HIGH,
                duration=120,
                notification_channels=["email", "webhook"]
            ),
            AlertRule(
                name="strategy_loss_significant",
                description="策略亏损严重",
                metric_name="strategy_total_pnl",
                condition="<",
                threshold=-10000.0,
                severity=AlertSeverity.CRITICAL,
                duration=0,  # 立即触发
                notification_channels=["email", "webhook", "sms"]
            ),
            AlertRule(
                name="failed_login_attempts",
                description="登录失败次数过多",
                metric_name="failed_login_attempts_per_minute",
                condition=">",
                threshold=10,
                severity=AlertSeverity.MEDIUM,
                duration=60,
                notification_channels=["email", "webhook"]
            ),
            AlertRule(
                name="disk_space_low",
                description="磁盘空间不足",
                metric_name="disk_usage_percent",
                condition=">",
                threshold=90.0,
                severity=AlertSeverity.HIGH,
                duration=300,
                notification_channels=["email", "webhook"]
            )
        ]
        
        for rule in default_rules:
            self.add_rule(rule)
    
    def _init_notification_handlers(self):
        """初始化通知处理器"""
        self.notification_handlers = {
            "email": self._send_email_notification,
            "webhook": self._send_webhook_notification,
            "sms": self._send_sms_notification,
            "slack": self._send_slack_notification
        }
    
    def add_rule(self, rule: AlertRule):
        """添加报警规则"""
        self.rules[rule.name] = rule
        self.logger.info(f"Added alert rule: {rule.name}", rule_name=rule.name)
    
    def remove_rule(self, rule_name: str):
        """移除报警规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            self.logger.info(f"Removed alert rule: {rule_name}", rule_name=rule_name)
    
    def update_rule(self, rule_name: str, updates: Dict[str, Any]):
        """更新报警规则"""
        if rule_name in self.rules:
            rule = self.rules[rule_name]
            for key, value in updates.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            self.logger.info(f"Updated alert rule: {rule_name}", rule_name=rule_name)
    
    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                await self._check_rules()
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _check_rules(self):
        """检查所有报警规则"""
        current_time = datetime.utcnow()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                # 获取指标值
                metric_value = await self._get_metric_value(rule.metric_name)
                if metric_value is None:
                    continue
                
                # 检查条件
                condition_met = self._evaluate_condition(
                    metric_value, rule.condition, rule.threshold
                )
                
                if condition_met:
                    await self._handle_condition_met(rule, metric_value, current_time)
                else:
                    await self._handle_condition_not_met(rule_name, current_time)
                    
            except Exception as e:
                self.logger.error(
                    f"Error checking rule {rule_name}: {e}",
                    rule_name=rule_name,
                    error=str(e)
                )
    
    async def _get_metric_value(self, metric_name: str) -> Optional[float]:
        """获取指标值"""
        # 这里可以扩展支持不同的指标源
        if metric_name == "system_cpu_usage":
            import psutil
            return psutil.cpu_percent()
        elif metric_name == "system_memory_usage_percent":
            import psutil
            return psutil.virtual_memory().percent
        elif metric_name.startswith("strategy_"):
            # 从策略指标中获取
            summary = metrics_collector.get_custom_metrics_summary(metric_name)
            return summary.get("latest", 0) if summary else None
        else:
            # 从自定义指标中获取
            summary = metrics_collector.get_custom_metrics_summary(metric_name)
            return summary.get("latest", 0) if summary else None
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """评估条件"""
        if condition == ">":
            return value > threshold
        elif condition == "<":
            return value < threshold
        elif condition == ">=":
            return value >= threshold
        elif condition == "<=":
            return value <= threshold
        elif condition == "==":
            return value == threshold
        elif condition == "!=":
            return value != threshold
        else:
            return False
    
    async def _handle_condition_met(self, rule: AlertRule, value: float, current_time: datetime):
        """处理条件满足的情况"""
        alert_key = f"{rule.name}_active"
        
        if alert_key in self.active_alerts:
            # 更新现有报警
            alert = self.active_alerts[alert_key]
            alert.updated_at = current_time
            alert.value = value
        else:
            # 检查是否需要等待持续时间
            if rule.duration > 0:
                # 这里应该实现持续时间逻辑
                # 为简化，暂时直接创建报警
                pass
            
            # 创建新报警
            alert = Alert(
                rule_name=rule.name,
                message=f"{rule.description}: 当前值 {value:.2f}, 阈值 {rule.threshold:.2f}",
                severity=rule.severity,
                status=AlertStatus.ACTIVE,
                created_at=current_time,
                updated_at=current_time,
                value=value,
                threshold=rule.threshold,
                metadata={
                    "metric_name": rule.metric_name,
                    "condition": rule.condition,
                    "tags": rule.tags
                }
            )
            
            self.active_alerts[alert_key] = alert
            self.alert_history.append(alert)
            
            # 发送通知
            await self._send_notifications(alert, rule)
            
            # 记录安全事件（如果是安全相关的报警）
            if "security" in rule.tags.get("category", ""):
                log_security_event(
                    event_type="alert_triggered",
                    severity=rule.severity.value,
                    details={
                        "rule_name": rule.name,
                        "value": value,
                        "threshold": rule.threshold
                    }
                )
            
            self.logger.warning(
                f"Alert triggered: {rule.name}",
                rule_name=rule.name,
                severity=rule.severity.value,
                value=value,
                threshold=rule.threshold
            )
    
    async def _handle_condition_not_met(self, rule_name: str, current_time: datetime):
        """处理条件不满足的情况"""
        alert_key = f"{rule_name}_active"
        
        if alert_key in self.active_alerts:
            # 解决报警
            alert = self.active_alerts[alert_key]
            alert.status = AlertStatus.RESOLVED
            alert.updated_at = current_time
            
            # 发送解决通知
            await self._send_resolution_notification(alert)
            
            # 从活跃报警中移除
            del self.active_alerts[alert_key]
            
            self.logger.info(
                f"Alert resolved: {rule_name}",
                rule_name=rule_name
            )
    
    async def _send_notifications(self, alert: Alert, rule: AlertRule):
        """发送通知"""
        for channel in rule.notification_channels:
            if channel in self.notification_handlers:
                try:
                    await self.notification_handlers[channel](alert)
                except Exception as e:
                    self.logger.error(
                        f"Failed to send {channel} notification: {e}",
                        channel=channel,
                        alert_id=alert.alert_id
                    )
    
    async def _send_resolution_notification(self, alert: Alert):
        """发送解决通知"""
        # 这里可以发送报警解决的通知
        self.logger.info(f"Alert resolved notification sent for: {alert.rule_name}")
    
    async def _send_email_notification(self, alert: Alert):
        """发送邮件通知"""
        # 这里需要配置SMTP设置
        self.logger.info(f"Email notification sent for alert: {alert.alert_id}")
    
    async def _send_webhook_notification(self, alert: Alert):
        """发送Webhook通知"""
        webhook_url = "http://your-webhook-url.com/alerts"  # 从配置中获取
        
        payload = {
            "alert_id": alert.alert_id,
            "rule_name": alert.rule_name,
            "message": alert.message,
            "severity": alert.severity.value,
            "status": alert.status.value,
            "value": alert.value,
            "threshold": alert.threshold,
            "created_at": alert.created_at.isoformat(),
            "metadata": alert.metadata
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        self.logger.info(f"Webhook notification sent for alert: {alert.alert_id}")
                    else:
                        self.logger.error(f"Webhook notification failed with status: {response.status}")
        except Exception as e:
            self.logger.error(f"Failed to send webhook notification: {e}")
    
    async def _send_sms_notification(self, alert: Alert):
        """发送短信通知"""
        # 这里需要集成短信服务
        self.logger.info(f"SMS notification sent for alert: {alert.alert_id}")
    
    async def _send_slack_notification(self, alert: Alert):
        """发送Slack通知"""
        # 这里需要配置Slack Webhook
        self.logger.info(f"Slack notification sent for alert: {alert.alert_id}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃的报警"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取报警历史"""
        return self.alert_history[-limit:]
    
    def acknowledge_alert(self, alert_id: str):
        """确认报警"""
        for alert in self.active_alerts.values():
            if alert.alert_id == alert_id:
                alert.status = AlertStatus.ACKNOWLEDGED
                alert.updated_at = datetime.utcnow()
                self.logger.info(f"Alert acknowledged: {alert_id}")
                break
    
    def silence_alert(self, alert_id: str, duration_minutes: int = 60):
        """静默报警"""
        for alert in self.active_alerts.values():
            if alert.alert_id == alert_id:
                alert.status = AlertStatus.SILENCED
                alert.updated_at = datetime.utcnow()
                # 这里应该设置静默到期时间
                self.logger.info(f"Alert silenced for {duration_minutes} minutes: {alert_id}")
                break


# 全局报警管理器实例
alert_manager = AlertManager()


# 便捷函数
def trigger_custom_alert(
    name: str,
    message: str,
    severity: AlertSeverity,
    metadata: Dict[str, Any] = None
):
    """触发自定义报警"""
    alert = Alert(
        rule_name=name,
        message=message,
        severity=severity,
        status=AlertStatus.ACTIVE,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        value=0,
        threshold=0,
        metadata=metadata or {}
    )
    
    alert_manager.active_alerts[f"{name}_custom"] = alert
    alert_manager.alert_history.append(alert)
    
    logger.warning(f"Custom alert triggered: {name}", alert_name=name, severity=severity.value)
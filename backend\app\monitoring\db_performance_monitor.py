"""
数据库性能监控系统
实时监控数据库性能指标、连接状态、查询性能等
"""

import asyncio
import json
import logging
import psutil
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import statistics

from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import db_manager
from app.core.connection_pool_optimizer import pool_optimizer
from app.monitoring.slow_query_analyzer import slow_query_analyzer


logger = logging.getLogger(__name__)


@dataclass
class DatabaseMetrics:
    """数据库性能指标"""
    timestamp: datetime
    
    # 连接池指标
    pool_size: int
    active_connections: int
    idle_connections: int
    pool_utilization: float
    connection_waits: int
    connection_timeouts: int
    
    # 查询性能指标
    queries_per_second: float
    avg_query_time: float
    slow_queries_count: int
    query_cache_hit_ratio: float
    
    # 事务指标
    transactions_per_second: float
    avg_transaction_time: float
    rollback_ratio: float
    
    # 资源使用指标
    cpu_usage: float
    memory_usage: float
    disk_io_read: float
    disk_io_write: float
    
    # 数据库特定指标
    database_size: int
    table_sizes: Dict[str, int]
    index_usage: Dict[str, float]
    
    # 错误统计
    connection_errors: int
    query_errors: int
    timeout_errors: int


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric: str
    operator: str  # gt, lt, eq, gte, lte
    threshold: float
    severity: str  # critical, warning, info
    description: str
    enabled: bool = True
    cooldown: int = 300  # 告警冷却时间（秒）
    last_triggered: Optional[datetime] = None


@dataclass
class Alert:
    """告警事件"""
    rule_name: str
    metric: str
    current_value: float
    threshold: float
    severity: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.query_stats = {
            'count': 0,
            'total_time': 0,
            'errors': 0,
            'slow_queries': 0
        }
        self.transaction_stats = {
            'count': 0,
            'total_time': 0,
            'rollbacks': 0
        }
        self.connection_stats = {
            'connections': 0,
            'waits': 0,
            'timeouts': 0,
            'errors': 0
        }
        
        # 时间窗口统计
        self.time_window = 60  # 60秒窗口
        self.query_times = deque(maxlen=1000)
        self.transaction_times = deque(maxlen=1000)
        
    def record_query(self, execution_time: float, is_slow: bool = False, has_error: bool = False):
        """记录查询执行"""
        self.query_stats['count'] += 1
        self.query_stats['total_time'] += execution_time
        self.query_times.append((time.time(), execution_time))
        
        if is_slow:
            self.query_stats['slow_queries'] += 1
        if has_error:
            self.query_stats['errors'] += 1
    
    def record_transaction(self, execution_time: float, is_rollback: bool = False):
        """记录事务执行"""
        self.transaction_stats['count'] += 1
        self.transaction_stats['total_time'] += execution_time
        self.transaction_times.append((time.time(), execution_time))
        
        if is_rollback:
            self.transaction_stats['rollbacks'] += 1
    
    def record_connection_event(self, event_type: str):
        """记录连接事件"""
        if event_type in self.connection_stats:
            self.connection_stats[event_type] += 1
    
    def get_qps(self) -> float:
        """获取每秒查询数"""
        current_time = time.time()
        cutoff_time = current_time - self.time_window
        
        recent_queries = [t for t, _ in self.query_times if t >= cutoff_time]
        return len(recent_queries) / self.time_window
    
    def get_avg_query_time(self) -> float:
        """获取平均查询时间"""
        if not self.query_times:
            return 0
        
        current_time = time.time()
        cutoff_time = current_time - self.time_window
        
        recent_times = [exec_time for t, exec_time in self.query_times if t >= cutoff_time]
        return statistics.mean(recent_times) if recent_times else 0
    
    def get_tps(self) -> float:
        """获取每秒事务数"""
        current_time = time.time()
        cutoff_time = current_time - self.time_window
        
        recent_transactions = [t for t, _ in self.transaction_times if t >= cutoff_time]
        return len(recent_transactions) / self.time_window
    
    def get_rollback_ratio(self) -> float:
        """获取回滚比例"""
        if self.transaction_stats['count'] == 0:
            return 0
        return self.transaction_stats['rollbacks'] / self.transaction_stats['count']


class DatabasePerformanceMonitor:
    """数据库性能监控器"""
    
    def __init__(self):
        self.collector = PerformanceCollector()
        self.metrics_history: deque = deque(maxlen=1440)  # 保存24小时数据（每分钟一个点）
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_handlers: List[Callable] = []
        
        # 监控配置
        self.config = {
            'collection_interval': 60,  # 数据收集间隔（秒）
            'enable_resource_monitoring': True,
            'enable_query_monitoring': True,
            'enable_alerting': True,
            'max_metrics_history': 1440,
        }
        
        # 初始化默认告警规则
        self._initialize_default_alert_rules()
    
    def _initialize_default_alert_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                name="high_pool_utilization",
                metric="pool_utilization",
                operator="gt",
                threshold=0.8,
                severity="warning",
                description="连接池利用率过高"
            ),
            AlertRule(
                name="slow_query_rate",
                metric="slow_queries_count",
                operator="gt",
                threshold=10,
                severity="warning",
                description="慢查询数量过多"
            ),
            AlertRule(
                name="high_cpu_usage",
                metric="cpu_usage",
                operator="gt",
                threshold=80.0,
                severity="critical",
                description="CPU使用率过高"
            ),
            AlertRule(
                name="high_memory_usage",
                metric="memory_usage",
                operator="gt",
                threshold=85.0,
                severity="warning",
                description="内存使用率过高"
            ),
            AlertRule(
                name="connection_errors",
                metric="connection_errors",
                operator="gt",
                threshold=5,
                severity="critical",
                description="数据库连接错误过多"
            ),
            AlertRule(
                name="low_cache_hit_ratio",
                metric="query_cache_hit_ratio",
                operator="lt",
                threshold=0.7,
                severity="warning",
                description="查询缓存命中率过低"
            ),
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.name] = rule
    
    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    async def collect_metrics(self) -> DatabaseMetrics:
        """收集性能指标"""
        timestamp = datetime.now()
        
        # 连接池指标
        pool_stats = await pool_optimizer.get_connection_pool_stats()
        pool_size = pool_stats.pool_size if pool_stats else 0
        active_connections = pool_stats.checked_out if pool_stats else 0
        idle_connections = pool_stats.checked_in if pool_stats else 0
        pool_utilization = pool_stats.pool_utilization if pool_stats else 0
        
        # 查询性能指标
        qps = self.collector.get_qps()
        avg_query_time = self.collector.get_avg_query_time()
        slow_queries_count = self.collector.query_stats['slow_queries']
        
        # 缓存命中率
        cache_stats = await self._get_cache_stats()
        query_cache_hit_ratio = cache_stats.get('hit_ratio', 0)
        
        # 事务指标
        tps = self.collector.get_tps()
        avg_transaction_time = statistics.mean([t for _, t in self.collector.transaction_times]) if self.collector.transaction_times else 0
        rollback_ratio = self.collector.get_rollback_ratio()
        
        # 系统资源指标
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 磁盘IO指标
        disk_io = psutil.disk_io_counters()
        disk_io_read = disk_io.read_bytes / (1024 * 1024) if disk_io else 0  # MB
        disk_io_write = disk_io.write_bytes / (1024 * 1024) if disk_io else 0  # MB
        
        # 数据库特定指标
        database_size = await self._get_database_size()
        table_sizes = await self._get_table_sizes()
        index_usage = await self._get_index_usage()
        
        # 错误统计
        connection_errors = self.collector.connection_stats['errors']
        query_errors = self.collector.query_stats['errors']
        timeout_errors = self.collector.connection_stats['timeouts']
        
        metrics = DatabaseMetrics(
            timestamp=timestamp,
            pool_size=pool_size,
            active_connections=active_connections,
            idle_connections=idle_connections,
            pool_utilization=pool_utilization,
            connection_waits=self.collector.connection_stats['waits'],
            connection_timeouts=timeout_errors,
            queries_per_second=qps,
            avg_query_time=avg_query_time,
            slow_queries_count=slow_queries_count,
            query_cache_hit_ratio=query_cache_hit_ratio,
            transactions_per_second=tps,
            avg_transaction_time=avg_transaction_time,
            rollback_ratio=rollback_ratio,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_io_read=disk_io_read,
            disk_io_write=disk_io_write,
            database_size=database_size,
            table_sizes=table_sizes,
            index_usage=index_usage,
            connection_errors=connection_errors,
            query_errors=query_errors,
            timeout_errors=timeout_errors
        )
        
        # 存储到历史记录
        self.metrics_history.append(metrics)
        
        # 检查告警
        if self.config['enable_alerting']:
            await self._check_alerts(metrics)
        
        return metrics
    
    async def _get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            from app.core.query_cache import cache_manager
            return await cache_manager.get_stats()
        except Exception as e:
            logger.warning(f"获取缓存统计失败: {e}")
            return {}
    
    async def _get_database_size(self) -> int:
        """获取数据库大小"""
        try:
            async with db_manager.get_session() as session:
                # SQLite数据库大小查询
                result = await session.execute(text("PRAGMA page_count"))
                page_count = result.scalar()
                
                result = await session.execute(text("PRAGMA page_size"))
                page_size = result.scalar()
                
                return (page_count or 0) * (page_size or 0)
        except Exception as e:
            logger.warning(f"获取数据库大小失败: {e}")
            return 0
    
    async def _get_table_sizes(self) -> Dict[str, int]:
        """获取表大小"""
        table_sizes = {}
        
        try:
            async with db_manager.get_session() as session:
                # 获取所有表名
                result = await session.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """))
                tables = [row.name for row in result.fetchall()]
                
                # 获取每个表的行数
                for table in tables:
                    try:
                        result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        table_sizes[table] = count
                    except Exception as e:
                        logger.debug(f"获取表 {table} 大小失败: {e}")
                        table_sizes[table] = 0
        
        except Exception as e:
            logger.warning(f"获取表大小失败: {e}")
        
        return table_sizes
    
    async def _get_index_usage(self) -> Dict[str, float]:
        """获取索引使用情况"""
        index_usage = {}
        
        try:
            async with db_manager.get_session() as session:
                # SQLite没有直接的索引使用统计，这里模拟
                result = await session.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND sql IS NOT NULL
                """))
                indexes = [row.name for row in result.fetchall()]
                
                # 为每个索引分配一个模拟的使用率
                for index in indexes:
                    index_usage[index] = 0.8  # 模拟80%使用率
        
        except Exception as e:
            logger.warning(f"获取索引使用情况失败: {e}")
        
        return index_usage
    
    async def _check_alerts(self, metrics: DatabaseMetrics):
        """检查告警条件"""
        current_time = datetime.now()
        
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if (rule.last_triggered and 
                (current_time - rule.last_triggered).total_seconds() < rule.cooldown):
                continue
            
            # 获取指标值
            metric_value = getattr(metrics, rule.metric, None)
            if metric_value is None:
                continue
            
            # 检查告警条件
            triggered = False
            if rule.operator == "gt" and metric_value > rule.threshold:
                triggered = True
            elif rule.operator == "lt" and metric_value < rule.threshold:
                triggered = True
            elif rule.operator == "gte" and metric_value >= rule.threshold:
                triggered = True
            elif rule.operator == "lte" and metric_value <= rule.threshold:
                triggered = True
            elif rule.operator == "eq" and metric_value == rule.threshold:
                triggered = True
            
            if triggered:
                # 创建告警
                alert = Alert(
                    rule_name=rule_name,
                    metric=rule.metric,
                    current_value=metric_value,
                    threshold=rule.threshold,
                    severity=rule.severity,
                    message=f"{rule.description}: {rule.metric}={metric_value} {rule.operator} {rule.threshold}",
                    timestamp=current_time
                )
                
                # 更新告警状态
                self.active_alerts[rule_name] = alert
                rule.last_triggered = current_time
                
                # 通知告警处理器
                for handler in self.alert_handlers:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(alert)
                        else:
                            handler(alert)
                    except Exception as e:
                        logger.error(f"告警处理器执行失败: {e}")
                
                logger.warning(f"告警触发: {alert.message}")
            else:
                # 检查是否需要解除告警
                if rule_name in self.active_alerts:
                    alert = self.active_alerts[rule_name]
                    if not alert.resolved:
                        alert.resolved = True
                        alert.resolved_at = current_time
                        logger.info(f"告警解除: {rule_name}")
    
    async def get_performance_report(self, hours: int = 1) -> Dict[str, Any]:
        """获取性能报告"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围的指标
        recent_metrics = [
            m for m in self.metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {'error': 'No metrics available for the specified time range'}
        
        # 计算统计信息
        avg_qps = statistics.mean([m.queries_per_second for m in recent_metrics])
        max_qps = max([m.queries_per_second for m in recent_metrics])
        avg_query_time = statistics.mean([m.avg_query_time for m in recent_metrics])
        max_query_time = max([m.avg_query_time for m in recent_metrics])
        
        avg_pool_utilization = statistics.mean([m.pool_utilization for m in recent_metrics])
        max_pool_utilization = max([m.pool_utilization for m in recent_metrics])
        
        avg_cpu = statistics.mean([m.cpu_usage for m in recent_metrics])
        max_cpu = max([m.cpu_usage for m in recent_metrics])
        
        avg_memory = statistics.mean([m.memory_usage for m in recent_metrics])
        max_memory = max([m.memory_usage for m in recent_metrics])
        
        total_slow_queries = sum([m.slow_queries_count for m in recent_metrics])
        total_connection_errors = sum([m.connection_errors for m in recent_metrics])
        
        # 活跃告警
        active_alerts_list = [
            {
                'rule_name': alert.rule_name,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved
            }
            for alert in self.active_alerts.values()
            if not alert.resolved
        ]
        
        return {
            'report_period': f'{hours} hours',
            'generated_at': datetime.now().isoformat(),
            'metrics_count': len(recent_metrics),
            'performance_summary': {
                'queries': {
                    'avg_qps': round(avg_qps, 2),
                    'max_qps': round(max_qps, 2),
                    'avg_query_time': round(avg_query_time, 4),
                    'max_query_time': round(max_query_time, 4),
                    'total_slow_queries': total_slow_queries
                },
                'connections': {
                    'avg_pool_utilization': round(avg_pool_utilization, 2),
                    'max_pool_utilization': round(max_pool_utilization, 2),
                    'total_connection_errors': total_connection_errors
                },
                'resources': {
                    'avg_cpu_usage': round(avg_cpu, 2),
                    'max_cpu_usage': round(max_cpu, 2),
                    'avg_memory_usage': round(avg_memory, 2),
                    'max_memory_usage': round(max_memory, 2)
                }
            },
            'active_alerts': active_alerts_list,
            'health_status': self._get_health_status(recent_metrics[-1] if recent_metrics else None)
        }
    
    def _get_health_status(self, latest_metrics: Optional[DatabaseMetrics]) -> str:
        """获取健康状态"""
        if not latest_metrics:
            return 'unknown'
        
        # 检查关键指标
        critical_issues = 0
        warning_issues = 0
        
        if latest_metrics.pool_utilization > 0.9:
            critical_issues += 1
        elif latest_metrics.pool_utilization > 0.8:
            warning_issues += 1
        
        if latest_metrics.cpu_usage > 90:
            critical_issues += 1
        elif latest_metrics.cpu_usage > 80:
            warning_issues += 1
        
        if latest_metrics.memory_usage > 95:
            critical_issues += 1
        elif latest_metrics.memory_usage > 85:
            warning_issues += 1
        
        if latest_metrics.connection_errors > 0:
            critical_issues += 1
        
        if latest_metrics.avg_query_time > 5.0:
            warning_issues += 1
        
        # 返回状态
        if critical_issues > 0:
            return 'critical'
        elif warning_issues > 0:
            return 'warning'
        else:
            return 'healthy'
    
    async def export_metrics(self, filepath: str, hours: int = 24):
        """导出指标数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤数据
        export_data = [
            asdict(m) for m in self.metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        # 添加元数据
        export_payload = {
            'export_info': {
                'generated_at': datetime.now().isoformat(),
                'period_hours': hours,
                'metrics_count': len(export_data)
            },
            'metrics': export_data
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_payload, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"性能指标已导出到: {filepath}")
    
    def register_engine_events(self, engine: Engine):
        """注册引擎事件监听"""
        
        @event.listens_for(engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._perf_start_time = time.time()
        
        @event.listens_for(engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            if hasattr(context, '_perf_start_time'):
                execution_time = time.time() - context._perf_start_time
                is_slow = execution_time > 1.0  # 1秒为慢查询阈值
                self.collector.record_query(execution_time, is_slow)
        
        @event.listens_for(engine, "handle_error")
        def receive_handle_error(exception_context):
            self.collector.record_query(0, False, True)
        
        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            self.collector.record_connection_event('connections')
        
        @event.listens_for(engine, "connect")
        def receive_connect(dbapi_connection, connection_record):
            logger.debug("数据库连接建立")
        
        @event.listens_for(engine, "close")
        def receive_close(dbapi_connection, connection_record):
            logger.debug("数据库连接关闭")


# 全局性能监控器实例
db_performance_monitor = DatabasePerformanceMonitor()


# 告警处理器示例
async def log_alert_handler(alert: Alert):
    """日志告警处理器"""
    logger.warning(f"数据库告警: {alert.message}")


async def email_alert_handler(alert: Alert):
    """邮件告警处理器（示例）"""
    # 这里可以集成邮件发送功能
    logger.info(f"发送邮件告警: {alert.message}")


# 初始化和启动函数
async def init_performance_monitoring():
    """初始化性能监控"""
    # 注册事件监听器
    if db_manager.engine:
        db_performance_monitor.register_engine_events(db_manager.engine.sync_engine)
    
    # 注册告警处理器
    db_performance_monitor.add_alert_handler(log_alert_handler)
    
    logger.info("数据库性能监控已启动")


async def start_monitoring_loop():
    """启动监控循环"""
    while True:
        try:
            await db_performance_monitor.collect_metrics()
            await asyncio.sleep(db_performance_monitor.config['collection_interval'])
        except Exception as e:
            logger.error(f"性能监控循环错误: {e}")
            await asyncio.sleep(60)  # 出错时等待60秒再重试


async def generate_daily_performance_report():
    """生成每日性能报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = f"/Users/<USER>/Desktop/quant-platform/backend/logs/db_performance_report_{timestamp}.json"
    
    report = await db_performance_monitor.get_performance_report(hours=24)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    logger.info(f"每日性能报告已生成: {report_path}")
    return report_path


if __name__ == "__main__":
    async def test_performance_monitor():
        """测试性能监控器"""
        print("测试数据库性能监控器...")
        
        # 初始化监控
        await init_performance_monitoring()
        
        # 收集一次指标
        metrics = await db_performance_monitor.collect_metrics()
        
        print(f"性能指标:")
        print(f"  连接池利用率: {metrics.pool_utilization:.2%}")
        print(f"  QPS: {metrics.queries_per_second:.2f}")
        print(f"  平均查询时间: {metrics.avg_query_time:.3f}s")
        print(f"  CPU使用率: {metrics.cpu_usage:.1f}%")
        print(f"  内存使用率: {metrics.memory_usage:.1f}%")
        print(f"  数据库大小: {metrics.database_size / (1024*1024):.2f} MB")
        
        # 生成报告
        report = await db_performance_monitor.get_performance_report()
        print(f"\n健康状态: {report['health_status']}")
        print(f"活跃告警数: {len(report['active_alerts'])}")
        
        print("测试完成")
    
    asyncio.run(test_performance_monitor())
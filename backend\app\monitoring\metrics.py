"""
统一监控指标管理
"""

import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

import psutil
from prometheus_client import Counter, Histogram, Gauge, Info, Summary
from prometheus_client.core import CollectorRegistry


class MetricType(Enum):
    """指标类型枚举"""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"
    INFO = "info"


@dataclass
class MetricDefinition:
    """指标定义"""

    name: str
    description: str
    metric_type: MetricType
    labels: List[str] = field(default_factory=list)
    buckets: Optional[List[float]] = None
    unit: str = ""
    namespace: str = "quant_platform"


class MetricsManager:
    """指标管理器"""

    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self.metrics: Dict[str, Any] = {}
        self.metric_definitions: Dict[str, MetricDefinition] = {}

        # 初始化所有指标
        self._init_system_metrics()
        self._init_api_metrics()
        self._init_trading_metrics()
        self._init_market_data_metrics()
        self._init_backtest_metrics()
        self._init_user_metrics()
        self._init_database_metrics()
        self._init_cache_metrics()
        self._init_task_metrics()
        self._init_websocket_metrics()

    def _init_system_metrics(self):
        """初始化系统指标"""
        system_metrics = [
            MetricDefinition(
                name="system_cpu_usage",
                description="系统CPU使用率",
                metric_type=MetricType.GAUGE,
                unit="percent",
            ),
            MetricDefinition(
                name="system_memory_usage",
                description="系统内存使用率",
                metric_type=MetricType.GAUGE,
                unit="percent",
            ),
            MetricDefinition(
                name="system_disk_usage",
                description="系统磁盘使用率",
                metric_type=MetricType.GAUGE,
                unit="percent",
            ),
            MetricDefinition(
                name="system_network_bytes_sent",
                description="网络发送字节数",
                metric_type=MetricType.COUNTER,
                unit="bytes",
            ),
            MetricDefinition(
                name="system_network_bytes_received",
                description="网络接收字节数",
                metric_type=MetricType.COUNTER,
                unit="bytes",
            ),
            MetricDefinition(
                name="system_load_average",
                description="系统负载平均值",
                metric_type=MetricType.GAUGE,
                labels=["period"],
            ),
            MetricDefinition(
                name="system_uptime",
                description="系统运行时间",
                metric_type=MetricType.GAUGE,
                unit="seconds",
            ),
        ]

        for metric_def in system_metrics:
            self._register_metric(metric_def)

    def _init_api_metrics(self):
        """初始化API指标"""
        api_metrics = [
            MetricDefinition(
                name="api_requests_total",
                description="API请求总数",
                metric_type=MetricType.COUNTER,
                labels=["method", "endpoint", "status_code"],
            ),
            MetricDefinition(
                name="api_request_duration",
                description="API请求持续时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["method", "endpoint"],
                buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
                unit="seconds",
            ),
            MetricDefinition(
                name="api_active_connections",
                description="活跃API连接数",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="api_errors_total",
                description="API错误总数",
                metric_type=MetricType.COUNTER,
                labels=["endpoint", "error_type"],
            ),
            MetricDefinition(
                name="api_rate_limit_hits",
                description="API限流触发次数",
                metric_type=MetricType.COUNTER,
                labels=["endpoint", "client_id"],
            ),
        ]

        for metric_def in api_metrics:
            self._register_metric(metric_def)

    def _init_trading_metrics(self):
        """初始化交易指标"""
        trading_metrics = [
            MetricDefinition(
                name="trading_orders_total",
                description="交易订单总数",
                metric_type=MetricType.COUNTER,
                labels=["user_id", "symbol", "direction", "status"],
            ),
            MetricDefinition(
                name="trading_order_processing_time",
                description="订单处理时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["order_type"],
                buckets=[0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5],
                unit="seconds",
            ),
            MetricDefinition(
                name="trading_trades_total",
                description="成交总数",
                metric_type=MetricType.COUNTER,
                labels=["user_id", "symbol", "direction"],
            ),
            MetricDefinition(
                name="trading_volume_total",
                description="交易总量",
                metric_type=MetricType.COUNTER,
                labels=["symbol", "direction"],
            ),
            MetricDefinition(
                name="trading_turnover_total",
                description="交易总额",
                metric_type=MetricType.COUNTER,
                labels=["symbol"],
                unit="currency",
            ),
            MetricDefinition(
                name="trading_active_orders",
                description="活跃订单数",
                metric_type=MetricType.GAUGE,
                labels=["user_id"],
            ),
            MetricDefinition(
                name="trading_position_count",
                description="持仓数量",
                metric_type=MetricType.GAUGE,
                labels=["user_id", "symbol"],
            ),
            MetricDefinition(
                name="trading_pnl",
                description="盈亏",
                metric_type=MetricType.GAUGE,
                labels=["user_id", "symbol"],
                unit="currency",
            ),
            MetricDefinition(
                name="trading_risk_alerts",
                description="风险告警次数",
                metric_type=MetricType.COUNTER,
                labels=["user_id", "risk_type"],
            ),
        ]

        for metric_def in trading_metrics:
            self._register_metric(metric_def)

    def _init_market_data_metrics(self):
        """初始化行情数据指标"""
        market_data_metrics = [
            MetricDefinition(
                name="market_data_ticks_total",
                description="行情tick总数",
                metric_type=MetricType.COUNTER,
                labels=["symbol", "exchange"],
            ),
            MetricDefinition(
                name="market_data_latency",
                description="行情延迟",
                metric_type=MetricType.HISTOGRAM,
                labels=["symbol", "source"],
                buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
                unit="seconds",
            ),
            MetricDefinition(
                name="market_data_subscriptions",
                description="行情订阅数",
                metric_type=MetricType.GAUGE,
                labels=["symbol"],
            ),
            MetricDefinition(
                name="market_data_connection_status",
                description="行情连接状态",
                metric_type=MetricType.GAUGE,
                labels=["source"],
            ),
            MetricDefinition(
                name="market_data_errors_total",
                description="行情错误总数",
                metric_type=MetricType.COUNTER,
                labels=["source", "error_type"],
            ),
            MetricDefinition(
                name="market_data_processing_time",
                description="行情处理时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["symbol"],
                buckets=[0.001, 0.005, 0.01, 0.05, 0.1],
                unit="seconds",
            ),
        ]

        for metric_def in market_data_metrics:
            self._register_metric(metric_def)

    def _init_backtest_metrics(self):
        """初始化回测指标"""
        backtest_metrics = [
            MetricDefinition(
                name="backtest_runs_total",
                description="回测运行总数",
                metric_type=MetricType.COUNTER,
                labels=["user_id", "strategy", "status"],
            ),
            MetricDefinition(
                name="backtest_duration",
                description="回测持续时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["strategy"],
                buckets=[1, 5, 10, 30, 60, 300, 600, 1800],
                unit="seconds",
            ),
            MetricDefinition(
                name="backtest_active_runs",
                description="活跃回测数",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="backtest_queue_size",
                description="回测队列大小",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="backtest_return_rate",
                description="回测收益率",
                metric_type=MetricType.HISTOGRAM,
                labels=["strategy"],
                buckets=[-0.5, -0.2, -0.1, 0, 0.1, 0.2, 0.5, 1.0],
                unit="percent",
            ),
            MetricDefinition(
                name="backtest_sharpe_ratio",
                description="回测夏普比率",
                metric_type=MetricType.HISTOGRAM,
                labels=["strategy"],
                buckets=[0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0],
            ),
        ]

        for metric_def in backtest_metrics:
            self._register_metric(metric_def)

    def _init_user_metrics(self):
        """初始化用户指标"""
        user_metrics = [
            MetricDefinition(
                name="user_registrations_total",
                description="用户注册总数",
                metric_type=MetricType.COUNTER,
            ),
            MetricDefinition(
                name="user_active_sessions",
                description="活跃用户会话数",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="user_login_attempts",
                description="用户登录尝试次数",
                metric_type=MetricType.COUNTER,
                labels=["status"],
            ),
            MetricDefinition(
                name="user_session_duration",
                description="用户会话持续时间",
                metric_type=MetricType.HISTOGRAM,
                buckets=[60, 300, 900, 1800, 3600, 7200, 14400],
                unit="seconds",
            ),
            MetricDefinition(
                name="user_actions_total",
                description="用户操作总数",
                metric_type=MetricType.COUNTER,
                labels=["user_id", "action"],
            ),
        ]

        for metric_def in user_metrics:
            self._register_metric(metric_def)

    def _init_database_metrics(self):
        """初始化数据库指标"""
        database_metrics = [
            MetricDefinition(
                name="database_connections_active",
                description="活跃数据库连接数",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="database_query_duration",
                description="数据库查询持续时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["table", "operation"],
                buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
                unit="seconds",
            ),
            MetricDefinition(
                name="database_queries_total",
                description="数据库查询总数",
                metric_type=MetricType.COUNTER,
                labels=["table", "operation"],
            ),
            MetricDefinition(
                name="database_errors_total",
                description="数据库错误总数",
                metric_type=MetricType.COUNTER,
                labels=["error_type"],
            ),
            MetricDefinition(
                name="database_pool_size",
                description="数据库连接池大小",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="database_deadlocks_total",
                description="数据库死锁总数",
                metric_type=MetricType.COUNTER,
            ),
        ]

        for metric_def in database_metrics:
            self._register_metric(metric_def)

    def _init_cache_metrics(self):
        """初始化缓存指标"""
        cache_metrics = [
            MetricDefinition(
                name="cache_hits_total",
                description="缓存命中总数",
                metric_type=MetricType.COUNTER,
                labels=["cache_type"],
            ),
            MetricDefinition(
                name="cache_misses_total",
                description="缓存未命中总数",
                metric_type=MetricType.COUNTER,
                labels=["cache_type"],
            ),
            MetricDefinition(
                name="cache_operations_duration",
                description="缓存操作持续时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["operation"],
                buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5],
                unit="seconds",
            ),
            MetricDefinition(
                name="cache_memory_usage",
                description="缓存内存使用量",
                metric_type=MetricType.GAUGE,
                labels=["cache_type"],
                unit="bytes",
            ),
            MetricDefinition(
                name="cache_evictions_total",
                description="缓存驱逐总数",
                metric_type=MetricType.COUNTER,
                labels=["cache_type"],
            ),
        ]

        for metric_def in cache_metrics:
            self._register_metric(metric_def)

    def _init_task_metrics(self):
        """初始化任务指标"""
        task_metrics = [
            MetricDefinition(
                name="task_executions_total",
                description="任务执行总数",
                metric_type=MetricType.COUNTER,
                labels=["task_name", "status"],
            ),
            MetricDefinition(
                name="task_duration",
                description="任务执行持续时间",
                metric_type=MetricType.HISTOGRAM,
                labels=["task_name"],
                buckets=[1, 5, 10, 30, 60, 300, 600, 1800],
                unit="seconds",
            ),
            MetricDefinition(
                name="task_queue_size",
                description="任务队列大小",
                metric_type=MetricType.GAUGE,
                labels=["queue_name"],
            ),
            MetricDefinition(
                name="task_workers_active",
                description="活跃任务工作者数",
                metric_type=MetricType.GAUGE,
                labels=["queue_name"],
            ),
            MetricDefinition(
                name="task_failures_total",
                description="任务失败总数",
                metric_type=MetricType.COUNTER,
                labels=["task_name", "error_type"],
            ),
            MetricDefinition(
                name="task_retries_total",
                description="任务重试总数",
                metric_type=MetricType.COUNTER,
                labels=["task_name"],
            ),
        ]

        for metric_def in task_metrics:
            self._register_metric(metric_def)

    def _init_websocket_metrics(self):
        """初始化WebSocket指标"""
        websocket_metrics = [
            MetricDefinition(
                name="websocket_connections_active",
                description="活跃WebSocket连接数",
                metric_type=MetricType.GAUGE,
            ),
            MetricDefinition(
                name="websocket_messages_sent",
                description="WebSocket发送消息数",
                metric_type=MetricType.COUNTER,
                labels=["message_type"],
            ),
            MetricDefinition(
                name="websocket_messages_received",
                description="WebSocket接收消息数",
                metric_type=MetricType.COUNTER,
                labels=["message_type"],
            ),
            MetricDefinition(
                name="websocket_connection_duration",
                description="WebSocket连接持续时间",
                metric_type=MetricType.HISTOGRAM,
                buckets=[60, 300, 900, 1800, 3600, 7200, 14400],
                unit="seconds",
            ),
            MetricDefinition(
                name="websocket_errors_total",
                description="WebSocket错误总数",
                metric_type=MetricType.COUNTER,
                labels=["error_type"],
            ),
        ]

        for metric_def in websocket_metrics:
            self._register_metric(metric_def)

    def _register_metric(self, metric_def: MetricDefinition):
        """注册指标"""
        full_name = f"{metric_def.namespace}_{metric_def.name}"

        if metric_def.metric_type == MetricType.COUNTER:
            metric = Counter(
                full_name,
                metric_def.description,
                labelnames=metric_def.labels,
                registry=self.registry,
            )
        elif metric_def.metric_type == MetricType.GAUGE:
            metric = Gauge(
                full_name,
                metric_def.description,
                labelnames=metric_def.labels,
                registry=self.registry,
            )
        elif metric_def.metric_type == MetricType.HISTOGRAM:
            metric = Histogram(
                full_name,
                metric_def.description,
                labelnames=metric_def.labels,
                buckets=metric_def.buckets,
                registry=self.registry,
            )
        elif metric_def.metric_type == MetricType.SUMMARY:
            metric = Summary(
                full_name,
                metric_def.description,
                labelnames=metric_def.labels,
                registry=self.registry,
            )
        elif metric_def.metric_type == MetricType.INFO:
            metric = Info(
                full_name,
                metric_def.description,
                labelnames=metric_def.labels,
                registry=self.registry,
            )
        else:
            raise ValueError(f"不支持的指标类型: {metric_def.metric_type}")

        self.metrics[metric_def.name] = metric
        self.metric_definitions[metric_def.name] = metric_def

    def get_metric(self, name: str):
        """获取指标"""
        return self.metrics.get(name)

    def increment_counter(
        self, name: str, labels: Optional[Dict[str, str]] = None, value: float = 1.0
    ):
        """递增计数器"""
        metric = self.get_metric(name)
        if metric and hasattr(metric, "inc"):
            if labels:
                metric.labels(**labels).inc(value)
            else:
                metric.inc(value)

    def set_gauge(
        self, name: str, value: float, labels: Optional[Dict[str, str]] = None
    ):
        """设置仪表盘值"""
        metric = self.get_metric(name)
        if metric and hasattr(metric, "set"):
            if labels:
                metric.labels(**labels).set(value)
            else:
                metric.set(value)

    def observe_histogram(
        self, name: str, value: float, labels: Optional[Dict[str, str]] = None
    ):
        """观察直方图值"""
        metric = self.get_metric(name)
        if metric and hasattr(metric, "observe"):
            if labels:
                metric.labels(**labels).observe(value)
            else:
                metric.observe(value)

    def observe_summary(
        self, name: str, value: float, labels: Optional[Dict[str, str]] = None
    ):
        """观察摘要值"""
        metric = self.get_metric(name)
        if metric and hasattr(metric, "observe"):
            if labels:
                metric.labels(**labels).observe(value)
            else:
                metric.observe(value)

    def time_function(self, metric_name: str, labels: Optional[Dict[str, str]] = None):
        """函数计时装饰器"""

        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    self.observe_histogram(metric_name, duration, labels)

            return wrapper

        return decorator

    def update_system_metrics(self):
        """更新系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.set_gauge("system_cpu_usage", cpu_percent)

        # 内存使用率
        memory = psutil.virtual_memory()
        self.set_gauge("system_memory_usage", memory.percent)

        # 磁盘使用率
        disk = psutil.disk_usage("/")
        self.set_gauge("system_disk_usage", disk.percent)

        # 网络统计
        net_io = psutil.net_io_counters()
        self.set_gauge("system_network_bytes_sent", net_io.bytes_sent)
        self.set_gauge("system_network_bytes_received", net_io.bytes_recv)

        # 负载平均值
        if hasattr(psutil, "getloadavg"):
            load_avg = psutil.getloadavg()
            self.set_gauge("system_load_average", load_avg[0], {"period": "1min"})
            self.set_gauge("system_load_average", load_avg[1], {"period": "5min"})
            self.set_gauge("system_load_average", load_avg[2], {"period": "15min"})

        # 系统启动时间
        boot_time = psutil.boot_time()
        uptime = time.time() - boot_time
        self.set_gauge("system_uptime", uptime)

    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return self.metrics.copy()

    def get_metric_definitions(self) -> Dict[str, MetricDefinition]:
        """获取所有指标定义"""
        return self.metric_definitions.copy()

    def export_metrics(self) -> str:
        """导出指标（Prometheus格式）"""
        from prometheus_client import generate_latest

        return generate_latest(self.registry).decode("utf-8")


# 全局指标管理器实例
metrics_manager = None


def get_metrics_manager() -> MetricsManager:
    """获取指标管理器实例"""
    global metrics_manager
    if metrics_manager is None:
        metrics_manager = MetricsManager()
    return metrics_manager


def init_metrics_manager(
    registry: Optional[CollectorRegistry] = None,
) -> MetricsManager:
    """初始化指标管理器"""
    global metrics_manager
    metrics_manager = MetricsManager(registry)
    return metrics_manager


# 便捷函数
def increment_counter(
    name: str, labels: Optional[Dict[str, str]] = None, value: float = 1.0
):
    """递增计数器"""
    get_metrics_manager().increment_counter(name, labels, value)


def set_gauge(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """设置仪表盘值"""
    get_metrics_manager().set_gauge(name, value, labels)


def observe_histogram(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """观察直方图值"""
    get_metrics_manager().observe_histogram(name, value, labels)


def observe_summary(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """观察摘要值"""
    get_metrics_manager().observe_summary(name, value, labels)


def time_function(metric_name: str, labels: Optional[Dict[str, str]] = None):
    """函数计时装饰器"""
    return get_metrics_manager().time_function(metric_name, labels)

"""
监控指标收集器
用于收集和上报各种业务和系统指标
"""

import time
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import psutil
import threading
from queue import Queue

from loguru import logger
from prometheus_client import Counter, Histogram, Gauge, Info, generate_latest
from app.core.logging_config import log_business_metric, get_contextual_logger


class MetricType(Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    INFO = "info"


@dataclass
class MetricDefinition:
    name: str
    metric_type: MetricType
    description: str
    labels: List[str] = field(default_factory=list)
    buckets: Optional[List[float]] = None


@dataclass
class MetricValue:
    name: str
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.prometheus_metrics = {}
        self.custom_metrics = defaultdict(list)
        self.metric_queue = Queue()
        self.logger = get_contextual_logger("metrics")
        
        # 初始化Prometheus指标
        self._init_prometheus_metrics()
        
        # 启动后台处理线程
        self._start_background_processor()
    
    def _init_prometheus_metrics(self):
        """初始化Prometheus指标"""
        
        # 请求相关指标
        self.prometheus_metrics['http_requests_total'] = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code']
        )
        
        self.prometheus_metrics['http_request_duration_seconds'] = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            buckets=[0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        # 数据库相关指标
        self.prometheus_metrics['database_queries_total'] = Counter(
            'database_queries_total',
            'Total database queries',
            ['operation', 'table', 'status']
        )
        
        self.prometheus_metrics['database_query_duration_seconds'] = Histogram(
            'database_query_duration_seconds',
            'Database query duration in seconds',
            ['operation', 'table']
        )
        
        self.prometheus_metrics['database_connections_active'] = Gauge(
            'database_connections_active',
            'Number of active database connections'
        )
        
        # 交易相关指标
        self.prometheus_metrics['trading_orders_total'] = Counter(
            'trading_orders_total',
            'Total trading orders',
            ['type', 'status', 'symbol']
        )
        
        self.prometheus_metrics['trading_volume'] = Gauge(
            'trading_volume',
            'Trading volume',
            ['symbol', 'timeframe']
        )
        
        self.prometheus_metrics['trading_latency_seconds'] = Histogram(
            'trading_latency_seconds',
            'Trading operation latency in seconds',
            ['operation'],
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
        )
        
        # 系统资源指标
        self.prometheus_metrics['system_cpu_usage'] = Gauge(
            'system_cpu_usage',
            'System CPU usage percentage'
        )
        
        self.prometheus_metrics['system_memory_usage'] = Gauge(
            'system_memory_usage',
            'System memory usage in bytes'
        )
        
        self.prometheus_metrics['system_disk_usage'] = Gauge(
            'system_disk_usage',
            'System disk usage percentage',
            ['device']
        )
        
        # 错误相关指标
        self.prometheus_metrics['errors_total'] = Counter(
            'errors_total',
            'Total errors',
            ['type', 'severity', 'component']
        )
        
        # 业务指标
        self.prometheus_metrics['active_users'] = Gauge(
            'active_users',
            'Number of active users'
        )
        
        self.prometheus_metrics['strategy_performance'] = Gauge(
            'strategy_performance',
            'Strategy performance metrics',
            ['strategy_id', 'metric_type']
        )
    
    def _start_background_processor(self):
        """启动后台指标处理线程"""
        def process_metrics():
            while True:
                try:
                    # 处理队列中的指标
                    while not self.metric_queue.empty():
                        metric = self.metric_queue.get_nowait()
                        self._process_metric(metric)
                        self.metric_queue.task_done()
                    
                    # 收集系统指标
                    self._collect_system_metrics()
                    
                    time.sleep(10)  # 每10秒运行一次
                    
                except Exception as e:
                    self.logger.error(f"Error in metrics processor: {e}")
                    time.sleep(5)
        
        thread = threading.Thread(target=process_metrics, daemon=True)
        thread.start()
    
    def record_http_request(
        self, 
        method: str, 
        endpoint: str, 
        status_code: int, 
        duration: float
    ):
        """记录HTTP请求指标"""
        # Prometheus指标
        self.prometheus_metrics['http_requests_total'].labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        self.prometheus_metrics['http_request_duration_seconds'].labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        # 自定义日志
        log_business_metric(
            "http_request",
            duration,
            {
                "method": method,
                "endpoint": endpoint,
                "status_code": str(status_code)
            }
        )
    
    def record_database_query(
        self, 
        operation: str, 
        table: str, 
        duration: float, 
        success: bool = True
    ):
        """记录数据库查询指标"""
        status = "success" if success else "error"
        
        self.prometheus_metrics['database_queries_total'].labels(
            operation=operation,
            table=table,
            status=status
        ).inc()
        
        self.prometheus_metrics['database_query_duration_seconds'].labels(
            operation=operation,
            table=table
        ).observe(duration)
        
        log_business_metric(
            "database_query",
            duration,
            {
                "operation": operation,
                "table": table,
                "status": status
            }
        )
    
    def record_trading_order(
        self, 
        order_type: str, 
        status: str, 
        symbol: str, 
        volume: float = 0
    ):
        """记录交易订单指标"""
        self.prometheus_metrics['trading_orders_total'].labels(
            type=order_type,
            status=status,
            symbol=symbol
        ).inc()
        
        if volume > 0:
            self.prometheus_metrics['trading_volume'].labels(
                symbol=symbol,
                timeframe="current"
            ).set(volume)
        
        log_business_metric(
            "trading_order",
            volume,
            {
                "type": order_type,
                "status": status,
                "symbol": symbol
            }
        )
    
    def record_trading_latency(self, operation: str, duration: float):
        """记录交易延迟指标"""
        self.prometheus_metrics['trading_latency_seconds'].labels(
            operation=operation
        ).observe(duration)
        
        # 如果延迟过高，记录警告
        if duration > 1.0:
            self.logger.warning(
                f"High trading latency detected: {operation} took {duration:.3f}s",
                operation=operation,
                duration=duration
            )
    
    def record_error(self, error_type: str, severity: str, component: str):
        """记录错误指标"""
        self.prometheus_metrics['errors_total'].labels(
            type=error_type,
            severity=severity,
            component=component
        ).inc()
        
        log_business_metric(
            "error_occurred",
            1,
            {
                "type": error_type,
                "severity": severity,
                "component": component
            }
        )
    
    def update_active_users(self, count: int):
        """更新活跃用户数"""
        self.prometheus_metrics['active_users'].set(count)
        
        log_business_metric("active_users", count)
    
    def update_strategy_performance(
        self, 
        strategy_id: str, 
        metric_type: str, 
        value: float
    ):
        """更新策略性能指标"""
        self.prometheus_metrics['strategy_performance'].labels(
            strategy_id=strategy_id,
            metric_type=metric_type
        ).set(value)
        
        log_business_metric(
            "strategy_performance",
            value,
            {
                "strategy_id": strategy_id,
                "metric_type": metric_type
            }
        )
    
    def update_database_connections(self, count: int):
        """更新数据库连接数"""
        self.prometheus_metrics['database_connections_active'].set(count)
        
        # 如果连接数过高，记录警告
        if count > 50:
            self.logger.warning(
                f"High database connection count: {count}",
                connection_count=count
            )
    
    def _collect_system_metrics(self):
        """收集系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.prometheus_metrics['system_cpu_usage'].set(cpu_percent)
            
            # 内存使用
            memory = psutil.virtual_memory()
            self.prometheus_metrics['system_memory_usage'].set(memory.used)
            
            # 磁盘使用
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    usage_percent = (usage.used / usage.total) * 100
                    self.prometheus_metrics['system_disk_usage'].labels(
                        device=disk.device
                    ).set(usage_percent)
                except:
                    continue
            
            # 记录到日志
            log_business_metric("system_cpu_usage", cpu_percent)
            log_business_metric("system_memory_usage", memory.used)
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
    
    def _process_metric(self, metric: MetricValue):
        """处理单个指标"""
        self.custom_metrics[metric.name].append(metric)
        
        # 清理旧指标（保留最近1小时的数据）
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        self.custom_metrics[metric.name] = [
            m for m in self.custom_metrics[metric.name] 
            if m.timestamp > cutoff_time
        ]
    
    def get_custom_metrics_summary(self, metric_name: str) -> Dict[str, Any]:
        """获取自定义指标摘要"""
        metrics = self.custom_metrics.get(metric_name, [])
        if not metrics:
            return {}
        
        values = [m.value for m in metrics]
        return {
            "count": len(values),
            "avg": sum(values) / len(values),
            "min": min(values),
            "max": max(values),
            "latest": values[-1] if values else 0,
            "timestamp": metrics[-1].timestamp.isoformat() if metrics else None
        }
    
    def get_prometheus_metrics(self) -> str:
        """获取Prometheus格式的指标"""
        return generate_latest().decode('utf-8')
    
    def add_custom_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """添加自定义指标"""
        metric = MetricValue(
            name=name,
            value=value,
            labels=labels or {}
        )
        self.metric_queue.put(metric)


# 全局指标收集器实例
metrics_collector = MetricsCollector()


# 便捷函数
def record_http_request(method: str, endpoint: str, status_code: int, duration: float):
    """记录HTTP请求"""
    metrics_collector.record_http_request(method, endpoint, status_code, duration)


def record_database_query(operation: str, table: str, duration: float, success: bool = True):
    """记录数据库查询"""
    metrics_collector.record_database_query(operation, table, duration, success)


def record_trading_order(order_type: str, status: str, symbol: str, volume: float = 0):
    """记录交易订单"""
    metrics_collector.record_trading_order(order_type, status, symbol, volume)


def record_error(error_type: str, severity: str, component: str):
    """记录错误"""
    metrics_collector.record_error(error_type, severity, component)


def record_custom_metric(name: str, value: float, labels: Dict[str, str] = None):
    """记录自定义指标"""
    metrics_collector.add_custom_metric(name, value, labels)
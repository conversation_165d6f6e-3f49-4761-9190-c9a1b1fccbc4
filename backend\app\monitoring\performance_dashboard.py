"""
数据库性能监控仪表板
提供实时性能指标监控和分析
"""

import asyncio
import json
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import psutil
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import db_manager
from app.core.connection_pool_optimizer import pool_optimizer
from app.core.smart_cache_manager import smart_cache_manager
from app.monitoring.slow_query_analyzer import slow_query_analyzer

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    database_connections: int
    active_queries: int
    cache_hit_rate: float
    avg_query_time: float


@dataclass
class DatabaseMetrics:
    """数据库指标"""
    timestamp: datetime
    total_queries: int
    slow_queries: int
    query_errors: int
    connection_pool_size: int
    connection_pool_usage: float
    deadlocks: int
    lock_waits: int
    table_scans: int
    index_usage_rate: float


@dataclass
class PerformanceAlert:
    """性能告警"""
    timestamp: datetime
    severity: str  # info, warning, error, critical
    category: str  # connection, query, cache, system
    message: str
    details: Dict[str, Any]
    resolved: bool = False


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.system_metrics_history: deque = deque(maxlen=1440)  # 24小时，每分钟一个点
        self.database_metrics_history: deque = deque(maxlen=1440)
        self.alerts: deque = deque(maxlen=1000)
        
        self.monitoring_config = {
            'collection_interval': 60,  # 秒
            'alert_thresholds': {
                'cpu_usage': 80.0,
                'memory_usage': 85.0,
                'disk_usage': 90.0,
                'query_time': 1.0,
                'slow_query_ratio': 0.1,
                'connection_pool_usage': 0.85,
                'cache_hit_rate': 0.8,
                'error_rate': 0.05
            },
            'enable_real_time_alerts': True,
            'enable_auto_reporting': True
        }
        
        self.is_monitoring = False
        self.last_metrics_time = datetime.now()
    
    async def start_monitoring(self):
        """启动性能监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        async def monitoring_loop():
            while self.is_monitoring:
                try:
                    # 收集系统指标
                    system_metrics = await self._collect_system_metrics()
                    self.system_metrics_history.append(system_metrics)
                    
                    # 收集数据库指标
                    db_metrics = await self._collect_database_metrics()
                    self.database_metrics_history.append(db_metrics)
                    
                    # 检查告警条件
                    if self.monitoring_config['enable_real_time_alerts']:
                        await self._check_alert_conditions(system_metrics, db_metrics)
                    
                    # 更新时间
                    self.last_metrics_time = datetime.now()
                    
                    # 等待下一次收集
                    await asyncio.sleep(self.monitoring_config['collection_interval'])
                    
                except Exception as e:
                    logger.error(f"性能监控异常: {e}")
                    await asyncio.sleep(60)
        
        # 启动监控任务
        asyncio.create_task(monitoring_loop())
        logger.info("数据库性能监控已启动")
    
    async def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        logger.info("数据库性能监控已停止")
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent
        
        # 数据库连接数
        try:
            conn_info = await db_manager.get_connection_info()
            database_connections = conn_info.get('total_connections', 0)
        except:
            database_connections = 0
        
        # 缓存命中率
        try:
            cache_stats = await smart_cache_manager.get_stats()
            cache_hit_rate = cache_stats['hit_ratio']
        except:
            cache_hit_rate = 0.0
        
        # 平均查询时间（从慢查询分析器获取）
        try:
            query_report = await slow_query_analyzer.get_slow_query_report(hours=1)
            total_queries = query_report['summary']['total_queries']
            if total_queries > 0:
                # 简化计算，实际应该从查询指标中获取
                avg_query_time = 0.1
            else:
                avg_query_time = 0.0
        except:
            avg_query_time = 0.0
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            database_connections=database_connections,
            active_queries=0,  # 需要从数据库获取
            cache_hit_rate=cache_hit_rate,
            avg_query_time=avg_query_time
        )
    
    async def _collect_database_metrics(self) -> DatabaseMetrics:
        """收集数据库指标"""
        try:
            # 连接池指标
            pool_stats = await pool_optimizer.get_connection_pool_stats()
            connection_pool_size = pool_stats.pool_size if pool_stats else 0
            connection_pool_usage = pool_stats.pool_utilization if pool_stats else 0.0
            
            # 慢查询指标
            query_report = await slow_query_analyzer.get_slow_query_report(hours=1)
            total_queries = query_report['summary']['total_queries']
            slow_queries = query_report['summary']['total_slow_queries']
            
            # 简化的数据库指标收集
            # 在实际环境中，这些指标应该从数据库的系统表中获取
            return DatabaseMetrics(
                timestamp=datetime.now(),
                total_queries=total_queries,
                slow_queries=slow_queries,
                query_errors=0,  # 需要从错误日志获取
                connection_pool_size=connection_pool_size,
                connection_pool_usage=connection_pool_usage,
                deadlocks=0,  # 需要从数据库统计获取
                lock_waits=0,  # 需要从数据库统计获取
                table_scans=0,  # 需要从数据库统计获取
                index_usage_rate=0.95  # 简化假设
            )
            
        except Exception as e:
            logger.error(f"收集数据库指标失败: {e}")
            return DatabaseMetrics(
                timestamp=datetime.now(),
                total_queries=0,
                slow_queries=0,
                query_errors=0,
                connection_pool_size=0,
                connection_pool_usage=0.0,
                deadlocks=0,
                lock_waits=0,
                table_scans=0,
                index_usage_rate=0.0
            )
    
    async def _check_alert_conditions(self, system_metrics: SystemMetrics, db_metrics: DatabaseMetrics):
        """检查告警条件"""
        thresholds = self.monitoring_config['alert_thresholds']
        
        # 系统资源告警
        if system_metrics.cpu_usage > thresholds['cpu_usage']:
            await self._create_alert(
                'warning',
                'system',
                f'CPU使用率过高: {system_metrics.cpu_usage:.1f}%',
                {'cpu_usage': system_metrics.cpu_usage}
            )
        
        if system_metrics.memory_usage > thresholds['memory_usage']:
            await self._create_alert(
                'warning',
                'system',
                f'内存使用率过高: {system_metrics.memory_usage:.1f}%',
                {'memory_usage': system_metrics.memory_usage}
            )
        
        if system_metrics.disk_usage > thresholds['disk_usage']:
            await self._create_alert(
                'error',
                'system',
                f'磁盘使用率过高: {system_metrics.disk_usage:.1f}%',
                {'disk_usage': system_metrics.disk_usage}
            )
        
        # 数据库性能告警
        if db_metrics.connection_pool_usage > thresholds['connection_pool_usage']:
            await self._create_alert(
                'warning',
                'connection',
                f'连接池使用率过高: {db_metrics.connection_pool_usage:.1%}',
                {'pool_usage': db_metrics.connection_pool_usage}
            )
        
        # 查询性能告警
        if db_metrics.total_queries > 0:
            slow_query_ratio = db_metrics.slow_queries / db_metrics.total_queries
            if slow_query_ratio > thresholds['slow_query_ratio']:
                await self._create_alert(
                    'warning',
                    'query',
                    f'慢查询比例过高: {slow_query_ratio:.1%}',
                    {'slow_query_ratio': slow_query_ratio}
                )
        
        # 缓存性能告警
        if system_metrics.cache_hit_rate < thresholds['cache_hit_rate']:
            await self._create_alert(
                'info',
                'cache',
                f'缓存命中率较低: {system_metrics.cache_hit_rate:.1%}',
                {'cache_hit_rate': system_metrics.cache_hit_rate}
            )
    
    async def _create_alert(self, severity: str, category: str, message: str, details: Dict[str, Any]):
        """创建告警"""
        alert = PerformanceAlert(
            timestamp=datetime.now(),
            severity=severity,
            category=category,
            message=message,
            details=details
        )
        
        self.alerts.append(alert)
        
        # 记录到日志
        log_func = {
            'info': logger.info,
            'warning': logger.warning,
            'error': logger.error,
            'critical': logger.critical
        }.get(severity, logger.info)
        
        log_func(f"性能告警 [{category}]: {message}")
    
    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        current_system = self.system_metrics_history[-1] if self.system_metrics_history else None
        current_db = self.database_metrics_history[-1] if self.database_metrics_history else None
        
        # 活跃告警
        active_alerts = [
            alert for alert in self.alerts
            if not alert.resolved and 
            (datetime.now() - alert.timestamp).total_seconds() < 3600  # 1小时内
        ]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': asdict(current_system) if current_system else None,
            'database_metrics': asdict(current_db) if current_db else None,
            'active_alerts': [asdict(alert) for alert in active_alerts],
            'monitoring_status': {
                'is_running': self.is_monitoring,
                'last_collection': self.last_metrics_time.isoformat(),
                'data_points': len(self.system_metrics_history)
            }
        }
    
    async def get_historical_trends(self, hours: int = 24) -> Dict[str, Any]:
        """获取历史趋势"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围的数据
        system_data = [
            m for m in self.system_metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        db_data = [
            m for m in self.database_metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        # 计算趋势
        trends = {}
        
        if system_data:
            trends['cpu_trend'] = self._calculate_trend([m.cpu_usage for m in system_data])
            trends['memory_trend'] = self._calculate_trend([m.memory_usage for m in system_data])
            trends['cache_hit_trend'] = self._calculate_trend([m.cache_hit_rate for m in system_data])
        
        if db_data:
            trends['query_count_trend'] = self._calculate_trend([m.total_queries for m in db_data])
            trends['slow_query_trend'] = self._calculate_trend([m.slow_queries for m in db_data])
        
        return {
            'time_range_hours': hours,
            'system_metrics_history': [asdict(m) for m in system_data],
            'database_metrics_history': [asdict(m) for m in db_data],
            'trends': trends,
            'summary': {
                'total_data_points': len(system_data),
                'alert_count': len([
                    a for a in self.alerts
                    if a.timestamp >= cutoff_time
                ])
            }
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return 'stable'
        
        recent_avg = sum(values[-5:]) / min(len(values), 5)
        early_avg = sum(values[:5]) / min(len(values), 5)
        
        if recent_avg > early_avg * 1.1:
            return 'increasing'
        elif recent_avg < early_avg * 0.9:
            return 'decreasing'
        else:
            return 'stable'
    
    async def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 最近的指标
        recent_system = [
            m for m in self.system_metrics_history
            if m.timestamp >= cutoff_time
        ]
        recent_db = [
            m for m in self.database_metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        # 告警统计
        recent_alerts = [
            a for a in self.alerts
            if a.timestamp >= cutoff_time
        ]
        
        alert_stats = defaultdict(int)
        for alert in recent_alerts:
            alert_stats[alert.severity] += 1
        
        # 性能指标统计
        performance_stats = {}
        
        if recent_system:
            performance_stats['system'] = {
                'avg_cpu_usage': sum(m.cpu_usage for m in recent_system) / len(recent_system),
                'max_cpu_usage': max(m.cpu_usage for m in recent_system),
                'avg_memory_usage': sum(m.memory_usage for m in recent_system) / len(recent_system),
                'avg_cache_hit_rate': sum(m.cache_hit_rate for m in recent_system) / len(recent_system),
            }
        
        if recent_db:
            total_queries = sum(m.total_queries for m in recent_db)
            total_slow_queries = sum(m.slow_queries for m in recent_db)
            
            performance_stats['database'] = {
                'total_queries': total_queries,
                'total_slow_queries': total_slow_queries,
                'slow_query_ratio': total_slow_queries / max(total_queries, 1),
                'avg_pool_usage': sum(m.connection_pool_usage for m in recent_db) / len(recent_db),
            }
        
        # 性能评分
        performance_score = await self._calculate_performance_score(performance_stats)
        
        return {
            'time_range_hours': hours,
            'performance_score': performance_score,
            'performance_stats': performance_stats,
            'alert_summary': {
                'total_alerts': len(recent_alerts),
                'by_severity': dict(alert_stats),
                'active_alerts': len([a for a in recent_alerts if not a.resolved])
            },
            'recommendations': await self._generate_performance_recommendations(performance_stats),
            'generated_at': datetime.now().isoformat()
        }
    
    async def _calculate_performance_score(self, stats: Dict[str, Any]) -> float:
        """计算性能评分（0-100）"""
        score = 100.0
        
        if 'system' in stats:
            system_stats = stats['system']
            
            # CPU使用率扣分
            if system_stats['avg_cpu_usage'] > 80:
                score -= 20
            elif system_stats['avg_cpu_usage'] > 60:
                score -= 10
            
            # 内存使用率扣分
            if system_stats['avg_memory_usage'] > 85:
                score -= 15
            elif system_stats['avg_memory_usage'] > 70:
                score -= 5
            
            # 缓存命中率加分
            if system_stats['avg_cache_hit_rate'] > 0.9:
                score += 5
            elif system_stats['avg_cache_hit_rate'] < 0.7:
                score -= 10
        
        if 'database' in stats:
            db_stats = stats['database']
            
            # 慢查询比例扣分
            if db_stats['slow_query_ratio'] > 0.1:
                score -= 20
            elif db_stats['slow_query_ratio'] > 0.05:
                score -= 10
            
            # 连接池使用率
            if db_stats['avg_pool_usage'] > 0.9:
                score -= 15
            elif db_stats['avg_pool_usage'] > 0.8:
                score -= 5
        
        return max(0, min(100, score))
    
    async def _generate_performance_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        if 'system' in stats:
            system_stats = stats['system']
            
            if system_stats['avg_cpu_usage'] > 80:
                recommendations.append("CPU使用率过高，建议优化查询性能或增加服务器资源")
            
            if system_stats['avg_memory_usage'] > 85:
                recommendations.append("内存使用率过高，建议优化内存使用或增加内存")
            
            if system_stats['avg_cache_hit_rate'] < 0.7:
                recommendations.append("缓存命中率较低，建议优化缓存策略")
        
        if 'database' in stats:
            db_stats = stats['database']
            
            if db_stats['slow_query_ratio'] > 0.1:
                recommendations.append("慢查询比例过高，建议优化SQL查询和添加索引")
            
            if db_stats['avg_pool_usage'] > 0.85:
                recommendations.append("连接池使用率过高，建议增加连接池大小或优化连接使用")
        
        if not recommendations:
            recommendations.append("当前性能状况良好，继续保持")
        
        return recommendations
    
    async def export_performance_report(self, filepath: str, hours: int = 24):
        """导出性能报告"""
        report_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'time_range_hours': hours,
                'report_type': 'database_performance_comprehensive'
            },
            'real_time_metrics': await self.get_real_time_metrics(),
            'historical_trends': await self.get_historical_trends(hours),
            'performance_summary': await self.get_performance_summary(hours),
            'slow_query_report': await slow_query_analyzer.get_slow_query_report(hours),
            'cache_report': await smart_cache_manager.get_smart_cache_report(),
            'connection_pool_report': await pool_optimizer.get_performance_report()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"性能报告已导出到: {filepath}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


# 监控管理函数
async def start_performance_monitoring():
    """启动性能监控"""
    await performance_monitor.start_monitoring()


async def stop_performance_monitoring():
    """停止性能监控"""
    await performance_monitor.stop_monitoring()


async def get_performance_dashboard_data() -> Dict[str, Any]:
    """获取仪表板数据"""
    return {
        'real_time': await performance_monitor.get_real_time_metrics(),
        'trends': await performance_monitor.get_historical_trends(hours=6),
        'summary': await performance_monitor.get_performance_summary(hours=24)
    }


if __name__ == "__main__":
    async def test_performance_monitor():
        """测试性能监控器"""
        print("测试性能监控器...")
        
        # 启动监控
        await start_performance_monitoring()
        
        # 等待收集一些数据
        await asyncio.sleep(65)
        
        # 获取实时指标
        real_time = await performance_monitor.get_real_time_metrics()
        print(f"实时指标: {real_time['system_metrics']}")
        
        # 获取性能摘要
        summary = await performance_monitor.get_performance_summary(hours=1)
        print(f"性能评分: {summary['performance_score']}")
        print(f"建议: {summary['recommendations']}")
        
        # 导出报告
        await performance_monitor.export_performance_report(
            '/Users/<USER>/Desktop/quant-platform/backend/logs/performance_report.json',
            hours=1
        )
        
        # 停止监控
        await stop_performance_monitoring()
        print("测试完成")
    
    asyncio.run(test_performance_monitor())
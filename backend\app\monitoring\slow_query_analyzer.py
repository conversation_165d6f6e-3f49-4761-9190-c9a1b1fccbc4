"""
慢查询分析工具
用于监控、分析和优化数据库查询性能
"""

import asyncio
import json
import logging
import re
import time
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import db_manager


logger = logging.getLogger(__name__)


@dataclass
class QueryMetrics:
    """查询指标数据"""
    query_id: str
    sql_pattern: str
    execution_time: float
    execution_count: int
    avg_execution_time: float
    max_execution_time: float
    min_execution_time: float
    total_execution_time: float
    first_seen: datetime
    last_seen: datetime
    parameters_sample: List[Dict[str, Any]]
    error_count: int
    error_messages: List[str]
    table_names: List[str]
    operation_type: str  # SELECT, INSERT, UPDATE, DELETE
    complexity_score: int
    optimization_suggestions: List[str]


@dataclass
class SlowQueryEvent:
    """慢查询事件"""
    timestamp: datetime
    query_id: str
    sql_statement: str
    parameters: Dict[str, Any]
    execution_time: float
    database_name: str
    connection_id: str
    stack_trace: Optional[str] = None
    explain_plan: Optional[Dict[str, Any]] = None


class QueryPatternMatcher:
    """查询模式匹配器"""
    
    @staticmethod
    def normalize_query(sql: str) -> str:
        """标准化SQL查询，提取模式"""
        # 移除多余空白
        sql = re.sub(r'\s+', ' ', sql.strip())
        
        # 替换字符串字面量
        sql = re.sub(r"'[^']*'", "'?'", sql)
        
        # 替换数字字面量
        sql = re.sub(r'\b\d+\b', '?', sql)
        
        # 替换参数占位符
        sql = re.sub(r':\w+', ':?', sql)
        sql = re.sub(r'\$\d+', '$?', sql)
        sql = re.sub(r'\?', '?', sql)
        
        # 替换IN子句中的多个值
        sql = re.sub(r'IN\s*\([^)]*\)', 'IN (?)', sql, flags=re.IGNORECASE)
        
        return sql.upper()
    
    @staticmethod
    def extract_tables(sql: str) -> List[str]:
        """提取查询涉及的表名"""
        tables = []
        
        # 匹配FROM子句
        from_matches = re.findall(r'FROM\s+(\w+)', sql, re.IGNORECASE)
        tables.extend(from_matches)
        
        # 匹配JOIN子句
        join_matches = re.findall(r'JOIN\s+(\w+)', sql, re.IGNORECASE)
        tables.extend(join_matches)
        
        # 匹配UPDATE语句
        update_matches = re.findall(r'UPDATE\s+(\w+)', sql, re.IGNORECASE)
        tables.extend(update_matches)
        
        # 匹配INSERT INTO语句
        insert_matches = re.findall(r'INSERT\s+INTO\s+(\w+)', sql, re.IGNORECASE)
        tables.extend(insert_matches)
        
        # 匹配DELETE FROM语句
        delete_matches = re.findall(r'DELETE\s+FROM\s+(\w+)', sql, re.IGNORECASE)
        tables.extend(delete_matches)
        
        return list(set(tables))
    
    @staticmethod
    def get_operation_type(sql: str) -> str:
        """获取查询操作类型"""
        sql_upper = sql.strip().upper()
        
        if sql_upper.startswith('SELECT'):
            return 'SELECT'
        elif sql_upper.startswith('INSERT'):
            return 'INSERT'
        elif sql_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif sql_upper.startswith('DELETE'):
            return 'DELETE'
        elif sql_upper.startswith('CREATE'):
            return 'CREATE'
        elif sql_upper.startswith('DROP'):
            return 'DROP'
        elif sql_upper.startswith('ALTER'):
            return 'ALTER'
        else:
            return 'OTHER'
    
    @staticmethod
    def calculate_complexity_score(sql: str) -> int:
        """计算查询复杂度分数"""
        score = 0
        sql_upper = sql.upper()
        
        # 基础分数
        score += 1
        
        # JOIN操作
        join_count = len(re.findall(r'\bJOIN\b', sql_upper))
        score += join_count * 2
        
        # 子查询
        subquery_count = sql_upper.count('(SELECT')
        score += subquery_count * 3
        
        # WHERE条件
        where_conditions = len(re.findall(r'\bWHERE\b|\bAND\b|\bOR\b', sql_upper))
        score += where_conditions
        
        # 聚合函数
        aggregate_count = len(re.findall(r'\b(COUNT|SUM|AVG|MAX|MIN|GROUP)\b', sql_upper))
        score += aggregate_count * 2
        
        # ORDER BY
        if 'ORDER BY' in sql_upper:
            score += 1
        
        # LIMIT/OFFSET
        if any(keyword in sql_upper for keyword in ['LIMIT', 'OFFSET', 'TOP']):
            score += 1
        
        return score


class SlowQueryAnalyzer:
    """慢查询分析器"""
    
    def __init__(self, slow_threshold: float = 1.0, max_events: int = 10000):
        self.slow_threshold = slow_threshold  # 慢查询阈值（秒）
        self.max_events = max_events
        
        # 存储查询数据
        self.query_metrics: Dict[str, QueryMetrics] = {}
        self.slow_query_events: deque = deque(maxlen=max_events)
        
        # 统计数据
        self.total_queries = 0
        self.slow_queries = 0
        self.start_time = datetime.now()
        
        # 查询分类统计
        self.query_stats_by_type = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'avg_time': 0,
            'slow_count': 0
        })
        
        # 事件监听器
        self.event_listeners: List[Callable] = []
        
        # 配置参数
        self.config = {
            'enable_explain_plan': True,
            'enable_stack_trace': False,
            'sample_parameters': True,
            'max_parameter_samples': 10,
            'analysis_interval': 300,  # 5分钟分析一次
        }
    
    def add_event_listener(self, listener: Callable[[SlowQueryEvent], None]):
        """添加事件监听器"""
        self.event_listeners.append(listener)
    
    async def analyze_query(
        self, 
        sql: str, 
        parameters: Optional[Dict[str, Any]] = None,
        execution_time: float = 0,
        connection_id: str = '',
        database_name: str = 'default'
    ) -> Optional[SlowQueryEvent]:
        """分析单个查询"""
        self.total_queries += 1
        
        # 标准化查询
        normalized_sql = QueryPatternMatcher.normalize_query(sql)
        query_id = self._generate_query_id(normalized_sql)
        
        # 提取查询信息
        tables = QueryPatternMatcher.extract_tables(sql)
        operation_type = QueryPatternMatcher.get_operation_type(sql)
        complexity_score = QueryPatternMatcher.calculate_complexity_score(sql)
        
        # 更新或创建查询指标
        if query_id in self.query_metrics:
            metrics = self.query_metrics[query_id]
            metrics.execution_count += 1
            metrics.total_execution_time += execution_time
            metrics.avg_execution_time = metrics.total_execution_time / metrics.execution_count
            metrics.max_execution_time = max(metrics.max_execution_time, execution_time)
            metrics.min_execution_time = min(metrics.min_execution_time, execution_time)
            metrics.last_seen = datetime.now()
            
            # 采样参数
            if parameters and self.config['sample_parameters']:
                if len(metrics.parameters_sample) < self.config['max_parameter_samples']:
                    metrics.parameters_sample.append(parameters.copy())
        else:
            metrics = QueryMetrics(
                query_id=query_id,
                sql_pattern=normalized_sql,
                execution_time=execution_time,
                execution_count=1,
                avg_execution_time=execution_time,
                max_execution_time=execution_time,
                min_execution_time=execution_time,
                total_execution_time=execution_time,
                first_seen=datetime.now(),
                last_seen=datetime.now(),
                parameters_sample=[parameters.copy()] if parameters and self.config['sample_parameters'] else [],
                error_count=0,
                error_messages=[],
                table_names=tables,
                operation_type=operation_type,
                complexity_score=complexity_score,
                optimization_suggestions=[]
            )
            self.query_metrics[query_id] = metrics
        
        # 更新类型统计
        type_stats = self.query_stats_by_type[operation_type]
        type_stats['count'] += 1
        type_stats['total_time'] += execution_time
        type_stats['avg_time'] = type_stats['total_time'] / type_stats['count']
        
        # 检查是否为慢查询
        slow_event = None
        if execution_time >= self.slow_threshold:
            self.slow_queries += 1
            type_stats['slow_count'] += 1
            
            # 创建慢查询事件
            slow_event = SlowQueryEvent(
                timestamp=datetime.now(),
                query_id=query_id,
                sql_statement=sql,
                parameters=parameters or {},
                execution_time=execution_time,
                database_name=database_name,
                connection_id=connection_id,
            )
            
            # 获取执行计划
            if self.config['enable_explain_plan']:
                try:
                    slow_event.explain_plan = await self._get_explain_plan(sql, parameters)
                except Exception as e:
                    logger.warning(f"获取执行计划失败: {e}")
            
            # 添加到慢查询事件队列
            self.slow_query_events.append(slow_event)
            
            # 通知事件监听器
            for listener in self.event_listeners:
                try:
                    if asyncio.iscoroutinefunction(listener):
                        await listener(slow_event)
                    else:
                        listener(slow_event)
                except Exception as e:
                    logger.error(f"事件监听器执行失败: {e}")
            
            logger.warning(f"慢查询检测 ({execution_time:.3f}s): {sql[:100]}...")
        
        return slow_event
    
    def _generate_query_id(self, normalized_sql: str) -> str:
        """生成查询ID"""
        import hashlib
        return hashlib.md5(normalized_sql.encode()).hexdigest()[:16]
    
    async def _get_explain_plan(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取查询执行计划"""
        try:
            async with db_manager.get_session() as session:
                # SQLite的执行计划查询
                explain_sql = f"EXPLAIN QUERY PLAN {sql}"
                result = await session.execute(text(explain_sql), parameters or {})
                plan_rows = result.fetchall()
                
                return {
                    'plan': [dict(row._mapping) for row in plan_rows],
                    'type': 'sqlite_query_plan'
                }
        except Exception as e:
            logger.debug(f"获取执行计划失败: {e}")
            return None
    
    async def generate_optimization_suggestions(self, query_id: str) -> List[str]:
        """生成查询优化建议"""
        if query_id not in self.query_metrics:
            return []
        
        metrics = self.query_metrics[query_id]
        suggestions = []
        
        # 基于执行时间的建议
        if metrics.avg_execution_time > 5.0:
            suggestions.append("考虑添加适当的索引以提高查询性能")
        
        if metrics.avg_execution_time > 10.0:
            suggestions.append("查询执行时间过长，建议重构查询逻辑")
        
        # 基于复杂度的建议
        if metrics.complexity_score > 10:
            suggestions.append("查询复杂度较高，考虑分解为多个简单查询")
        
        # 基于表数量的建议
        if len(metrics.table_names) > 5:
            suggestions.append("涉及表过多，考虑优化数据模型或使用视图")
        
        # 基于操作类型的建议
        if metrics.operation_type == 'SELECT' and 'LIMIT' not in metrics.sql_pattern:
            if metrics.avg_execution_time > 2.0:
                suggestions.append("长时间SELECT查询建议添加LIMIT子句")
        
        # 基于执行频率的建议
        if metrics.execution_count > 1000 and metrics.avg_execution_time > 0.1:
            suggestions.append("高频查询建议使用缓存机制")
        
        # 检查是否缺少索引
        if any(keyword in metrics.sql_pattern for keyword in ['WHERE', 'ORDER BY', 'GROUP BY']):
            suggestions.append("检查WHERE/ORDER BY/GROUP BY字段是否有合适的索引")
        
        # 更新建议到指标
        metrics.optimization_suggestions = suggestions
        return suggestions
    
    async def get_slow_query_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成慢查询报告"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围内的慢查询
        recent_slow_queries = [
            event for event in self.slow_query_events
            if event.timestamp >= cutoff_time
        ]
        
        # 统计信息
        total_slow_queries = len(recent_slow_queries)
        
        # 按查询ID分组统计
        slow_by_query = defaultdict(list)
        for event in recent_slow_queries:
            slow_by_query[event.query_id].append(event)
        
        # 生成Top慢查询
        top_slow_queries = []
        for query_id, events in slow_by_query.items():
            if query_id in self.query_metrics:
                metrics = self.query_metrics[query_id]
                avg_time = sum(e.execution_time for e in events) / len(events)
                max_time = max(e.execution_time for e in events)
                
                top_slow_queries.append({
                    'query_id': query_id,
                    'sql_pattern': metrics.sql_pattern,
                    'operation_type': metrics.operation_type,
                    'table_names': metrics.table_names,
                    'slow_count': len(events),
                    'avg_execution_time': avg_time,
                    'max_execution_time': max_time,
                    'optimization_suggestions': await self.generate_optimization_suggestions(query_id)
                })
        
        # 按平均执行时间排序
        top_slow_queries.sort(key=lambda x: x['avg_execution_time'], reverse=True)
        
        # 按操作类型统计
        slow_by_type = defaultdict(lambda: {'count': 0, 'total_time': 0})
        for event in recent_slow_queries:
            if event.query_id in self.query_metrics:
                op_type = self.query_metrics[event.query_id].operation_type
                slow_by_type[op_type]['count'] += 1
                slow_by_type[op_type]['total_time'] += event.execution_time
        
        # 按表统计
        slow_by_table = defaultdict(lambda: {'count': 0, 'total_time': 0})
        for event in recent_slow_queries:
            if event.query_id in self.query_metrics:
                for table in self.query_metrics[event.query_id].table_names:
                    slow_by_table[table]['count'] += 1
                    slow_by_table[table]['total_time'] += event.execution_time
        
        # 时间分布
        hourly_distribution = defaultdict(int)
        for event in recent_slow_queries:
            hour = event.timestamp.hour
            hourly_distribution[hour] += 1
        
        return {
            'report_period': f'{hours} hours',
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_queries': self.total_queries,
                'total_slow_queries': total_slow_queries,
                'slow_query_ratio': total_slow_queries / self.total_queries if self.total_queries > 0 else 0,
                'avg_queries_per_hour': self.total_queries / max(1, (datetime.now() - self.start_time).total_seconds() / 3600),
                'unique_slow_patterns': len(slow_by_query),
            },
            'top_slow_queries': top_slow_queries[:10],
            'slow_queries_by_type': dict(slow_by_type),
            'slow_queries_by_table': dict(slow_by_table),
            'hourly_distribution': dict(hourly_distribution),
            'optimization_priorities': self._get_optimization_priorities(top_slow_queries)
        }
    
    def _get_optimization_priorities(self, top_slow_queries: List[Dict]) -> List[Dict[str, Any]]:
        """获取优化优先级"""
        priorities = []
        
        for query in top_slow_queries[:5]:  # 只看前5个
            impact_score = query['slow_count'] * query['avg_execution_time']
            
            priorities.append({
                'query_id': query['query_id'],
                'priority': 'HIGH' if impact_score > 100 else 'MEDIUM' if impact_score > 20 else 'LOW',
                'impact_score': impact_score,
                'reason': f"执行{query['slow_count']}次，平均{query['avg_execution_time']:.2f}秒",
                'suggestions': query['optimization_suggestions'][:3]  # 只显示前3个建议
            })
        
        return sorted(priorities, key=lambda x: x['impact_score'], reverse=True)
    
    async def export_report(self, filepath: str, hours: int = 24):
        """导出报告到文件"""
        report = await self.get_slow_query_report(hours)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"慢查询报告已导出到: {filepath}")
    
    async def clear_old_data(self, days: int = 7):
        """清理旧数据"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # 清理旧的查询指标
        old_query_ids = [
            query_id for query_id, metrics in self.query_metrics.items()
            if metrics.last_seen < cutoff_time
        ]
        
        for query_id in old_query_ids:
            del self.query_metrics[query_id]
        
        logger.info(f"清理了 {len(old_query_ids)} 个过期查询指标")
    
    def register_engine_events(self, engine: Engine):
        """注册引擎事件监听"""
        
        @event.listens_for(engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
            context._query_sql = statement
            context._query_parameters = parameters
        
        @event.listens_for(engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            if hasattr(context, '_query_start_time'):
                execution_time = time.time() - context._query_start_time
                
                # 异步分析查询
                asyncio.create_task(self.analyze_query(
                    sql=statement,
                    parameters=parameters if isinstance(parameters, dict) else {},
                    execution_time=execution_time,
                    connection_id=str(id(conn)),
                    database_name='default'
                ))


# 全局慢查询分析器实例
slow_query_analyzer = SlowQueryAnalyzer()


async def init_slow_query_monitoring():
    """初始化慢查询监控"""
    # 注册数据库引擎事件
    if db_manager.engine:
        slow_query_analyzer.register_engine_events(db_manager.engine.sync_engine)
        logger.info("慢查询监控已启动")
    else:
        logger.warning("数据库引擎未初始化，无法启动慢查询监控")


async def generate_daily_slow_query_report():
    """生成每日慢查询报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = f"/Users/<USER>/Desktop/quant-platform/backend/logs/slow_query_report_{timestamp}.json"
    
    await slow_query_analyzer.export_report(report_path, hours=24)
    return report_path


if __name__ == "__main__":
    async def test_slow_query_analyzer():
        """测试慢查询分析器"""
        print("测试慢查询分析器...")
        
        # 模拟一些查询
        test_queries = [
            ("SELECT * FROM orders WHERE user_id = :user_id", {'user_id': 1}, 0.5),
            ("SELECT * FROM market_data WHERE symbol_code = :symbol ORDER BY timestamp DESC", {'symbol': '000001'}, 2.1),
            ("SELECT COUNT(*) FROM trades WHERE trade_time > :start_time", {'start_time': '2024-01-01'}, 1.5),
        ]
        
        for sql, params, exec_time in test_queries:
            await slow_query_analyzer.analyze_query(sql, params, exec_time)
        
        # 生成报告
        report = await slow_query_analyzer.get_slow_query_report(hours=1)
        
        print("分析报告:")
        print(f"  总查询数: {report['summary']['total_queries']}")
        print(f"  慢查询数: {report['summary']['total_slow_queries']}")
        print(f"  慢查询比例: {report['summary']['slow_query_ratio']:.2%}")
        
        if report['top_slow_queries']:
            print("  Top慢查询:")
            for i, query in enumerate(report['top_slow_queries'][:3], 1):
                print(f"    {i}. {query['operation_type']} - {query['avg_execution_time']:.2f}s")
        
        print("测试完成")
    
    asyncio.run(test_slow_query_analyzer())
"""
Python 版本运行时监控模块
提供版本兼容性检查的 API 端点和监控指标
"""
import sys
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# 配置日志
logger = logging.getLogger(__name__)

# 版本要求配置
MIN_VERSION = (3, 10)
MAX_VERSION = (3, 11, 99)
RECOMMENDED_VERSION = "3.10.13"

# 关键依赖兼容性映射
CRITICAL_DEPENDENCIES = {
    "vnpy": {
        "max_python": (3, 11),
        "risk_level": "HIGH",
        "impact": "交易核心功能可能崩溃"
    },
    "TA-Lib": {
        "max_python": (3, 11),
        "risk_level": "HIGH", 
        "impact": "技术指标计算异常"
    },
    "SQLAlchemy": {
        "max_python": (3, 12),
        "risk_level": "MEDIUM",
        "impact": "数据库操作偶发失败"
    },
    "FastAPI": {
        "max_python": (3, 12),
        "risk_level": "MEDIUM",
        "impact": "API 响应不一致"
    }
}

class VersionStatus(BaseModel):
    """版本状态响应模型"""
    status: str
    current_version: str
    required_range: str
    recommended_version: str
    compatibility_issues: list = []
    risk_level: str = "LOW"

# 创建路由器
router = APIRouter(prefix="/health", tags=["健康检查"])

@router.get("/version", response_model=VersionStatus)
def check_version() -> VersionStatus:
    """
    检查 Python 版本兼容性
    
    Returns:
        VersionStatus: 版本状态信息
    """
    current_version = sys.version_info[:3]
    current_version_str = f"{current_version[0]}.{current_version[1]}.{current_version[2]}"
    required_range = f"{MIN_VERSION[0]}.{MIN_VERSION[1]} ~ {MAX_VERSION[0]}.{MAX_VERSION[1]}.x"
    
    # 初始化响应
    response = VersionStatus(
        current_version=current_version_str,
        required_range=required_range,
        recommended_version=RECOMMENDED_VERSION,
        status="ok"
    )
    
    # 版本兼容性检查
    if current_version[:2] < MIN_VERSION:
        response.status = "critical"
        response.risk_level = "HIGH"
        response.compatibility_issues.append({
            "type": "version_too_low",
            "message": f"Python 版本过低: {current_version_str} < {MIN_VERSION[0]}.{MIN_VERSION[1]}",
            "impact": "系统可能无法正常运行"
        })
        logger.critical(f"Python 版本过低: {current_version_str}")
        
    elif current_version[:2] > MAX_VERSION[:2]:
        response.status = "warning"
        response.risk_level = "HIGH"
        response.compatibility_issues.append({
            "type": "version_too_high",
            "message": f"Python 版本过高: {current_version_str} > {MAX_VERSION[0]}.{MAX_VERSION[1]}.x",
            "impact": "关键依赖可能不兼容"
        })
        
        # 检查关键依赖兼容性
        for dep, info in CRITICAL_DEPENDENCIES.items():
            if current_version[:2] > info["max_python"]:
                response.compatibility_issues.append({
                    "type": "dependency_incompatible",
                    "dependency": dep,
                    "message": f"{dep} 不支持 Python {current_version_str}",
                    "impact": info["impact"],
                    "risk_level": info["risk_level"]
                })
        
        logger.warning(f"Python 版本过高，存在兼容性风险: {current_version_str}")
    
    else:
        # 版本在支持范围内
        if current_version_str != RECOMMENDED_VERSION:
            response.status = "ok"
            response.risk_level = "LOW"
            response.compatibility_issues.append({
                "type": "version_suboptimal",
                "message": f"建议使用推荐版本 {RECOMMENDED_VERSION}",
                "impact": "当前版本可用，但推荐版本经过更充分测试"
            })
        else:
            response.status = "optimal"
            response.risk_level = "LOW"
        
        logger.info(f"Python 版本兼容性检查通过: {current_version_str}")
    
    return response

@router.get("/version/dependencies")
def check_dependencies() -> Dict[str, Any]:
    """
    检查关键依赖的兼容性状态
    
    Returns:
        Dict: 依赖兼容性报告
    """
    current_version = sys.version_info[:2]
    
    dependency_status = {}
    overall_risk = "LOW"
    
    for dep, info in CRITICAL_DEPENDENCIES.items():
        is_compatible = current_version <= info["max_python"]
        
        dependency_status[dep] = {
            "compatible": is_compatible,
            "max_supported_python": f"{info['max_python'][0]}.{info['max_python'][1]}",
            "risk_level": info["risk_level"],
            "impact": info["impact"]
        }
        
        if not is_compatible and info["risk_level"] == "HIGH":
            overall_risk = "HIGH"
        elif not is_compatible and info["risk_level"] == "MEDIUM" and overall_risk != "HIGH":
            overall_risk = "MEDIUM"
    
    return {
        "python_version": f"{current_version[0]}.{current_version[1]}",
        "overall_risk": overall_risk,
        "dependencies": dependency_status,
        "recommendations": [
            f"使用 Python {RECOMMENDED_VERSION} 以确保最佳兼容性",
            "定期检查依赖更新和兼容性",
            "在升级 Python 版本前进行充分测试"
        ]
    }

@router.get("/version/metrics")
def get_version_metrics() -> Dict[str, Any]:
    """
    获取版本相关的监控指标
    
    Returns:
        Dict: 监控指标
    """
    current_version = sys.version_info[:3]
    
    # 计算兼容性分数 (0-100)
    compatibility_score = 100
    
    if current_version[:2] < MIN_VERSION or current_version[:2] > MAX_VERSION[:2]:
        compatibility_score = 0
    elif f"{current_version[0]}.{current_version[1]}.{current_version[2]}" != RECOMMENDED_VERSION:
        compatibility_score = 80
    
    # 计算风险等级数值
    risk_score = 0
    for dep, info in CRITICAL_DEPENDENCIES.items():
        if current_version[:2] > info["max_python"]:
            if info["risk_level"] == "HIGH":
                risk_score += 30
            elif info["risk_level"] == "MEDIUM":
                risk_score += 10
    
    return {
        "python_version_major": current_version[0],
        "python_version_minor": current_version[1],
        "python_version_micro": current_version[2],
        "compatibility_score": compatibility_score,
        "risk_score": min(risk_score, 100),
        "is_recommended_version": f"{current_version[0]}.{current_version[1]}.{current_version[2]}" == RECOMMENDED_VERSION,
        "incompatible_dependencies": len([
            dep for dep, info in CRITICAL_DEPENDENCIES.items()
            if current_version[:2] > info["max_python"]
        ])
    }

def log_version_warning():
    """在应用启动时记录版本警告"""
    current_version = sys.version_info[:3]
    current_version_str = f"{current_version[0]}.{current_version[1]}.{current_version[2]}"
    
    if current_version[:2] < MIN_VERSION or current_version[:2] > MAX_VERSION[:2]:
        logger.critical(
            f"⚠️ Python 版本兼容性问题: 当前 {current_version_str}, "
            f"要求 {MIN_VERSION[0]}.{MIN_VERSION[1]} ~ {MAX_VERSION[0]}.{MAX_VERSION[1]}.x"
        )
    elif current_version_str != RECOMMENDED_VERSION:
        logger.info(
            f"💡 建议使用推荐的 Python 版本: {RECOMMENDED_VERSION} "
            f"(当前: {current_version_str})"
        )
    else:
        logger.info(f"✅ Python 版本最优: {current_version_str}")

"""
仓位管理器
负责仓位规模计算、调整和优化
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: float  # 持仓数量（负数表示空头）
    avg_cost: float  # 平均成本
    current_price: float  # 当前价格
    market_value: float  # 市值
    unrealized_pnl: float  # 未实现盈亏
    realized_pnl: float  # 已实现盈亏
    last_updated: datetime
    
    @property
    def weight(self) -> float:
        """计算仓位权重（需要外部提供组合总价值）"""
        return self.market_value
    
    @property
    def return_rate(self) -> float:
        """收益率"""
        if self.avg_cost == 0:
            return 0
        return (self.current_price - self.avg_cost) / self.avg_cost

@dataclass
class PositionTarget:
    """目标仓位"""
    symbol: str
    target_quantity: float
    target_weight: float
    priority: int = 1  # 调仓优先级
    reason: str = ""  # 调仓原因

class PositionManager:
    """仓位管理器"""
    
    def __init__(self, config):
        self.config = config
        self.positions: Dict[str, Position] = {}
        self.transaction_costs = config.get('transaction_costs', {
            'commission_rate': 0.0003,
            'stamp_tax_rate': 0.001,
            'transfer_fee_rate': 0.00002
        })
        
    def update_position(
        self,
        symbol: str,
        quantity_change: float,
        price: float,
        transaction_type: str = 'trade'
    ) -> Position:
        """
        更新持仓
        
        Args:
            symbol: 股票代码
            quantity_change: 数量变化
            price: 交易价格
            transaction_type: 交易类型 ('trade', 'dividend', 'split')
        """
        if symbol not in self.positions:
            # 新建持仓
            position = Position(
                symbol=symbol,
                quantity=quantity_change,
                avg_cost=price,
                current_price=price,
                market_value=quantity_change * price,
                unrealized_pnl=0,
                realized_pnl=0,
                last_updated=datetime.now()
            )
        else:
            position = self.positions[symbol]
            
            if transaction_type == 'trade':
                # 计算新的平均成本
                if (position.quantity > 0 and quantity_change > 0) or \
                   (position.quantity < 0 and quantity_change < 0):
                    # 同向加仓
                    total_cost = position.quantity * position.avg_cost + quantity_change * price
                    new_quantity = position.quantity + quantity_change
                    if new_quantity != 0:
                        position.avg_cost = total_cost / new_quantity
                    position.quantity = new_quantity
                else:
                    # 反向交易或平仓
                    if abs(quantity_change) >= abs(position.quantity):
                        # 完全平仓或反向开仓
                        realized_pnl = (price - position.avg_cost) * position.quantity
                        position.realized_pnl += realized_pnl
                        
                        remaining_quantity = quantity_change + position.quantity
                        if remaining_quantity != 0:
                            position.quantity = remaining_quantity
                            position.avg_cost = price
                        else:
                            position.quantity = 0
                    else:
                        # 部分平仓
                        realized_pnl = (price - position.avg_cost) * (-quantity_change)
                        position.realized_pnl += realized_pnl
                        position.quantity += quantity_change
                        
                # 更新当前价格和市值
                position.current_price = price
                position.market_value = position.quantity * price
                position.unrealized_pnl = (price - position.avg_cost) * position.quantity
                position.last_updated = datetime.now()
                
        self.positions[symbol] = position
        
        # 清理零持仓
        if abs(position.quantity) < 1e-6:
            del self.positions[symbol]
            
        return position
    
    def update_market_prices(self, price_data: Dict[str, float]):
        """更新市场价格"""
        for symbol, price in price_data.items():
            if symbol in self.positions:
                position = self.positions[symbol]
                position.current_price = price
                position.market_value = position.quantity * price
                position.unrealized_pnl = (price - position.avg_cost) * position.quantity
                position.last_updated = datetime.now()
    
    def calculate_portfolio_metrics(self, total_capital: float) -> Dict[str, float]:
        """计算组合指标"""
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
        
        cash = total_capital - total_market_value
        portfolio_value = total_capital + total_unrealized_pnl + total_realized_pnl
        
        return {
            'total_market_value': total_market_value,
            'cash': cash,
            'portfolio_value': portfolio_value,
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_realized_pnl': total_realized_pnl,
            'total_pnl': total_unrealized_pnl + total_realized_pnl,
            'portfolio_return': (total_unrealized_pnl + total_realized_pnl) / total_capital,
            'position_count': len(self.positions),
            'utilization_rate': total_market_value / total_capital
        }
    
    def calculate_position_weights(self, total_capital: float) -> Dict[str, float]:
        """计算仓位权重"""
        weights = {}
        for symbol, position in self.positions.items():
            weights[symbol] = position.market_value / total_capital
        return weights
    
    def generate_rebalance_orders(
        self,
        target_weights: Dict[str, float],
        current_prices: Dict[str, float],
        total_capital: float,
        min_trade_amount: float = 1000,
        max_turnover: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        生成调仓订单
        
        Args:
            target_weights: 目标权重
            current_prices: 当前价格
            total_capital: 总资本
            min_trade_amount: 最小交易金额
            max_turnover: 最大换手率
        
        Returns:
            订单列表
        """
        current_weights = self.calculate_position_weights(total_capital)
        orders = []
        total_turnover = 0
        
        # 计算需要调整的仓位
        all_symbols = set(target_weights.keys()) | set(current_weights.keys())
        adjustments = []
        
        for symbol in all_symbols:
            current_weight = current_weights.get(symbol, 0)
            target_weight = target_weights.get(symbol, 0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.001:  # 权重差异大于0.1%
                trade_value = weight_diff * total_capital
                
                if abs(trade_value) >= min_trade_amount:
                    adjustments.append({
                        'symbol': symbol,
                        'current_weight': current_weight,
                        'target_weight': target_weight,
                        'weight_diff': weight_diff,
                        'trade_value': trade_value,
                        'priority': abs(weight_diff)  # 优先调整权重差异大的
                    })
        
        # 按优先级排序
        adjustments.sort(key=lambda x: x['priority'], reverse=True)
        
        # 生成订单，控制换手率
        for adj in adjustments:
            if total_turnover >= max_turnover:
                break
                
            symbol = adj['symbol']
            trade_value = adj['trade_value']
            
            if symbol not in current_prices:
                continue
                
            price = current_prices[symbol]
            current_quantity = self.positions.get(symbol, Position(
                symbol, 0, 0, price, 0, 0, 0, datetime.now()
            )).quantity
            
            target_quantity = int(adj['target_weight'] * total_capital / price)
            quantity_diff = target_quantity - current_quantity
            
            if abs(quantity_diff) >= 1:  # 至少交易1股
                side = 'buy' if quantity_diff > 0 else 'sell'
                
                # 计算交易成本
                trade_amount = abs(quantity_diff) * price
                transaction_cost = self._calculate_transaction_cost(
                    trade_amount, side
                )
                
                orders.append({
                    'symbol': symbol,
                    'quantity': abs(quantity_diff),
                    'side': side,
                    'price': price,
                    'trade_value': trade_amount,
                    'transaction_cost': transaction_cost,
                    'current_weight': adj['current_weight'],
                    'target_weight': adj['target_weight'],
                    'priority': adj['priority']
                })
                
                total_turnover += trade_amount / total_capital
        
        return orders
    
    def optimize_portfolio_weights(
        self,
        expected_returns: pd.Series,
        covariance_matrix: pd.DataFrame,
        risk_aversion: float = 1.0,
        weight_bounds: Tuple[float, float] = (0, 0.1),
        total_weight: float = 0.95
    ) -> Dict[str, float]:
        """
        基于均值-方差优化计算最优权重
        
        Args:
            expected_returns: 预期收益率
            covariance_matrix: 协方差矩阵
            risk_aversion: 风险厌恶系数
            weight_bounds: 权重边界
            total_weight: 总权重限制
        """
        try:
            from scipy.optimize import minimize
            
            n_assets = len(expected_returns)
            symbols = expected_returns.index.tolist()
            
            # 目标函数：最大化效用 = 收益 - 0.5 * 风险厌恶系数 * 风险
            def objective(weights):
                portfolio_return = np.dot(weights, expected_returns)
                portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))
                return -(portfolio_return - 0.5 * risk_aversion * portfolio_variance)
            
            # 约束条件
            constraints = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - total_weight}  # 权重和约束
            ]
            
            # 边界条件
            bounds = [weight_bounds for _ in range(n_assets)]
            
            # 初始权重（等权重）
            initial_weights = np.ones(n_assets) * total_weight / n_assets
            
            # 优化
            result = minimize(
                objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'ftol': 1e-9, 'disp': False}
            )
            
            if result.success:
                optimal_weights = dict(zip(symbols, result.x))
                # 过滤掉很小的权重
                optimal_weights = {k: v for k, v in optimal_weights.items() if v > 0.001}
                return optimal_weights
            else:
                # 优化失败，使用等权重
                equal_weight = total_weight / len(symbols)
                return {symbol: equal_weight for symbol in symbols}
                
        except ImportError:
            # 没有scipy，使用简单的权重分配
            total_expected_return = expected_returns.sum()
            if total_expected_return > 0:
                # 按预期收益率分配权重
                normalized_returns = expected_returns / total_expected_return
                weights = normalized_returns * total_weight
                # 应用权重限制
                weights = weights.clip(weight_bounds[0], weight_bounds[1])
                # 重新标准化
                weights = weights / weights.sum() * total_weight
                return weights.to_dict()
            else:
                # 等权重分配
                equal_weight = total_weight / len(expected_returns)
                return {symbol: equal_weight for symbol in expected_returns.index}
    
    def calculate_sector_exposure(
        self,
        sector_mapping: Dict[str, str],
        total_capital: float
    ) -> Dict[str, float]:
        """计算行业敞口"""
        sector_exposure = {}
        weights = self.calculate_position_weights(total_capital)
        
        for symbol, weight in weights.items():
            sector = sector_mapping.get(symbol, 'Unknown')
            sector_exposure[sector] = sector_exposure.get(sector, 0) + weight
            
        return sector_exposure
    
    def _calculate_transaction_cost(self, trade_amount: float, side: str) -> float:
        """计算交易成本"""
        # 佣金
        commission = trade_amount * self.transaction_costs['commission_rate']
        commission = max(commission, 5)  # 最低5元
        
        # 印花税（仅卖出）
        stamp_tax = 0
        if side == 'sell':
            stamp_tax = trade_amount * self.transaction_costs['stamp_tax_rate']
            
        # 过户费
        transfer_fee = trade_amount * self.transaction_costs['transfer_fee_rate']
        
        return commission + stamp_tax + transfer_fee
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取持仓摘要"""
        if not self.positions:
            return {
                'position_count': 0,
                'total_market_value': 0,
                'total_unrealized_pnl': 0,
                'total_realized_pnl': 0,
                'top_positions': [],
                'worst_performers': [],
                'best_performers': []
            }
            
        positions_list = list(self.positions.values())
        
        # 按市值排序
        positions_by_value = sorted(positions_list, key=lambda x: abs(x.market_value), reverse=True)
        top_positions = positions_by_value[:10]
        
        # 按收益率排序
        positions_with_return = [p for p in positions_list if p.avg_cost > 0]
        if positions_with_return:
            best_performers = sorted(positions_with_return, key=lambda x: x.return_rate, reverse=True)[:5]
            worst_performers = sorted(positions_with_return, key=lambda x: x.return_rate)[:5]
        else:
            best_performers = []
            worst_performers = []
        
        return {
            'position_count': len(self.positions),
            'total_market_value': sum(p.market_value for p in positions_list),
            'total_unrealized_pnl': sum(p.unrealized_pnl for p in positions_list),
            'total_realized_pnl': sum(p.realized_pnl for p in positions_list),
            'top_positions': [
                {
                    'symbol': p.symbol,
                    'quantity': p.quantity,
                    'market_value': p.market_value,
                    'unrealized_pnl': p.unrealized_pnl,
                    'return_rate': p.return_rate
                } for p in top_positions
            ],
            'best_performers': [
                {
                    'symbol': p.symbol,
                    'return_rate': p.return_rate,
                    'unrealized_pnl': p.unrealized_pnl
                } for p in best_performers
            ],
            'worst_performers': [
                {
                    'symbol': p.symbol,
                    'return_rate': p.return_rate,
                    'unrealized_pnl': p.unrealized_pnl
                } for p in worst_performers
            ]
        }
    
    def export_positions(self) -> pd.DataFrame:
        """导出持仓数据"""
        if not self.positions:
            return pd.DataFrame()
            
        data = []
        for position in self.positions.values():
            data.append({
                'symbol': position.symbol,
                'quantity': position.quantity,
                'avg_cost': position.avg_cost,
                'current_price': position.current_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'realized_pnl': position.realized_pnl,
                'return_rate': position.return_rate,
                'last_updated': position.last_updated
            })
            
        return pd.DataFrame(data)
"""
风险管理系统
实现多层级风险控制机制
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, date
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """风险警报类型"""
    POSITION_LIMIT = "position_limit"
    DRAWDOWN = "drawdown"
    VOLATILITY = "volatility"
    CONCENTRATION = "concentration"
    LIQUIDITY = "liquidity"
    VAR_BREACH = "var_breach"

@dataclass
class RiskAlert:
    """风险警报"""
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    timestamp: datetime
    symbol: Optional[str] = None
    current_value: Optional[float] = None
    threshold: Optional[float] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass 
class RiskLimits:
    """风险限制配置"""
    # 仓位限制
    max_position_size: float = 0.1  # 单股最大仓位
    max_total_position: float = 0.95  # 总仓位限制
    max_sector_exposure: float = 0.3  # 单行业最大敞口
    max_positions_count: int = 20  # 最大持仓数量
    
    # 损失限制
    max_daily_loss: float = 0.05  # 日最大损失
    max_drawdown: float = 0.2  # 最大回撤
    stop_loss_threshold: float = 0.1  # 止损阈值
    
    # 波动率限制
    max_portfolio_volatility: float = 0.3  # 最大组合波动率
    max_tracking_error: float = 0.1  # 最大跟踪误差
    
    # 流动性限制
    min_avg_volume: float = 1000000  # 最小平均成交量
    max_volume_participation: float = 0.1  # 最大成交量参与率
    
    # VaR限制
    var_confidence_level: float = 0.05  # VaR置信水平
    max_var: float = 0.03  # 最大VaR

class RiskManager:
    """风险管理器"""
    
    def __init__(self, limits: RiskLimits):
        self.limits = limits
        self.alerts: List[RiskAlert] = []
        self.risk_metrics: Dict[str, float] = {}
        
    def check_pre_trade_risk(
        self,
        symbol: str,
        quantity: float,
        price: float,
        side: str,
        current_positions: Dict[str, float],
        portfolio_value: float,
        market_data: Optional[pd.DataFrame] = None
    ) -> Tuple[bool, List[RiskAlert]]:
        """
        交易前风险检查
        
        Returns:
            (是否允许交易, 风险警报列表)
        """
        alerts = []
        allow_trade = True
        
        # 1. 单股仓位限制检查
        position_alert = self._check_position_limits(
            symbol, quantity, price, current_positions, portfolio_value
        )
        if position_alert:
            alerts.append(position_alert)
            if position_alert.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                allow_trade = False
                
        # 2. 总仓位检查
        total_position_alert = self._check_total_position_limit(
            quantity, price, current_positions, portfolio_value
        )
        if total_position_alert:
            alerts.append(total_position_alert)
            if total_position_alert.risk_level == RiskLevel.CRITICAL:
                allow_trade = False
                
        # 3. 流动性检查
        if market_data is not None:
            liquidity_alert = self._check_liquidity_risk(
                symbol, quantity, price, market_data
            )
            if liquidity_alert:
                alerts.append(liquidity_alert)
                if liquidity_alert.risk_level == RiskLevel.CRITICAL:
                    allow_trade = False
                    
        # 4. 集中度风险检查
        concentration_alert = self._check_concentration_risk(
            symbol, quantity, price, current_positions, portfolio_value
        )
        if concentration_alert:
            alerts.append(concentration_alert)
            if concentration_alert.risk_level == RiskLevel.CRITICAL:
                allow_trade = False
                
        return allow_trade, alerts
    
    def check_portfolio_risk(
        self,
        positions: Dict[str, float],
        portfolio_value: float,
        returns_history: pd.Series,
        benchmark_returns: Optional[pd.Series] = None
    ) -> List[RiskAlert]:
        """组合级风险检查"""
        alerts = []
        
        # 1. 回撤检查
        drawdown_alert = self._check_drawdown_risk(returns_history)
        if drawdown_alert:
            alerts.append(drawdown_alert)
            
        # 2. 波动率检查
        volatility_alert = self._check_volatility_risk(returns_history)
        if volatility_alert:
            alerts.append(volatility_alert)
            
        # 3. VaR检查
        var_alert = self._check_var_risk(returns_history)
        if var_alert:
            alerts.append(var_alert)
            
        # 4. 跟踪误差检查
        if benchmark_returns is not None:
            tracking_alert = self._check_tracking_error(returns_history, benchmark_returns)
            if tracking_alert:
                alerts.append(tracking_alert)
                
        # 5. 日损失检查
        daily_loss_alert = self._check_daily_loss(returns_history)
        if daily_loss_alert:
            alerts.append(daily_loss_alert)
            
        return alerts
    
    def _check_position_limits(
        self,
        symbol: str,
        quantity: float,
        price: float,
        current_positions: Dict[str, float],
        portfolio_value: float
    ) -> Optional[RiskAlert]:
        """检查单股仓位限制"""
        current_position = current_positions.get(symbol, 0)
        new_position_value = (current_position + quantity) * price
        position_ratio = new_position_value / portfolio_value
        
        if position_ratio > self.limits.max_position_size:
            risk_level = RiskLevel.CRITICAL if position_ratio > self.limits.max_position_size * 1.2 else RiskLevel.HIGH
            
            return RiskAlert(
                alert_type=AlertType.POSITION_LIMIT,
                risk_level=risk_level,
                message=f"{symbol} 仓位比例 {position_ratio:.2%} 超过限制 {self.limits.max_position_size:.2%}",
                timestamp=datetime.now(),
                symbol=symbol,
                current_value=position_ratio,
                threshold=self.limits.max_position_size
            )
        return None
    
    def _check_total_position_limit(
        self,
        quantity: float,
        price: float,
        current_positions: Dict[str, float],
        portfolio_value: float
    ) -> Optional[RiskAlert]:
        """检查总仓位限制"""
        current_total_value = sum(abs(pos * price) for pos in current_positions.values())
        new_total_value = current_total_value + abs(quantity * price)
        total_position_ratio = new_total_value / portfolio_value
        
        if total_position_ratio > self.limits.max_total_position:
            return RiskAlert(
                alert_type=AlertType.POSITION_LIMIT,
                risk_level=RiskLevel.CRITICAL,
                message=f"总仓位比例 {total_position_ratio:.2%} 超过限制 {self.limits.max_total_position:.2%}",
                timestamp=datetime.now(),
                current_value=total_position_ratio,
                threshold=self.limits.max_total_position
            )
        return None
    
    def _check_liquidity_risk(
        self,
        symbol: str,
        quantity: float,
        price: float,
        market_data: pd.DataFrame
    ) -> Optional[RiskAlert]:
        """检查流动性风险"""
        if symbol not in market_data.index:
            return RiskAlert(
                alert_type=AlertType.LIQUIDITY,
                risk_level=RiskLevel.HIGH,
                message=f"{symbol} 缺少市场数据",
                timestamp=datetime.now(),
                symbol=symbol
            )
            
        stock_data = market_data.loc[symbol]
        avg_volume = stock_data.get('avg_volume_20', 0)
        daily_volume = stock_data.get('volume', 0)
        
        # 检查平均成交量
        if avg_volume < self.limits.min_avg_volume:
            return RiskAlert(
                alert_type=AlertType.LIQUIDITY,
                risk_level=RiskLevel.HIGH,
                message=f"{symbol} 平均成交量 {avg_volume} 低于最低要求 {self.limits.min_avg_volume}",
                timestamp=datetime.now(),
                symbol=symbol,
                current_value=avg_volume,
                threshold=self.limits.min_avg_volume
            )
            
        # 检查成交量参与率
        if daily_volume > 0:
            participation_rate = abs(quantity) / daily_volume
            if participation_rate > self.limits.max_volume_participation:
                return RiskAlert(
                    alert_type=AlertType.LIQUIDITY,
                    risk_level=RiskLevel.MEDIUM,
                    message=f"{symbol} 成交量参与率 {participation_rate:.2%} 超过限制 {self.limits.max_volume_participation:.2%}",
                    timestamp=datetime.now(),
                    symbol=symbol,
                    current_value=participation_rate,
                    threshold=self.limits.max_volume_participation
                )
        return None
    
    def _check_concentration_risk(
        self,
        symbol: str,
        quantity: float,
        price: float,
        current_positions: Dict[str, float],
        portfolio_value: float
    ) -> Optional[RiskAlert]:
        """检查集中度风险"""
        # 检查持仓数量
        active_positions = len([pos for pos in current_positions.values() if abs(pos) > 0])
        if symbol not in current_positions or current_positions[symbol] == 0:
            active_positions += 1  # 新增持仓
            
        if active_positions > self.limits.max_positions_count:
            return RiskAlert(
                alert_type=AlertType.CONCENTRATION,
                risk_level=RiskLevel.HIGH,
                message=f"持仓数量 {active_positions} 超过限制 {self.limits.max_positions_count}",
                timestamp=datetime.now(),
                current_value=active_positions,
                threshold=self.limits.max_positions_count
            )
        return None
    
    def _check_drawdown_risk(self, returns: pd.Series) -> Optional[RiskAlert]:
        """检查回撤风险"""
        if len(returns) < 2:
            return None
            
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        current_drawdown = abs(drawdown.iloc[-1])
        
        if current_drawdown > self.limits.max_drawdown:
            risk_level = RiskLevel.CRITICAL if current_drawdown > self.limits.max_drawdown * 1.5 else RiskLevel.HIGH
            
            return RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                risk_level=risk_level,
                message=f"当前回撤 {current_drawdown:.2%} 超过限制 {self.limits.max_drawdown:.2%}",
                timestamp=datetime.now(),
                current_value=current_drawdown,
                threshold=self.limits.max_drawdown
            )
        return None
    
    def _check_volatility_risk(self, returns: pd.Series) -> Optional[RiskAlert]:
        """检查波动率风险"""
        if len(returns) < 20:  # 需要足够的数据
            return None
            
        # 计算滚动波动率
        rolling_vol = returns.rolling(20).std() * np.sqrt(252)
        current_vol = rolling_vol.iloc[-1]
        
        if current_vol > self.limits.max_portfolio_volatility:
            risk_level = RiskLevel.HIGH if current_vol > self.limits.max_portfolio_volatility * 1.3 else RiskLevel.MEDIUM
            
            return RiskAlert(
                alert_type=AlertType.VOLATILITY,
                risk_level=risk_level,
                message=f"组合波动率 {current_vol:.2%} 超过限制 {self.limits.max_portfolio_volatility:.2%}",
                timestamp=datetime.now(),
                current_value=current_vol,
                threshold=self.limits.max_portfolio_volatility
            )
        return None
    
    def _check_var_risk(self, returns: pd.Series) -> Optional[RiskAlert]:
        """检查VaR风险"""
        if len(returns) < 100:  # 需要足够的历史数据
            return None
            
        # 计算VaR
        var = returns.quantile(self.limits.var_confidence_level)
        
        if abs(var) > self.limits.max_var:
            return RiskAlert(
                alert_type=AlertType.VAR_BREACH,
                risk_level=RiskLevel.HIGH,
                message=f"VaR {abs(var):.2%} 超过限制 {self.limits.max_var:.2%}",
                timestamp=datetime.now(),
                current_value=abs(var),
                threshold=self.limits.max_var
            )
        return None
    
    def _check_tracking_error(self, returns: pd.Series, benchmark_returns: pd.Series) -> Optional[RiskAlert]:
        """检查跟踪误差"""
        # 对齐数据
        aligned_data = pd.DataFrame({
            'portfolio': returns,
            'benchmark': benchmark_returns
        }).dropna()
        
        if len(aligned_data) < 50:
            return None
            
        # 计算跟踪误差
        excess_returns = aligned_data['portfolio'] - aligned_data['benchmark']
        tracking_error = excess_returns.std() * np.sqrt(252)
        
        if tracking_error > self.limits.max_tracking_error:
            return RiskAlert(
                alert_type=AlertType.VOLATILITY,
                risk_level=RiskLevel.MEDIUM,
                message=f"跟踪误差 {tracking_error:.2%} 超过限制 {self.limits.max_tracking_error:.2%}",
                timestamp=datetime.now(),
                current_value=tracking_error,
                threshold=self.limits.max_tracking_error
            )
        return None
    
    def _check_daily_loss(self, returns: pd.Series) -> Optional[RiskAlert]:
        """检查日损失"""
        if len(returns) == 0:
            return None
            
        latest_return = returns.iloc[-1]
        
        if latest_return < -self.limits.max_daily_loss:
            return RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.HIGH,
                message=f"日损失 {abs(latest_return):.2%} 超过限制 {self.limits.max_daily_loss:.2%}",
                timestamp=datetime.now(),
                current_value=abs(latest_return),
                threshold=self.limits.max_daily_loss
            )
        return None
    
    def calculate_position_size(
        self,
        symbol: str,
        signal_strength: float,
        current_price: float,
        portfolio_value: float,
        volatility: float,
        current_positions: Dict[str, float]
    ) -> float:
        """
        基于风险的仓位规模计算
        
        使用Kelly公式的改进版本
        """
        # 基础仓位大小
        base_position_size = self.limits.max_position_size * abs(signal_strength)
        
        # 波动率调整
        volatility_adjustment = min(1.0, 0.2 / max(volatility, 0.01))
        
        # 当前持仓调整
        current_position = current_positions.get(symbol, 0)
        current_weight = abs(current_position * current_price) / portfolio_value
        
        # 如果已有较大仓位，减少新增
        position_adjustment = max(0.1, 1 - current_weight / self.limits.max_position_size)
        
        # 计算目标仓位
        target_position_size = base_position_size * volatility_adjustment * position_adjustment
        
        # 确保不超过限制
        target_position_size = min(target_position_size, self.limits.max_position_size)
        
        # 转换为股数
        target_shares = int(target_position_size * portfolio_value / current_price)
        
        return target_shares
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        recent_alerts = [alert for alert in self.alerts if 
                        (datetime.now() - alert.timestamp).days < 7]
        
        alert_counts = {}
        for alert in recent_alerts:
            risk_level = alert.risk_level.value
            alert_counts[risk_level] = alert_counts.get(risk_level, 0) + 1
            
        return {
            'total_alerts': len(recent_alerts),
            'alert_distribution': alert_counts,
            'risk_metrics': self.risk_metrics,
            'limits': self.limits.__dict__
        }
    
    def add_alert(self, alert: RiskAlert):
        """添加风险警报"""
        self.alerts.append(alert)
        
        # 保留最近1000条警报
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-1000:]
    
    def get_alerts_by_type(self, alert_type: AlertType, days: int = 7) -> List[RiskAlert]:
        """按类型获取警报"""
        cutoff_date = datetime.now() - pd.Timedelta(days=days)
        return [alert for alert in self.alerts 
                if alert.alert_type == alert_type and alert.timestamp >= cutoff_date]
    
    def get_alerts_by_symbol(self, symbol: str, days: int = 7) -> List[RiskAlert]:
        """按股票获取警报"""
        cutoff_date = datetime.now() - pd.Timedelta(days=days)
        return [alert for alert in self.alerts 
                if alert.symbol == symbol and alert.timestamp >= cutoff_date]
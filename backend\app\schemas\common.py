"""
通用数据模型和响应格式
提供标准化的API响应格式和通用数据结构
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from pydantic import BaseModel, Field, ConfigDict

# 泛型类型变量
T = TypeVar('T')

# ===== 基础响应模型 =====

class BaseResponse(BaseModel):
    """基础响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True
    )
    
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    request_id: Optional[str] = Field(None, description="请求ID")

class SuccessResponse(BaseResponse, Generic[T]):
    """成功响应模型"""
    success: bool = Field(True, description="请求成功")
    data: T = Field(..., description="响应数据")
    code: int = Field(200, description="状态码")

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(False, description="请求失败")
    error: Dict[str, Any] = Field(..., description="错误详情")
    code: int = Field(400, description="错误状态码")

class ValidationErrorResponse(ErrorResponse):
    """验证错误响应模型"""
    code: int = Field(422, description="验证错误状态码")
    error: Dict[str, Any] = Field(
        ..., 
        description="验证错误详情",
        example={
            "type": "validation_error",
            "message": "输入数据验证失败",
            "details": [
                {
                    "field": "email",
                    "message": "邮箱格式不正确",
                    "input": "invalid-email"
                }
            ]
        }
    )

# ===== 分页响应模型 =====

class PaginationMeta(BaseModel):
    """分页元数据"""
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., ge=1, le=100, description="每页大小")
    total: int = Field(..., ge=0, description="总记录数")
    total_pages: int = Field(..., ge=0, description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class PaginatedResponse(BaseResponse, Generic[T]):
    """分页响应模型"""
    data: List[T] = Field(..., description="数据列表")
    pagination: PaginationMeta = Field(..., description="分页信息")

# ===== 请求模型 =====

class PaginationRequest(BaseModel):
    """分页请求参数"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页大小")

class SortRequest(BaseModel):
    """排序请求参数"""
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_order: Optional[str] = Field("asc", pattern="^(asc|desc)$", description="排序方向")

class SearchRequest(BaseModel):
    """搜索请求参数"""
    keyword: Optional[str] = Field(None, max_length=100, description="搜索关键词")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")

class ListRequest(PaginationRequest, SortRequest, SearchRequest):
    """列表查询请求参数 - 组合了分页、排序、搜索功能"""

# ===== 操作响应模型 =====

class CreateResponse(BaseModel):
    """创建操作响应"""
    id: Union[str, int] = Field(..., description="创建的资源ID")
    message: str = Field("创建成功", description="操作消息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class UpdateResponse(BaseModel):
    """更新操作响应"""
    id: Union[str, int] = Field(..., description="更新的资源ID")
    message: str = Field("更新成功", description="操作消息")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

class DeleteResponse(BaseModel):
    """删除操作响应"""
    id: Union[str, int] = Field(..., description="删除的资源ID")
    message: str = Field("删除成功", description="操作消息")
    deleted_at: datetime = Field(default_factory=datetime.now, description="删除时间")

class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    total: int = Field(..., description="总操作数")
    success_count: int = Field(..., description="成功数量")
    failure_count: int = Field(..., description="失败数量")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="错误详情")

# ===== 状态响应模型 =====

class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str = Field("healthy", description="服务状态")
    version: str = Field(..., description="服务版本")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="依赖服务状态")

class MetricsResponse(BaseModel):
    """指标响应"""
    metrics: Dict[str, Union[int, float, str]] = Field(..., description="指标数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="采集时间")

# ===== 文件上传响应模型 =====

class FileUploadResponse(BaseModel):
    """文件上传响应"""
    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(..., description="文件大小(字节)")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")
    file_url: Optional[str] = Field(None, description="文件访问URL")

# ===== WebSocket消息模型 =====

class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Any = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间戳")
    channel: Optional[str] = Field(None, description="消息频道")
    user_id: Optional[str] = Field(None, description="用户ID")

class WebSocketResponse(BaseModel):
    """WebSocket响应模型"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("", description="响应消息")
    data: Any = Field(None, description="响应数据")

# ===== 异步任务响应模型 =====

class TaskResponse(BaseModel):
    """异步任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field("", description="任务消息")
    progress: Optional[float] = Field(None, ge=0, le=100, description="任务进度(百分比)")
    result: Optional[Any] = Field(None, description="任务结果")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

# ===== 统计响应模型 =====

class StatsResponse(BaseModel):
    """统计数据响应"""
    total: int = Field(..., description="总数")
    active: int = Field(..., description="活跃数")
    inactive: int = Field(..., description="非活跃数")
    growth_rate: Optional[float] = Field(None, description="增长率")
    period: str = Field(..., description="统计周期")
    timestamp: datetime = Field(default_factory=datetime.now, description="统计时间")

# ===== 配置响应模型 =====

class ConfigResponse(BaseModel):
    """配置响应模型"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="配置描述")
    is_public: bool = Field(True, description="是否公开配置")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

# ===== 响应工厂函数 =====

def create_success_response(
    data: T, 
    message: str = "操作成功",
    code: int = 200
) -> SuccessResponse[T]:
    """创建成功响应"""
    return SuccessResponse(
        data=data,
        message=message,
        code=code
    )

def create_error_response(
    message: str,
    error_type: str = "error",
    details: Optional[Any] = None,
    code: int = 400
) -> ErrorResponse:
    """创建错误响应"""
    return ErrorResponse(
        error={
            "type": error_type,
            "message": message,
            "details": details
        },
        code=code
    )

def create_paginated_response(
    items: List[T],
    total: int,
    page: int,
    page_size: int,
    message: str = "查询成功"
) -> PaginatedResponse[T]:
    """创建分页响应"""
    total_pages = (total + page_size - 1) // page_size
    
    return PaginatedResponse(
        data=items,
        message=message,
        pagination=PaginationMeta(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
    )

def create_task_response(
    task_id: str,
    status: str = "pending",
    message: str = "任务已创建"
) -> TaskResponse:
    """创建任务响应"""
    return TaskResponse(
        task_id=task_id,
        status=status,
        message=message
    )

# ===== 类型别名 =====

# 常用响应类型别名
ApiResponse = Union[SuccessResponse[Any], ErrorResponse]
ListApiResponse = Union[PaginatedResponse[Any], ErrorResponse]
SimpleResponse = Union[SuccessResponse[str], ErrorResponse]

# 导出所有模型
__all__ = [
    # 基础响应
    "BaseResponse",
    "SuccessResponse", 
    "ErrorResponse",
    "ValidationErrorResponse",
    
    # 分页相关
    "PaginationMeta",
    "PaginatedResponse",
    "PaginationRequest",
    
    # 请求模型
    "SortRequest",
    "SearchRequest", 
    "ListRequest",
    
    # 操作响应
    "CreateResponse",
    "UpdateResponse",
    "DeleteResponse",
    "BatchOperationResponse",
    
    # 状态响应
    "HealthCheckResponse",
    "MetricsResponse",
    
    # 文件响应
    "FileUploadResponse",
    
    # WebSocket
    "WebSocketMessage",
    "WebSocketResponse",
    
    # 任务响应
    "TaskResponse",
    
    # 统计响应
    "StatsResponse",
    
    # 配置响应
    "ConfigResponse",
    
    # 工厂函数
    "create_success_response",
    "create_error_response", 
    "create_paginated_response",
    "create_task_response",
    
    # 类型别名
    "ApiResponse",
    "ListApiResponse",
    "SimpleResponse"
]

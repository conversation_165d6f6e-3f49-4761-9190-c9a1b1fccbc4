"""
Market data schemas for the quantitative trading platform.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator


class TickData(BaseModel):
    """Tick data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Timestamp of the tick")
    price: Decimal = Field(..., description="Last trade price")
    volume: int = Field(..., description="Trade volume")
    bid_price: Optional[Decimal] = Field(None, description="Best bid price")
    ask_price: Optional[Decimal] = Field(None, description="Best ask price")
    bid_volume: Optional[int] = Field(None, description="Best bid volume")
    ask_volume: Optional[int] = Field(None, description="Best ask volume")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class KlineData(BaseModel):
    """K-line (candlestick) data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="K-line timestamp")
    open_price: Decimal = Field(..., description="Opening price")
    high_price: Decimal = Field(..., description="Highest price")
    low_price: Decimal = Field(..., description="Lowest price")
    close_price: Decimal = Field(..., description="Closing price")
    volume: int = Field(..., description="Trading volume")
    amount: Optional[Decimal] = Field(None, description="Trading amount")
    interval: str = Field(..., description="Time interval (1m, 5m, 1h, 1d, etc.)")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class MarketDepth(BaseModel):
    """Market depth (order book) data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Market depth timestamp")
    bids: List[List[Decimal]] = Field(..., description="Bid prices and volumes")
    asks: List[List[Decimal]] = Field(..., description="Ask prices and volumes")

    @field_validator("bids", "asks")
    @classmethod
    def validate_depth_data(cls, v):
        """Validate bid/ask depth data format."""
        if not isinstance(v, list):
            raise ValueError("Depth data must be a list")
        for item in v:
            if not isinstance(item, list) or len(item) != 2:
                raise ValueError("Each depth item must be [price, volume]")
        return v

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class MarketDataRequest(BaseModel):
    """Market data subscription request schema."""

    symbols: List[str] = Field(..., description="List of symbols to subscribe")
    data_types: List[str] = Field(..., description="Types of data (tick, kline, depth)")
    interval: Optional[str] = Field(None, description="K-line interval for kline data")

    @field_validator("data_types")
    @classmethod
    def validate_data_types(cls, v):
        """Validate data types."""
        valid_types = {"tick", "kline", "depth"}
        for data_type in v:
            if data_type not in valid_types:
                raise ValueError(f"Invalid data type: {data_type}")
        return v


class MarketDataResponse(BaseModel):
    """Market data response schema."""

    symbol: str = Field(..., description="Symbol identifier")
    data_type: str = Field(..., description="Type of data")
    data: Dict[str, Any] = Field(..., description="Market data payload")
    timestamp: datetime = Field(..., description="Response timestamp")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class MarketStatus(BaseModel):
    """Market status schema."""

    market: str = Field(..., description="Market identifier")
    status: str = Field(..., description="Market status (open, closed, pre_open, etc.)")
    trading_day: str = Field(..., description="Trading day")
    open_time: Optional[datetime] = Field(None, description="Market open time")
    close_time: Optional[datetime] = Field(None, description="Market close time")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class QuoteData(BaseModel):
    """Quote data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Quote timestamp")
    bid_price: Decimal = Field(..., description="Bid price")
    ask_price: Decimal = Field(..., description="Ask price")
    bid_volume: int = Field(..., description="Bid volume")
    ask_volume: int = Field(..., description="Ask volume")
    last_price: Optional[Decimal] = Field(None, description="Last trade price")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class TradeData(BaseModel):
    """Trade data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Trade timestamp")
    price: Decimal = Field(..., description="Trade price")
    volume: int = Field(..., description="Trade volume")
    direction: Optional[str] = Field(None, description="Trade direction (buy/sell)")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class WatchlistItemBase(BaseModel):
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")


class WatchlistItemCreate(WatchlistItemBase):
    """创建观察列表项目的模型"""


class WatchlistItemResponse(WatchlistItemBase):
    id: int
    user_id: int
    created_at: datetime

    class Config:
        orm_mode = True

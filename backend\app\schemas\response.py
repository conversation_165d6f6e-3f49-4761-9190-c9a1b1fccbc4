"""
通用响应模式
"""
from typing import Any, Generic, Optional, TypeVar
from pydantic import BaseModel

DataType = TypeVar('DataType')

class Response(BaseModel, Generic[DataType]):
    """通用响应模式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[DataType] = None
    code: int = 200
    
    @classmethod
    def success_response(cls, data: Any = None, message: str = "操作成功") -> "Response":
        """创建成功响应"""
        return cls(success=True, message=message, data=data, code=200)
    
    @classmethod
    def error_response(cls, message: str = "操作失败", code: int = 400) -> "Response":
        """创建错误响应"""
        return cls(success=False, message=message, data=None, code=code)
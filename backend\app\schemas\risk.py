"""
风控相关的Pydantic模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class RiskLevel(str, Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """告警状态"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    IGNORED = "ignored"


class RiskLimitCreate(BaseModel):
    """风控限制创建模型"""
    max_position_size: float = Field(0.2, ge=0, le=1, description="单个持仓最大占比")
    max_leverage: float = Field(2.0, ge=1, le=10, description="最大杠杆倍数")
    max_daily_loss: float = Field(0.05, ge=0, le=1, description="单日最大亏损比例")
    max_drawdown: float = Field(0.15, ge=0, le=1, description="最大回撤比例")
    stop_loss_required: bool = Field(True, description="是否强制止损")
    default_stop_loss: float = Field(0.05, ge=0, le=1, description="默认止损比例")
    max_orders_per_day: int = Field(100, ge=1, description="每日最大订单数")
    max_order_value: float = Field(100000, gt=0, description="单笔订单最大金额")
    allowed_symbols: Optional[List[str]] = Field(default_factory=list, description="允许交易的标的")
    blocked_symbols: Optional[List[str]] = Field(default_factory=list, description="禁止交易的标的")
    trading_hours_only: bool = Field(True, description="仅在交易时间下单")


class RiskLimitUpdate(BaseModel):
    """风控限制更新模型"""
    max_position_size: Optional[float] = Field(None, ge=0, le=1)
    max_leverage: Optional[float] = Field(None, ge=1, le=10)
    max_daily_loss: Optional[float] = Field(None, ge=0, le=1)
    max_drawdown: Optional[float] = Field(None, ge=0, le=1)
    stop_loss_required: Optional[bool] = None
    default_stop_loss: Optional[float] = Field(None, ge=0, le=1)
    max_orders_per_day: Optional[int] = Field(None, ge=1)
    max_order_value: Optional[float] = Field(None, gt=0)
    allowed_symbols: Optional[List[str]] = None
    blocked_symbols: Optional[List[str]] = None
    trading_hours_only: Optional[bool] = None


class RiskLimitResponse(BaseModel):
    """风控限制响应模型"""
    user_id: int
    max_position_size: float
    max_leverage: float
    max_daily_loss: float
    max_drawdown: float
    stop_loss_required: bool
    default_stop_loss: float
    max_orders_per_day: int
    max_order_value: float
    allowed_symbols: List[str]
    blocked_symbols: List[str]
    trading_hours_only: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RiskMetricsResponse(BaseModel):
    """风险指标响应模型"""
    user_id: int
    timestamp: datetime
    capital_usage_rate: float = Field(..., description="资金使用率")
    position_concentration: float = Field(..., description="持仓集中度")
    max_drawdown: float = Field(..., description="最大回撤")
    current_drawdown: float = Field(..., description="当前回撤")
    var_95: float = Field(..., description="95% VaR")
    var_99: float = Field(..., description="99% VaR")
    sharpe_ratio: float = Field(..., description="夏普比率")
    leverage_ratio: float = Field(..., description="杠杆率")
    margin_ratio: float = Field(..., description="保证金比例")
    risk_score: int = Field(..., ge=0, le=100, description="风险评分")
    risk_level: str = Field(..., description="风险等级")
    warnings: List[str] = Field(default_factory=list, description="风险警告")


class RiskAlertResponse(BaseModel):
    """风险告警响应模型"""
    id: str
    user_id: int
    type: str
    level: RiskLevel
    status: AlertStatus
    title: str
    message: str
    details: Optional[Dict[str, Any]] = None
    created_at: datetime
    resolved_at: Optional[datetime] = None
    resolution_note: Optional[str] = None

    class Config:
        from_attributes = True


class RiskReportResponse(BaseModel):
    """风险报告响应模型"""
    user_id: int
    period: str
    generated_at: datetime
    summary: Dict[str, Any]
    metrics_history: Dict[str, Any]
    risk_events: List[Dict[str, Any]]
    recommendations: List[str]

    class Config:
        from_attributes = True


class OrderRiskCheck(BaseModel):
    """订单风险检查请求"""
    symbol: str
    direction: str  # BUY/SELL
    volume: int
    price: float
    order_type: str = "LIMIT"


class RiskCheckResponse(BaseModel):
    """风险检查响应"""
    passed: bool
    risk_score: int
    checks: List[Dict[str, Any]]
    recommendation: str
    warnings: Optional[List[str]] = None
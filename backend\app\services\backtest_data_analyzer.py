"""
回测数据分析器
提供深度的回测结果分析，包括风险指标、绩效归因、统计分析等
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from scipy import stats
import json

logger = logging.getLogger(__name__)


@dataclass
class RiskMetrics:
    """风险指标"""
    var_95: float = 0.0  # 95% VaR
    var_99: float = 0.0  # 99% VaR
    cvar_95: float = 0.0  # 95% CVaR
    cvar_99: float = 0.0  # 99% CVaR
    beta: float = 0.0  # 贝塔系数
    alpha: float = 0.0  # 阿尔法系数
    information_ratio: float = 0.0  # 信息比率
    tracking_error: float = 0.0  # 跟踪误差
    downside_deviation: float = 0.0  # 下行标准差
    sortino_ratio: float = 0.0  # 索提诺比率


@dataclass
class PerformanceAttribution:
    """绩效归因分析"""
    asset_allocation: Dict[str, float] = None  # 资产配置贡献
    security_selection: Dict[str, float] = None  # 个股选择贡献
    interaction_effect: float = 0.0  # 交互效应
    total_active_return: float = 0.0  # 总主动收益


@dataclass
class SeasonalityAnalysis:
    """季节性分析"""
    monthly_returns: Dict[str, float] = None  # 月度收益统计
    quarterly_returns: Dict[str, float] = None  # 季度收益统计
    day_of_week_returns: Dict[str, float] = None  # 周内效应
    best_month: str = ""  # 最佳月份
    worst_month: str = ""  # 最差月份


class BacktestDataAnalyzer:
    """回测数据分析器"""
    
    def __init__(self):
        self.risk_free_rate = 0.03  # 无风险利率
        
    async def analyze_backtest_results(
        self,
        backtest_result: Dict[str, Any],
        benchmark_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """
        分析回测结果
        
        Args:
            backtest_result: 回测结果数据
            benchmark_data: 基准数据
            
        Returns:
            详细分析结果
        """
        try:
            # 解析回测数据
            equity_curve = self._parse_equity_curve(backtest_result.get("equity_curve", []))
            trades = backtest_result.get("trades", [])
            
            if equity_curve.empty:
                raise ValueError("权益曲线数据为空")
            
            # 计算收益率序列
            returns = self._calculate_returns(equity_curve)
            
            # 风险指标分析
            risk_metrics = self._calculate_risk_metrics(returns, benchmark_data)
            
            # 绩效归因分析
            attribution = self._analyze_performance_attribution(trades, returns)
            
            # 季节性分析
            seasonality = self._analyze_seasonality(equity_curve)
            
            # 回撤分析
            drawdown_analysis = self._analyze_drawdowns(equity_curve)
            
            # 交易分析
            trade_analysis = self._analyze_trades(trades)
            
            # 滚动绩效分析
            rolling_performance = self._calculate_rolling_performance(returns)
            
            # 统计显著性检验
            statistical_tests = self._perform_statistical_tests(returns)
            
            # Monte Carlo 模拟
            monte_carlo_results = await self._run_monte_carlo_simulation(returns)
            
            analysis_result = {
                "risk_metrics": self._risk_metrics_to_dict(risk_metrics),
                "performance_attribution": self._attribution_to_dict(attribution),
                "seasonality_analysis": self._seasonality_to_dict(seasonality),
                "drawdown_analysis": drawdown_analysis,
                "trade_analysis": trade_analysis,
                "rolling_performance": rolling_performance,
                "statistical_tests": statistical_tests,
                "monte_carlo": monte_carlo_results,
                "summary": self._generate_analysis_summary(risk_metrics, attribution, seasonality)
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"回测结果分析失败: {str(e)}")
            raise
    
    def _parse_equity_curve(self, equity_data: List[Dict]) -> pd.DataFrame:
        """解析权益曲线数据"""
        if not equity_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(equity_data)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        return df
    
    def _calculate_returns(self, equity_curve: pd.DataFrame) -> pd.Series:
        """计算收益率序列"""
        if 'value' not in equity_curve.columns:
            return pd.Series()
        
        returns = equity_curve['value'].pct_change().dropna()
        return returns
    
    def _calculate_risk_metrics(
        self, 
        returns: pd.Series, 
        benchmark_data: Optional[pd.DataFrame] = None
    ) -> RiskMetrics:
        """计算风险指标"""
        if returns.empty:
            return RiskMetrics()
        
        # VaR计算
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        
        # CVaR计算
        cvar_95 = returns[returns <= var_95].mean()
        cvar_99 = returns[returns <= var_99].mean()
        
        # 下行标准差
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() * np.sqrt(252) if len(negative_returns) > 0 else 0
        
        # 索提诺比率
        annual_return = returns.mean() * 252
        sortino_ratio = (annual_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
        
        # 基准相关指标
        beta, alpha, information_ratio, tracking_error = 0, 0, 0, 0
        if benchmark_data is not None:
            # 这里可以添加与基准的比较分析
            pass
        
        return RiskMetrics(
            var_95=var_95,
            var_99=var_99,
            cvar_95=cvar_95,
            cvar_99=cvar_99,
            beta=beta,
            alpha=alpha,
            information_ratio=information_ratio,
            tracking_error=tracking_error,
            downside_deviation=downside_deviation,
            sortino_ratio=sortino_ratio
        )
    
    def _analyze_performance_attribution(
        self, 
        trades: List[Dict], 
        returns: pd.Series
    ) -> PerformanceAttribution:
        """绩效归因分析"""
        # 简化的归因分析
        asset_allocation = {}
        security_selection = {}
        
        # 按股票统计交易贡献
        symbol_pnl = {}
        for trade in trades:
            if trade.get("action") == "卖出" and "pnl" in trade:
                symbol = trade["symbol"]
                pnl = trade["pnl"]
                if symbol not in symbol_pnl:
                    symbol_pnl[symbol] = 0
                symbol_pnl[symbol] += pnl
        
        total_pnl = sum(symbol_pnl.values())
        if total_pnl != 0:
            for symbol, pnl in symbol_pnl.items():
                contribution = pnl / total_pnl
                security_selection[symbol] = contribution
        
        return PerformanceAttribution(
            asset_allocation=asset_allocation,
            security_selection=security_selection,
            interaction_effect=0.0,
            total_active_return=returns.sum()
        )
    
    def _analyze_seasonality(self, equity_curve: pd.DataFrame) -> SeasonalityAnalysis:
        """季节性分析"""
        if equity_curve.empty or 'value' not in equity_curve.columns:
            return SeasonalityAnalysis()
        
        # 计算收益率
        returns = equity_curve['value'].pct_change().dropna()
        
        # 月度收益统计
        monthly_returns = {}
        for month in range(1, 13):
            month_returns = returns[returns.index.month == month]
            if not month_returns.empty:
                monthly_returns[f"{month:02d}"] = month_returns.mean()
        
        # 季度收益统计
        quarterly_returns = {}
        for quarter in range(1, 5):
            quarter_returns = returns[returns.index.quarter == quarter]
            if not quarter_returns.empty:
                quarterly_returns[f"Q{quarter}"] = quarter_returns.mean()
        
        # 星期效应
        day_of_week_returns = {}
        weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        for i in range(7):
            day_returns = returns[returns.index.dayofweek == i]
            if not day_returns.empty:
                day_of_week_returns[weekdays[i]] = day_returns.mean()
        
        # 最佳/最差月份
        best_month = max(monthly_returns.items(), key=lambda x: x[1])[0] if monthly_returns else ""
        worst_month = min(monthly_returns.items(), key=lambda x: x[1])[0] if monthly_returns else ""
        
        return SeasonalityAnalysis(
            monthly_returns=monthly_returns,
            quarterly_returns=quarterly_returns,
            day_of_week_returns=day_of_week_returns,
            best_month=best_month,
            worst_month=worst_month
        )
    
    def _analyze_drawdowns(self, equity_curve: pd.DataFrame) -> Dict[str, Any]:
        """回撤分析"""
        if equity_curve.empty or 'value' not in equity_curve.columns:
            return {}
        
        values = equity_curve['value']
        
        # 计算回撤
        peak = values.expanding().max()
        drawdown = (values - peak) / peak
        
        # 最大回撤
        max_drawdown = drawdown.min()
        max_drawdown_date = drawdown.idxmin()
        
        # 回撤持续时间分析
        drawdown_periods = []
        in_drawdown = False
        start_date = None
        
        for date, dd in drawdown.items():
            if dd < -0.01 and not in_drawdown:  # 开始回撤（超过1%）
                in_drawdown = True
                start_date = date
            elif dd >= -0.01 and in_drawdown:  # 结束回撤
                in_drawdown = False
                if start_date:
                    duration = (date - start_date).days
                    max_dd_in_period = drawdown[start_date:date].min()
                    drawdown_periods.append({
                        "start_date": start_date.isoformat(),
                        "end_date": date.isoformat(),
                        "duration_days": duration,
                        "max_drawdown": max_dd_in_period
                    })
        
        # 平均回撤统计
        avg_drawdown_duration = np.mean([p["duration_days"] for p in drawdown_periods]) if drawdown_periods else 0
        avg_drawdown_depth = np.mean([p["max_drawdown"] for p in drawdown_periods]) if drawdown_periods else 0
        
        return {
            "max_drawdown": max_drawdown,
            "max_drawdown_date": max_drawdown_date.isoformat() if max_drawdown_date else None,
            "drawdown_periods": drawdown_periods,
            "avg_drawdown_duration": avg_drawdown_duration,
            "avg_drawdown_depth": avg_drawdown_depth,
            "total_drawdown_periods": len(drawdown_periods)
        }
    
    def _analyze_trades(self, trades: List[Dict]) -> Dict[str, Any]:
        """交易分析"""
        if not trades:
            return {}
        
        # 按类型分类交易
        buy_trades = [t for t in trades if t.get("action") == "买入"]
        sell_trades = [t for t in trades if t.get("action") == "卖出"]
        
        # 交易频率分析
        if trades:
            trade_dates = [datetime.fromisoformat(t["timestamp"]) for t in trades if "timestamp" in t]
            if len(trade_dates) > 1:
                trade_frequency = len(trade_dates) / (trade_dates[-1] - trade_dates[0]).days * 365
            else:
                trade_frequency = 0
        else:
            trade_frequency = 0
        
        # 持仓时间分析
        holding_periods = []
        symbol_positions = {}
        
        for trade in trades:
            symbol = trade.get("symbol")
            action = trade.get("action")
            timestamp = trade.get("timestamp")
            
            if not all([symbol, action, timestamp]):
                continue
                
            trade_date = datetime.fromisoformat(timestamp)
            
            if action == "买入":
                symbol_positions[symbol] = trade_date
            elif action == "卖出" and symbol in symbol_positions:
                buy_date = symbol_positions[symbol]
                holding_period = (trade_date - buy_date).days
                holding_periods.append(holding_period)
                del symbol_positions[symbol]
        
        avg_holding_period = np.mean(holding_periods) if holding_periods else 0
        
        # 盈亏交易统计
        profitable_trades = [t for t in sell_trades if t.get("pnl", 0) > 0]
        losing_trades = [t for t in sell_trades if t.get("pnl", 0) < 0]
        
        # 交易规模分析
        trade_amounts = [t.get("amount", 0) for t in trades if "amount" in t]
        avg_trade_amount = np.mean(trade_amounts) if trade_amounts else 0
        
        return {
            "total_trades": len(trades),
            "buy_trades": len(buy_trades),
            "sell_trades": len(sell_trades),
            "trade_frequency_per_year": trade_frequency,
            "avg_holding_period_days": avg_holding_period,
            "profitable_trades": len(profitable_trades),
            "losing_trades": len(losing_trades),
            "avg_trade_amount": avg_trade_amount,
            "largest_trade": max(trade_amounts) if trade_amounts else 0,
            "smallest_trade": min(trade_amounts) if trade_amounts else 0
        }
    
    def _calculate_rolling_performance(self, returns: pd.Series) -> Dict[str, Any]:
        """滚动绩效分析"""
        if returns.empty:
            return {}
        
        # 滚动窗口大小（天数）
        windows = [30, 90, 252]  # 1月、3月、1年
        rolling_stats = {}
        
        for window in windows:
            if len(returns) >= window:
                rolling_return = returns.rolling(window).mean() * 252  # 年化
                rolling_volatility = returns.rolling(window).std() * np.sqrt(252)  # 年化波动率
                rolling_sharpe = (rolling_return - self.risk_free_rate) / rolling_volatility
                
                rolling_stats[f"{window}_days"] = {
                    "avg_return": rolling_return.mean(),
                    "avg_volatility": rolling_volatility.mean(),
                    "avg_sharpe": rolling_sharpe.mean(),
                    "best_period_return": rolling_return.max(),
                    "worst_period_return": rolling_return.min(),
                    "best_period_date": rolling_return.idxmax().isoformat() if not rolling_return.empty else None,
                    "worst_period_date": rolling_return.idxmin().isoformat() if not rolling_return.empty else None
                }
        
        return rolling_stats
    
    def _perform_statistical_tests(self, returns: pd.Series) -> Dict[str, Any]:
        """统计显著性检验"""
        if returns.empty or len(returns) < 30:
            return {}
        
        tests = {}
        
        # 正态性检验 (Shapiro-Wilk)
        if len(returns) <= 5000:  # Shapiro-Wilk test limitation
            shapiro_stat, shapiro_p = stats.shapiro(returns)
            tests["normality_test"] = {
                "test": "Shapiro-Wilk",
                "statistic": shapiro_stat,
                "p_value": shapiro_p,
                "is_normal": shapiro_p > 0.05
            }
        
        # 偏度和峰度
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        tests["skewness"] = skewness
        tests["kurtosis"] = kurtosis
        tests["excess_kurtosis"] = kurtosis - 3
        
        # Jarque-Bera检验
        jb_stat, jb_p = stats.jarque_bera(returns)
        tests["jarque_bera_test"] = {
            "statistic": jb_stat,
            "p_value": jb_p,
            "is_normal": jb_p > 0.05
        }
        
        # 平稳性检验 (ADF test)
        try:
            from statsmodels.tsa.stattools import adfuller
            adf_result = adfuller(returns)
            tests["stationarity_test"] = {
                "test": "Augmented Dickey-Fuller",
                "adf_statistic": adf_result[0],
                "p_value": adf_result[1],
                "is_stationary": adf_result[1] < 0.05
            }
        except ImportError:
            # statsmodels not available
            pass
        
        return tests
    
    async def _run_monte_carlo_simulation(
        self, 
        returns: pd.Series, 
        n_simulations: int = 1000,
        n_periods: int = 252
    ) -> Dict[str, Any]:
        """Monte Carlo模拟"""
        if returns.empty:
            return {}
        
        # 计算收益率统计特征
        mean_return = returns.mean()
        std_return = returns.std()
        
        # 运行模拟
        simulated_paths = []
        final_returns = []
        
        for _ in range(n_simulations):
            # 生成随机收益率序列
            random_returns = np.random.normal(mean_return, std_return, n_periods)
            # 计算累积收益
            cumulative_return = (1 + random_returns).cumprod()
            simulated_paths.append(cumulative_return.tolist())
            final_returns.append(cumulative_return[-1] - 1)  # 最终收益率
        
        # 统计分析
        final_returns = np.array(final_returns)
        
        confidence_intervals = {
            "5%": np.percentile(final_returns, 5),
            "25%": np.percentile(final_returns, 25),
            "50%": np.percentile(final_returns, 50),
            "75%": np.percentile(final_returns, 75),
            "95%": np.percentile(final_returns, 95)
        }
        
        # 概率统计
        prob_positive = (final_returns > 0).sum() / n_simulations
        prob_outperform_10 = (final_returns > 0.1).sum() / n_simulations
        prob_loss_more_10 = (final_returns < -0.1).sum() / n_simulations
        
        return {
            "n_simulations": n_simulations,
            "n_periods": n_periods,
            "confidence_intervals": confidence_intervals,
            "expected_return": final_returns.mean(),
            "return_volatility": final_returns.std(),
            "probability_positive": prob_positive,
            "probability_outperform_10pct": prob_outperform_10,
            "probability_loss_more_10pct": prob_loss_more_10,
            "worst_case_5pct": confidence_intervals["5%"],
            "best_case_5pct": confidence_intervals["95%"]
        }
    
    def _risk_metrics_to_dict(self, metrics: RiskMetrics) -> Dict[str, float]:
        """将风险指标转换为字典"""
        return {
            "var_95": round(metrics.var_95 * 100, 2),
            "var_99": round(metrics.var_99 * 100, 2),
            "cvar_95": round(metrics.cvar_95 * 100, 2),
            "cvar_99": round(metrics.cvar_99 * 100, 2),
            "beta": round(metrics.beta, 2),
            "alpha": round(metrics.alpha * 100, 2),
            "information_ratio": round(metrics.information_ratio, 2),
            "tracking_error": round(metrics.tracking_error * 100, 2),
            "downside_deviation": round(metrics.downside_deviation * 100, 2),
            "sortino_ratio": round(metrics.sortino_ratio, 2)
        }
    
    def _attribution_to_dict(self, attribution: PerformanceAttribution) -> Dict[str, Any]:
        """将绩效归因转换为字典"""
        return {
            "asset_allocation": attribution.asset_allocation or {},
            "security_selection": attribution.security_selection or {},
            "interaction_effect": round(attribution.interaction_effect, 4),
            "total_active_return": round(attribution.total_active_return, 4)
        }
    
    def _seasonality_to_dict(self, seasonality: SeasonalityAnalysis) -> Dict[str, Any]:
        """将季节性分析转换为字典"""
        return {
            "monthly_returns": {k: round(v * 100, 2) for k, v in (seasonality.monthly_returns or {}).items()},
            "quarterly_returns": {k: round(v * 100, 2) for k, v in (seasonality.quarterly_returns or {}).items()},
            "day_of_week_returns": {k: round(v * 100, 2) for k, v in (seasonality.day_of_week_returns or {}).items()},
            "best_month": seasonality.best_month,
            "worst_month": seasonality.worst_month
        }
    
    def _generate_analysis_summary(
        self, 
        risk_metrics: RiskMetrics, 
        attribution: PerformanceAttribution,
        seasonality: SeasonalityAnalysis
    ) -> Dict[str, Any]:
        """生成分析总结"""
        summary = {
            "risk_level": "中等",  # 基于风险指标判断
            "return_consistency": "稳定",  # 基于波动率判断
            "seasonal_bias": seasonality.best_month if seasonality.best_month else "无明显偏好",
            "key_strengths": [],
            "key_risks": [],
            "recommendations": []
        }
        
        # 基于分析结果生成建议
        if risk_metrics.sortino_ratio > 1.5:
            summary["key_strengths"].append("优秀的风险调整收益")
        
        if risk_metrics.var_95 < -0.05:
            summary["key_risks"].append("单日最大损失可能超过5%")
        
        if risk_metrics.downside_deviation > 0.15:
            summary["recommendations"].append("考虑降低投资组合波动性")
        
        return summary
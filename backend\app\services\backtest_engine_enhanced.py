"""
增强版回测引擎
提供完整的回测功能，包括数据处理、信号生成、订单执行、绩效分析等
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum
import asyncio
import random

logger = logging.getLogger(__name__)


class OrderAction(Enum):
    """订单操作类型"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class BacktestOrder:
    """回测订单"""
    timestamp: datetime
    symbol: str
    action: OrderAction
    quantity: int
    price: float
    order_id: str = None
    commission: float = 0.0
    slippage: float = 0.0


@dataclass
class BacktestPosition:
    """回测持仓"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


@dataclass
class BacktestMetrics:
    """回测指标"""
    total_return: float = 0.0
    annual_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0


class BacktestEngine:
    """增强版回测引擎"""
    
    def __init__(self):
        self.initial_capital = 100000.0
        self.current_capital = 100000.0
        self.commission_rate = 0.0003  # 万三手续费
        self.slippage_rate = 0.0001    # 万一滑点
        
        self.positions: Dict[str, BacktestPosition] = {}
        self.orders: List[BacktestOrder] = []
        self.daily_returns: List[float] = []
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.trade_log: List[Dict] = []
        
        self.start_date: datetime = None
        self.end_date: datetime = None
        self.current_date: datetime = None
        
    async def run_backtest(
        self,
        strategy_code: str,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float = 100000.0,
        benchmark: str = "000300.SH",
        parameters: Dict = None
    ) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            strategy_code: 策略代码
            symbols: 交易标的列表
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            benchmark: 基准指标
            parameters: 策略参数
            
        Returns:
            回测结果字典
        """
        try:
            # 初始化回测参数
            self.initial_capital = initial_capital
            self.current_capital = initial_capital
            self.start_date = start_date
            self.end_date = end_date
            self.current_date = start_date
            
            # 获取历史数据
            logger.info(f"获取历史数据: {symbols}, {start_date} - {end_date}")
            price_data = await self._get_price_data(symbols, start_date, end_date)
            
            if price_data.empty:
                raise ValueError("未获取到历史数据")
            
            # 初始化策略
            strategy = self._initialize_strategy(strategy_code, parameters or {})
            
            # 执行回测主循环
            logger.info("开始执行回测...")
            await self._run_backtest_loop(strategy, price_data, symbols)
            
            # 计算绩效指标
            metrics = self._calculate_metrics()
            
            # 生成回测报告
            report = self._generate_report(metrics, price_data, benchmark)
            
            logger.info("回测完成")
            return report
            
        except Exception as e:
            logger.error(f"回测失败: {str(e)}")
            raise
    
    async def _get_price_data(
        self, 
        symbols: List[str], 
        start_date: datetime, 
        end_date: datetime
    ) -> pd.DataFrame:
        """获取价格数据"""
        # 模拟生成价格数据
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        data = {}
        for symbol in symbols:
            # 生成随机价格数据
            np.random.seed(hash(symbol) % 2**32)  # 为每个标的设置不同的随机种子
            
            prices = []
            base_price = random.uniform(10, 100)  # 基础价格
            
            for i, date in enumerate(date_range):
                # 模拟价格波动
                if i == 0:
                    price = base_price
                else:
                    # 添加趋势和随机波动
                    trend = 0.0001 * i  # 微小上升趋势
                    volatility = random.uniform(-0.05, 0.05)  # 5%的随机波动
                    price = prices[-1] * (1 + trend + volatility)
                
                prices.append(round(price, 2))
            
            data[f"{symbol}_open"] = prices
            data[f"{symbol}_high"] = [p * random.uniform(1.0, 1.03) for p in prices]
            data[f"{symbol}_low"] = [p * random.uniform(0.97, 1.0) for p in prices]
            data[f"{symbol}_close"] = prices
            data[f"{symbol}_volume"] = [random.randint(1000000, 10000000) for _ in prices]
        
        df = pd.DataFrame(data, index=date_range)
        return df
    
    def _initialize_strategy(self, strategy_code: str, parameters: Dict) -> Dict:
        """初始化策略"""
        # 简化的策略初始化
        strategy = {
            "code": strategy_code,
            "parameters": parameters,
            "indicators": {},
            "signals": {}
        }
        
        # 解析策略参数
        strategy["ma_short"] = parameters.get("ma_short", 5)
        strategy["ma_long"] = parameters.get("ma_long", 20)
        strategy["rsi_period"] = parameters.get("rsi_period", 14)
        strategy["position_size"] = parameters.get("position_size", 0.1)  # 10%仓位
        
        return strategy
    
    async def _run_backtest_loop(
        self, 
        strategy: Dict, 
        price_data: pd.DataFrame, 
        symbols: List[str]
    ):
        """执行回测主循环"""
        for i, (date, row) in enumerate(price_data.iterrows()):
            self.current_date = date
            
            # 更新持仓市值
            self._update_positions_value(row, symbols)
            
            # 生成交易信号
            signals = self._generate_signals(strategy, price_data.iloc[:i+1], symbols)
            
            # 执行交易
            for symbol, signal in signals.items():
                await self._execute_trade(symbol, signal, row)
            
            # 记录权益曲线
            total_value = self._calculate_total_value(row, symbols)
            self.equity_curve.append((date, total_value))
            
            # 计算当日收益率
            if len(self.equity_curve) > 1:
                prev_value = self.equity_curve[-2][1]
                daily_return = (total_value - prev_value) / prev_value
                self.daily_returns.append(daily_return)
    
    def _update_positions_value(self, row: pd.Series, symbols: List[str]):
        """更新持仓市值"""
        for symbol in symbols:
            if symbol in self.positions:
                position = self.positions[symbol]
                current_price = row.get(f"{symbol}_close", position.avg_price)
                position.current_price = current_price
                position.market_value = position.quantity * current_price
                position.unrealized_pnl = (current_price - position.avg_price) * position.quantity
    
    def _generate_signals(
        self, 
        strategy: Dict, 
        historical_data: pd.DataFrame, 
        symbols: List[str]
    ) -> Dict[str, OrderAction]:
        """生成交易信号"""
        signals = {}
        
        if len(historical_data) < max(strategy["ma_short"], strategy["ma_long"]):
            return {symbol: OrderAction.HOLD for symbol in symbols}
        
        for symbol in symbols:
            close_prices = historical_data[f"{symbol}_close"]
            
            # 计算技术指标
            ma_short = close_prices.rolling(strategy["ma_short"]).mean().iloc[-1]
            ma_long = close_prices.rolling(strategy["ma_long"]).mean().iloc[-1]
            
            # 简单的均线策略
            current_price = close_prices.iloc[-1]
            
            if pd.isna(ma_short) or pd.isna(ma_long):
                signals[symbol] = OrderAction.HOLD
                continue
            
            # 生成信号
            if ma_short > ma_long and symbol not in self.positions:
                # 金叉且无持仓，买入
                signals[symbol] = OrderAction.BUY
            elif ma_short < ma_long and symbol in self.positions:
                # 死叉且有持仓，卖出
                signals[symbol] = OrderAction.SELL
            else:
                signals[symbol] = OrderAction.HOLD
        
        return signals
    
    async def _execute_trade(self, symbol: str, signal: OrderAction, row: pd.Series):
        """执行交易"""
        if signal == OrderAction.HOLD:
            return
        
        current_price = row.get(f"{symbol}_close", 0)
        if current_price <= 0:
            return
        
        # 计算滑点和手续费
        slippage = current_price * self.slippage_rate
        
        if signal == OrderAction.BUY:
            # 计算买入数量
            available_capital = self.current_capital * 0.95  # 保留5%资金
            trade_value = available_capital * 0.1  # 每次使用10%资金
            quantity = int(trade_value / (current_price + slippage))
            
            if quantity > 0:
                # 执行买入
                trade_price = current_price + slippage
                total_cost = quantity * trade_price
                commission = total_cost * self.commission_rate
                
                # 创建订单
                order = BacktestOrder(
                    timestamp=self.current_date,
                    symbol=symbol,
                    action=signal,
                    quantity=quantity,
                    price=trade_price,
                    commission=commission,
                    slippage=slippage
                )
                self.orders.append(order)
                
                # 更新持仓
                if symbol in self.positions:
                    position = self.positions[symbol]
                    total_quantity = position.quantity + quantity
                    total_cost_basis = position.quantity * position.avg_price + total_cost
                    position.avg_price = total_cost_basis / total_quantity
                    position.quantity = total_quantity
                else:
                    self.positions[symbol] = BacktestPosition(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=trade_price,
                        current_price=current_price
                    )
                
                # 更新资金
                self.current_capital -= (total_cost + commission)
                
                # 记录交易日志
                self.trade_log.append({
                    "timestamp": self.current_date,
                    "symbol": symbol,
                    "action": "买入",
                    "quantity": quantity,
                    "price": trade_price,
                    "amount": total_cost,
                    "commission": commission,
                    "capital": self.current_capital
                })
        
        elif signal == OrderAction.SELL and symbol in self.positions:
            # 执行卖出
            position = self.positions[symbol]
            trade_price = current_price - slippage
            total_value = position.quantity * trade_price
            commission = total_value * self.commission_rate
            
            # 创建订单
            order = BacktestOrder(
                timestamp=self.current_date,
                symbol=symbol,
                action=signal,
                quantity=position.quantity,
                price=trade_price,
                commission=commission,
                slippage=slippage
            )
            self.orders.append(order)
            
            # 计算盈亏
            realized_pnl = (trade_price - position.avg_price) * position.quantity - commission
            position.realized_pnl += realized_pnl
            
            # 更新资金
            self.current_capital += (total_value - commission)
            
            # 记录交易日志
            self.trade_log.append({
                "timestamp": self.current_date,
                "symbol": symbol,
                "action": "卖出",
                "quantity": position.quantity,
                "price": trade_price,
                "amount": total_value,
                "commission": commission,
                "pnl": realized_pnl,
                "capital": self.current_capital
            })
            
            # 清除持仓
            del self.positions[symbol]
    
    def _calculate_total_value(self, row: pd.Series, symbols: List[str]) -> float:
        """计算总资产价值"""
        total_value = self.current_capital
        
        for symbol, position in self.positions.items():
            current_price = row.get(f"{symbol}_close", position.avg_price)
            total_value += position.quantity * current_price
        
        return total_value
    
    def _calculate_metrics(self) -> BacktestMetrics:
        """计算绩效指标"""
        if not self.equity_curve:
            return BacktestMetrics()
        
        # 计算基础指标
        final_value = self.equity_curve[-1][1]
        total_return = (final_value - self.initial_capital) / self.initial_capital
        
        # 计算年化收益率
        trading_days = len(self.equity_curve)
        annual_return = ((final_value / self.initial_capital) ** (252 / trading_days)) - 1
        
        # 计算波动率
        if self.daily_returns:
            volatility = np.std(self.daily_returns) * np.sqrt(252)
        else:
            volatility = 0.0
        
        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        max_drawdown = self._calculate_max_drawdown()
        
        # 计算交易统计
        winning_trades = len([trade for trade in self.trade_log 
                             if trade.get("action") == "卖出" and trade.get("pnl", 0) > 0])
        losing_trades = len([trade for trade in self.trade_log 
                            if trade.get("action") == "卖出" and trade.get("pnl", 0) < 0])
        total_trades = winning_trades + losing_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 计算平均盈亏
        wins = [trade.get("pnl", 0) for trade in self.trade_log 
                if trade.get("action") == "卖出" and trade.get("pnl", 0) > 0]
        losses = [trade.get("pnl", 0) for trade in self.trade_log 
                  if trade.get("action") == "卖出" and trade.get("pnl", 0) < 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        largest_win = max(wins) if wins else 0
        largest_loss = min(losses) if losses else 0
        
        # 计算盈亏比
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        return BacktestMetrics(
            total_return=total_return,
            annual_return=annual_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            largest_win=largest_win,
            largest_loss=largest_loss
        )
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if not self.equity_curve:
            return 0.0
        
        values = [v for _, v in self.equity_curve]
        peak = values[0]
        max_drawdown = 0.0
        
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def _generate_report(
        self, 
        metrics: BacktestMetrics, 
        price_data: pd.DataFrame, 
        benchmark: str
    ) -> Dict[str, Any]:
        """生成回测报告"""
        # 准备权益曲线数据
        equity_data = [
            {
                "date": date.isoformat(),
                "value": value,
                "return": (value - self.initial_capital) / self.initial_capital
            }
            for date, value in self.equity_curve
        ]
        
        # 准备交易记录
        trade_records = []
        for trade in self.trade_log:
            trade_record = trade.copy()
            trade_record["timestamp"] = trade_record["timestamp"].isoformat()
            trade_records.append(trade_record)
        
        # 生成月度收益统计
        monthly_returns = self._calculate_monthly_returns()
        
        report = {
            "summary": {
                "start_date": self.start_date.isoformat(),
                "end_date": self.end_date.isoformat(),
                "initial_capital": self.initial_capital,
                "final_capital": self.equity_curve[-1][1] if self.equity_curve else self.initial_capital,
                "total_return": round(metrics.total_return * 100, 2),
                "annual_return": round(metrics.annual_return * 100, 2),
                "volatility": round(metrics.volatility * 100, 2),
                "sharpe_ratio": round(metrics.sharpe_ratio, 2),
                "max_drawdown": round(metrics.max_drawdown * 100, 2),
                "win_rate": round(metrics.win_rate * 100, 2)
            },
            "performance": {
                "total_trades": metrics.total_trades,
                "winning_trades": metrics.winning_trades,
                "losing_trades": metrics.losing_trades,
                "avg_win": round(metrics.avg_win, 2),
                "avg_loss": round(metrics.avg_loss, 2),
                "largest_win": round(metrics.largest_win, 2),
                "largest_loss": round(metrics.largest_loss, 2),
                "profit_factor": round(metrics.profit_factor, 2)
            },
            "equity_curve": equity_data,
            "trades": trade_records,
            "monthly_returns": monthly_returns,
            "positions": [
                {
                    "symbol": pos.symbol,
                    "quantity": pos.quantity,
                    "avg_price": round(pos.avg_price, 2),
                    "current_price": round(pos.current_price, 2),
                    "market_value": round(pos.market_value, 2),
                    "unrealized_pnl": round(pos.unrealized_pnl, 2)
                }
                for pos in self.positions.values()
            ]
        }
        
        return report
    
    def _calculate_monthly_returns(self) -> List[Dict]:
        """计算月度收益"""
        if not self.equity_curve:
            return []
        
        monthly_data = {}
        for date, value in self.equity_curve:
            month_key = date.strftime("%Y-%m")
            if month_key not in monthly_data:
                monthly_data[month_key] = {"start": value, "end": value}
            else:
                monthly_data[month_key]["end"] = value
        
        monthly_returns = []
        for month, data in monthly_data.items():
            monthly_return = (data["end"] - data["start"]) / data["start"]
            monthly_returns.append({
                "month": month,
                "return": round(monthly_return * 100, 2),
                "start_value": round(data["start"], 2),
                "end_value": round(data["end"], 2)
            })
        
        return monthly_returns
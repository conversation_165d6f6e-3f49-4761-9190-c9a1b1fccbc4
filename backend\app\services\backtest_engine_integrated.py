"""
集成回测引擎
整合AkShare数据源、pyecharts可视化和回测分析功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
import json
import asyncio
from dataclasses import dataclass, asdict
from enum import Enum

from .akshare_data_source import get_akshare_client
from .backtest_visualizer import BacktestVisualizer
from .backtest_data_analyzer import BacktestDataAnalyzer

logger = logging.getLogger(__name__)


class OrderAction(Enum):
    """订单操作类型"""
    BUY = "买入"
    SELL = "卖出"
    HOLD = "持有"


@dataclass
class IntegratedBacktestOrder:
    """集成回测订单"""
    timestamp: str
    symbol: str
    action: str
    quantity: int
    price: float
    order_id: str = None
    commission: float = 0.0
    slippage: float = 0.0
    total_cost: float = 0.0


@dataclass
class IntegratedBacktestPosition:
    """集成回测持仓"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    pnl_percentage: float = 0.0


@dataclass  
class IntegratedBacktestMetrics:
    """集成回测指标"""
    # 收益指标
    total_return: float = 0.0
    annual_return: float = 0.0
    cumulative_return: float = 0.0
    
    # 风险指标
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    
    # 交易指标
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # 其他指标
    calmar_ratio: float = 0.0
    information_ratio: float = 0.0
    beta: float = 0.0
    alpha: float = 0.0


class IntegratedBacktestEngine:
    """集成回测引擎"""
    
    def __init__(self):
        self.initial_capital = 100000.0
        self.current_capital = 100000.0
        self.available_cash = 100000.0
        
        # 交易成本设置
        self.commission_rate = 0.0003  # 万三手续费
        self.stamp_duty_rate = 0.001   # 千一印花税（卖出）
        self.slippage_rate = 0.0001    # 万一滑点
        self.min_commission = 5.0      # 最低手续费5元
        
        # 回测数据
        self.positions: Dict[str, IntegratedBacktestPosition] = {}
        self.orders: List[IntegratedBacktestOrder] = []
        self.daily_records: List[Dict] = []
        self.equity_curve: List[Dict] = []
        self.trade_log: List[Dict] = []
        
        # 时间管理
        self.start_date: datetime = None
        self.end_date: datetime = None
        self.current_date: datetime = None
        self.trading_days: List[datetime] = []
        
        # 组件实例
        self.visualizer = BacktestVisualizer()
        self.analyzer = BacktestDataAnalyzer()
    
    async def run_comprehensive_backtest(
        self,
        strategy_config: Dict[str, Any],
        data_config: Dict[str, Any],
        backtest_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        运行综合回测
        
        Args:
            strategy_config: 策略配置
            data_config: 数据配置
            backtest_config: 回测配置
            
        Returns:
            完整的回测结果
        """
        try:
            logger.info("开始运行综合回测...")
            
            # 初始化配置
            backtest_config = backtest_config or {}
            self._initialize_config(backtest_config)
            
            # 获取数据
            logger.info("获取历史数据...")
            data = await self._fetch_data(data_config)
            
            if data.empty:
                raise ValueError("未获取到历史数据")
            
            # 运行回测
            logger.info("执行回测策略...")
            await self._execute_backtest(strategy_config, data)
            
            # 计算指标
            logger.info("计算绩效指标...")
            metrics = self._calculate_comprehensive_metrics()
            
            # 进行高级分析
            logger.info("执行高级分析...")
            analysis_result = await self.analyzer.analyze_backtest_result({
                "equity_curve": self.equity_curve,
                "trades": self.trade_log,
                "positions": [asdict(pos) for pos in self.positions.values()],
                "daily_records": self.daily_records
            })
            
            # 生成可视化
            logger.info("生成可视化图表...")
            visualization_data = await self.visualizer.generate_visualization_data(
                backtest_result={
                    "equity_curve": self.equity_curve,
                    "trades": self.trade_log,
                    "positions": [asdict(pos) for pos in self.positions.values()],
                    "summary": asdict(metrics),
                    "performance": self._get_performance_summary()
                },
                analysis_result=analysis_result
            )
            
            # 生成完整报告
            comprehensive_report = {
                "backtest_info": {
                    "start_date": self.start_date.isoformat(),
                    "end_date": self.end_date.isoformat(),
                    "initial_capital": self.initial_capital,
                    "final_capital": self.current_capital,
                    "total_trading_days": len(self.trading_days),
                    "strategy_config": strategy_config,
                    "data_config": data_config,
                    "timestamp": datetime.now().isoformat()
                },
                "metrics": asdict(metrics),
                "equity_curve": self.equity_curve,
                "positions": [asdict(pos) for pos in self.positions.values()],
                "trades": self.trade_log,
                "daily_records": self.daily_records,
                "analysis": analysis_result,
                "visualization": visualization_data,
                "performance_summary": self._get_performance_summary()
            }
            
            logger.info("综合回测完成")
            return comprehensive_report
            
        except Exception as e:
            logger.error(f"综合回测失败: {str(e)}")
            raise
    
    def _initialize_config(self, config: Dict[str, Any]):
        """初始化回测配置"""
        self.initial_capital = config.get("initial_capital", 100000.0)
        self.current_capital = self.initial_capital
        self.available_cash = self.initial_capital
        
        self.commission_rate = config.get("commission_rate", 0.0003)
        self.stamp_duty_rate = config.get("stamp_duty_rate", 0.001)
        self.slippage_rate = config.get("slippage_rate", 0.0001)
        self.min_commission = config.get("min_commission", 5.0)
        
        # 清空之前的数据
        self.positions.clear()
        self.orders.clear()
        self.daily_records.clear()
        self.equity_curve.clear()
        self.trade_log.clear()
    
    async def _fetch_data(self, data_config: Dict[str, Any]) -> pd.DataFrame:
        """获取回测数据"""
        try:
            symbols = data_config.get("symbols", [])
            start_date = data_config.get("start_date")
            end_date = data_config.get("end_date")
            data_source = data_config.get("source", "akshare")
            
            if not symbols:
                raise ValueError("股票代码列表不能为空")
            
            # 设置日期
            self.start_date = pd.to_datetime(start_date)
            self.end_date = pd.to_datetime(end_date)
            
            if data_source == "akshare":
                # 使用AkShare获取数据
                akshare_client = await get_akshare_client()
                
                all_data = {}
                for symbol in symbols:
                    try:
                        stock_data = await akshare_client.get_stock_data(
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date,
                            period="daily"
                        )
                        
                        if not stock_data.empty:
                            all_data[symbol] = stock_data
                        
                    except Exception as e:
                        logger.warning(f"获取 {symbol} 数据失败: {str(e)}")
                        continue
                
                if not all_data:
                    raise ValueError("未获取到任何股票数据")
                
                # 合并数据
                combined_data = pd.DataFrame()
                for symbol, data in all_data.items():
                    data_copy = data.copy()
                    data_copy['symbol'] = symbol
                    combined_data = pd.concat([combined_data, data_copy])
                
                # 生成交易日历
                self.trading_days = sorted(combined_data.index.unique())
                
                return combined_data
            
            else:
                # 其他数据源可以在这里扩展
                raise ValueError(f"不支持的数据源: {data_source}")
        
        except Exception as e:
            logger.error(f"获取数据失败: {str(e)}")
            raise
    
    async def _execute_backtest(self, strategy_config: Dict[str, Any], data: pd.DataFrame):
        """执行回测策略"""
        try:
            strategy_type = strategy_config.get("type", "simple_ma")
            parameters = strategy_config.get("parameters", {})
            
            # 按日期顺序处理数据
            for trading_date in self.trading_days:
                self.current_date = trading_date
                
                # 获取当日数据
                daily_data = data[data.index == trading_date]
                
                if daily_data.empty:
                    continue
                
                # 更新持仓市值
                await self._update_positions(daily_data)
                
                # 生成交易信号
                signals = self._generate_signals(strategy_type, daily_data, parameters)
                
                # 执行交易
                await self._execute_trades(signals, daily_data)
                
                # 记录日度数据
                self._record_daily_data(trading_date, daily_data)
            
        except Exception as e:
            logger.error(f"执行回测策略失败: {str(e)}")
            raise
    
    def _generate_signals(self, strategy_type: str, data: pd.DataFrame, parameters: Dict) -> Dict[str, str]:
        """生成交易信号"""
        signals = {}
        
        if strategy_type == "simple_ma":
            # 简单移动平均策略
            short_window = parameters.get("short_window", 5)
            long_window = parameters.get("long_window", 20)
            
            for symbol in data['symbol'].unique():
                symbol_data = data[data['symbol'] == symbol]
                
                if len(symbol_data) > 0:
                    # 简化的信号生成逻辑（实际应用中需要历史数据计算MA）
                    current_price = symbol_data['close'].iloc[0]
                    
                    # 随机生成信号作为示例（实际应该基于技术指标）
                    random_signal = np.random.choice(['BUY', 'SELL', 'HOLD'], p=[0.1, 0.1, 0.8])
                    signals[symbol] = random_signal
        
        elif strategy_type == "rsi":
            # RSI策略
            rsi_period = parameters.get("rsi_period", 14)
            oversold = parameters.get("oversold", 30)
            overbought = parameters.get("overbought", 70)
            
            # 简化的RSI信号生成
            for symbol in data['symbol'].unique():
                symbol_data = data[data['symbol'] == symbol]
                
                if len(symbol_data) > 0:
                    # 模拟RSI计算结果
                    mock_rsi = np.random.uniform(20, 80)
                    
                    if mock_rsi < oversold:
                        signals[symbol] = 'BUY'
                    elif mock_rsi > overbought:
                        signals[symbol] = 'SELL'
                    else:
                        signals[symbol] = 'HOLD'
        
        else:
            # 默认持有策略
            for symbol in data['symbol'].unique():
                signals[symbol] = 'HOLD'
        
        return signals
    
    async def _execute_trades(self, signals: Dict[str, str], data: pd.DataFrame):
        """执行交易"""
        for symbol, signal in signals.items():
            if signal == 'HOLD':
                continue
            
            symbol_data = data[data['symbol'] == symbol]
            if symbol_data.empty:
                continue
            
            current_price = symbol_data['close'].iloc[0]
            
            if signal == 'BUY':
                await self._execute_buy_order(symbol, current_price)
            elif signal == 'SELL':
                await self._execute_sell_order(symbol, current_price)
    
    async def _execute_buy_order(self, symbol: str, price: float):
        """执行买入订单"""
        try:
            # 计算可买入数量（按100股为单位）
            available_amount = self.available_cash * 0.95  # 保留5%现金
            quantity = int(available_amount / price / 100) * 100  # 按手买入
            
            if quantity <= 0:
                return
            
            # 计算交易成本
            trade_amount = quantity * price
            commission = max(trade_amount * self.commission_rate, self.min_commission)
            slippage = trade_amount * self.slippage_rate
            total_cost = trade_amount + commission + slippage
            
            if total_cost > self.available_cash:
                return
            
            # 创建订单
            order = IntegratedBacktestOrder(
                timestamp=self.current_date.isoformat(),
                symbol=symbol,
                action='买入',
                quantity=quantity,
                price=price,
                order_id=f"BUY_{symbol}_{self.current_date.strftime('%Y%m%d')}_{len(self.orders)+1}",
                commission=commission,
                slippage=slippage,
                total_cost=total_cost
            )
            
            self.orders.append(order)
            
            # 更新持仓
            if symbol in self.positions:
                pos = self.positions[symbol]
                total_quantity = pos.quantity + quantity
                total_cost_basis = pos.quantity * pos.avg_price + trade_amount
                pos.avg_price = total_cost_basis / total_quantity
                pos.quantity = total_quantity
            else:
                self.positions[symbol] = IntegratedBacktestPosition(
                    symbol=symbol,
                    quantity=quantity,
                    avg_price=price,
                    current_price=price
                )
            
            # 更新现金
            self.available_cash -= total_cost
            
            # 记录交易日志
            self.trade_log.append({
                "timestamp": self.current_date.isoformat(),
                "symbol": symbol,
                "action": "买入",
                "quantity": quantity,
                "price": price,
                "amount": trade_amount,
                "commission": commission,
                "slippage": slippage,
                "total_cost": total_cost
            })
            
        except Exception as e:
            logger.error(f"执行买入订单失败 {symbol}: {str(e)}")
    
    async def _execute_sell_order(self, symbol: str, price: float):
        """执行卖出订单"""
        try:
            if symbol not in self.positions or self.positions[symbol].quantity <= 0:
                return
            
            pos = self.positions[symbol]
            quantity = pos.quantity
            
            # 计算交易收入
            trade_amount = quantity * price
            commission = max(trade_amount * self.commission_rate, self.min_commission)
            stamp_duty = trade_amount * self.stamp_duty_rate
            slippage = trade_amount * self.slippage_rate
            total_cost = commission + stamp_duty + slippage
            net_amount = trade_amount - total_cost
            
            # 创建订单
            order = IntegratedBacktestOrder(
                timestamp=self.current_date.isoformat(),
                symbol=symbol,
                action='卖出',
                quantity=quantity,
                price=price,
                order_id=f"SELL_{symbol}_{self.current_date.strftime('%Y%m%d')}_{len(self.orders)+1}",
                commission=commission,
                slippage=slippage,
                total_cost=total_cost
            )
            
            self.orders.append(order)
            
            # 计算已实现盈亏
            cost_basis = pos.quantity * pos.avg_price
            realized_pnl = trade_amount - cost_basis - total_cost
            pos.realized_pnl += realized_pnl
            
            # 更新持仓
            pos.quantity = 0
            
            # 更新现金
            self.available_cash += net_amount
            
            # 记录交易日志
            self.trade_log.append({
                "timestamp": self.current_date.isoformat(),
                "symbol": symbol,
                "action": "卖出",
                "quantity": quantity,
                "price": price,
                "amount": trade_amount,
                "commission": commission,
                "stamp_duty": stamp_duty,
                "slippage": slippage,
                "total_cost": total_cost,
                "net_amount": net_amount,
                "pnl": realized_pnl
            })
            
        except Exception as e:
            logger.error(f"执行卖出订单失败 {symbol}: {str(e)}")
    
    async def _update_positions(self, data: pd.DataFrame):
        """更新持仓市值"""
        total_market_value = 0.0
        
        for symbol, pos in self.positions.items():
            if pos.quantity > 0:
                symbol_data = data[data['symbol'] == symbol]
                if not symbol_data.empty:
                    current_price = symbol_data['close'].iloc[0]
                    pos.current_price = current_price
                    pos.market_value = pos.quantity * current_price
                    pos.unrealized_pnl = pos.market_value - (pos.quantity * pos.avg_price)
                    pos.pnl_percentage = (pos.unrealized_pnl / (pos.quantity * pos.avg_price)) * 100
                    
                    total_market_value += pos.market_value
        
        # 更新总资产
        self.current_capital = self.available_cash + total_market_value
    
    def _record_daily_data(self, date: datetime, data: pd.DataFrame):
        """记录日度数据"""
        daily_record = {
            "date": date.isoformat(),
            "total_capital": self.current_capital,
            "available_cash": self.available_cash,
            "market_value": self.current_capital - self.available_cash,
            "daily_return": 0.0,
            "cumulative_return": ((self.current_capital - self.initial_capital) / self.initial_capital) * 100
        }
        
        # 计算日收益率
        if len(self.daily_records) > 0:
            prev_capital = self.daily_records[-1]["total_capital"]
            daily_record["daily_return"] = ((self.current_capital - prev_capital) / prev_capital) * 100
        
        self.daily_records.append(daily_record)
        
        # 添加到权益曲线
        self.equity_curve.append({
            "date": date.isoformat(),
            "value": self.current_capital,
            "return": daily_record["cumulative_return"] / 100
        })
    
    def _calculate_comprehensive_metrics(self) -> IntegratedBacktestMetrics:
        """计算综合绩效指标"""
        if not self.daily_records:
            return IntegratedBacktestMetrics()
        
        # 基础数据
        daily_returns = [record["daily_return"] / 100 for record in self.daily_records if record["daily_return"] != 0]
        total_days = len(self.daily_records)
        trading_days = len([r for r in daily_returns if r != 0])
        
        # 收益指标
        total_return = ((self.current_capital - self.initial_capital) / self.initial_capital) * 100
        annual_return = (pow(self.current_capital / self.initial_capital, 365.0 / total_days) - 1) * 100
        
        # 风险指标
        volatility = np.std(daily_returns) * np.sqrt(252) * 100 if daily_returns else 0
        
        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        excess_returns = [r - risk_free_rate/252 for r in daily_returns]
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if excess_returns and np.std(excess_returns) > 0 else 0
        
        # Sortino比率
        downside_returns = [r for r in daily_returns if r < 0]
        downside_std = np.std(downside_returns) if downside_returns else 0
        sortino_ratio = np.mean(excess_returns) / downside_std * np.sqrt(252) if downside_std > 0 else 0
        
        # 最大回撤
        peak = self.initial_capital
        max_drawdown = 0
        max_drawdown_duration = 0
        current_drawdown_duration = 0
        
        for record in self.daily_records:
            current_value = record["total_capital"]
            if current_value > peak:
                peak = current_value
                current_drawdown_duration = 0
            else:
                drawdown = (peak - current_value) / peak * 100
                max_drawdown = max(max_drawdown, drawdown)
                current_drawdown_duration += 1
                max_drawdown_duration = max(max_drawdown_duration, current_drawdown_duration)
        
        # 交易指标
        profitable_trades = [trade for trade in self.trade_log if trade.get("pnl", 0) > 0]
        losing_trades = [trade for trade in self.trade_log if trade.get("pnl", 0) < 0]
        
        total_trades = len([trade for trade in self.trade_log if "pnl" in trade])
        winning_trades = len(profitable_trades)
        losing_trade_count = len(losing_trades)
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        avg_win = np.mean([trade["pnl"] for trade in profitable_trades]) if profitable_trades else 0
        avg_loss = np.mean([abs(trade["pnl"]) for trade in losing_trades]) if losing_trades else 0
        
        profit_factor = (avg_win * winning_trades) / (avg_loss * losing_trade_count) if avg_loss > 0 and losing_trade_count > 0 else 0
        
        largest_win = max([trade["pnl"] for trade in profitable_trades]) if profitable_trades else 0
        largest_loss = min([trade["pnl"] for trade in losing_trades]) if losing_trades else 0
        
        # 其他指标
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
        
        return IntegratedBacktestMetrics(
            total_return=total_return,
            annual_return=annual_return,
            cumulative_return=total_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_drawdown_duration,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trade_count,
            win_rate=win_rate,
            profit_factor=profit_factor,
            avg_win=avg_win,
            avg_loss=avg_loss,
            largest_win=largest_win,
            largest_loss=largest_loss,
            calmar_ratio=calmar_ratio,
            information_ratio=0.0,  # 需要基准数据计算
            beta=0.0,  # 需要基准数据计算
            alpha=0.0   # 需要基准数据计算
        )
    
    def _get_performance_summary(self) -> Dict[str, Any]:
        """获取绩效摘要"""
        return {
            "initial_capital": self.initial_capital,
            "final_capital": self.current_capital,
            "available_cash": self.available_cash,
            "total_market_value": self.current_capital - self.available_cash,
            "total_trades": len(self.trade_log),
            "active_positions": len([pos for pos in self.positions.values() if pos.quantity > 0]),
            "trading_period_days": len(self.daily_records),
            "commission_paid": sum([order.commission for order in self.orders]),
            "slippage_cost": sum([order.slippage for order in self.orders])
        }


# 全局引擎实例
integrated_backtest_engine = IntegratedBacktestEngine()


async def get_integrated_backtest_engine() -> IntegratedBacktestEngine:
    """获取集成回测引擎实例"""
    return integrated_backtest_engine
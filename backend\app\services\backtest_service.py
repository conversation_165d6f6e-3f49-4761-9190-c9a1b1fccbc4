# 回测服务
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union
import uuid
import json
import logging

from sqlalchemy import and_, desc, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.backtest import BacktestTask as Backtest
from app.db.models.strategy import Strategy
from app.db.models.user import User
from app.schemas.backtest import (
    BacktestAnalysisRequest,
    BacktestCreate,
    BacktestOptimizationConfig,
    BacktestStatus,
    BacktestUpdate,
)
from app.services.backtest_engine import BacktestEngine, BacktestResult
from app.utils.exceptions import DataNotFoundError

# 设置日志
logger = logging.getLogger(__name__)


class BacktestService:
    """回测服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_backtest(
        self, user_id: int, backtest_data: BacktestCreate
    ) -> Backtest:
        """创建回测任务"""
        backtest = Backtest(
            user_id=user_id,
            name=backtest_data.name,
            description=backtest_data.description,
            strategy_id=backtest_data.strategy_id,
            start_date=backtest_data.start_date,
            end_date=backtest_data.end_date,
            initial_capital=backtest_data.initial_capital,
            symbols=backtest_data.symbols,
            benchmark=backtest_data.benchmark,
            commission_rate=backtest_data.commission_rate,
            slippage_rate=backtest_data.slippage_rate,
            rebalance_frequency=backtest_data.rebalance_frequency,
            max_position_size=backtest_data.max_position_size,
            parameters=backtest_data.parameters,
            status=BacktestStatus.PENDING,
            created_at=datetime.now(),
        )

        self.db.add(backtest)
        await self.db.commit()
        await self.db.refresh(backtest)

        return backtest

    async def get_backtest_by_id(self, backtest_id: Union[int, str, uuid.UUID]) -> Optional[Backtest]:
        """根据ID获取回测"""
        result = await self.db.execute(
            select(Backtest).where(Backtest.id == backtest_id)
        )
        return result.scalar_one_or_none()

    async def get_strategy_by_id(self, strategy_id: Union[int, str, uuid.UUID]) -> Optional[Strategy]:
        """根据ID获取策略"""
        result = await self.db.execute(
            select(Strategy).where(Strategy.id == strategy_id)
        )
        return result.scalar_one_or_none()

    async def get_user_backtests(
        self,
        user_id: int,
        status: Optional[BacktestStatus] = None,
        strategy_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Backtest], int]:
        """获取用户回测列表"""
        # 构建查询条件
        conditions = [Backtest.user_id == user_id]

        if status:
            conditions.append(Backtest.status == status)
        if strategy_id:
            conditions.append(Backtest.strategy_id == strategy_id)

        # 查询回测列表
        query = select(Backtest).where(and_(*conditions)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        backtests = result.scalars().all()

        # 查询总数
        count_query = select(func.count(Backtest.id)).where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()

        return list(backtests), total

    async def update_backtest(
        self, backtest_id: int, backtest_update: BacktestUpdate
    ) -> Backtest:
        """更新回测配置"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        # 更新字段
        update_data = backtest_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(backtest, field, value)

        backtest.updated_at = datetime.now()

        await self.db.commit()
        await self.db.refresh(backtest)

        return backtest

    async def delete_backtest(self, backtest_id: int) -> bool:
        """删除回测"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        await self.db.delete(backtest)
        await self.db.commit()

        return True

    async def start_backtest(self, backtest_id: int) -> bool:
        """启动回测"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        backtest.status = BacktestStatus.RUNNING
        backtest.started_at = datetime.now()
        backtest.updated_at = datetime.now()

        await self.db.commit()

        return True

    async def stop_backtest(self, backtest_id: int) -> bool:
        """停止回测"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        backtest.status = BacktestStatus.STOPPED
        backtest.updated_at = datetime.now()

        await self.db.commit()

        return True

    async def complete_backtest(
        self, backtest_id: int, result_data: Dict[str, Any]
    ) -> bool:
        """完成回测"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        backtest.status = BacktestStatus.COMPLETED
        backtest.completed_at = datetime.now()
        backtest.updated_at = datetime.now()
        backtest.result = result_data

        await self.db.commit()

        return True

    async def run_backtest_task(self, backtest_id: int) -> None:
        """运行回测任务（后台任务）"""
        try:
            # 更新状态为运行中
            await self.start_backtest(backtest_id)

            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                raise DataNotFoundError("回测不存在")

            # ----------------- 构建回测引擎 -----------------
            from app.services.local_data_loader import LocalDataLoader
            
            # 获取第一个标的符号（处理symbols列表）
            primary_symbol = backtest.symbols[0] if backtest.symbols and len(backtest.symbols) > 0 else "000001.SZ"
            
            # 使用本地CSV数据加载器
            data_loader = LocalDataLoader()
            historical_df = data_loader.load_historical_data(
                symbol=primary_symbol,
                start_date=backtest.start_date,
                end_date=backtest.end_date
            )
            
            if historical_df is None or historical_df.empty:
                raise ValueError(f"无法加载符号 {primary_symbol} 的历史数据")

            def data_loader_sync(*_args, **_kwargs):
                return historical_df

            # 示例策略: 双均线交叉
            def sma_strategy(data):
                fast = data["close"].rolling(window=10).mean()
                slow = data["close"].rolling(window=30).mean()
                signal = (fast > slow).astype(int)
                signal = signal.diff().fillna(0)  # 发生穿越时买入/卖出
                return signal

            engine = BacktestEngine(
                data_loader=data_loader_sync,
                strategy_callable=sma_strategy,
                symbol=primary_symbol,
                start=backtest.start_date,
                end=backtest.end_date,
                initial_capital=backtest.initial_capital,
            )

            result: BacktestResult = engine.run()

            # ----------------- 保存结果 -----------------
            await self.complete_backtest(
                backtest_id,
                {
                    "metrics": result.metrics,
                    "symbol": primary_symbol,
                    "data_points": len(historical_df),
                },
            )

        except Exception as e:
            # 回测失败
            backtest = await self.get_backtest_by_id(backtest_id)
            if backtest:
                backtest.status = BacktestStatus.FAILED
                backtest.error_message = str(e)
                backtest.updated_at = datetime.now()
                await self.db.commit()

    async def get_backtest_result(self, backtest_id: int) -> Optional[Dict[str, Any]]:
        """获取回测结果"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        return backtest.result

    async def get_daily_returns(self, backtest_id: int) -> List[Dict[str, Any]]:
        """获取每日收益数据"""
        try:
            # Get backtest from database - 直接使用self.db
            result = await self.db.execute(
                text("SELECT result FROM backtests WHERE id = :id"),
                {"id": backtest_id}
            )
            backtest_data = result.fetchone()

            if not backtest_data or not backtest_data[0]:
                return []

            # Extract daily returns from backtest result
            backtest_result = json.loads(backtest_data[0]) if isinstance(backtest_data[0], str) else backtest_data[0]
            return backtest_result.get('daily_returns', [])
        except Exception as e:
            logger.error(f"获取每日收益失败: {str(e)}")
            return []

    async def get_backtest_positions(
        self, backtest_id: int, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取回测持仓记录"""
        try:
            # 直接使用self.db
            query = text("SELECT result FROM backtests WHERE id = :id")
            result = await self.db.execute(query, {"id": backtest_id})
            backtest_data = result.fetchone()

            if not backtest_data or not backtest_data[0]:
                return []

            backtest_result = json.loads(backtest_data[0]) if isinstance(backtest_data[0], str) else backtest_data[0]
            positions = backtest_result.get('positions', [])

            # Filter by symbol if provided
            if symbol:
                positions = [pos for pos in positions if pos.get('symbol') == symbol]

            return positions
        except Exception as e:
            logger.error(f"获取持仓记录失败: {str(e)}")
            return []

    async def get_backtest_trades(
        self, backtest_id: int, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取回测交易记录"""
        try:
            # 直接使用self.db
            query = text("SELECT result FROM backtests WHERE id = :id")
            result = await self.db.execute(query, {"id": backtest_id})
            backtest_data = result.fetchone()

            if not backtest_data or not backtest_data[0]:
                return []

            backtest_result = json.loads(backtest_data[0]) if isinstance(backtest_data[0], str) else backtest_data[0]
            trades = backtest_result.get('trades', [])

            # Filter by symbol if provided
            if symbol:
                trades = [trade for trade in trades if trade.get('symbol') == symbol]

            return trades
        except Exception as e:
            logger.error(f"获取交易记录失败: {str(e)}")
            return []

    async def analyze_backtest(
        self, backtest_id: int, analysis_request: BacktestAnalysisRequest
    ) -> Dict[str, Any]:
        """分析回测结果"""
        try:
            # 直接使用self.db
            query = text("SELECT result FROM backtests WHERE id = :id")
            result = await self.db.execute(query, {"id": backtest_id})
            backtest_data = result.fetchone()

            if not backtest_data or not backtest_data[0]:
                return {"error": "回测数据不存在"}

            backtest_result = json.loads(backtest_data[0]) if isinstance(backtest_data[0], str) else backtest_data[0]

            # Basic analysis based on analysis_type
            if analysis_request.analysis_type == "performance":
                metrics = {
                    "total_return": backtest_result.get("total_return", 0),
                    "annual_return": backtest_result.get("annual_return", 0),
                    "max_drawdown": backtest_result.get("max_drawdown", 0),
                    "sharpe_ratio": backtest_result.get("sharpe_ratio", 0),
                    "win_rate": backtest_result.get("win_rate", 0)
                }
            elif analysis_request.analysis_type == "risk":
                metrics = {
                    "volatility": backtest_result.get("volatility", 0),
                    "var": backtest_result.get("var", 0),
                    "beta": backtest_result.get("beta", 0)
                }
            else:
                metrics = backtest_result.get("metrics", {})

            return {
                "analysis_type": analysis_request.analysis_type,
                "metrics": metrics,
                "charts": backtest_result.get("charts", []),
                "insights": backtest_result.get("insights", []),
            }
        except Exception as e:
            logger.error(f"回测分析失败: {str(e)}")
            return {
                "analysis_type": analysis_request.analysis_type,
                "metrics": {},
                "charts": [],
                "insights": [],
                "error": str(e)
            }

    async def start_optimization_task(
        self, backtest_id: int, optimization_config: BacktestOptimizationConfig
    ) -> str:
        """启动参数优化任务"""
        import uuid
        import asyncio
        
        task_id = str(uuid.uuid4())
        
        # 启动后台优化任务
        asyncio.create_task(self.run_optimization_task(task_id, backtest_id, optimization_config))
        
        return task_id

    async def run_optimization_task(self, task_id: str, backtest_id: int, optimization_config) -> None:
        """运行参数优化任务"""
        from loguru import logger
        
        try:
            logger.info(f"开始参数优化任务: {task_id}")
            
            # 获取原始回测
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                logger.error(f"回测 {backtest_id} 不存在")
                return
            
            # 简化的网格搜索优化
            best_params = None
            best_score = float('-inf')
            optimization_results = []
            
            # 模拟参数优化过程
            param_combinations = self._generate_parameter_combinations(optimization_config)
            
            for params in param_combinations[:10]:  # 限制测试数量
                # 运行回测
                score = await self._evaluate_parameters(backtest, params)
                optimization_results.append({
                    'parameters': params,
                    'score': score
                })
                
                if score > best_score:
                    best_score = score
                    best_params = params
            
            # 保存优化结果
            logger.info(f"参数优化任务完成: {task_id}, 最佳分数: {best_score}")
            
        except Exception as e:
            logger.error(f"参数优化任务失败 {task_id}: {e}")

    async def generate_report(
        self, backtest_id: int, report_format: str = "json"
    ) -> Dict[str, Any]:
        """生成回测报告"""
        backtest = await self.get_backtest_by_id(backtest_id)
        if not backtest:
            raise DataNotFoundError("回测不存在")

        # TODO: 实现报告生成
        return {
            "backtest_id": backtest_id,
            "name": backtest.name,
            "format": report_format,
            "generated_at": datetime.now().isoformat(),
            "summary": backtest.result or {},
            "sections": [],
        }

    async def compare_backtests(self, backtest_ids: List[int]) -> Dict[str, Any]:
        """对比多个回测结果"""
        # TODO: 实现回测对比
        return {
            "backtest_ids": backtest_ids,
            "comparison_metrics": {},
            "charts": [],
            "insights": [],
        }

    async def get_user_backtest_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户回测统计"""
        # 查询各状态回测数量
        result = await self.db.execute(
            select(Backtest.status, func.count(Backtest.id))
            .where(Backtest.user_id == user_id)
            .group_by(Backtest.status)
        )
        status_counts = dict(result.all())

        # 查询最近回测
        recent_result = await self.db.execute(
            select(Backtest)
            .where(Backtest.user_id == user_id)
            .order_by(desc(Backtest.created_at))
            .limit(5)
        )
        recent_backtests = recent_result.scalars().all()

        return {
            "total_backtests": sum(status_counts.values()),
            "pending_backtests": status_counts.get(BacktestStatus.PENDING, 0),
            "running_backtests": status_counts.get(BacktestStatus.RUNNING, 0),
            "completed_backtests": status_counts.get(BacktestStatus.COMPLETED, 0),
            "failed_backtests": status_counts.get(BacktestStatus.FAILED, 0),
            "recent_backtests": [
                {
                    "id": bt.id,
                    "name": bt.name,
                    "status": bt.status,
                    "created_at": bt.created_at.isoformat(),
                }
                for bt in recent_backtests
            ],
        }

    def _generate_parameter_combinations(self, optimization_config) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools
        
        # 简化实现 - 生成参数网格
        param_grid = {}
        
        # 示例：如果优化配置包含参数范围
        if hasattr(optimization_config, 'parameter_ranges'):
            for param_name, param_range in optimization_config.parameter_ranges.items():
                if isinstance(param_range, dict) and 'min' in param_range and 'max' in param_range:
                    # 生成数值范围
                    step = param_range.get('step', (param_range['max'] - param_range['min']) / 10)
                    values = []
                    current = param_range['min']
                    while current <= param_range['max']:
                        values.append(current)
                        current += step
                    param_grid[param_name] = values
                elif isinstance(param_range, list):
                    # 离散值列表
                    param_grid[param_name] = param_range
        
        # 生成所有参数组合
        if not param_grid:
            return [{}]  # 无参数优化时返回空字典
        
        keys = list(param_grid.keys())
        values = list(param_grid.values())
        combinations = []
        
        for combo in itertools.product(*values):
            combinations.append(dict(zip(keys, combo)))
        
        return combinations
    
    async def _evaluate_parameters(self, backtest, parameters: Dict[str, Any]) -> float:
        """评估参数组合的性能"""
        # 简化实现 - 返回模拟分数
        # 实际实现应该运行回测并计算收益、夏普比率等指标
        
        import random
        import math
        
        # 模拟评估过程
        base_score = random.uniform(0.8, 1.2)
        
        # 根据参数调整分数
        for param_name, param_value in parameters.items():
            if isinstance(param_value, (int, float)):
                # 简单的参数影响模拟
                score_impact = math.sin(param_value * 0.1) * 0.1
                base_score += score_impact
        
        # 添加一些随机性
        base_score += random.uniform(-0.05, 0.05)
        
        return max(0, base_score)  # 确保分数非负

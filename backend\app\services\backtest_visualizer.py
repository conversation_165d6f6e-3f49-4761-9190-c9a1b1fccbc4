"""
回测结果可视化器
生成回测结果的图表和可视化数据，支持多种图表类型
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import json
import base64
from io import BytesIO
import os
import tempfile

# Pyecharts imports
try:
    from pyecharts import options as opts
    from pyecharts.charts import Line, Bar, Pie, Scatter, HeatMap, Radar, Boxplot, Kline
    from pyecharts.commons.utils import JsCode
    from pyecharts.globals import ThemeType
    from pyecharts.render import make_snapshot
    from snapshot_selenium import snapshot
    PYECHARTS_AVAILABLE = True
except ImportError:
    PYECHARTS_AVAILABLE = False

logger = logging.getLogger(__name__)


class BacktestVisualizer:
    """回测结果可视化器"""
    
    def __init__(self):
        self.colors = {
            "primary": "#1f77b4",
            "success": "#2ca02c", 
            "danger": "#d62728",
            "warning": "#ff7f0e",
            "info": "#17a2b8",
            "secondary": "#6c757d"
        }
        
    async def generate_visualization_data(
        self,
        backtest_result: Dict[str, Any],
        analysis_result: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        生成可视化数据
        
        Args:
            backtest_result: 回测结果
            analysis_result: 分析结果
            
        Returns:
            可视化数据字典
        """
        try:
            visualization_data = {}
            
            # 1. 权益曲线图
            visualization_data["equity_curve"] = self._create_equity_curve_chart(
                backtest_result.get("equity_curve", [])
            )
            
            # 2. 回撤图
            visualization_data["drawdown_chart"] = self._create_drawdown_chart(
                backtest_result.get("equity_curve", [])
            )
            
            # 3. 收益分布直方图
            visualization_data["return_distribution"] = self._create_return_distribution_chart(
                backtest_result.get("equity_curve", [])
            )
            
            # 4. 月度收益热力图
            visualization_data["monthly_returns_heatmap"] = self._create_monthly_returns_heatmap(
                analysis_result.get("seasonality_analysis", {}) if analysis_result else {}
            )
            
            # 5. 滚动绩效图
            visualization_data["rolling_performance"] = self._create_rolling_performance_chart(
                analysis_result.get("rolling_performance", {}) if analysis_result else {}
            )
            
            # 6. 交易分析图
            visualization_data["trade_analysis"] = self._create_trade_analysis_charts(
                backtest_result.get("trades", [])
            )
            
            # 7. 风险指标雷达图
            visualization_data["risk_radar"] = self._create_risk_radar_chart(
                analysis_result.get("risk_metrics", {}) if analysis_result else {}
            )
            
            # 8. 持仓分析饼图
            visualization_data["position_allocation"] = self._create_position_allocation_chart(
                backtest_result.get("positions", [])
            )
            
            # 9. 绩效对比表
            visualization_data["performance_table"] = self._create_performance_table(
                backtest_result, analysis_result
            )
            
            # 10. Monte Carlo仿真结果
            if analysis_result and "monte_carlo" in analysis_result:
                visualization_data["monte_carlo"] = self._create_monte_carlo_chart(
                    analysis_result["monte_carlo"]
                )
            
            return visualization_data
            
        except Exception as e:
            logger.error(f"生成可视化数据失败: {str(e)}")
            raise
    
    def _create_equity_curve_chart(self, equity_data: List[Dict]) -> Dict[str, Any]:
        """创建权益曲线图"""
        if not equity_data:
            return {}
        
        chart_data = {
            "type": "line",
            "title": "策略权益曲线",
            "xAxis": {
                "type": "datetime",
                "data": [item["date"] for item in equity_data]
            },
            "yAxis": {
                "type": "value",
                "name": "资产价值"
            },
            "series": [
                {
                    "name": "策略收益",
                    "type": "line",
                    "data": [
                        {
                            "date": item["date"],
                            "value": item["value"],
                            "return": item.get("return", 0) * 100
                        }
                        for item in equity_data
                    ],
                    "color": self.colors["primary"],
                    "smooth": True
                }
            ],
            "tooltip": {
                "formatter": "日期: {date}<br/>资产价值: ¥{value:,.2f}<br/>累计收益率: {return:.2f}%"
            }
        }
        
        return chart_data
    
    def _create_drawdown_chart(self, equity_data: List[Dict]) -> Dict[str, Any]:
        """创建回撤图"""
        if not equity_data:
            return {}
        
        # 计算回撤
        values = [item["value"] for item in equity_data]
        drawdowns = []
        peak = values[0]
        
        for i, value in enumerate(values):
            if value > peak:
                peak = value
            drawdown = (value - peak) / peak * 100
            drawdowns.append({
                "date": equity_data[i]["date"],
                "drawdown": drawdown
            })
        
        chart_data = {
            "type": "area",
            "title": "策略回撤分析",
            "xAxis": {
                "type": "datetime",
                "data": [item["date"] for item in drawdowns]
            },
            "yAxis": {
                "type": "value",
                "name": "回撤 (%)",
                "max": 0
            },
            "series": [
                {
                    "name": "回撤",
                    "type": "area",
                    "data": [item["drawdown"] for item in drawdowns],
                    "color": self.colors["danger"],
                    "areaStyle": {
                        "opacity": 0.3
                    }
                }
            ],
            "tooltip": {
                "formatter": "日期: {date}<br/>回撤: {value:.2f}%"
            }
        }
        
        return chart_data
    
    def _create_return_distribution_chart(self, equity_data: List[Dict]) -> Dict[str, Any]:
        """创建收益分布直方图"""
        if len(equity_data) < 2:
            return {}
        
        # 计算日收益率
        daily_returns = []
        for i in range(1, len(equity_data)):
            prev_value = equity_data[i-1]["value"]
            curr_value = equity_data[i]["value"]
            daily_return = (curr_value - prev_value) / prev_value * 100
            daily_returns.append(daily_return)
        
        # 创建直方图数据
        if daily_returns:
            hist_data, bin_edges = np.histogram(daily_returns, bins=30)
            bin_centers = [(bin_edges[i] + bin_edges[i+1]) / 2 for i in range(len(bin_edges)-1)]
            
            chart_data = {
                "type": "bar",
                "title": "日收益率分布",
                "xAxis": {
                    "type": "value",
                    "name": "日收益率 (%)"
                },
                "yAxis": {
                    "type": "value",
                    "name": "频次"
                },
                "series": [
                    {
                        "name": "频次",
                        "type": "bar",
                        "data": [
                            {"x": bin_centers[i], "y": hist_data[i]}
                            for i in range(len(bin_centers))
                        ],
                        "color": self.colors["info"]
                    }
                ],
                "statistics": {
                    "mean": np.mean(daily_returns),
                    "std": np.std(daily_returns),
                    "skew": float(pd.Series(daily_returns).skew()),
                    "kurtosis": float(pd.Series(daily_returns).kurtosis())
                }
            }
        else:
            chart_data = {}
        
        return chart_data
    
    def _create_monthly_returns_heatmap(self, seasonality_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建月度收益热力图"""
        monthly_returns = seasonality_data.get("monthly_returns", {})
        
        if not monthly_returns:
            return {}
        
        # 重组数据为热力图格式
        months = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
        month_names = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
        
        heatmap_data = []
        for i, month in enumerate(months):
            if month in monthly_returns:
                heatmap_data.append({
                    "month": month_names[i],
                    "return": monthly_returns[month],
                    "color_intensity": abs(monthly_returns[month])
                })
        
        chart_data = {
            "type": "heatmap",
            "title": "月度收益热力图",
            "data": heatmap_data,
            "colorScale": {
                "min": min([item["return"] for item in heatmap_data]) if heatmap_data else 0,
                "max": max([item["return"] for item in heatmap_data]) if heatmap_data else 0,
                "colors": ["#d62728", "#ffffff", "#2ca02c"]  # 红-白-绿
            }
        }
        
        return chart_data
    
    def _create_rolling_performance_chart(self, rolling_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建滚动绩效图"""
        if not rolling_data:
            return {}
        
        chart_data = {
            "type": "multi_line",
            "title": "滚动绩效分析",
            "data": {},
            "metrics": ["avg_return", "avg_volatility", "avg_sharpe"]
        }
        
        for window, stats in rolling_data.items():
            chart_data["data"][window] = {
                "收益率": stats.get("avg_return", 0) * 100,
                "波动率": stats.get("avg_volatility", 0) * 100,
                "夏普比率": stats.get("avg_sharpe", 0)
            }
        
        return chart_data
    
    def _create_trade_analysis_charts(self, trades: List[Dict]) -> Dict[str, Any]:
        """创建交易分析图表"""
        if not trades:
            return {}
        
        # 交易盈亏分布
        pnl_data = []
        trade_amounts = []
        monthly_trades = {}
        
        for trade in trades:
            if trade.get("action") == "卖出" and "pnl" in trade:
                pnl_data.append(trade["pnl"])
            
            if "amount" in trade:
                trade_amounts.append(trade["amount"])
            
            # 月度交易统计
            if "timestamp" in trade:
                try:
                    trade_date = datetime.fromisoformat(trade["timestamp"])
                    month_key = trade_date.strftime("%Y-%m")
                    monthly_trades[month_key] = monthly_trades.get(month_key, 0) + 1
                except:
                    pass
        
        charts = {}
        
        # 盈亏分布图
        if pnl_data:
            profitable = [p for p in pnl_data if p > 0]
            unprofitable = [p for p in pnl_data if p <= 0]
            
            charts["pnl_distribution"] = {
                "type": "bar",
                "title": "交易盈亏分布",
                "data": [
                    {"category": "盈利交易", "count": len(profitable), "color": self.colors["success"]},
                    {"category": "亏损交易", "count": len(unprofitable), "color": self.colors["danger"]}
                ]
            }
        
        # 交易金额分布
        if trade_amounts:
            charts["amount_distribution"] = {
                "type": "histogram",
                "title": "交易金额分布",
                "bins": 20,
                "data": trade_amounts
            }
        
        # 月度交易频率
        if monthly_trades:
            charts["monthly_frequency"] = {
                "type": "bar",
                "title": "月度交易频率",
                "xAxis": list(monthly_trades.keys()),
                "yAxis": list(monthly_trades.values())
            }
        
        return charts
    
    def _create_risk_radar_chart(self, risk_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """创建风险指标雷达图"""
        if not risk_metrics:
            return {}
        
        # 标准化风险指标（转换为0-100分）
        indicators = []
        
        # 收益指标（越高越好）
        if "alpha" in risk_metrics:
            alpha_score = min(100, max(0, (risk_metrics["alpha"] + 5) / 10 * 100))
            indicators.append({"name": "Alpha", "value": alpha_score})
        
        # 夏普比率（越高越好）
        sharpe_score = min(100, max(0, risk_metrics.get("sortino_ratio", 0) / 3 * 100))
        indicators.append({"name": "夏普比率", "value": sharpe_score})
        
        # VaR指标（绝对值越小越好）
        var_score = min(100, max(0, 100 - abs(risk_metrics.get("var_95", 0))))
        indicators.append({"name": "风险价值", "value": var_score})
        
        # 下行风险（越小越好）
        downside_score = min(100, max(0, 100 - risk_metrics.get("downside_deviation", 0)))
        indicators.append({"name": "下行风险", "value": downside_score})
        
        # 信息比率（越高越好）
        info_score = min(100, max(0, (risk_metrics.get("information_ratio", 0) + 1) / 2 * 100))
        indicators.append({"name": "信息比率", "value": info_score})
        
        chart_data = {
            "type": "radar",
            "title": "风险指标雷达图",
            "indicators": indicators,
            "max": 100,
            "color": self.colors["primary"]
        }
        
        return chart_data
    
    def _create_position_allocation_chart(self, positions: List[Dict]) -> Dict[str, Any]:
        """创建持仓分配饼图"""
        if not positions:
            return {}
        
        total_value = sum(pos.get("market_value", 0) for pos in positions)
        
        if total_value <= 0:
            return {}
        
        pie_data = []
        for pos in positions:
            market_value = pos.get("market_value", 0)
            if market_value > 0:
                percentage = market_value / total_value * 100
                pie_data.append({
                    "name": pos.get("symbol", "未知"),
                    "value": market_value,
                    "percentage": percentage
                })
        
        # 按市值排序
        pie_data.sort(key=lambda x: x["value"], reverse=True)
        
        chart_data = {
            "type": "pie",
            "title": "持仓分配",
            "data": pie_data,
            "total_value": total_value
        }
        
        return chart_data
    
    def _create_performance_table(
        self, 
        backtest_result: Dict[str, Any], 
        analysis_result: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建绩效对比表"""
        summary = backtest_result.get("summary", {})
        performance = backtest_result.get("performance", {})
        risk_metrics = analysis_result.get("risk_metrics", {}) if analysis_result else {}
        
        table_data = {
            "title": "绩效指标汇总",
            "sections": [
                {
                    "name": "收益指标",
                    "metrics": [
                        {"name": "总收益率", "value": f"{summary.get('total_return', 0):.2f}%"},
                        {"name": "年化收益率", "value": f"{summary.get('annual_return', 0):.2f}%"},
                        {"name": "最终资产", "value": f"¥{summary.get('final_capital', 0):,.2f}"}
                    ]
                },
                {
                    "name": "风险指标",
                    "metrics": [
                        {"name": "最大回撤", "value": f"{summary.get('max_drawdown', 0):.2f}%"},
                        {"name": "波动率", "value": f"{summary.get('volatility', 0):.2f}%"},
                        {"name": "夏普比率", "value": f"{summary.get('sharpe_ratio', 0):.2f}"},
                        {"name": "VaR(95%)", "value": f"{risk_metrics.get('var_95', 0):.2f}%"}
                    ]
                },
                {
                    "name": "交易指标",
                    "metrics": [
                        {"name": "总交易次数", "value": str(performance.get('total_trades', 0))},
                        {"name": "胜率", "value": f"{summary.get('win_rate', 0):.2f}%"},
                        {"name": "盈亏比", "value": f"{performance.get('profit_factor', 0):.2f}"},
                        {"name": "平均盈利", "value": f"¥{performance.get('avg_win', 0):,.2f}"}
                    ]
                }
            ]
        }
        
        return table_data
    
    def _create_monte_carlo_chart(self, monte_carlo_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建Monte Carlo仿真图表"""
        if not monte_carlo_data:
            return {}
        
        confidence_intervals = monte_carlo_data.get("confidence_intervals", {})
        
        chart_data = {
            "type": "box_plot",
            "title": "Monte Carlo 仿真结果",
            "data": {
                "confidence_intervals": confidence_intervals,
                "expected_return": monte_carlo_data.get("expected_return", 0) * 100,
                "probability_positive": monte_carlo_data.get("probability_positive", 0) * 100,
                "worst_case": confidence_intervals.get("5%", 0) * 100,
                "best_case": confidence_intervals.get("95%", 0) * 100
            },
            "interpretation": {
                "盈利概率": f"{monte_carlo_data.get('probability_positive', 0) * 100:.1f}%",
                "预期收益": f"{monte_carlo_data.get('expected_return', 0) * 100:.2f}%",
                "最坏情况(5%)": f"{confidence_intervals.get('5%', 0) * 100:.2f}%",
                "最好情况(95%)": f"{confidence_intervals.get('95%', 0) * 100:.2f}%"
            }
        }
        
        return chart_data
    
    async def create_pyecharts_line_chart(self, equity_data: List[Dict]) -> str:
        """使用pyecharts创建权益曲线图"""
        if not PYECHARTS_AVAILABLE or not equity_data:
            return ""
        
        try:
            dates = [item["date"] for item in equity_data]
            values = [item["value"] for item in equity_data]
            
            line = (
                Line(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
                .add_xaxis(dates)
                .add_yaxis(
                    "策略权益",
                    values,
                    is_smooth=True,
                    itemstyle_opts=opts.ItemStyleOpts(color="#1f77b4"),
                    linestyle_opts=opts.LineStyleOpts(width=2)
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(title="策略权益曲线"),
                    xaxis_opts=opts.AxisOpts(
                        type_="time",
                        axislabel_opts=opts.LabelOpts(rotate=45)
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="资产价值",
                        axislabel_opts=opts.LabelOpts(formatter="{value}")
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        axis_pointer_type="cross"
                    ),
                    datazoom_opts=[
                        opts.DataZoomOpts(),
                        opts.DataZoomOpts(type_="inside")
                    ]
                )
            )
            
            # 保存到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix=".html", delete=False)
            line.render(temp_file.name)
            
            # 读取HTML内容
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 清理临时文件
            os.unlink(temp_file.name)
            
            return html_content
            
        except Exception as e:
            logger.error(f"创建pyecharts线图失败: {str(e)}")
            return ""
    
    async def create_pyecharts_pie_chart(self, positions: List[Dict]) -> str:
        """使用pyecharts创建持仓分配饼图"""
        if not PYECHARTS_AVAILABLE or not positions:
            return ""
        
        try:
            total_value = sum(pos.get("market_value", 0) for pos in positions)
            if total_value <= 0:
                return ""
            
            pie_data = []
            for pos in positions:
                market_value = pos.get("market_value", 0)
                if market_value > 0:
                    pie_data.append([
                        pos.get("symbol", "未知"),
                        round(market_value, 2)
                    ])
            
            pie = (
                Pie(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
                .add(
                    "",
                    pie_data,
                    radius=["40%", "70%"],
                    label_opts=opts.LabelOpts(
                        position="outside",
                        formatter="{a|{a}}{abg|}\n{hr|}\n {b|{b}: }{c}  {per|{d}%}  ",
                        background_color="#eee",
                        border_color="#aaa",
                        border_width=1,
                        border_radius=4,
                        rich={
                            "a": {"color": "#999", "lineHeight": 22, "align": "center"},
                            "abg": {"backgroundColor": "#e3e3e3", "width": "100%", "align": "right", "height": 22, "borderRadius": [4, 4, 0, 0]},
                            "hr": {"borderColor": "#aaa", "width": "100%", "borderWidth": 0.5, "height": 0},
                            "b": {"fontSize": 16, "lineHeight": 33},
                            "per": {"color": "#eee", "backgroundColor": "#334455", "padding": [2, 4], "borderRadius": 2},
                        }
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(title="持仓分配"),
                    legend_opts=opts.LegendOpts(type_="scroll", pos_left="80%", orient="vertical")
                )
            )
            
            # 保存到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix=".html", delete=False)
            pie.render(temp_file.name)
            
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            os.unlink(temp_file.name)
            
            return html_content
            
        except Exception as e:
            logger.error(f"创建pyecharts饼图失败: {str(e)}")
            return ""
    
    async def create_pyecharts_bar_chart(self, data: Dict[str, Any]) -> str:
        """使用pyecharts创建柱状图"""
        if not PYECHARTS_AVAILABLE:
            return ""
        
        try:
            categories = data.get("categories", [])
            values = data.get("values", [])
            title = data.get("title", "柱状图")
            
            bar = (
                Bar(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
                .add_xaxis(categories)
                .add_yaxis("数值", values)
                .set_global_opts(
                    title_opts=opts.TitleOpts(title=title),
                    xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
                    yaxis_opts=opts.AxisOpts(name="数值"),
                    tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow")
                )
            )
            
            temp_file = tempfile.NamedTemporaryFile(suffix=".html", delete=False)
            bar.render(temp_file.name)
            
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            os.unlink(temp_file.name)
            
            return html_content
            
        except Exception as e:
            logger.error(f"创建pyecharts柱状图失败: {str(e)}")
            return ""
    
    async def create_pyecharts_kline_chart(self, stock_data: List[Dict]) -> str:
        """使用pyecharts创建K线图"""
        if not PYECHARTS_AVAILABLE or not stock_data:
            return ""
        
        try:
            dates = [item["date"] for item in stock_data]
            kline_data = [
                [item["open"], item["close"], item["low"], item["high"]]
                for item in stock_data
            ]
            
            kline = (
                Kline(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
                .add_xaxis(dates)
                .add_yaxis("K线", kline_data)
                .set_global_opts(
                    title_opts=opts.TitleOpts(title="股价K线图"),
                    xaxis_opts=opts.AxisOpts(
                        type_="category",
                        is_scale=True,
                        boundary_gap=False,
                        axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                        splitline_opts=opts.SplitLineOpts(is_show=False),
                        split_number=20,
                        min_="dataMin",
                        max_="dataMax",
                    ),
                    yaxis_opts=opts.AxisOpts(
                        is_scale=True,
                        splitline_opts=opts.SplitLineOpts(is_show=True)
                    ),
                    tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="line"),
                    datazoom_opts=[
                        opts.DataZoomOpts(is_show=False, type_="inside", xaxis_index=[0], range_end=100),
                        opts.DataZoomOpts(is_show=True, xaxis_index=[0], type_="slider", top="90%", range_end=100),
                    ],
                )
            )
            
            temp_file = tempfile.NamedTemporaryFile(suffix=".html", delete=False)
            kline.render(temp_file.name)
            
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            os.unlink(temp_file.name)
            
            return html_content
            
        except Exception as e:
            logger.error(f"创建pyecharts K线图失败: {str(e)}")
            return ""

    async def export_chart_image(self, chart_data: Dict[str, Any], format: str = "png") -> str:
        """导出图表为图片（返回base64编码）"""
        try:
            if not PYECHARTS_AVAILABLE:
                # 如果pyecharts不可用，返回一个占位符
                placeholder_data = f"Chart data for {chart_data.get('title', 'Untitled Chart')}"
                image_bytes = placeholder_data.encode('utf-8')
                base64_string = base64.b64encode(image_bytes).decode('utf-8')
                return f"data:image/{format};base64,{base64_string}"
            
            # 如果可用，生成实际的图表HTML
            chart_type = chart_data.get("type", "")
            if chart_type == "line" and "equity_curve" in str(chart_data):
                return await self.create_pyecharts_line_chart(chart_data.get("data", []))
            elif chart_type == "pie":
                return await self.create_pyecharts_pie_chart(chart_data.get("data", []))
            elif chart_type == "bar":
                return await self.create_pyecharts_bar_chart(chart_data)
            else:
                # 默认返回JSON格式的图表数据
                return json.dumps(chart_data, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"导出图表图片失败: {str(e)}")
            return ""
    
    async def generate_report_pdf(self, visualization_data: Dict[str, Any]) -> bytes:
        """生成PDF报告"""
        try:
            # 这里可以使用ReportLab或其他PDF生成库
            # 当前返回一个简单的文本报告
            
            report_content = "# 回测报告\n\n"
            
            for chart_name, chart_data in visualization_data.items():
                if isinstance(chart_data, dict) and "title" in chart_data:
                    report_content += f"## {chart_data['title']}\n\n"
                    # 添加图表描述和关键数据
                    if "statistics" in chart_data:
                        for key, value in chart_data["statistics"].items():
                            report_content += f"- {key}: {value:.4f}\n"
                    report_content += "\n"
            
            return report_content.encode('utf-8')
            
        except Exception as e:
            logger.error(f"生成PDF报告失败: {str(e)}")
            return b""
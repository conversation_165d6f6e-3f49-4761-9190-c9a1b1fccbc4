"""
CTP交易接口服务
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from decimal import Decimal
from typing import Any, Callable, Dict, List, Optional

from sqlalchemy import insert, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.ctp_config import (
    CTPConfig,
    CTPError,
    CTPOrderRef,
    CTPStatus,
    ctp_config,
    get_error_message,
)
from app.core.database import get_db
from app.db.models.ctp_models import CTPAccount, CTPOrder, CTPPosition, CTPTrade
from app.schemas.trading import OrderRequest, OrderResponse
from app.services.ctp_wrapper import CTPWrapper, get_ctp_wrapper

# 导入监控系统
try:
    from app.monitoring.ctp_alerts import CTPAlertManager
    from app.monitoring.ctp_metrics import CTPMetricsCollector

    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logging.warning("Monitoring system not available")

# 导入性能优化组件
try:
    from app.core.cache import <PERSON><PERSON><PERSON>eyPrefix, cache_manager
    from app.core.database_optimizer import db_optimizer
    from app.core.performance_optimizer import performance_optimizer

    OPTIMIZATION_AVAILABLE = True
except ImportError:
    OPTIMIZATION_AVAILABLE = False
    logging.warning("Performance optimization not available")

logger = logging.getLogger(__name__)


class CTPService:
    """CTP交易服务"""

    def __init__(self, config: Optional[CTPConfig] = None):
        self.config = config or ctp_config
        self.status = CTPStatus()
        self.order_ref_manager = CTPOrderRef(
            prefix=self.config.order_ref_prefix, max_ref=self.config.max_order_ref
        )

        # 初始化监控系统
        if MONITORING_AVAILABLE:
            self.metrics_collector = CTPMetricsCollector()
            self.alert_manager = CTPAlertManager()
            logger.info("Monitoring system initialized")
        else:
            self.metrics_collector = None
            self.alert_manager = None

        # 初始化CTP底层封装
        self.ctp_wrapper = get_ctp_wrapper(config)

        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            "on_order": [],
            "on_trade": [],
            "on_position": [],
            "on_account": [],
            "on_tick": [],
            "on_error": [],
        }

        # 数据缓存
        self.orders: Dict[str, Dict] = {}
        self.trades: Dict[str, Dict] = {}
        self.positions: Dict[str, Dict] = {}
        self.account: Optional[Dict] = None
        self.ticks: Dict[str, Dict] = {}

        # 异步锁
        self._lock = asyncio.Lock()
        self._db_session: Optional[AsyncSession] = None

        # 注册CTP回调
        self._register_ctp_callbacks()

        logger.info("CTP服务初始化完成")

    def _register_ctp_callbacks(self):
        """注册CTP回调函数"""
        # 注册交易回调
        self.ctp_wrapper.register_callback("on_rsp_order_insert", self._on_order_insert)
        self.ctp_wrapper.register_callback("on_rtn_order", self._on_order_update)
        self.ctp_wrapper.register_callback("on_rtn_trade", self._on_trade)
        self.ctp_wrapper.register_callback(
            "on_rsp_qry_trading_account", self._on_account_update
        )
        self.ctp_wrapper.register_callback(
            "on_rsp_qry_investor_position", self._on_position_update
        )

        # 注册行情回调
        self.ctp_wrapper.register_callback(
            "on_rtn_depth_market_data", self._on_market_data
        )

        # 注册错误回调
        self.ctp_wrapper.register_callback("on_rsp_error", self._on_error)

    async def _on_order_insert(
        self, order_data: Dict, error_info: Optional[Dict], request_id: int
    ):
        """订单录入回调"""
        if error_info:
            logger.error(f"订单录入失败: {error_info}")
            return

        # 处理订单录入成功
        logger.info(f"订单录入成功: {order_data}")

        # 触发用户回调
        for callback in self.callbacks.get("on_order", []):
            try:
                await callback(order_data)
            except Exception as e:
                logger.error(f"订单回调执行失败: {e}")

    async def _on_order_update(self, order_data: Dict):
        """订单状态更新回调"""
        logger.info(f"订单状态更新: {order_data}")

        # 更新本地缓存
        order_ref = order_data.get("OrderRef")
        if order_ref:
            self.orders[order_ref] = order_data

        # 触发用户回调
        for callback in self.callbacks.get("on_order", []):
            try:
                await callback(order_data)
            except Exception as e:
                logger.error(f"订单回调执行失败: {e}")

    async def _on_trade(self, trade_data: Dict):
        """成交回调"""
        logger.info(f"成交回报: {trade_data}")

        # 更新本地缓存
        trade_id = trade_data.get("TradeID")
        if trade_id:
            self.trades[trade_id] = trade_data

        # 触发用户回调
        for callback in self.callbacks.get("on_trade", []):
            try:
                await callback(trade_data)
            except Exception as e:
                logger.error(f"成交回调执行失败: {e}")

    async def _on_account_update(
        self, account_data: Dict, error_info: Optional[Dict], request_id: int
    ):
        """账户更新回调"""
        if error_info:
            logger.error(f"账户查询失败: {error_info}")
            return

        logger.info(f"账户信息更新: {account_data}")

        # 更新本地缓存
        self.account = account_data

        # 触发用户回调
        for callback in self.callbacks.get("on_account", []):
            try:
                await callback(account_data)
            except Exception as e:
                logger.error(f"账户回调执行失败: {e}")

    async def _on_position_update(
        self, position_data: Dict, error_info: Optional[Dict], request_id: int
    ):
        """持仓更新回调"""
        if error_info:
            logger.error(f"持仓查询失败: {error_info}")
            return

        logger.info(f"持仓信息更新: {position_data}")

        # 更新本地缓存
        instrument_id = position_data.get("InstrumentID")
        if instrument_id:
            self.positions[instrument_id] = position_data

        # 触发用户回调
        for callback in self.callbacks.get("on_position", []):
            try:
                await callback(position_data)
            except Exception as e:
                logger.error(f"持仓回调执行失败: {e}")

    async def _on_market_data(self, market_data: Dict):
        """行情数据回调"""
        symbol = market_data.get("InstrumentID")
        if symbol:
            # 转换为标准格式
            tick_data = {
                "symbol": symbol,
                "exchange": market_data.get("ExchangeID", ""),
                "last_price": market_data.get("LastPrice", 0.0),
                "bid_price": market_data.get("BidPrice1", 0.0),
                "ask_price": market_data.get("AskPrice1", 0.0),
                "bid_volume": market_data.get("BidVolume1", 0),
                "ask_volume": market_data.get("AskVolume1", 0),
                "volume": market_data.get("Volume", 0),
                "turnover": market_data.get("Turnover", 0.0),
                "open_interest": market_data.get("OpenInterest", 0),
                "update_time": market_data.get("UpdateTime", ""),
                "update_millisec": market_data.get("UpdateMillisec", 0),
                "timestamp": datetime.now().isoformat(),
            }

            # 更新本地缓存
            self.ticks[symbol] = tick_data

            # 触发用户回调
            for callback in self.callbacks.get("on_tick", []):
                try:
                    await callback(tick_data)
                except Exception as e:
                    logger.error(f"行情回调执行失败: {e}")

    async def _on_error(self, error_info: Dict, request_id: int):
        """错误回调"""
        logger.error(f"CTP错误: {error_info}")

        # 更新状态
        self.status.last_error = error_info.get("ErrorMsg", "未知错误")
        self.status.error_count += 1

        # 触发用户回调
        for callback in self.callbacks.get("on_error", []):
            try:
                await callback(error_info)
            except Exception as e:
                logger.error(f"错误回调执行失败: {e}")

    async def initialize(self) -> bool:
        """初始化CTP连接"""
        try:
            logger.info("开始初始化CTP连接...")

            # 验证配置
            if not self._validate_config():
                raise CTPError(-1, "CTP配置验证失败")

            # 初始化数据库会话
            self._db_session = next(get_db())

            # 连接交易前置
            if not await self.ctp_wrapper.connect_trade():
                raise CTPError(-1, "连接交易前置失败")

            # 连接行情前置
            if not await self.ctp_wrapper.connect_md():
                raise CTPError(-1, "连接行情前置失败")

            # 交易登录
            if not await self.ctp_wrapper.login_trade():
                raise CTPError(-1, "交易登录失败")

            # 行情登录
            if not await self.ctp_wrapper.login_md():
                raise CTPError(-1, "行情登录失败")

            # 更新状态
            self.status = self.ctp_wrapper.get_status()

            logger.info("CTP连接初始化完成")
            return True

        except Exception as e:
            logger.error(f"CTP初始化失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            return False

    def _validate_config(self) -> bool:
        """验证配置"""
        required_fields = [
            "broker_id",
            "user_id",
            "password",
            "trade_front",
            "md_front",
        ]
        for field in required_fields:
            if not getattr(self.config, field):
                logger.error(f"CTP配置缺少必要字段: {field}")
                return False
        return True

    async def submit_order(
        self, order_request: OrderRequest, user_id: int
    ) -> OrderResponse:
        """提交订单（性能优化版本）"""
        try:
            # 使用性能优化器提交订单
            if OPTIMIZATION_AVAILABLE:
                order_data = {
                    "user_id": user_id,
                    "symbol": order_request.symbol,
                    "direction": order_request.direction,
                    "offset": order_request.offset,
                    "order_type": order_request.order_type,
                    "price": order_request.price,
                    "volume": order_request.volume,
                }
                order_id = await performance_optimizer.submit_order(order_data)

                # 缓存订单请求
                await cache_manager.cache.set(
                    CacheKeyPrefix.ORDER_DATA,
                    f"request_{order_id}",
                    order_data,
                    ttl=3600,
                )

            async with self._lock:
                if not self.ctp_wrapper.is_ready():
                    raise CTPError(-1, "CTP接口未就绪")

                # 生成订单引用
                order_ref = self.order_ref_manager.get_next_ref()

                # 创建CTP订单记录
                ctp_order = CTPOrder(
                    user_id=user_id,
                    order_ref=order_ref,
                    instrument_id=order_request.symbol,
                    exchange_id=self._get_exchange_id(order_request.symbol),
                    direction=self._convert_direction(order_request.direction),
                    offset_flag=self._convert_offset(order_request.offset),
                    order_price_type=self._convert_order_type(order_request.order_type),
                    limit_price=Decimal(str(order_request.price)),
                    volume_total_original=order_request.volume,
                    time_condition="3",  # 当日有效
                    volume_condition="1",  # 任何数量
                    volume_total=order_request.volume,
                )

                # 保存到数据库
                if self._db_session:
                    self._db_session.add(ctp_order)
                    await self._db_session.commit()
                    await self._db_session.refresh(ctp_order)

                # 构造订单数据
                order_data = {
                    "instrument_id": order_request.symbol,
                    "order_ref": order_ref,
                    "direction": self._convert_direction(order_request.direction),
                    "offset_flag": self._convert_offset(order_request.offset),
                    "order_price_type": self._convert_order_type(
                        order_request.order_type
                    ),
                    "limit_price": Decimal(str(order_request.price)),
                    "volume": order_request.volume,
                }

                # 提交到CTP
                success, result = await self.ctp_wrapper.insert_order(order_data)

                if success:
                    # 更新订单系统ID
                    ctp_order.order_sys_id = result
                    if self._db_session:
                        await self._db_session.commit()

                    # 更新统计
                    self.status.order_count += 1

                    logger.info(f"订单提交成功: {order_ref}")

                    return OrderResponse(
                        success=True,
                        message="订单提交成功",
                        data={
                            "order_ref": order_ref,
                            "order_id": str(ctp_order.id),
                            "order_sys_id": result,
                            "symbol": order_request.symbol,
                            "direction": order_request.direction,
                            "price": order_request.price,
                            "volume": order_request.volume,
                            "status": "SUBMITTED",
                        },
                    )
                else:
                    raise CTPError(-1, f"订单提交失败: {result}")

        except Exception as e:
            logger.error(f"订单提交失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            raise CTPError(-1, f"订单提交失败: {e}")

    async def cancel_order(self, order_ref: str, user_id: int) -> bool:
        """撤销订单"""
        try:
            async with self._lock:
                if not self.ctp_wrapper.is_ready():
                    raise CTPError(-1, "CTP接口未就绪")

                # 查找订单
                if self._db_session:
                    result = await self._db_session.execute(
                        select(CTPOrder).where(
                            CTPOrder.order_ref == order_ref, CTPOrder.user_id == user_id
                        )
                    )
                    order = result.scalar_one_or_none()

                    if not order:
                        raise CTPError(-1, f"订单不存在: {order_ref}")

                    # 构造撤单数据
                    cancel_data = {
                        "order_ref": order_ref,
                        "instrument_id": order.instrument_id,
                        "exchange_id": order.exchange_id,
                        "order_sys_id": order.order_sys_id,
                    }

                    # 提交撤单到CTP
                    success, result = await self.ctp_wrapper.cancel_order(cancel_data)

                    if success:
                        logger.info(f"订单撤销成功: {order_ref}")
                        return True
                    else:
                        raise CTPError(-1, f"订单撤销失败: {result}")

                return False

        except Exception as e:
            logger.error(f"订单撤销失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            raise CTPError(-1, f"订单撤销失败: {e}")

    async def query_orders(self, user_id: int) -> List[Dict]:
        """查询订单"""
        try:
            if self._db_session:
                result = await self._db_session.execute(
                    select(CTPOrder)
                    .where(CTPOrder.user_id == user_id)
                    .order_by(CTPOrder.created_at.desc())
                )
                orders = result.scalars().all()

                return [self._order_to_dict(order) for order in orders]

            return []

        except Exception as e:
            logger.error(f"查询订单失败: {e}")
            raise CTPError(-1, f"查询订单失败: {e}")

    async def query_trades(self, user_id: int) -> List[Dict]:
        """查询成交"""
        try:
            if self._db_session:
                result = await self._db_session.execute(
                    select(CTPTrade)
                    .where(CTPTrade.user_id == user_id)
                    .order_by(CTPTrade.created_at.desc())
                )
                trades = result.scalars().all()

                return [self._trade_to_dict(trade) for trade in trades]

            return []

        except Exception as e:
            logger.error(f"查询成交失败: {e}")
            raise CTPError(-1, f"查询成交失败: {e}")

    async def query_positions(self, user_id: int) -> List[Dict]:
        """查询持仓"""
        try:
            if self._db_session:
                result = await self._db_session.execute(
                    select(CTPPosition).where(CTPPosition.user_id == user_id)
                )
                positions = result.scalars().all()

                return [self._position_to_dict(position) for position in positions]

            return []

        except Exception as e:
            logger.error(f"查询持仓失败: {e}")
            raise CTPError(-1, f"查询持仓失败: {e}")

    async def query_account(self, user_id: int) -> Optional[Dict]:
        """查询账户"""
        try:
            if self._db_session:
                result = await self._db_session.execute(
                    select(CTPAccount)
                    .where(CTPAccount.user_id == user_id)
                    .order_by(CTPAccount.created_at.desc())
                    .limit(1)
                )
                account = result.scalar_one_or_none()

                if account:
                    return self._account_to_dict(account)

            return None

        except Exception as e:
            logger.error(f"查询账户失败: {e}")
            raise CTPError(-1, f"查询账户失败: {e}")

    def get_status(self) -> CTPStatus:
        """获取连接状态"""
        # 更新状态
        self.status = self.ctp_wrapper.get_status()
        return self.status

    def add_callback(self, event: str, callback: Callable):
        """添加回调函数"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)

    def remove_callback(self, event: str, callback: Callable):
        """移除回调函数"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)

    def _get_exchange_id(self, symbol: str) -> str:
        """根据合约代码获取交易所代码"""
        # 简单的交易所映射逻辑
        if symbol.startswith(("cu", "al", "zn", "pb", "ni", "sn", "au", "ag")):
            return "SHFE"
        elif symbol.startswith(("c", "m", "y", "p", "a", "b", "jm", "i", "j")):
            return "DCE"
        elif symbol.startswith(("CF", "SR", "TA", "OI", "MA", "FG", "RM", "ZC")):
            return "CZCE"
        else:
            return "SHFE"  # 默认上期所

    def _convert_direction(self, direction: str) -> str:
        """转换交易方向"""
        return "0" if direction.upper() in ["BUY", "LONG"] else "1"

    def _convert_offset(self, offset: str) -> str:
        """转换开平标志"""
        offset_map = {
            "OPEN": "0",
            "CLOSE": "1",
            "CLOSE_TODAY": "3",
            "CLOSE_YESTERDAY": "4",
        }
        return offset_map.get(offset.upper(), "0")

    def _convert_order_type(self, order_type: str) -> str:
        """转换订单类型"""
        type_map = {"LIMIT": "2", "MARKET": "1", "STOP": "3"}
        return type_map.get(order_type.upper(), "2")

    def _order_to_dict(self, order: CTPOrder) -> Dict:
        """订单对象转字典"""
        return {
            "id": str(order.id),
            "order_ref": order.order_ref,
            "order_sys_id": order.order_sys_id,
            "symbol": order.instrument_id,
            "exchange": order.exchange_id,
            "direction": order.direction,
            "offset": order.offset_flag,
            "price": float(order.limit_price),
            "volume": order.volume_total_original,
            "traded": order.volume_traded,
            "remaining": order.volume_total,
            "status": order.order_status,
            "insert_time": order.insert_time,
            "created_at": order.created_at.isoformat() if order.created_at else None,
        }

    def _trade_to_dict(self, trade: CTPTrade) -> Dict:
        """成交对象转字典"""
        return {
            "id": str(trade.id),
            "trade_id": trade.trade_id,
            "order_ref": trade.order_ref,
            "symbol": trade.instrument_id,
            "exchange": trade.exchange_id,
            "direction": trade.direction,
            "offset": trade.offset_flag,
            "price": float(trade.price),
            "volume": trade.volume,
            "trade_time": trade.trade_time,
            "created_at": trade.created_at.isoformat() if trade.created_at else None,
        }

    def _position_to_dict(self, position: CTPPosition) -> Dict:
        """持仓对象转字典"""
        return {
            "id": str(position.id),
            "symbol": position.instrument_id,
            "direction": position.position_direction,
            "position": position.position,
            "yd_position": position.yd_position,
            "frozen": position.long_frozen + position.short_frozen,
            "cost": float(position.position_cost),
            "margin": float(position.use_margin),
            "profit": float(position.position_profit),
            "created_at": (
                position.created_at.isoformat() if position.created_at else None
            ),
        }

    def _account_to_dict(self, account: CTPAccount) -> Dict:
        """账户对象转字典"""
        return {
            "id": str(account.id),
            "account_id": account.account_id,
            "balance": float(account.balance),
            "available": float(account.available),
            "margin": float(account.curr_margin),
            "frozen_margin": float(account.frozen_margin),
            "frozen_cash": float(account.frozen_cash),
            "position_profit": float(account.position_profit),
            "close_profit": float(account.close_profit),
            "commission": float(account.commission),
            "trading_day": account.trading_day,
            "created_at": (
                account.created_at.isoformat() if account.created_at else None
            ),
        }

    async def subscribe_market_data(self, symbols: List[str]) -> bool:
        """订阅行情数据"""
        try:
            if not self.ctp_wrapper.is_ready():
                raise CTPError(-1, "CTP接口未就绪")

            # 检查订阅数量限制
            if len(symbols) > self.config.max_subscribe_count:
                raise CTPError(
                    -1,
                    f"订阅数量超过限制: {len(symbols)} > {self.config.max_subscribe_count}",
                )

            # 提交订阅到CTP
            success = await self.ctp_wrapper.subscribe_market_data(symbols)

            if success:
                self.status.subscribe_count += len(symbols)
                logger.info(f"订阅行情成功: {symbols}")
                return True
            else:
                raise CTPError(-1, "订阅行情失败")

        except Exception as e:
            logger.error(f"订阅行情失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            raise CTPError(-1, f"订阅行情失败: {e}")

    async def unsubscribe_market_data(self, symbols: List[str]) -> bool:
        """取消订阅行情数据"""
        try:
            if not self.ctp_wrapper.is_ready():
                raise CTPError(-1, "CTP接口未就绪")

            # 提交取消订阅到CTP
            success = await self.ctp_wrapper.unsubscribe_market_data(symbols)

            if success:
                # 清理本地缓存
                for symbol in symbols:
                    if symbol in self.ticks:
                        del self.ticks[symbol]

                self.status.subscribe_count -= len(symbols)
                logger.info(f"取消订阅行情成功: {symbols}")
                return True
            else:
                raise CTPError(-1, "取消订阅行情失败")

        except Exception as e:
            logger.error(f"取消订阅行情失败: {e}")
            raise CTPError(-1, f"取消订阅行情失败: {e}")

    async def get_tick_data(self, symbol: str) -> Optional[Dict]:
        """获取最新行情数据"""
        return self.ticks.get(symbol)

    async def disconnect(self):
        """断开连接"""
        try:
            logger.info("断开CTP连接...")

            # 使用CTP封装断开连接
            await self.ctp_wrapper.disconnect()

            # 更新状态
            self.status = self.ctp_wrapper.get_status()

            # 清理数据
            self.orders.clear()
            self.trades.clear()
            self.positions.clear()
            self.ticks.clear()
            self.account = None

            # 关闭数据库会话
            if self._db_session:
                await self._db_session.close()
                self._db_session = None

            logger.info("CTP连接已断开")

        except Exception as e:
            logger.error(f"断开CTP连接失败: {e}")
            raise CTPError(-1, f"断开连接失败: {e}")

    async def reconnect(self) -> bool:
        """重新连接"""
        try:
            logger.info("重新连接CTP...")

            # 先断开现有连接
            await self.disconnect()

            # 重新初始化
            return await self.initialize()

        except Exception as e:
            logger.error(f"重新连接失败: {e}")
            return False


class CTPMarketDataService:
    """CTP行情数据服务"""

    def __init__(self, ctp_service: CTPService):
        self.ctp_service = ctp_service
        self.subscribers: Dict[str, set] = {}  # symbol -> set of client_ids
        self.client_subscriptions: Dict[str, set] = {}  # client_id -> set of symbols

    async def subscribe(self, client_id: str, symbols: List[str]):
        """客户端订阅行情"""
        try:
            # 记录客户端订阅
            if client_id not in self.client_subscriptions:
                self.client_subscriptions[client_id] = set()

            new_symbols = []
            for symbol in symbols:
                if symbol not in self.subscribers:
                    self.subscribers[symbol] = set()
                    new_symbols.append(symbol)

                self.subscribers[symbol].add(client_id)
                self.client_subscriptions[client_id].add(symbol)

            # 订阅新的合约
            if new_symbols:
                await self.ctp_service.subscribe_market_data(new_symbols)

            logger.info(f"客户端 {client_id} 订阅行情: {symbols}")

        except Exception as e:
            logger.error(f"订阅行情失败: {e}")
            raise

    async def unsubscribe(self, client_id: str, symbols: List[str] = None):
        """客户端取消订阅行情"""
        try:
            if client_id not in self.client_subscriptions:
                return

            # 如果没有指定symbols，取消所有订阅
            if symbols is None:
                symbols = list(self.client_subscriptions[client_id])

            symbols_to_unsubscribe = []
            for symbol in symbols:
                if symbol in self.subscribers and client_id in self.subscribers[symbol]:
                    self.subscribers[symbol].remove(client_id)
                    self.client_subscriptions[client_id].discard(symbol)

                    # 如果没有其他客户端订阅这个合约，取消订阅
                    if not self.subscribers[symbol]:
                        del self.subscribers[symbol]
                        symbols_to_unsubscribe.append(symbol)

            # 清理空的客户端记录
            if not self.client_subscriptions[client_id]:
                del self.client_subscriptions[client_id]

            # 取消订阅没有客户端的合约
            if symbols_to_unsubscribe:
                await self.ctp_service.unsubscribe_market_data(symbols_to_unsubscribe)

            logger.info(f"客户端 {client_id} 取消订阅行情: {symbols}")

        except Exception as e:
            logger.error(f"取消订阅行情失败: {e}")
            raise

    def get_subscribers(self, symbol: str) -> set:
        """获取合约的订阅客户端"""
        return self.subscribers.get(symbol, set())

    def get_client_subscriptions(self, client_id: str) -> set:
        """获取客户端的订阅合约"""
        return self.client_subscriptions.get(client_id, set())

    # 监控系统集成方法
    def _record_metrics(
        self, operation: str, success: bool = True, duration: float = 0.0
    ):
        """记录操作指标"""
        if self.metrics_collector:
            try:
                self.metrics_collector.record_operation(operation, success, duration)
                self.metrics_collector.update_connection_status(
                    self.status.trade_connected, self.status.md_connected
                )
            except Exception as e:
                logger.warning(f"Failed to record metrics: {e}")

    def _check_alerts(self):
        """检查告警条件"""
        if self.alert_manager:
            try:
                # 检查连接状态
                if not self.status.trade_connected:
                    self.alert_manager.check_connection_alert("trade", False)
                if not self.status.md_connected:
                    self.alert_manager.check_connection_alert("md", False)

                # 检查错误率
                if self.status.error_count > 0:
                    error_rate = self.status.error_count / max(
                        self.status.order_count, 1
                    )
                    self.alert_manager.check_error_rate_alert(error_rate)

            except Exception as e:
                logger.warning(f"Failed to check alerts: {e}")

    async def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        status = {
            "service_status": {
                "trade_connected": self.status.trade_connected,
                "md_connected": self.status.md_connected,
                "trade_logged_in": self.status.trade_logged_in,
                "md_logged_in": self.status.md_logged_in,
                "is_ready": self.status.is_ready,
                "error_count": self.status.error_count,
                "order_count": self.status.order_count,
                "trade_count": self.status.trade_count,
            }
        }

        if self.metrics_collector:
            status["metrics"] = await self.metrics_collector.get_current_metrics()

        if self.alert_manager:
            status["alerts"] = await self.alert_manager.get_active_alerts()

        return status


# 全局CTP服务实例
ctp_service = CTPService()
ctp_market_service = CTPMarketDataService(ctp_service)

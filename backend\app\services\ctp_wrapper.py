"""
CTP底层封装
负责与CTP API的直接交互
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from decimal import Decimal
from typing import Any, Callable, Dict, List, Optional, Tuple

from app.core.ctp_config import CTPConfig, CTPError, CTPStatus, ctp_config

logger = logging.getLogger(__name__)


class CTPWrapper:
    """CTP底层封装类"""

    def __init__(self, config: Optional[CTPConfig] = None):
        self.config = config or ctp_config
        self.status = CTPStatus()

        # CTP API 对象
        self.trade_api = None
        self.md_api = None

        # 连接状态
        self._trade_connected = False
        self._md_connected = False
        self._trade_logged_in = False
        self._md_logged_in = False

        # 回调函数注册
        self.callbacks: Dict[str, List[Callable]] = {
            "on_front_connected": [],
            "on_front_disconnected": [],
            "on_rsp_user_login": [],
            "on_rsp_user_logout": [],
            "on_rsp_order_insert": [],
            "on_rsp_order_action": [],
            "on_rtn_order": [],
            "on_rtn_trade": [],
            "on_rsp_qry_trading_account": [],
            "on_rsp_qry_investor_position": [],
            "on_rtn_depth_market_data": [],
            "on_rsp_sub_market_data": [],
            "on_rsp_unsub_market_data": [],
            "on_rsp_error": [],
        }

        # 请求ID管理
        self._request_id = 0

        # 线程锁
        self._lock = threading.Lock()

        logger.info("CTP底层封装初始化完成")

    def get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        with self._lock:
            self._request_id += 1
            return self._request_id

    def register_callback(self, event: str, callback: Callable):
        """注册回调函数"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
        else:
            logger.warning(f"未知的回调事件: {event}")

    def unregister_callback(self, event: str, callback: Callable):
        """取消注册回调函数"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)

    def trigger_callback(self, event: str, *args, **kwargs):
        """触发回调函数"""
        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"回调函数执行失败 {event}: {e}")

    async def connect_trade(self) -> bool:
        """连接交易前置"""
        try:
            logger.info("开始连接交易前置")

            # 这里应该使用实际的CTP API
            # 由于vnpy等库需要在实际环境中安装，这里提供模拟实现
            await asyncio.sleep(1)  # 模拟连接时间

            self._trade_connected = True
            self.status.trade_connected = True
            self.status.trade_connect_time = datetime.now().isoformat()

            # 触发连接成功回调
            self.trigger_callback("on_front_connected", "trade")

            logger.info("交易前置连接成功")
            return True

        except Exception as e:
            logger.error(f"连接交易前置失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            return False

    async def connect_md(self) -> bool:
        """连接行情前置"""
        try:
            logger.info("开始连接行情前置")

            # 模拟连接过程
            await asyncio.sleep(1)

            self._md_connected = True
            self.status.md_connected = True
            self.status.md_connect_time = datetime.now().isoformat()

            # 触发连接成功回调
            self.trigger_callback("on_front_connected", "md")

            logger.info("行情前置连接成功")
            return True

        except Exception as e:
            logger.error(f"连接行情前置失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            return False

    async def login_trade(self) -> bool:
        """交易登录"""
        try:
            if not self._trade_connected:
                raise CTPError(-1, "交易前置未连接")

            logger.info("开始交易登录")

            # 模拟登录过程
            await asyncio.sleep(1)

            self._trade_logged_in = True
            self.status.trade_logged_in = True
            self.status.trade_login_time = datetime.now().isoformat()

            # 触发登录成功回调
            login_info = {
                "BrokerID": self.config.broker_id,
                "UserID": self.config.user_id,
                "TradingDay": datetime.now().strftime("%Y%m%d"),
                "LoginTime": datetime.now().strftime("%H:%M:%S"),
                "SystemName": "CTP System",
                "FrontID": 1,
                "SessionID": 1,
                "MaxOrderRef": "999999",
            }
            self.trigger_callback(
                "on_rsp_user_login", login_info, None, self.get_next_request_id()
            )

            logger.info("交易登录成功")
            return True

        except Exception as e:
            logger.error(f"交易登录失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            return False

    async def login_md(self) -> bool:
        """行情登录"""
        try:
            if not self._md_connected:
                raise CTPError(-1, "行情前置未连接")

            logger.info("开始行情登录")

            # 模拟登录过程
            await asyncio.sleep(1)

            self._md_logged_in = True
            self.status.md_logged_in = True
            self.status.md_login_time = datetime.now().isoformat()

            # 触发登录成功回调
            login_info = {
                "BrokerID": self.config.broker_id,
                "UserID": self.config.user_id,
                "TradingDay": datetime.now().strftime("%Y%m%d"),
                "LoginTime": datetime.now().strftime("%H:%M:%S"),
            }
            self.trigger_callback(
                "on_rsp_user_login", login_info, None, self.get_next_request_id()
            )

            logger.info("行情登录成功")
            return True

        except Exception as e:
            logger.error(f"行情登录失败: {e}")
            self.status.last_error = str(e)
            self.status.error_count += 1
            return False

    async def logout_trade(self) -> bool:
        """交易登出"""
        try:
            if not self._trade_logged_in:
                return True

            logger.info("开始交易登出")

            # 模拟登出过程
            await asyncio.sleep(0.5)

            self._trade_logged_in = False
            self.status.trade_logged_in = False

            # 触发登出回调
            logout_info = {
                "BrokerID": self.config.broker_id,
                "UserID": self.config.user_id,
            }
            self.trigger_callback(
                "on_rsp_user_logout", logout_info, None, self.get_next_request_id()
            )

            logger.info("交易登出成功")
            return True

        except Exception as e:
            logger.error(f"交易登出失败: {e}")
            return False

    async def logout_md(self) -> bool:
        """行情登出"""
        try:
            if not self._md_logged_in:
                return True

            logger.info("开始行情登出")

            # 模拟登出过程
            await asyncio.sleep(0.5)

            self._md_logged_in = False
            self.status.md_logged_in = False

            logger.info("行情登出成功")
            return True

        except Exception as e:
            logger.error(f"行情登出失败: {e}")
            return False

    async def insert_order(self, order_data: Dict) -> Tuple[bool, str]:
        """报单录入"""
        try:
            if not self._trade_logged_in:
                raise CTPError(-1, "交易未登录")

            logger.info(f"开始报单录入: {order_data}")

            # 构造CTP报单请求
            order_req = {
                "BrokerID": self.config.broker_id,
                "InvestorID": self.config.user_id,
                "InstrumentID": order_data["instrument_id"],
                "OrderRef": order_data["order_ref"],
                "UserID": self.config.user_id,
                "OrderPriceType": order_data["order_price_type"],
                "Direction": order_data["direction"],
                "CombOffsetFlag": order_data["offset_flag"],
                "CombHedgeFlag": "1",  # 投机
                "LimitPrice": float(order_data["limit_price"]),
                "VolumeTotalOriginal": order_data["volume"],
                "TimeCondition": order_data.get("time_condition", "3"),  # 当日有效
                "VolumeCondition": order_data.get("volume_condition", "1"),  # 任何数量
                "MinVolume": 1,
                "ContingentCondition": "1",  # 立即
                "StopPrice": 0,
                "ForceCloseReason": "0",  # 非强平
                "IsAutoSuspend": 0,
                "BusinessUnit": "",
                "RequestID": self.get_next_request_id(),
                "UserForceClose": 0,
            }

            # 模拟报单过程
            await asyncio.sleep(0.1)

            # 模拟报单响应
            order_rsp = order_req.copy()
            order_rsp.update(
                {
                    "OrderLocalID": f"local_{order_data['order_ref']}",
                    "OrderSysID": f"sys_{int(time.time())}",
                    "OrderStatus": "0",  # 全部成交
                    "StatusMsg": "报单成功",
                    "InsertDate": datetime.now().strftime("%Y%m%d"),
                    "InsertTime": datetime.now().strftime("%H:%M:%S"),
                    "UpdateTime": datetime.now().strftime("%H:%M:%S"),
                }
            )

            # 触发报单回调
            self.trigger_callback(
                "on_rsp_order_insert", order_rsp, None, order_req["RequestID"]
            )

            # 模拟成交回报
            await asyncio.sleep(0.1)
            trade_data = {
                "BrokerID": self.config.broker_id,
                "InvestorID": self.config.user_id,
                "InstrumentID": order_data["instrument_id"],
                "OrderRef": order_data["order_ref"],
                "UserID": self.config.user_id,
                "ExchangeID": self._get_exchange_id(order_data["instrument_id"]),
                "TradeID": f"trade_{int(time.time())}",
                "Direction": order_data["direction"],
                "OrderSysID": order_rsp["OrderSysID"],
                "ParticipantID": self.config.user_id,
                "ClientID": self.config.user_id,
                "TradingRole": "1",
                "ExchangeInstID": order_data["instrument_id"],
                "OffsetFlag": order_data["offset_flag"][0],
                "HedgeFlag": "1",
                "Price": float(order_data["limit_price"]),
                "Volume": order_data["volume"],
                "TradeDate": datetime.now().strftime("%Y%m%d"),
                "TradeTime": datetime.now().strftime("%H:%M:%S"),
                "TradeType": "0",
                "PriceSource": "0",
                "TraderID": self.config.user_id,
                "OrderLocalID": order_rsp["OrderLocalID"],
                "ClearingPartID": self.config.broker_id,
                "BusinessUnit": "",
                "SequenceNo": int(time.time()),
                "TradingDay": datetime.now().strftime("%Y%m%d"),
                "SettlementID": 1,
                "BrokerOrderSeq": int(time.time()),
                "TradeSource": "0",
            }

            # 触发成交回调
            self.trigger_callback("on_rtn_trade", trade_data)

            logger.info(f"报单录入成功: {order_rsp['OrderSysID']}")
            return True, order_rsp["OrderSysID"]

        except Exception as e:
            logger.error(f"报单录入失败: {e}")
            # 触发错误回调
            error_info = {
                "ErrorID": -1,
                "ErrorMsg": str(e),
            }
            self.trigger_callback(
                "on_rsp_error", error_info, self.get_next_request_id()
            )
            return False, str(e)

    async def cancel_order(self, order_data: Dict) -> Tuple[bool, str]:
        """撤单"""
        try:
            if not self._trade_logged_in:
                raise CTPError(-1, "交易未登录")

            logger.info(f"开始撤单: {order_data}")

            # 构造CTP撤单请求
            cancel_req = {
                "BrokerID": self.config.broker_id,
                "InvestorID": self.config.user_id,
                "OrderActionRef": self.get_next_request_id(),
                "OrderRef": order_data["order_ref"],
                "RequestID": self.get_next_request_id(),
                "FrontID": 1,
                "SessionID": 1,
                "ExchangeID": order_data.get("exchange_id", ""),
                "OrderSysID": order_data.get("order_sys_id", ""),
                "ActionFlag": "0",  # 删除
                "LimitPrice": 0,
                "VolumeChange": 0,
                "UserID": self.config.user_id,
                "InstrumentID": order_data["instrument_id"],
                "InvestUnitID": "",
                "IPAddress": "",
                "MacAddress": "",
            }

            # 模拟撤单过程
            await asyncio.sleep(0.1)

            # 模拟撤单响应
            cancel_rsp = cancel_req.copy()
            cancel_rsp.update(
                {
                    "OrderLocalID": f"local_{order_data['order_ref']}",
                    "ActionLocalID": f"action_{cancel_req['OrderActionRef']}",
                }
            )

            # 触发撤单回调
            self.trigger_callback(
                "on_rsp_order_action", cancel_rsp, None, cancel_req["RequestID"]
            )

            logger.info(f"撤单成功: {order_data['order_ref']}")
            return True, "撤单成功"

        except Exception as e:
            logger.error(f"撤单失败: {e}")
            # 触发错误回调
            error_info = {
                "ErrorID": -1,
                "ErrorMsg": str(e),
            }
            self.trigger_callback(
                "on_rsp_error", error_info, self.get_next_request_id()
            )
            return False, str(e)

    async def query_account(self) -> Tuple[bool, Optional[Dict]]:
        """查询资金账户"""
        try:
            if not self._trade_logged_in:
                raise CTPError(-1, "交易未登录")

            logger.info("开始查询资金账户")

            # 模拟查询过程
            await asyncio.sleep(0.1)

            # 模拟账户数据
            account_data = {
                "BrokerID": self.config.broker_id,
                "AccountID": self.config.user_id,
                "PreMortgage": 0.0,
                "PreCredit": 0.0,
                "PreDeposit": 1000000.0,  # 上次存款额
                "PreBalance": 1000000.0,  # 上次结算准备金
                "PreMargin": 0.0,
                "InterestBase": 0.0,
                "Interest": 0.0,
                "Deposit": 0.0,
                "Withdraw": 0.0,
                "FrozenMargin": 0.0,
                "FrozenCash": 0.0,
                "FrozenCommission": 0.0,
                "CurrMargin": 0.0,
                "CashIn": 0.0,
                "Commission": 0.0,
                "CloseProfit": 0.0,
                "PositionProfit": 0.0,
                "Balance": 1000000.0,  # 结算准备金
                "Available": 1000000.0,  # 可用资金
                "WithdrawQuota": 1000000.0,
                "Reserve": 0.0,
                "TradingDay": datetime.now().strftime("%Y%m%d"),
                "SettlementID": 1,
                "Credit": 0.0,
                "Mortgage": 0.0,
                "ExchangeMargin": 0.0,
                "DeliveryMargin": 0.0,
                "ExchangeDeliveryMargin": 0.0,
                "ReserveBalance": 0.0,
                "CurrencyID": "CNY",
                "PreFundMortgageIn": 0.0,
                "PreFundMortgageOut": 0.0,
                "FundMortgageIn": 0.0,
                "FundMortgageOut": 0.0,
                "FundMortgageAvailable": 0.0,
                "MortgageableFund": 0.0,
                "SpecProductMargin": 0.0,
                "SpecProductFrozenMargin": 0.0,
                "SpecProductCommission": 0.0,
                "SpecProductFrozenCommission": 0.0,
                "SpecProductPositionProfit": 0.0,
                "SpecProductCloseProfit": 0.0,
                "SpecProductPositionProfitByAlg": 0.0,
                "SpecProductExchangeMargin": 0.0,
                "BizType": "1",
                "FrozenSwap": 0.0,
                "RemainSwap": 0.0,
            }

            # 触发查询回调
            self.trigger_callback(
                "on_rsp_qry_trading_account",
                account_data,
                None,
                self.get_next_request_id(),
            )

            logger.info("查询资金账户成功")
            return True, account_data

        except Exception as e:
            logger.error(f"查询资金账户失败: {e}")
            return False, None

    async def query_position(self) -> Tuple[bool, List[Dict]]:
        """查询持仓"""
        try:
            if not self._trade_logged_in:
                raise CTPError(-1, "交易未登录")

            logger.info("开始查询持仓")

            # 模拟查询过程
            await asyncio.sleep(0.1)

            # 模拟持仓数据（空持仓）
            positions = []

            # 触发查询回调
            for position in positions:
                self.trigger_callback(
                    "on_rsp_qry_investor_position",
                    position,
                    None,
                    self.get_next_request_id(),
                )

            logger.info("查询持仓成功")
            return True, positions

        except Exception as e:
            logger.error(f"查询持仓失败: {e}")
            return False, []

    async def subscribe_market_data(self, symbols: List[str]) -> bool:
        """订阅行情"""
        try:
            if not self._md_logged_in:
                raise CTPError(-1, "行情未登录")

            logger.info(f"开始订阅行情: {symbols}")

            # 模拟订阅过程
            await asyncio.sleep(0.1)

            # 触发订阅回调
            for symbol in symbols:
                sub_info = {
                    "InstrumentID": symbol,
                }
                self.trigger_callback(
                    "on_rsp_sub_market_data", sub_info, None, self.get_next_request_id()
                )

            logger.info(f"订阅行情成功: {symbols}")
            return True

        except Exception as e:
            logger.error(f"订阅行情失败: {e}")
            return False

    async def unsubscribe_market_data(self, symbols: List[str]) -> bool:
        """取消订阅行情"""
        try:
            if not self._md_logged_in:
                raise CTPError(-1, "行情未登录")

            logger.info(f"开始取消订阅行情: {symbols}")

            # 模拟取消订阅过程
            await asyncio.sleep(0.1)

            # 触发取消订阅回调
            for symbol in symbols:
                unsub_info = {
                    "InstrumentID": symbol,
                }
                self.trigger_callback(
                    "on_rsp_unsub_market_data",
                    unsub_info,
                    None,
                    self.get_next_request_id(),
                )

            logger.info(f"取消订阅行情成功: {symbols}")
            return True

        except Exception as e:
            logger.error(f"取消订阅行情失败: {e}")
            return False

    async def disconnect(self):
        """断开连接"""
        try:
            logger.info("开始断开连接")

            # 先登出
            await self.logout_trade()
            await self.logout_md()

            # 断开连接
            self._trade_connected = False
            self._md_connected = False
            self.status.trade_connected = False
            self.status.md_connected = False

            # 触发断开连接回调
            self.trigger_callback("on_front_disconnected", "trade")
            self.trigger_callback("on_front_disconnected", "md")

            logger.info("断开连接成功")

        except Exception as e:
            logger.error(f"断开连接失败: {e}")

    def _get_exchange_id(self, instrument_id: str) -> str:
        """根据合约代码获取交易所代码"""
        if (
            instrument_id.startswith("IF")
            or instrument_id.startswith("IC")
            or instrument_id.startswith("IH")
        ):
            return "CFFEX"  # 中金所
        elif (
            instrument_id.startswith("cu")
            or instrument_id.startswith("al")
            or instrument_id.startswith("zn")
        ):
            return "SHFE"  # 上期所
        elif (
            instrument_id.startswith("CF")
            or instrument_id.startswith("SR")
            or instrument_id.startswith("TA")
        ):
            return "CZCE"  # 郑商所
        elif (
            instrument_id.startswith("c")
            or instrument_id.startswith("m")
            or instrument_id.startswith("y")
        ):
            return "DCE"  # 大商所
        elif (
            instrument_id.startswith("T")
            or instrument_id.startswith("TF")
            or instrument_id.startswith("TS")
        ):
            return "CFFEX"  # 中金所国债
        else:
            return "UNKNOWN"

    def get_status(self) -> CTPStatus:
        """获取连接状态"""
        return self.status

    def is_ready(self) -> bool:
        """检查是否准备就绪"""
        return (
            self._trade_connected
            and self._trade_logged_in
            and self._md_connected
            and self._md_logged_in
        )


# 全局CTP封装实例
ctp_wrapper = None


def get_ctp_wrapper(config: Optional[CTPConfig] = None) -> CTPWrapper:
    """获取CTP封装实例"""
    global ctp_wrapper
    if ctp_wrapper is None:
        ctp_wrapper = CTPWrapper(config)
    return ctp_wrapper


def init_ctp_wrapper(config: Optional[CTPConfig] = None) -> CTPWrapper:
    """初始化CTP封装"""
    global ctp_wrapper
    ctp_wrapper = CTPWrapper(config)
    return ctp_wrapper

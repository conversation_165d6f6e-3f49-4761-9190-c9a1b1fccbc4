"""
数据抓取服务
定时抓取股票数据，支持批量处理和缓存
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pathlib import Path
import json
import time

from app.services.tushare_data_service import tushare_service, get_stock_list, get_stock_daily_data
from app.services.akshare_data_service import akshare_service, get_stock_list_akshare, get_stock_daily_data_akshare
from app.core.database import get_db
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

class DataCrawlerService:
    """数据抓取服务"""
    
    def __init__(self):
        self.data_dir = Path("data/processed")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.log_dir = Path("logs/crawler")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.is_running = False
        self.last_run_time = None
        self.stats = {
            "total_stocks": 0,
            "processed_stocks": 0,
            "failed_stocks": 0,
            "start_time": None,
            "end_time": None,
            "duration": 0
        }
    
    async def run_daily_crawl(self, days: int = 300, batch_size: int = 50):
        """运行每日数据抓取"""
        if self.is_running:
            logger.warning("数据抓取任务已在运行中")
            return
        
        self.is_running = True
        self.stats["start_time"] = datetime.now()
        
        try:
            logger.info("开始每日数据抓取任务...")
            
            # 1. 获取股票列表
            stock_list = await self._get_stock_list()
            self.stats["total_stocks"] = len(stock_list)
            logger.info(f"获取到{len(stock_list)}只股票")
            
            # 2. 批量抓取数据
            await self._batch_crawl_stock_data(stock_list, days, batch_size)
            
            # 3. 清理旧缓存
            await self._cleanup_old_data()
            
            # 4. 生成统计报告
            await self._generate_report()
            
            self.stats["end_time"] = datetime.now()
            self.stats["duration"] = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
            
            logger.info(f"数据抓取完成，耗时{self.stats['duration']:.2f}秒")
            
        except Exception as e:
            logger.error(f"数据抓取任务失败: {e}")
        finally:
            self.is_running = False
            self.last_run_time = datetime.now()
    
    async def _get_stock_list(self) -> List[Dict]:
        """获取股票列表"""
        try:
            # 优先使用Tushare
            async with tushare_service:
                stock_list = await tushare_service.get_stock_basic()
                if stock_list:
                    logger.info("使用Tushare获取股票列表")
                    return [
                        {
                            "ts_code": item[0],
                            "symbol": item[1],
                            "name": item[2],
                            "market": "SH" if item[0].endswith(".SH") else "SZ"
                        }
                        for item in stock_list
                        if len(item) >= 3
                    ]
        except Exception as e:
            logger.warning(f"Tushare获取股票列表失败: {e}")
        
        try:
            # 备用AKShare
            stock_list = await get_stock_list_akshare()
            if stock_list:
                logger.info("使用AKShare获取股票列表")
                return [
                    {
                        "ts_code": f"{item.get('code', '')}.{'SH' if item.get('code', '').startswith('6') else 'SZ'}",
                        "symbol": item.get('code', ''),
                        "name": item.get('name', ''),
                        "market": "SH" if item.get('code', '').startswith('6') else "SZ"
                    }
                    for item in stock_list
                    if item.get('code')
                ]
        except Exception as e:
            logger.error(f"AKShare获取股票列表失败: {e}")
        
        return []
    
    async def _batch_crawl_stock_data(self, stock_list: List[Dict], days: int, batch_size: int):
        """批量抓取股票数据"""
        total_batches = (len(stock_list) + batch_size - 1) // batch_size
        
        for i in range(0, len(stock_list), batch_size):
            batch_num = i // batch_size + 1
            batch = stock_list[i:i + batch_size]
            
            logger.info(f"处理第{batch_num}/{total_batches}批，共{len(batch)}只股票")
            
            # 并发处理批次内的股票
            tasks = [
                self._crawl_single_stock(stock, days)
                for stock in batch
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for stock, result in zip(batch, results):
                if isinstance(result, Exception):
                    logger.error(f"抓取{stock['symbol']}失败: {result}")
                    self.stats["failed_stocks"] += 1
                else:
                    self.stats["processed_stocks"] += 1
            
            # 批次间延迟，避免API限制
            if batch_num < total_batches:
                await asyncio.sleep(2)
            
            # 每10批输出进度
            if batch_num % 10 == 0:
                progress = (batch_num / total_batches) * 100
                logger.info(f"进度: {progress:.1f}% ({self.stats['processed_stocks']}/{self.stats['total_stocks']})")
    
    async def _crawl_single_stock(self, stock: Dict, days: int) -> Dict:
        """抓取单只股票数据"""
        symbol = stock["symbol"]
        ts_code = stock["ts_code"]
        
        try:
            # 优先使用Tushare
            data = await get_stock_daily_data(ts_code, days)
            
            if not data:
                # 备用AKShare
                data = await get_stock_daily_data_akshare(symbol, days)
            
            if data:
                # 保存数据
                await self._save_stock_data(stock, data)
                return {"status": "success", "records": len(data)}
            else:
                return {"status": "no_data", "records": 0}
                
        except Exception as e:
            logger.error(f"抓取{symbol}数据失败: {e}")
            raise
    
    async def _save_stock_data(self, stock: Dict, data: List[Dict]):
        """保存股票数据"""
        symbol = stock["symbol"]
        date_str = datetime.now().strftime('%Y%m%d')
        
        # 保存到文件
        file_path = self.data_dir / f"{symbol}_{date_str}.json"
        
        stock_data = {
            "stock_info": stock,
            "data": data,
            "update_time": datetime.now().isoformat(),
            "data_count": len(data)
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(stock_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存{symbol}数据失败: {e}")
    
    async def _cleanup_old_data(self, days_to_keep: int = 7):
        """清理旧数据"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cleaned_count = 0
        
        for data_file in self.data_dir.glob("*.json"):
            try:
                file_date = datetime.fromtimestamp(data_file.stat().st_mtime)
                if file_date < cutoff_date:
                    data_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                logger.warning(f"删除旧数据文件失败: {e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了{cleaned_count}个旧数据文件")
        
        # 清理缓存
        try:
            async with tushare_service:
                await tushare_service.cleanup_old_cache(days_to_keep)
            await akshare_service.cleanup_old_cache(days_to_keep)
        except Exception as e:
            logger.warning(f"清理缓存失败: {e}")
    
    async def _generate_report(self):
        """生成抓取报告"""
        report = {
            "date": datetime.now().strftime('%Y-%m-%d'),
            "stats": self.stats.copy(),
            "success_rate": (self.stats["processed_stocks"] / self.stats["total_stocks"] * 100) if self.stats["total_stocks"] > 0 else 0
        }
        
        report_path = self.log_dir / f"crawl_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"生成抓取报告: {report_path}")
            logger.info(f"成功率: {report['success_rate']:.2f}%")
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            "is_running": self.is_running,
            "last_run_time": self.last_run_time.isoformat() if self.last_run_time else None,
            "stats": self.stats.copy()
        }
    
    async def get_stock_data(self, symbol: str, date: str = None) -> Optional[Dict]:
        """获取已抓取的股票数据"""
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        
        file_path = self.data_dir / f"{symbol}_{date}.json"
        
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"读取{symbol}数据失败: {e}")
        
        return None
    
    async def get_available_dates(self, symbol: str) -> List[str]:
        """获取可用的数据日期"""
        dates = []
        
        for file_path in self.data_dir.glob(f"{symbol}_*.json"):
            try:
                date_str = file_path.stem.split('_')[-1]
                if len(date_str) == 8 and date_str.isdigit():
                    dates.append(date_str)
            except Exception:
                continue
        
        return sorted(dates, reverse=True)

# 创建全局实例
data_crawler = DataCrawlerService()

# 导出主要接口
async def run_daily_data_crawl(days: int = 300):
    """运行每日数据抓取"""
    await data_crawler.run_daily_crawl(days)

async def get_crawler_status():
    """获取抓取器状态"""
    return data_crawler.get_status()

async def get_processed_stock_data(symbol: str, date: str = None):
    """获取已处理的股票数据"""
    return await data_crawler.get_stock_data(symbol, date)

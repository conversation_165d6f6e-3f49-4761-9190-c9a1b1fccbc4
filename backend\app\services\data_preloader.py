"""
数据预加载服务
基于pythonstock的缓存策略，实现热门数据预加载和智能缓存预热
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

import pandas as pd
from loguru import logger

from app.core.data_storage import (
    hot_data_manager,
    warm_data_manager,
    cold_data_manager,
    api_rate_limiter
)
from app.services.mock_market_service import mock_market_service


class DataPreloader:
    """数据预加载服务"""
    
    def __init__(self):
        # 热门股票列表 (根据中国市场特点配置)
        self.popular_symbols = [
            # 主要指数
            '000001.SH',  # 上证指数
            '399001.SZ',  # 深证成指
            '399006.SZ',  # 创业板指
            '000300.SH',  # 沪深300
            
            # 热门股票
            '600519.SH',  # 贵州茅台
            '000858.SZ',  # 五粮液
            '600036.SH',  # 招商银行
            '000001.SZ',  # 平安银行
            '000002.SZ',  # 万科A
            '600000.SH',  # 浦发银行
            '000063.SZ',  # 中兴通讯
            '002415.SZ',  # 海康威视
            '300059.SZ',  # 东方财富
            '000166.SZ',  # 申万宏源
            
            # 科技股
            '002230.SZ',  # 科大讯飞
            '300750.SZ',  # 宁德时代
            '002594.SZ',  # 比亚迪
            '000725.SZ',  # 京东方A
            '002241.SZ',  # 歌尔股份
        ]
        
        # 预加载配置
        self.preload_config = {
            'realtime_quotes': True,      # 预加载实时行情
            'daily_klines': True,         # 预加载日K线
            'minute_klines': False,       # 分钟K线按需加载
            'historical_days': 30,        # 预加载30天历史数据
            'batch_size': 5,              # 批处理大小
            'delay_between_batches': 2.0  # 批次间延迟(秒)
        }
        
        self.preload_stats = {
            'total_symbols': 0,
            'success_count': 0,
            'error_count': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def preload_all_data(self) -> Dict[str, Any]:
        """预加载所有热门数据"""
        logger.info("开始预加载热门股票数据...")
        
        self.preload_stats['start_time'] = datetime.now()
        self.preload_stats['total_symbols'] = len(self.popular_symbols)
        
        try:
            # 分批预加载，避免API调用过于频繁
            for i in range(0, len(self.popular_symbols), self.preload_config['batch_size']):
                batch_symbols = self.popular_symbols[i:i + self.preload_config['batch_size']]
                await self._preload_batch(batch_symbols)
                
                # 批次间延迟
                if i + self.preload_config['batch_size'] < len(self.popular_symbols):
                    await asyncio.sleep(self.preload_config['delay_between_batches'])
            
            self.preload_stats['end_time'] = datetime.now()
            duration = (self.preload_stats['end_time'] - self.preload_stats['start_time']).total_seconds()
            
            logger.info(f"数据预加载完成: 成功 {self.preload_stats['success_count']}/{self.preload_stats['total_symbols']}, "
                       f"耗时 {duration:.2f}s")
            
            return self.preload_stats
            
        except Exception as e:
            logger.error(f"数据预加载失败: {e}")
            self.preload_stats['end_time'] = datetime.now()
            return self.preload_stats
    
    async def _preload_batch(self, symbols: List[str]):
        """预加载一批股票数据"""
        tasks = []
        
        for symbol in symbols:
            task = asyncio.create_task(self._preload_symbol_data(symbol))
            tasks.append(task)
        
        # 并发执行，但限制并发数
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"预加载 {symbol} 失败: {result}")
                self.preload_stats['error_count'] += 1
            else:
                self.preload_stats['success_count'] += 1
    
    async def _preload_symbol_data(self, symbol: str):
        """预加载单个股票的数据"""
        try:
            # 1. 预加载实时行情
            if self.preload_config['realtime_quotes']:
                await self._preload_realtime_quote(symbol)
            
            # 2. 预加载日K线数据
            if self.preload_config['daily_klines']:
                await self._preload_daily_klines(symbol)
            
            # 小延迟避免API调用过于频繁
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"预加载 {symbol} 数据失败: {e}")
            raise
    
    async def _preload_realtime_quote(self, symbol: str):
        """预加载实时行情"""
        try:
            # 检查缓存是否已存在
            cached_data = await hot_data_manager.get_realtime_data(symbol)
            if cached_data:
                logger.debug(f"实时行情缓存已存在: {symbol}")
                return
            
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('mock')
            
            # 从API获取数据
            quote_data = await mock_market_service.get_realtime_quote(symbol)
            
            if quote_data:
                # 存储到热数据缓存
                await hot_data_manager.store_realtime_data(symbol, quote_data)
                logger.debug(f"已预加载实时行情: {symbol}")
            
        except Exception as e:
            logger.error(f"预加载实时行情失败 {symbol}: {e}")
            raise
    
    async def _preload_daily_klines(self, symbol: str):
        """预加载日K线数据"""
        try:
            # 计算需要预加载的日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=self.preload_config['historical_days'])
            
            date_str = end_date.strftime('%Y%m%d')
            
            # 检查温数据缓存是否已存在
            cached_data = await warm_data_manager.load_daily_data(symbol, date_str)
            if cached_data is not None and not cached_data.empty:
                logger.debug(f"日K线缓存已存在: {symbol}")
                return
            
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('mock')
            
            # 从API获取K线数据
            kline_data = await mock_market_service.get_kline_data(
                symbol=symbol,
                start_date=start_date.strftime('%Y%m%d'),
                end_date=date_str,
                period='daily'
            )
            
            if kline_data and hasattr(kline_data, 'empty') and not kline_data.empty:
                # 存储到温数据缓存
                await warm_data_manager.store_daily_data(symbol, date_str, kline_data)
                logger.debug(f"已预加载日K线数据: {symbol} ({len(kline_data)} 条)")
            
        except Exception as e:
            logger.error(f"预加载日K线数据失败 {symbol}: {e}")
            raise
    
    async def preload_market_overview(self) -> bool:
        """预加载市场概览数据"""
        try:
            logger.info("开始预加载市场概览数据...")
            
            # 预加载主要指数
            index_symbols = ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH']
            
            for symbol in index_symbols:
                await self._preload_realtime_quote(symbol)
                await asyncio.sleep(0.1)
            
            logger.info("市场概览数据预加载完成")
            return True
            
        except Exception as e:
            logger.error(f"预加载市场概览数据失败: {e}")
            return False
    
    async def preload_sector_data(self) -> bool:
        """预加载板块数据"""
        try:
            logger.info("开始预加载板块数据...")
            
            # 主要板块代表股票
            sector_symbols = {
                '银行': ['600036.SH', '000001.SZ', '600000.SH'],
                '科技': ['000063.SZ', '002415.SZ', '300059.SZ'],
                '消费': ['600519.SH', '000858.SZ'],
                '地产': ['000002.SZ'],
                '汽车': ['002594.SZ']
            }
            
            for sector_name, symbols in sector_symbols.items():
                for symbol in symbols:
                    await self._preload_realtime_quote(symbol)
                    await asyncio.sleep(0.1)
                
                logger.debug(f"已预加载 {sector_name} 板块数据")
            
            logger.info("板块数据预加载完成")
            return True
            
        except Exception as e:
            logger.error(f"预加载板块数据失败: {e}")
            return False
    
    async def get_preload_status(self) -> Dict[str, Any]:
        """获取预加载状态"""
        # 检查缓存命中率
        cache_hits = 0
        cache_misses = 0
        
        for symbol in self.popular_symbols[:10]:  # 检查前10个股票
            cached_data = await hot_data_manager.get_realtime_data(symbol)
            if cached_data:
                cache_hits += 1
            else:
                cache_misses += 1
        
        cache_hit_rate = cache_hits / (cache_hits + cache_misses) if (cache_hits + cache_misses) > 0 else 0
        
        return {
            'preload_stats': self.preload_stats,
            'cache_hit_rate': cache_hit_rate,
            'popular_symbols_count': len(self.popular_symbols),
            'api_rate_limits': {
                api_name: api_rate_limiter.get_remaining_calls(api_name)
                for api_name in ['mock', 'tushare', 'akshare']
            }
        }
    
    async def refresh_expired_cache(self) -> int:
        """刷新过期缓存"""
        refreshed_count = 0
        
        try:
            logger.info("开始刷新过期缓存...")
            
            for symbol in self.popular_symbols:
                # 检查实时数据是否过期
                cached_data = await hot_data_manager.get_realtime_data(symbol)
                if not cached_data:
                    # 缓存已过期，重新加载
                    await self._preload_realtime_quote(symbol)
                    refreshed_count += 1
                    await asyncio.sleep(0.1)
            
            logger.info(f"缓存刷新完成，刷新了 {refreshed_count} 个股票的数据")
            return refreshed_count
            
        except Exception as e:
            logger.error(f"刷新缓存失败: {e}")
            return refreshed_count


# 全局实例
data_preloader = DataPreloader()


async def start_preload_service():
    """启动预加载服务"""
    try:
        # 预加载市场概览
        await data_preloader.preload_market_overview()
        
        # 预加载板块数据
        await data_preloader.preload_sector_data()
        
        # 预加载热门股票数据
        await data_preloader.preload_all_data()
        
        logger.info("预加载服务启动完成")
        
    except Exception as e:
        logger.error(f"预加载服务启动失败: {e}")


async def schedule_cache_refresh():
    """定时刷新缓存"""
    while True:
        try:
            # 每5分钟刷新一次过期缓存
            await asyncio.sleep(300)
            await data_preloader.refresh_expired_cache()
            
        except Exception as e:
            logger.error(f"定时缓存刷新失败: {e}")
            await asyncio.sleep(60)  # 出错后等待1分钟再重试


# 导出主要组件
__all__ = [
    'DataPreloader',
    'data_preloader',
    'start_preload_service',
    'schedule_cache_refresh'
]

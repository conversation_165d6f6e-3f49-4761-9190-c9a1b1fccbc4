"""
数据处理服务
对抓取的股票数据进行处理、计算和分析
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import json
import math

logger = logging.getLogger(__name__)

class DataProcessorService:
    """数据处理服务"""
    
    def __init__(self):
        self.data_dir = Path("data/processed")
        self.analysis_dir = Path("data/analysis")
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
    
    async def process_stock_data(self, symbol: str, data: List[Dict]) -> Dict:
        """处理单只股票数据"""
        if not data:
            return {}
        
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.sort_values('timestamp')
            
            # 基础统计
            basic_stats = self._calculate_basic_stats(df)
            
            # 技术指标
            technical_indicators = self._calculate_technical_indicators(df)
            
            # 风险指标
            risk_metrics = self._calculate_risk_metrics(df)
            
            # 趋势分析
            trend_analysis = self._analyze_trend(df)
            
            result = {
                "symbol": symbol,
                "data_count": len(data),
                "date_range": {
                    "start": df['timestamp'].min().isoformat(),
                    "end": df['timestamp'].max().isoformat()
                },
                "basic_stats": basic_stats,
                "technical_indicators": technical_indicators,
                "risk_metrics": risk_metrics,
                "trend_analysis": trend_analysis,
                "processed_time": datetime.now().isoformat()
            }
            
            # 保存分析结果
            await self._save_analysis_result(symbol, result)
            
            return result
            
        except Exception as e:
            logger.error(f"处理{symbol}数据失败: {e}")
            return {}
    
    def _calculate_basic_stats(self, df: pd.DataFrame) -> Dict:
        """计算基础统计指标"""
        try:
            current_price = df['close'].iloc[-1]
            price_change = df['close'].iloc[-1] - df['close'].iloc[0]
            price_change_pct = (price_change / df['close'].iloc[0]) * 100
            
            return {
                "current_price": float(current_price),
                "price_change": float(price_change),
                "price_change_pct": float(price_change_pct),
                "highest_price": float(df['high'].max()),
                "lowest_price": float(df['low'].min()),
                "average_price": float(df['close'].mean()),
                "average_volume": float(df['volume'].mean()),
                "total_volume": float(df['volume'].sum()),
                "volatility": float(df['close'].pct_change().std() * 100)
            }
        except Exception as e:
            logger.error(f"计算基础统计失败: {e}")
            return {}
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        try:
            indicators = {}
            
            # 移动平均线
            indicators['ma5'] = float(df['close'].rolling(5).mean().iloc[-1])
            indicators['ma10'] = float(df['close'].rolling(10).mean().iloc[-1])
            indicators['ma20'] = float(df['close'].rolling(20).mean().iloc[-1])
            indicators['ma60'] = float(df['close'].rolling(60).mean().iloc[-1])
            
            # RSI
            indicators['rsi'] = self._calculate_rsi(df['close'])
            
            # MACD
            macd_data = self._calculate_macd(df['close'])
            indicators.update(macd_data)
            
            # 布林带
            bollinger_data = self._calculate_bollinger_bands(df['close'])
            indicators.update(bollinger_data)
            
            # KDJ
            kdj_data = self._calculate_kdj(df)
            indicators.update(kdj_data)
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return float(rsi.iloc[-1])
        except:
            return 50.0
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
        """计算MACD指标"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line
            
            return {
                'macd': float(macd_line.iloc[-1]),
                'macd_signal': float(signal_line.iloc[-1]),
                'macd_histogram': float(histogram.iloc[-1])
            }
        except:
            return {'macd': 0.0, 'macd_signal': 0.0, 'macd_histogram': 0.0}
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict:
        """计算布林带"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return {
                'bb_upper': float(upper_band.iloc[-1]),
                'bb_middle': float(sma.iloc[-1]),
                'bb_lower': float(lower_band.iloc[-1]),
                'bb_width': float((upper_band.iloc[-1] - lower_band.iloc[-1]) / sma.iloc[-1] * 100)
            }
        except:
            return {'bb_upper': 0.0, 'bb_middle': 0.0, 'bb_lower': 0.0, 'bb_width': 0.0}
    
    def _calculate_kdj(self, df: pd.DataFrame, period: int = 9) -> Dict:
        """计算KDJ指标"""
        try:
            low_min = df['low'].rolling(window=period).min()
            high_max = df['high'].rolling(window=period).max()
            
            rsv = (df['close'] - low_min) / (high_max - low_min) * 100
            k = rsv.ewm(com=2).mean()
            d = k.ewm(com=2).mean()
            j = 3 * k - 2 * d
            
            return {
                'kdj_k': float(k.iloc[-1]),
                'kdj_d': float(d.iloc[-1]),
                'kdj_j': float(j.iloc[-1])
            }
        except:
            return {'kdj_k': 50.0, 'kdj_d': 50.0, 'kdj_j': 50.0}
    
    def _calculate_risk_metrics(self, df: pd.DataFrame) -> Dict:
        """计算风险指标"""
        try:
            returns = df['close'].pct_change().dropna()
            
            # 年化收益率
            annual_return = (df['close'].iloc[-1] / df['close'].iloc[0]) ** (252 / len(df)) - 1
            
            # 年化波动率
            annual_volatility = returns.std() * math.sqrt(252)
            
            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # VaR (95%置信度)
            var_95 = returns.quantile(0.05)
            
            return {
                "annual_return": float(annual_return * 100),
                "annual_volatility": float(annual_volatility * 100),
                "sharpe_ratio": float(sharpe_ratio),
                "max_drawdown": float(max_drawdown * 100),
                "var_95": float(var_95 * 100),
                "beta": self._calculate_beta(returns)
            }
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}
    
    def _calculate_beta(self, returns: pd.Series) -> float:
        """计算Beta值（相对于市场）"""
        try:
            # 这里简化处理，实际应该使用市场指数数据
            # 假设市场收益率的标准差为0.02
            market_volatility = 0.02
            stock_volatility = returns.std()
            correlation = 0.7  # 假设相关系数
            
            beta = correlation * (stock_volatility / market_volatility)
            return float(beta)
        except:
            return 1.0
    
    def _analyze_trend(self, df: pd.DataFrame) -> Dict:
        """趋势分析"""
        try:
            # 短期趋势（5日）
            short_trend = self._get_trend_direction(df['close'].tail(5))
            
            # 中期趋势（20日）
            medium_trend = self._get_trend_direction(df['close'].tail(20))
            
            # 长期趋势（60日）
            long_trend = self._get_trend_direction(df['close'].tail(60))
            
            # 支撑位和阻力位
            support_resistance = self._calculate_support_resistance(df)
            
            return {
                "short_term_trend": short_trend,
                "medium_term_trend": medium_trend,
                "long_term_trend": long_trend,
                "support_level": support_resistance["support"],
                "resistance_level": support_resistance["resistance"],
                "trend_strength": self._calculate_trend_strength(df)
            }
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {}
    
    def _get_trend_direction(self, prices: pd.Series) -> str:
        """获取趋势方向"""
        try:
            if len(prices) < 2:
                return "neutral"
            
            start_price = prices.iloc[0]
            end_price = prices.iloc[-1]
            change_pct = (end_price - start_price) / start_price * 100
            
            if change_pct > 2:
                return "strong_up"
            elif change_pct > 0.5:
                return "up"
            elif change_pct < -2:
                return "strong_down"
            elif change_pct < -0.5:
                return "down"
            else:
                return "neutral"
        except:
            return "neutral"
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """计算支撑位和阻力位"""
        try:
            # 简化计算：使用最近20天的最高价和最低价
            recent_data = df.tail(20)
            support = float(recent_data['low'].min())
            resistance = float(recent_data['high'].max())
            
            return {
                "support": support,
                "resistance": resistance
            }
        except:
            return {"support": 0.0, "resistance": 0.0}
    
    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """计算趋势强度"""
        try:
            # 使用ADX的简化版本
            returns = df['close'].pct_change().abs()
            trend_strength = returns.rolling(14).mean().iloc[-1] * 100
            return float(min(trend_strength, 100))
        except:
            return 50.0
    
    async def _save_analysis_result(self, symbol: str, result: Dict):
        """保存分析结果"""
        date_str = datetime.now().strftime('%Y%m%d')
        file_path = self.analysis_dir / f"{symbol}_analysis_{date_str}.json"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存{symbol}分析结果失败: {e}")
    
    async def batch_process_stocks(self, symbols: List[str]) -> Dict[str, Dict]:
        """批量处理股票数据"""
        results = {}
        
        for symbol in symbols:
            try:
                # 读取股票数据
                data_file = self.data_dir / f"{symbol}_{datetime.now().strftime('%Y%m%d')}.json"
                
                if data_file.exists():
                    with open(data_file, 'r', encoding='utf-8') as f:
                        stock_data = json.load(f)
                    
                    # 处理数据
                    result = await self.process_stock_data(symbol, stock_data.get('data', []))
                    results[symbol] = result
                else:
                    logger.warning(f"未找到{symbol}的数据文件")
                    
            except Exception as e:
                logger.error(f"处理{symbol}失败: {e}")
                results[symbol] = {}
        
        return results
    
    async def get_analysis_result(self, symbol: str, date: str = None) -> Optional[Dict]:
        """获取分析结果"""
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        
        file_path = self.analysis_dir / f"{symbol}_analysis_{date}.json"
        
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"读取{symbol}分析结果失败: {e}")
        
        return None

# 创建全局实例
data_processor = DataProcessorService()

# 导出主要接口
async def process_stock_analysis(symbol: str, data: List[Dict]) -> Dict:
    """处理股票分析"""
    return await data_processor.process_stock_data(symbol, data)

async def batch_analyze_stocks(symbols: List[str]) -> Dict[str, Dict]:
    """批量分析股票"""
    return await data_processor.batch_process_stocks(symbols)

async def get_stock_analysis(symbol: str, date: str = None) -> Optional[Dict]:
    """获取股票分析结果"""
    return await data_processor.get_analysis_result(symbol, date)

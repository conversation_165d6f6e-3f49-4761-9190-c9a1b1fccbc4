"""
增强的用户认证服务
专为生产环境设计，包含完整的安全特性
"""
import secrets
import hashlib
import hmac
from jose import jwt
import bcrypt
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from email_validator import validate_email, EmailNotValidError
import asyncio
from redis.asyncio import Redis
from dataclasses import dataclass

from app.core.config import get_settings
from app.core.logger import logger

settings = get_settings()

# JWT配置
JWT_SECRET_KEY = getattr(settings, 'JWT_SECRET_KEY', 'your-secret-key-here-change-in-production')
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# 安全配置
MAX_LOGIN_ATTEMPTS = 5
LOGIN_LOCKOUT_DURATION = 15  # 分钟
PASSWORD_MIN_LENGTH = 8
SESSION_TIMEOUT_HOURS = 24


@dataclass
class UserProfile:
    """用户配置文件"""
    id: int
    username: str
    email: str
    full_name: str
    roles: List[str]
    permissions: List[str]
    is_active: bool
    is_verified: bool
    is_superuser: bool
    trading_level: str
    risk_tolerance: str
    created_at: str
    last_login: Optional[str] = None


class EnhancedAuthService:
    """增强的用户认证服务"""
    
    def __init__(self):
        self.redis_client = None
        self.users_db = {}  # 生产环境中应使用真实数据库
        self.roles_db = {}
        self.permissions_db = {}
        self.sessions_db = {}
        self.login_attempts_db = {}
        
        # 初始化默认数据
        self._init_default_data()
        
        # 安全配置
        self.security_config = {
            "password_policy": {
                "min_length": PASSWORD_MIN_LENGTH,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special_chars": True,
                "max_age_days": 90,
                "history_count": 5
            },
            "session_policy": {
                "max_concurrent_sessions": 3,
                "idle_timeout_minutes": 30,
                "absolute_timeout_hours": SESSION_TIMEOUT_HOURS,
                "require_fresh_login_for_sensitive": True
            },
            "rate_limiting": {
                "login_attempts_per_ip": MAX_LOGIN_ATTEMPTS,
                "login_window_minutes": LOGIN_LOCKOUT_DURATION,
                "api_requests_per_minute": 100,
                "password_reset_per_hour": 3
            }
        }
    
    async def init_redis(self):
        """初始化Redis连接"""
        try:
            redis_url = getattr(settings, 'REDIS_URL', 'redis://localhost:6379')
            self.redis_client = Redis.from_url(redis_url)
            logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存存储: {e}")
            self.redis_client = None
    
    def _init_default_data(self):
        """初始化默认数据"""
        # 权限定义
        self.permissions_db = {
            # 用户管理权限
            "user.create": {"name": "创建用户", "resource": "user", "action": "create"},
            "user.read": {"name": "查看用户", "resource": "user", "action": "read"},
            "user.update": {"name": "更新用户", "resource": "user", "action": "update"},
            "user.delete": {"name": "删除用户", "resource": "user", "action": "delete"},
            
            # 交易权限
            "trading.create": {"name": "创建交易", "resource": "trading", "action": "create"},
            "trading.read": {"name": "查看交易", "resource": "trading", "action": "read"},
            "trading.update": {"name": "修改交易", "resource": "trading", "action": "update"},
            "trading.delete": {"name": "删除交易", "resource": "trading", "action": "delete"},
            "trading.execute": {"name": "执行交易", "resource": "trading", "action": "execute"},
            
            # 数据权限
            "data.read": {"name": "查看数据", "resource": "data", "action": "read"},
            "data.export": {"name": "导出数据", "resource": "data", "action": "export"},
            
            # 系统权限
            "system.admin": {"name": "系统管理", "resource": "system", "action": "admin"},
            "system.monitor": {"name": "系统监控", "resource": "system", "action": "monitor"},
            "system.config": {"name": "系统配置", "resource": "system", "action": "config"},
            
            # 账户权限
            "account.read": {"name": "查看账户", "resource": "account", "action": "read"},
            "account.update": {"name": "更新账户", "resource": "account", "action": "update"},
            "account.deposit": {"name": "账户充值", "resource": "account", "action": "deposit"},
            "account.withdraw": {"name": "账户提现", "resource": "account", "action": "withdraw"}
        }
        
        # 角色定义
        self.roles_db = {
            "super_admin": {
                "name": "超级管理员",
                "description": "拥有所有权限的系统管理员",
                "permissions": list(self.permissions_db.keys()),
                "is_system": True
            },
            "admin": {
                "name": "管理员",
                "description": "系统管理员，除超级权限外的所有权限",
                "permissions": [p for p in self.permissions_db.keys() if not p.startswith("system.admin")],
                "is_system": True
            },
            "senior_trader": {
                "name": "高级交易员",
                "description": "有经验的交易员，可执行高级交易策略",
                "permissions": [
                    "trading.create", "trading.read", "trading.update", "trading.delete", "trading.execute",
                    "data.read", "data.export", "account.read", "account.update", "account.deposit", "account.withdraw"
                ],
                "is_system": False
            },
            "trader": {
                "name": "交易员",
                "description": "普通交易员，可进行基本交易操作",
                "permissions": [
                    "trading.create", "trading.read", "trading.update", "trading.execute",
                    "data.read", "account.read", "account.update"
                ],
                "is_system": False
            },
            "analyst": {
                "name": "分析师",
                "description": "数据分析师，专注于市场分析",
                "permissions": [
                    "data.read", "data.export", "account.read", "account.update"
                ],
                "is_system": False
            },
            "viewer": {
                "name": "观察者",
                "description": "只读用户，可查看基本信息",
                "permissions": [
                    "data.read", "account.read"
                ],
                "is_system": False
            }
        }
        
        # 默认用户
        self.users_db = {
            1: UserProfile(
                id=1,
                username="admin",
                email="<EMAIL>",
                full_name="系统管理员",
                roles=["super_admin"],
                permissions=self.roles_db["super_admin"]["permissions"],
                is_active=True,
                is_verified=True,
                is_superuser=True,
                trading_level="professional",
                risk_tolerance="moderate",
                created_at=datetime.utcnow().isoformat()
            )
        }
        
        # 为默认用户设置密码
        self._set_user_password(1, "Admin@123456")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def _set_user_password(self, user_id: int, password: str):
        """设置用户密码"""
        hashed = self._hash_password(password)
        # 在实际应用中，这应该存储在数据库中
        if not hasattr(self, 'passwords_db'):
            self.passwords_db = {}
        self.passwords_db[user_id] = {
            "hashed_password": hashed,
            "created_at": datetime.utcnow().isoformat(),
            "must_change": False
        }
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """验证密码强度"""
        policy = self.security_config["password_policy"]
        errors = []
        score = 0
        
        # 长度检查
        if len(password) < policy["min_length"]:
            errors.append(f"密码长度至少{policy['min_length']}位")
        else:
            score += 1
        
        # 大写字母
        if policy["require_uppercase"] and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        elif re.search(r'[A-Z]', password):
            score += 1
        
        # 小写字母
        if policy["require_lowercase"] and not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        elif re.search(r'[a-z]', password):
            score += 1
        
        # 数字
        if policy["require_numbers"] and not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        elif re.search(r'\d', password):
            score += 1
        
        # 特殊字符
        if policy["require_special_chars"] and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含特殊字符")
        elif re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        
        # 常见密码检查
        common_passwords = [
            "123456", "password", "admin", "qwerty", "abc123", 
            "password123", "admin123", "123456789", "welcome"
        ]
        if password.lower() in common_passwords:
            errors.append("不能使用常见密码")
            score = 0
        
        # 连续字符检查
        if re.search(r'(.)\1{2,}', password):
            errors.append("密码不能包含连续重复字符")
        
        # 键盘序列检查
        keyboard_sequences = ["qwerty", "asdf", "zxcv", "123456", "abcdef"]
        for seq in keyboard_sequences:
            if seq in password.lower():
                errors.append("密码不能包含键盘序列")
                break
        
        strength_levels = ["很弱", "弱", "一般", "强", "很强"]
        strength = strength_levels[min(score, 4)]
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "score": score,
            "strength": strength,
            "recommendations": self._get_password_recommendations(password)
        }
    
    def _get_password_recommendations(self, password: str) -> List[str]:
        """获取密码改进建议"""
        recommendations = []
        
        if len(password) < 12:
            recommendations.append("建议使用12位以上的密码")
        
        if not re.search(r'[A-Z]', password):
            recommendations.append("添加大写字母")
        
        if not re.search(r'[a-z]', password):
            recommendations.append("添加小写字母")
        
        if not re.search(r'\d', password):
            recommendations.append("添加数字")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            recommendations.append("添加特殊字符")
        
        if len(set(password)) < len(password) * 0.7:
            recommendations.append("增加字符多样性")
        
        return recommendations
    
    async def register_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """用户注册"""
        try:
            # 验证必填字段
            required_fields = ["username", "email", "password", "full_name"]
            for field in required_fields:
                if not user_data.get(field):
                    return {
                        "success": False,
                        "message": f"缺少必填字段: {field}"
                    }
            
            username = user_data["username"].strip()
            email = user_data["email"].strip().lower()
            password = user_data["password"]
            full_name = user_data["full_name"].strip()
            
            # 验证用户名格式
            if not re.match(r'^[a-zA-Z0-9_]{3,30}$', username):
                return {
                    "success": False,
                    "message": "用户名只能包含字母、数字和下划线，长度3-30位"
                }
            
            # 检查用户名是否已存在
            for user in self.users_db.values():
                if user.username == username:
                    return {
                        "success": False,
                        "message": "用户名已存在"
                    }
            
            # 验证邮箱格式
            try:
                valid = validate_email(email)
                email = valid.email
            except EmailNotValidError as e:
                return {
                    "success": False,
                    "message": f"邮箱格式错误: {str(e)}"
                }
            
            # 检查邮箱是否已存在
            for user in self.users_db.values():
                if user.email == email:
                    return {
                        "success": False,
                        "message": "邮箱已被注册"
                    }
            
            # 验证密码强度
            password_check = self.validate_password_strength(password)
            if not password_check["valid"]:
                return {
                    "success": False,
                    "message": "密码强度不足",
                    "errors": password_check["errors"],
                    "recommendations": password_check["recommendations"]
                }
            
            # 创建用户
            user_id = max(self.users_db.keys()) + 1 if self.users_db else 1
            
            new_user = UserProfile(
                id=user_id,
                username=username,
                email=email,
                full_name=full_name,
                roles=["viewer"],  # 默认角色
                permissions=self.roles_db["viewer"]["permissions"],
                is_active=True,
                is_verified=False,  # 需要邮箱验证
                is_superuser=False,
                trading_level=user_data.get("trading_level", "beginner"),
                risk_tolerance=user_data.get("risk_tolerance", "conservative"),
                created_at=datetime.utcnow().isoformat()
            )
            
            self.users_db[user_id] = new_user
            self._set_user_password(user_id, password)
            
            # 生成邮箱验证令牌
            verification_token = self._generate_verification_token(user_id, email)
            
            # 记录注册事件
            await self._log_security_event("user_registered", {
                "user_id": user_id,
                "username": username,
                "email": email,
                "ip_address": user_data.get("ip_address"),
                "user_agent": user_data.get("user_agent")
            })
            
            logger.info(f"用户注册成功: {username} ({email})")
            
            return {
                "success": True,
                "message": "注册成功，请查收邮箱验证邮件",
                "user_id": user_id,
                "verification_token": verification_token,
                "user_info": {
                    "id": user_id,
                    "username": username,
                    "email": email,
                    "full_name": full_name,
                    "roles": new_user.roles,
                    "trading_level": new_user.trading_level
                }
            }
            
        except Exception as e:
            logger.error(f"用户注册失败: {e}")
            return {
                "success": False,
                "message": f"注册失败: {str(e)}"
            }
    
    async def authenticate_user(self, login_data: Dict[str, Any], request_info: Dict[str, Any]) -> Dict[str, Any]:
        """用户认证"""
        try:
            username_or_email = login_data.get("username") or login_data.get("email")
            password = login_data.get("password")
            ip_address = request_info.get("ip_address", "unknown")
            user_agent = request_info.get("user_agent", "unknown")
            
            if not username_or_email or not password:
                return {
                    "success": False,
                    "message": "用户名/邮箱和密码不能为空"
                }
            
            # 检查登录频率限制
            if await self._is_rate_limited(ip_address, "login"):
                return {
                    "success": False,
                    "message": "登录尝试过于频繁，请稍后再试"
                }
            
            # 查找用户
            user = None
            for u in self.users_db.values():
                if u.username == username_or_email or u.email == username_or_email:
                    user = u
                    break
            
            if not user:
                await self._record_login_attempt(username_or_email, ip_address, False, "user_not_found")
                return {
                    "success": False,
                    "message": "用户名或密码错误"
                }
            
            # 检查用户状态
            if not user.is_active:
                await self._record_login_attempt(username_or_email, ip_address, False, "account_disabled")
                return {
                    "success": False,
                    "message": "账户已被禁用"
                }
            
            # 验证密码
            user_password = self.passwords_db.get(user.id)
            if not user_password or not self._verify_password(password, user_password["hashed_password"]):
                await self._record_login_attempt(username_or_email, ip_address, False, "invalid_password")
                return {
                    "success": False,
                    "message": "用户名或密码错误"
                }
            
            # 检查是否需要强制修改密码
            if user_password.get("must_change"):
                return {
                    "success": False,
                    "message": "需要修改密码",
                    "require_password_change": True,
                    "user_id": user.id
                }
            
            # 登录成功
            await self._record_login_attempt(username_or_email, ip_address, True, None)
            
            # 生成令牌
            access_token = self._generate_access_token(user)
            refresh_token = self._generate_refresh_token(user)
            
            # 创建会话
            session_id = await self._create_session(user, access_token, refresh_token, request_info)
            
            # 更新最后登录时间
            user.last_login = datetime.utcnow().isoformat()
            
            # 记录登录事件
            await self._log_security_event("user_login", {
                "user_id": user.id,
                "username": user.username,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "session_id": session_id
            })
            
            logger.info(f"用户登录成功: {user.username} from {ip_address}")
            
            return {
                "success": True,
                "message": "登录成功",
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "session_id": session_id,
                "user_info": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "roles": user.roles,
                    "permissions": user.permissions,
                    "is_verified": user.is_verified,
                    "trading_level": user.trading_level,
                    "risk_tolerance": user.risk_tolerance
                }
            }
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return {
                "success": False,
                "message": f"认证失败: {str(e)}"
            }
    
    def _generate_access_token(self, user: UserProfile) -> str:
        """生成访问令牌"""
        payload = {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "roles": user.roles,
            "permissions": user.permissions,
            "exp": datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES),
            "iat": datetime.utcnow(),
            "type": "access",
            "jti": secrets.token_urlsafe(16)  # JWT ID
        }
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    def _generate_refresh_token(self, user: UserProfile) -> str:
        """生成刷新令牌"""
        payload = {
            "user_id": user.id,
            "username": user.username,
            "exp": datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS),
            "iat": datetime.utcnow(),
            "type": "refresh",
            "jti": secrets.token_urlsafe(16)
        }
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    def _generate_verification_token(self, user_id: int, email: str) -> str:
        """生成邮箱验证令牌"""
        payload = {
            "user_id": user_id,
            "email": email,
            "exp": datetime.utcnow() + timedelta(hours=24),
            "type": "email_verification",
            "jti": secrets.token_urlsafe(16)
        }
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    async def _create_session(self, user: UserProfile, access_token: str, refresh_token: str, request_info: Dict[str, Any]) -> str:
        """创建用户会话"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            "session_id": session_id,
            "user_id": user.id,
            "access_token": access_token,
            "refresh_token": refresh_token,
            "ip_address": request_info.get("ip_address"),
            "user_agent": request_info.get("user_agent"),
            "device_info": request_info.get("device_info"),
            "location": request_info.get("location"),
            "is_active": True,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)).isoformat(),
            "last_activity": datetime.utcnow().isoformat()
        }
        
        self.sessions_db[session_id] = session_data
        
        # 检查并清理过期会话
        await self._cleanup_expired_sessions(user.id)
        
        return session_id
    
    async def _record_login_attempt(self, username: str, ip_address: str, success: bool, failure_reason: Optional[str]):
        """记录登录尝试"""
        attempt_key = f"login_attempts:{ip_address}"
        
        attempt_data = {
            "username": username,
            "ip_address": ip_address,
            "success": success,
            "failure_reason": failure_reason,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if self.redis_client:
            try:
                # 使用Redis存储
                await self.redis_client.lpush(attempt_key, str(attempt_data))
                await self.redis_client.expire(attempt_key, 3600)  # 1小时过期
            except Exception as e:
                logger.warning(f"Redis记录登录尝试失败: {e}")
        
        # 内存备份
        if ip_address not in self.login_attempts_db:
            self.login_attempts_db[ip_address] = []
        
        self.login_attempts_db[ip_address].append(attempt_data)
        
        # 清理旧记录
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        self.login_attempts_db[ip_address] = [
            attempt for attempt in self.login_attempts_db[ip_address]
            if datetime.fromisoformat(attempt["timestamp"]) > cutoff_time
        ]
    
    async def _is_rate_limited(self, identifier: str, action: str) -> bool:
        """检查频率限制"""
        rate_config = self.security_config["rate_limiting"]
        
        if action == "login":
            limit = rate_config["login_attempts_per_ip"]
            window = rate_config["login_window_minutes"] * 60
        else:
            limit = rate_config["api_requests_per_minute"]
            window = 60
        
        key = f"rate_limit:{action}:{identifier}"
        
        if self.redis_client:
            try:
                current = await self.redis_client.incr(key)
                if current == 1:
                    await self.redis_client.expire(key, window)
                return current > limit
            except Exception as e:
                logger.warning(f"Redis频率限制检查失败: {e}")
        
        # 内存备份实现
        if identifier not in self.login_attempts_db:
            return False
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=rate_config["login_window_minutes"])
        recent_attempts = [
            attempt for attempt in self.login_attempts_db[identifier]
            if not attempt["success"] and datetime.fromisoformat(attempt["timestamp"]) > cutoff_time
        ]
        
        return len(recent_attempts) >= limit
    
    async def _log_security_event(self, event_type: str, event_data: Dict[str, Any]):
        """记录安全事件"""
        event = {
            "event_type": event_type,
            "timestamp": datetime.utcnow().isoformat(),
            "data": event_data
        }
        
        # 在生产环境中，这应该写入安全日志系统
        logger.info(f"安全事件: {event_type} - {event_data}")
    
    async def _cleanup_expired_sessions(self, user_id: int):
        """清理过期会话"""
        current_time = datetime.utcnow()
        expired_sessions = []
        
        for session_id, session in self.sessions_db.items():
            if session["user_id"] == user_id:
                expires_at = datetime.fromisoformat(session["expires_at"])
                if expires_at < current_time:
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions_db[session_id]
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return {
                "valid": True,
                "payload": payload
            }
        except jwt.ExpiredSignatureError:
            return {
                "valid": False,
                "error": "令牌已过期"
            }
        except jwt.InvalidTokenError:
            return {
                "valid": False,
                "error": "无效令牌"
            }
    
    def check_permission(self, user_id: int, required_permission: str) -> bool:
        """检查用户权限"""
        user = self.users_db.get(user_id)
        if not user:
            return False
        
        if user.is_superuser:
            return True
        
        return required_permission in user.permissions
    
    async def logout_user(self, session_id: str) -> Dict[str, Any]:
        """用户登出"""
        if session_id in self.sessions_db:
            session = self.sessions_db[session_id]
            session["is_active"] = False
            session["logout_time"] = datetime.utcnow().isoformat()
            
            # 记录登出事件
            await self._log_security_event("user_logout", {
                "user_id": session["user_id"],
                "session_id": session_id,
                "ip_address": session.get("ip_address")
            })
            
            logger.info(f"用户登出: session_id={session_id}")
            
            return {
                "success": True,
                "message": "登出成功"
            }
        else:
            return {
                "success": False,
                "message": "会话不存在"
            }
    
    def get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        user = self.users_db.get(user_id)
        if not user:
            return None
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "roles": user.roles,
            "permissions": user.permissions,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "trading_level": user.trading_level,
            "risk_tolerance": user.risk_tolerance,
            "created_at": user.created_at,
            "last_login": user.last_login
        }
    
    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        total_users = len(self.users_db)
        active_sessions = len([s for s in self.sessions_db.values() if s["is_active"]])
        
        return {
            "total_users": total_users,
            "active_sessions": active_sessions,
            "security_config": self.security_config,
            "available_roles": list(self.roles_db.keys()),
            "available_permissions": list(self.permissions_db.keys()),
            "redis_connected": self.redis_client is not None
        }


# 全局实例
enhanced_auth_service = EnhancedAuthService()


async def get_enhanced_auth_service() -> EnhancedAuthService:
    """获取增强认证服务实例"""
    if not enhanced_auth_service.redis_client:
        await enhanced_auth_service.init_redis()
    return enhanced_auth_service

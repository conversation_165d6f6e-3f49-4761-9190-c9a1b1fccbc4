"""
增强的市场数据服务
集成多个数据源，提供统一的数据接口
支持AkShare、Tushare和模拟数据的无缝切换
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np

# 数据源导入
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    ak = None

try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False
    ts = None

from app.core.config import get_settings
from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger(__name__)

settings = get_settings()


class EnhancedMarketDataService:
    """增强的市场数据服务"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        self.data_sources = []
        self.current_source = "mock"
        self._init_data_sources()
    
    def _init_data_sources(self):
        """初始化数据源"""
        # 检查AkShare
        if AKSHARE_AVAILABLE:
            self.data_sources.append("akshare")
            logger.info("✅ AkShare数据源可用")
        
        # 检查Tushare
        if TUSHARE_AVAILABLE:
            tushare_token = getattr(settings, 'TUSHARE_TOKEN', None)
            if tushare_token:
                try:
                    ts.set_token(tushare_token)
                    self.pro = ts.pro_api()
                    self.data_sources.append("tushare")
                    logger.info("✅ Tushare数据源可用")
                except Exception as e:
                    logger.warning(f"Tushare初始化失败: {e}")
        
        # 模拟数据源始终可用
        self.data_sources.append("mock")
        
        # 设置默认数据源
        if "akshare" in self.data_sources:
            self.current_source = "akshare"
        elif "tushare" in self.data_sources:
            self.current_source = "tushare"
        else:
            self.current_source = "mock"
        
        logger.info(f"📊 当前数据源: {self.current_source}")
        logger.info(f"📋 可用数据源: {', '.join(self.data_sources)}")
    
    async def get_stock_list(self, market: str = "A股") -> List[Dict[str, Any]]:
        """获取股票列表"""
        cache_key = f"stock_list_{market}"
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "akshare":
                data = await self._get_stock_list_akshare(market)
            elif self.current_source == "tushare":
                data = await self._get_stock_list_tushare(market)
            else:
                data = self._get_stock_list_mock(market)
            
            # 缓存数据
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            # 降级到模拟数据
            return self._get_stock_list_mock(market)
    
    async def get_realtime_data(self, symbols: List[str]) -> Dict[str, Any]:
        """获取实时行情数据"""
        cache_key = f"realtime_{','.join(symbols)}"
        
        # 实时数据缓存时间较短
        if self._is_cache_valid(cache_key, timeout=30):
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "akshare":
                data = await self._get_realtime_data_akshare(symbols)
            elif self.current_source == "tushare":
                data = await self._get_realtime_data_tushare(symbols)
            else:
                data = self._get_realtime_data_mock(symbols)
            
            self._cache_data(cache_key, data, timeout=30)
            return data
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return self._get_realtime_data_mock(symbols)
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: str, 
        end_date: str,
        period: str = "daily"
    ) -> Dict[str, Any]:
        """获取历史数据"""
        cache_key = f"historical_{symbol}_{start_date}_{end_date}_{period}"
        
        if self._is_cache_valid(cache_key, timeout=3600):  # 1小时缓存
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "akshare":
                data = await self._get_historical_data_akshare(symbol, start_date, end_date, period)
            elif self.current_source == "tushare":
                data = await self._get_historical_data_tushare(symbol, start_date, end_date, period)
            else:
                data = self._get_historical_data_mock(symbol, start_date, end_date, period)
            
            self._cache_data(cache_key, data, timeout=3600)
            return data
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return self._get_historical_data_mock(symbol, start_date, end_date, period)
    
    async def _get_stock_list_akshare(self, market: str) -> List[Dict[str, Any]]:
        """使用AkShare获取股票列表"""
        if market == "A股":
            df = ak.stock_info_a_code_name()
            return [
                {
                    "symbol": row["code"],
                    "name": row["name"],
                    "market": "A股",
                    "exchange": "SSE" if row["code"].startswith("6") else "SZSE"
                }
                for _, row in df.head(100).iterrows()  # 限制数量
            ]
        else:
            return self._get_stock_list_mock(market)
    
    async def _get_stock_list_tushare(self, market: str) -> List[Dict[str, Any]]:
        """使用Tushare获取股票列表"""
        if market == "A股":
            df = self.pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,market')
            return [
                {
                    "symbol": row["symbol"],
                    "name": row["name"],
                    "market": "A股",
                    "exchange": row["market"],
                    "industry": row["industry"]
                }
                for _, row in df.head(100).iterrows()
            ]
        else:
            return self._get_stock_list_mock(market)
    
    def _get_stock_list_mock(self, market: str) -> List[Dict[str, Any]]:
        """模拟股票列表数据"""
        mock_stocks = [
            {"symbol": "000001", "name": "平安银行", "market": "A股", "exchange": "SZSE", "industry": "银行"},
            {"symbol": "000002", "name": "万科A", "market": "A股", "exchange": "SZSE", "industry": "房地产"},
            {"symbol": "600000", "name": "浦发银行", "market": "A股", "exchange": "SSE", "industry": "银行"},
            {"symbol": "600036", "name": "招商银行", "market": "A股", "exchange": "SSE", "industry": "银行"},
            {"symbol": "000858", "name": "五粮液", "market": "A股", "exchange": "SZSE", "industry": "食品饮料"},
            {"symbol": "600519", "name": "贵州茅台", "market": "A股", "exchange": "SSE", "industry": "食品饮料"},
            {"symbol": "000725", "name": "京东方A", "market": "A股", "exchange": "SZSE", "industry": "电子"},
            {"symbol": "600276", "name": "恒瑞医药", "market": "A股", "exchange": "SSE", "industry": "医药生物"},
        ]
        return mock_stocks
    
    async def _get_realtime_data_akshare(self, symbols: List[str]) -> Dict[str, Any]:
        """使用AkShare获取实时数据"""
        result = {}
        for symbol in symbols:
            try:
                df = ak.stock_zh_a_spot_em()
                stock_data = df[df['代码'] == symbol]
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    result[symbol] = {
                        "symbol": symbol,
                        "name": row["名称"],
                        "price": float(row["最新价"]),
                        "change": float(row["涨跌额"]),
                        "change_percent": float(row["涨跌幅"]),
                        "volume": int(row["成交量"]),
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                logger.warning(f"获取{symbol}实时数据失败: {e}")
                result[symbol] = self._get_mock_realtime_data(symbol)
        
        return result
    
    async def _get_realtime_data_tushare(self, symbols: List[str]) -> Dict[str, Any]:
        """使用Tushare获取实时数据"""
        # Tushare的实时数据需要高级权限，这里使用模拟数据
        result = {}
        for symbol in symbols:
            result[symbol] = self._get_mock_realtime_data(symbol)
        return result
    
    def _get_realtime_data_mock(self, symbols: List[str]) -> Dict[str, Any]:
        """模拟实时数据"""
        result = {}
        for symbol in symbols:
            result[symbol] = self._get_mock_realtime_data(symbol)
        return result
    
    def _get_mock_realtime_data(self, symbol: str) -> Dict[str, Any]:
        """生成单个股票的模拟实时数据"""
        base_prices = {
            "000001": 12.45, "000002": 8.76, "600000": 7.89,
            "600036": 35.67, "000858": 128.90, "600519": 1680.00
        }
        
        base_price = base_prices.get(symbol, 10.0)
        change_percent = np.random.uniform(-3, 3)
        change = base_price * change_percent / 100
        current_price = base_price + change
        
        return {
            "symbol": symbol,
            "price": round(current_price, 2),
            "change": round(change, 2),
            "change_percent": round(change_percent, 2),
            "volume": np.random.randint(100000, 1000000),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _get_historical_data_akshare(self, symbol: str, start_date: str, end_date: str, period: str) -> Dict[str, Any]:
        """使用AkShare获取历史数据"""
        try:
            df = ak.stock_zh_a_hist(symbol=symbol, period=period, start_date=start_date, end_date=end_date)
            return {
                "symbol": symbol,
                "data": df.to_dict('records'),
                "count": len(df)
            }
        except Exception as e:
            logger.warning(f"AkShare获取历史数据失败: {e}")
            return self._get_historical_data_mock(symbol, start_date, end_date, period)
    
    async def _get_historical_data_tushare(self, symbol: str, start_date: str, end_date: str, period: str) -> Dict[str, Any]:
        """使用Tushare获取历史数据"""
        try:
            df = self.pro.daily(ts_code=f"{symbol}.SZ", start_date=start_date, end_date=end_date)
            return {
                "symbol": symbol,
                "data": df.to_dict('records'),
                "count": len(df)
            }
        except Exception as e:
            logger.warning(f"Tushare获取历史数据失败: {e}")
            return self._get_historical_data_mock(symbol, start_date, end_date, period)
    
    def _get_historical_data_mock(self, symbol: str, start_date: str, end_date: str, period: str) -> Dict[str, Any]:
        """生成模拟历史数据"""
        start = datetime.strptime(start_date, "%Y%m%d")
        end = datetime.strptime(end_date, "%Y%m%d")
        
        dates = pd.date_range(start=start, end=end, freq='D')
        dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
        
        base_price = 10.0
        data = []
        
        for i, date in enumerate(dates):
            # 生成随机价格走势
            change = np.random.uniform(-0.5, 0.5)
            base_price = max(base_price + change, 1.0)
            
            high = base_price * (1 + np.random.uniform(0, 0.03))
            low = base_price * (1 - np.random.uniform(0, 0.03))
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(base_price, 2),
                "high": round(high, 2),
                "low": round(low, 2),
                "close": round(base_price, 2),
                "volume": volume
            })
        
        return {
            "symbol": symbol,
            "data": data,
            "count": len(data)
        }
    
    def _is_cache_valid(self, key: str, timeout: int = None) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache:
            return False
        
        cache_timeout = timeout or self.cache_timeout
        return time.time() - self.cache[key]["timestamp"] < cache_timeout
    
    def _cache_data(self, key: str, data: Any, timeout: int = None):
        """缓存数据"""
        self.cache[key] = {
            "data": data,
            "timestamp": time.time()
        }
    
    def switch_data_source(self, source: str) -> bool:
        """切换数据源"""
        if source in self.data_sources:
            self.current_source = source
            logger.info(f"📊 数据源已切换到: {source}")
            return True
        else:
            logger.warning(f"数据源 {source} 不可用")
            return False
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        return {
            "current_source": self.current_source,
            "available_sources": self.data_sources,
            "akshare_available": AKSHARE_AVAILABLE,
            "tushare_available": TUSHARE_AVAILABLE,
            "cache_size": len(self.cache)
        }


# 全局实例
enhanced_market_service = EnhancedMarketDataService()


async def get_enhanced_market_service() -> EnhancedMarketDataService:
    """获取增强市场数据服务实例"""
    return enhanced_market_service

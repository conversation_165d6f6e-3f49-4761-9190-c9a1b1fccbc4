"""
增强的市场服务
基于MarketService的增强版本，提供更丰富的市场数据功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd

from app.services.market_service import MarketService
from app.services.enhanced_market_data_service import EnhancedMarketDataService
from app.core.config import get_settings
from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger(__name__)
settings = get_settings()


class EnhancedMarketService(MarketService):
    """增强的市场服务"""
    
    def __init__(self):
        super().__init__()
        self.enhanced_data_service = EnhancedMarketDataService()
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        logger.info("✅ 增强市场服务初始化完成")
    
    async def get_stock_quote(self, symbol: str) -> Dict[str, Any]:
        """获取股票实时行情"""
        try:
            # 先尝试从缓存获取
            cache_key = f"quote_{symbol}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 调用增强数据服务
            quote_data = await self.enhanced_data_service.get_realtime_quote(symbol)
            
            # 缓存结果
            self._cache_data(cache_key, quote_data)
            
            return quote_data
            
        except Exception as e:
            logger.error(f"获取股票行情失败 {symbol}: {e}")
            # 返回模拟数据
            return self._get_mock_quote(symbol)
    
    async def get_stock_kline(
        self, 
        symbol: str, 
        period: str = "1d",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取股票K线数据"""
        try:
            # 缓存键
            cache_key = f"kline_{symbol}_{period}_{start_date}_{end_date}_{limit}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 调用增强数据服务
            kline_data = await self.enhanced_data_service.get_kline_data(
                symbol, period, start_date, end_date, limit
            )
            
            # 缓存结果
            self._cache_data(cache_key, kline_data)
            
            return kline_data
            
        except Exception as e:
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            # 返回模拟数据
            return self._get_mock_kline(symbol, limit)
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            cache_key = "market_overview"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 获取主要指数
            indices = await self._get_major_indices()
            
            # 获取市场统计
            market_stats = await self._get_market_statistics()
            
            overview = {
                "timestamp": datetime.now().isoformat(),
                "indices": indices,
                "market_stats": market_stats,
                "trading_status": "open" if self._is_trading_time() else "closed"
            }
            
            self._cache_data(cache_key, overview)
            return overview
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return self._get_mock_market_overview()
    
    async def get_stock_list(
        self, 
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            cache_key = f"stock_list_{market}_{industry}_{page}_{page_size}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 调用增强数据服务
            stock_list = await self.enhanced_data_service.get_stock_list(
                market, industry, page, page_size
            )
            
            self._cache_data(cache_key, stock_list)
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return self._get_mock_stock_list(page, page_size)
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        try:
            cache_key = f"search_{keyword}_{limit}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 调用增强数据服务
            search_results = await self.enhanced_data_service.search_stocks(keyword, limit)
            
            self._cache_data(cache_key, search_results)
            return search_results
            
        except Exception as e:
            logger.error(f"搜索股票失败 {keyword}: {e}")
            return self._get_mock_search_results(keyword, limit)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_timeout
    
    def _cache_data(self, cache_key: str, data: Any):
        """缓存数据"""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    def _is_trading_time(self) -> bool:
        """判断是否在交易时间"""
        now = datetime.now()
        weekday = now.weekday()
        
        # 周末不交易
        if weekday >= 5:
            return False
        
        # 交易时间：9:30-11:30, 13:00-15:00
        time_str = now.strftime("%H:%M")
        morning_start = "09:30"
        morning_end = "11:30"
        afternoon_start = "13:00"
        afternoon_end = "15:00"
        
        return (morning_start <= time_str <= morning_end) or (afternoon_start <= time_str <= afternoon_end)
    
    async def _get_major_indices(self) -> List[Dict[str, Any]]:
        """获取主要指数"""
        indices = ["000001.SH", "399001.SZ", "399006.SZ"]  # 上证指数、深证成指、创业板指
        results = []
        
        for index in indices:
            try:
                quote = await self.get_stock_quote(index)
                results.append(quote)
            except Exception as e:
                logger.warning(f"获取指数失败 {index}: {e}")
                results.append(self._get_mock_quote(index))
        
        return results
    
    async def _get_market_statistics(self) -> Dict[str, Any]:
        """获取市场统计数据"""
        return {
            "total_stocks": 4500,
            "rising_stocks": 2100,
            "falling_stocks": 1800,
            "unchanged_stocks": 600,
            "total_volume": 850000000000,  # 总成交量
            "total_amount": 1200000000000,  # 总成交额
        }
    
    def _get_mock_quote(self, symbol: str) -> Dict[str, Any]:
        """获取模拟行情数据"""
        import random
        
        base_price = 10.0 + random.random() * 90.0
        change = (random.random() - 0.5) * 2.0
        
        return {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "price": round(base_price, 2),
            "change": round(change, 2),
            "change_percent": round(change / base_price * 100, 2),
            "volume": random.randint(1000000, 100000000),
            "amount": random.randint(10000000, 1000000000),
            "high": round(base_price + abs(change), 2),
            "low": round(base_price - abs(change), 2),
            "open": round(base_price + (random.random() - 0.5), 2),
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_mock_kline(self, symbol: str, limit: int) -> List[Dict[str, Any]]:
        """获取模拟K线数据"""
        import random
        
        klines = []
        base_price = 10.0 + random.random() * 90.0
        
        for i in range(limit):
            date = datetime.now() - timedelta(days=limit - i)
            
            open_price = base_price + (random.random() - 0.5) * 2
            close_price = open_price + (random.random() - 0.5) * 2
            high_price = max(open_price, close_price) + random.random()
            low_price = min(open_price, close_price) - random.random()
            
            klines.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(1000000, 50000000),
                "amount": random.randint(10000000, 500000000)
            })
            
            base_price = close_price
        
        return klines
    
    def _get_mock_market_overview(self) -> Dict[str, Any]:
        """获取模拟市场概览"""
        return {
            "timestamp": datetime.now().isoformat(),
            "indices": [
                self._get_mock_quote("000001.SH"),
                self._get_mock_quote("399001.SZ"),
                self._get_mock_quote("399006.SZ")
            ],
            "market_stats": {
                "total_stocks": 4500,
                "rising_stocks": 2100,
                "falling_stocks": 1800,
                "unchanged_stocks": 600,
                "total_volume": 850000000000,
                "total_amount": 1200000000000,
            },
            "trading_status": "open" if self._is_trading_time() else "closed"
        }
    
    def _get_mock_stock_list(self, page: int, page_size: int) -> Dict[str, Any]:
        """获取模拟股票列表"""
        import random
        
        stocks = []
        start_idx = (page - 1) * page_size
        
        for i in range(page_size):
            idx = start_idx + i + 1
            symbol = f"{idx:06d}.SH" if idx % 2 == 0 else f"{idx:06d}.SZ"
            stocks.append(self._get_mock_quote(symbol))
        
        return {
            "stocks": stocks,
            "total": 4500,
            "page": page,
            "page_size": page_size,
            "total_pages": (4500 + page_size - 1) // page_size
        }
    
    def _get_mock_search_results(self, keyword: str, limit: int) -> List[Dict[str, Any]]:
        """获取模拟搜索结果"""
        results = []
        for i in range(min(limit, 10)):
            symbol = f"{keyword}{i:03d}.SH"
            results.append(self._get_mock_quote(symbol))
        return results


# 创建全局实例
enhanced_market_service = EnhancedMarketService()

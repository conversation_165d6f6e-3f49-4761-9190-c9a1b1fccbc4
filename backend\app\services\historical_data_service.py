"""
历史数据服务
专门用于读取和处理/data/historical/Equity_Identification/目录中的CSV文件
"""

import os
import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import glob
import re
import io
import base64

logger = logging.getLogger(__name__)


class HistoricalDataService:
    """历史数据服务"""
    
    def __init__(self):
        # 获取历史数据目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(os.path.dirname(current_dir))
        project_dir = os.path.dirname(backend_dir)
        self.data_dir = os.path.join(project_dir, "data", "historical", "Equity_Identification")
        
        logger.info(f"历史数据服务初始化，数据目录: {self.data_dir}")
        
        # 缓存
        self._stock_list_cache = None
        self._cache_time = None
        self._cache_duration = 3600  # 缓存1小时
        self._industry_cache = None
    
    def _parse_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """解析文件名获取股票信息"""
        # 匹配格式：000001_平安银行.csv
        match = re.match(r'(\d{6})_(.+)\.csv', filename)
        if match:
            return {
                "symbol": match.group(1),
                "name": match.group(2).strip()
            }
        return None
    
    def _get_market_info(self, symbol: str) -> Dict[str, str]:
        """根据股票代码判断市场信息"""
        if symbol.startswith('6'):
            return {"market": "SH", "market_name": "上交所", "board": "主板"}
        elif symbol.startswith('00'):
            return {"market": "SZ", "market_name": "深交所", "board": "主板"}
        elif symbol.startswith('30'):
            return {"market": "SZ", "market_name": "深交所", "board": "创业板"}
        elif symbol.startswith('8') or symbol.startswith('4'):
            return {"market": "BJ", "market_name": "北交所", "board": "北交所"}
        else:
            return {"market": "SZ", "market_name": "深交所", "board": "主板"}
    
    def _guess_industry(self, stock_name: str) -> str:
        """根据股票名称猜测行业"""
        industry_keywords = {
            "银行": ["银行", "农商行", "城商行"],
            "保险": ["保险", "人寿", "财险", "再保险"],
            "证券": ["证券", "投资", "信托", "期货"],
            "房地产": ["地产", "房地产", "置业", "发展", "城建"],
            "医药生物": ["医药", "生物", "制药", "医疗", "健康", "药业"],
            "食品饮料": ["食品", "饮料", "酒", "乳业", "茶", "糖"],
            "电子": ["电子", "半导体", "芯片", "集成电路"],
            "计算机": ["软件", "信息", "数据", "科技", "网络", "互联"],
            "汽车": ["汽车", "客车", "轿车", "汽配"],
            "化工": ["化工", "化学", "石化", "塑料", "橡胶"],
            "钢铁": ["钢铁", "钢材", "金属", "冶金"],
            "电力": ["电力", "能源", "新能源", "光伏", "风电"],
            "交通运输": ["航空", "机场", "港口", "物流", "运输", "快递"],
            "建筑材料": ["建材", "水泥", "玻璃", "陶瓷"],
            "机械设备": ["机械", "设备", "重工", "装备"],
            "纺织服装": ["纺织", "服装", "服饰", "鞋业"],
            "传媒": ["传媒", "文化", "影视", "游戏", "广告"],
            "通信": ["通信", "电信", "移动", "联通"],
            "家电": ["电器", "家电", "空调", "冰箱"],
            "煤炭": ["煤炭", "煤业", "焦炭"],
            "有色金属": ["铝", "铜", "锌", "黄金", "稀土"],
            "农林牧渔": ["农业", "种业", "养殖", "畜牧", "渔业"],
            "公用事业": ["水务", "燃气", "环保", "公用"],
            "商贸零售": ["百货", "超市", "零售", "商业", "连锁"],
            "休闲服务": ["旅游", "酒店", "餐饮", "景区"],
            "国防军工": ["军工", "国防", "航天", "兵器"],
            "白酒": ["茅台", "五粮液", "泸州", "剑南春", "汾酒", "洋河"]
        }
        
        for industry, keywords in industry_keywords.items():
            for keyword in keywords:
                if keyword in stock_name:
                    return industry
        
        return "其他"
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取历史数据统计信息"""
        try:
            if not os.path.exists(self.data_dir):
                logger.warning(f"历史数据目录不存在: {self.data_dir}")
                return self._get_default_stats()
            
            # 获取所有CSV文件
            csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))
            total_stocks = len(csv_files)
            
            # 统计市场分布
            markets = {"SH": 0, "SZ": 0, "BJ": 0}
            industries = {}
            
            for csv_file in csv_files[:1000]:  # 只处理前1000个文件以提高性能
                filename = os.path.basename(csv_file)
                stock_info = self._parse_filename(filename)
                
                if stock_info:
                    symbol = stock_info["symbol"]
                    name = stock_info["name"]
                    
                    # 统计市场
                    market_info = self._get_market_info(symbol)
                    market = market_info["market"]
                    if market in markets:
                        markets[market] += 1
                    
                    # 统计行业
                    industry = self._guess_industry(name)
                    industries[industry] = industries.get(industry, 0) + 1
            
            # 获取数据时间范围（读取第一个文件的日期范围）
            data_range = {"start_date": None, "end_date": None}
            if csv_files:
                try:
                    df = pd.read_csv(csv_files[0], encoding='utf-8', nrows=1)
                    if not df.empty and '日期' in df.columns:
                        # 读取第一行和最后一行的日期
                        first_date = df['日期'].iloc[0]
                        # 读取最后一行
                        df_last = pd.read_csv(csv_files[0], encoding='utf-8', usecols=['日期'])
                        last_date = df_last['日期'].iloc[-1] if not df_last.empty else first_date
                        data_range = {
                            "start_date": first_date,
                            "end_date": last_date
                        }
                except Exception as e:
                    logger.error(f"读取数据范围失败: {e}")
            
            return {
                "total_stocks": total_stocks,
                "markets": markets,
                "industries": industries,
                "data_range": data_range,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return self._get_default_stats()
    
    def _get_default_stats(self) -> Dict[str, Any]:
        """获取默认统计信息"""
        return {
            "total_stocks": 0,
            "markets": {"SH": 0, "SZ": 0, "BJ": 0},
            "industries": {},
            "data_range": {"start_date": None, "end_date": None},
            "last_updated": datetime.now().isoformat()
        }
    
    async def get_stock_list(
        self,
        keyword: Optional[str] = None,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            # 检查缓存
            now = datetime.now()
            if (self._stock_list_cache and 
                self._cache_time and 
                (now - self._cache_time).seconds < self._cache_duration):
                stock_list = self._stock_list_cache
            else:
                # 重新加载股票列表
                stock_list = await self._load_stock_list()
                self._stock_list_cache = stock_list
                self._cache_time = now
            
            # 应用筛选条件
            filtered_list = stock_list
            
            # 关键词搜索
            if keyword:
                keyword_lower = keyword.lower()
                filtered_list = [
                    s for s in filtered_list
                    if keyword_lower in s["symbol"].lower() or 
                       keyword_lower in s["name"].lower()
                ]
            
            # 市场筛选
            if market:
                filtered_list = [s for s in filtered_list if s["market"] == market.upper()]
            
            # 行业筛选
            if industry:
                filtered_list = [s for s in filtered_list if s["industry"] == industry]
            
            # 分页
            total = len(filtered_list)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_stocks = filtered_list[start_idx:end_idx]
            
            # 为每只股票添加最新数据
            for stock in page_stocks:
                latest_data = await self._get_latest_stock_data(stock["symbol"], stock["file_path"])
                stock.update(latest_data)
            
            return {
                "stocks": page_stocks,
                "total": total,
                "page": page,
                "pageSize": page_size
            }
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return {"stocks": [], "total": 0, "page": page, "pageSize": page_size}
    
    async def _load_stock_list(self) -> List[Dict[str, Any]]:
        """加载股票列表"""
        stock_list = []
        
        if not os.path.exists(self.data_dir):
            return stock_list
        
        csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))
        
        for csv_file in csv_files:
            filename = os.path.basename(csv_file)
            stock_info = self._parse_filename(filename)
            
            if stock_info:
                symbol = stock_info["symbol"]
                name = stock_info["name"]
                
                # 获取市场信息
                market_info = self._get_market_info(symbol)
                
                # 获取行业
                industry = self._guess_industry(name)
                
                # 获取文件信息
                file_size = os.path.getsize(csv_file)
                
                stock_data = {
                    "symbol": symbol,
                    "name": name,
                    "market": market_info["market"],
                    "market_name": market_info["market_name"],
                    "board": market_info["board"],
                    "industry": industry,
                    "file_path": csv_file,
                    "file_size": f"{file_size / 1024 / 1024:.2f}MB" if file_size > 1024*1024 else f"{file_size / 1024:.2f}KB"
                }
                
                stock_list.append(stock_data)
        
        return stock_list
    
    async def _get_latest_stock_data(self, symbol: str, file_path: str) -> Dict[str, Any]:
        """获取股票最新数据"""
        try:
            # 读取CSV文件的最后几行
            df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                return self._get_default_latest_data()
            
            # 获取最新一行数据
            latest = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else latest
            
            # 获取数据统计
            total_records = len(df)
            start_date = df['日期'].iloc[0] if '日期' in df.columns else None
            end_date = df['日期'].iloc[-1] if '日期' in df.columns else None
            
            # 计算价格和涨跌
            last_price = float(latest.get('收盘价', 0))
            prev_close = float(prev.get('收盘价', last_price))
            change = last_price - prev_close
            change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            
            return {
                "last_price": round(last_price, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "total_records": total_records,
                "start_date": start_date,
                "end_date": end_date,
                "has_historical_data": True
            }
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 最新数据失败: {e}")
            return self._get_default_latest_data()
    
    def _get_default_latest_data(self) -> Dict[str, Any]:
        """获取默认最新数据"""
        return {
            "last_price": 0,
            "change": 0,
            "change_percent": 0,
            "total_records": 0,
            "start_date": None,
            "end_date": None,
            "has_historical_data": False
        }
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        page: int = 1,
        page_size: int = 100
    ) -> Optional[Dict[str, Any]]:
        """获取股票历史数据"""
        try:
            # 查找对应的CSV文件
            csv_pattern = os.path.join(self.data_dir, f"{symbol}_*.csv")
            csv_files = glob.glob(csv_pattern)
            
            if not csv_files:
                logger.warning(f"未找到股票 {symbol} 的历史数据文件")
                return None
            
            file_path = csv_files[0]
            
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                return None
            
            # 日期筛选
            if '日期' in df.columns:
                df['日期'] = pd.to_datetime(df['日期'])
                
                if start_date:
                    df = df[df['日期'] >= pd.to_datetime(start_date)]
                
                if end_date:
                    df = df[df['日期'] <= pd.to_datetime(end_date)]
                
                # 按日期降序排序
                df = df.sort_values('日期', ascending=False)
                
                # 转回字符串格式
                df['日期'] = df['日期'].dt.strftime('%Y-%m-%d')
            
            # 分页
            total = len(df)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_data = df.iloc[start_idx:end_idx]
            
            # 转换为字典列表
            data_list = page_data.to_dict('records')
            
            # 获取股票名称
            filename = os.path.basename(file_path)
            stock_info = self._parse_filename(filename)
            stock_name = stock_info["name"] if stock_info else f"股票{symbol}"
            
            return {
                "symbol": symbol,
                "name": stock_name,
                "data": data_list,
                "total": total,
                "page": page,
                "pageSize": page_size
            }
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 历史数据失败: {e}")
            return None
    
    async def get_latest_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票最新数据"""
        try:
            # 查找对应的CSV文件
            csv_pattern = os.path.join(self.data_dir, f"{symbol}_*.csv")
            csv_files = glob.glob(csv_pattern)
            
            if not csv_files:
                return None
            
            file_path = csv_files[0]
            
            # 读取最后一行数据
            df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                return None
            
            latest = df.iloc[-1].to_dict()
            
            # 获取股票名称
            filename = os.path.basename(file_path)
            stock_info = self._parse_filename(filename)
            
            return {
                "symbol": symbol,
                "name": stock_info["name"] if stock_info else f"股票{symbol}",
                "data": latest
            }
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 最新数据失败: {e}")
            return None
    
    async def get_industries(self) -> List[str]:
        """获取所有行业列表"""
        try:
            if self._industry_cache:
                return self._industry_cache
            
            # 从缓存的股票列表中提取行业
            if not self._stock_list_cache:
                await self.get_stock_list()
            
            industries = set()
            if self._stock_list_cache:
                for stock in self._stock_list_cache:
                    industries.add(stock.get("industry", "其他"))
            
            self._industry_cache = sorted(list(industries))
            return self._industry_cache
            
        except Exception as e:
            logger.error(f"获取行业列表失败: {e}")
            return []
    
    async def get_hot_stocks(self, category: Optional[str] = None, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门股票"""
        try:
            hot_stocks = []
            
            # 定义热门股票
            hot_stock_symbols = {
                "热门股票": ["600519", "000002", "000001", "600036", "000858", "002415", "300059", "600276"],
                "银行股": ["600036", "000001", "601398", "601288", "601939", "600016", "600000", "002142"],
                "科技股": ["000002", "002415", "300059", "002230", "000063", "000066", "000100", "300033"],
                "白酒股": ["600519", "000858", "002304", "000596", "600559", "000799", "603369", "600809"]
            }
            
            # 获取指定类别的股票代码
            if category and category in hot_stock_symbols:
                target_symbols = hot_stock_symbols[category]
            else:
                # 返回所有热门股票
                target_symbols = []
                for symbols in hot_stock_symbols.values():
                    target_symbols.extend(symbols)
                target_symbols = list(set(target_symbols))[:limit]
            
            # 查找对应的股票信息
            if not self._stock_list_cache:
                await self.get_stock_list()
            
            if self._stock_list_cache:
                for symbol in target_symbols[:limit]:
                    stock = next((s for s in self._stock_list_cache if s["symbol"] == symbol), None)
                    if stock:
                        # 获取最新数据
                        latest_data = await self._get_latest_stock_data(stock["symbol"], stock["file_path"])
                        stock_info = stock.copy()
                        stock_info.update(latest_data)
                        hot_stocks.append(stock_info)
            
            return hot_stocks
            
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return []
    
    async def export_data(
        self,
        symbols: List[str],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        format: str = "csv"
    ) -> Dict[str, Any]:
        """导出股票数据"""
        try:
            export_data = []
            
            for symbol in symbols:
                stock_data = await self.get_stock_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    page=1,
                    page_size=10000  # 获取所有数据
                )
                
                if stock_data and stock_data.get("data"):
                    for record in stock_data["data"]:
                        record["股票代码"] = symbol
                        record["股票名称"] = stock_data["name"]
                        export_data.append(record)
            
            if not export_data:
                return {"error": "没有找到要导出的数据"}
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 重新排序列
            columns_order = ["股票代码", "股票名称", "日期", "开盘价", "收盘价", "最高价", "最低价", 
                           "成交量(手)", "成交额(元)", "涨跌幅(%)", "换手率(%)"]
            
            # 只保留存在的列
            columns_order = [col for col in columns_order if col in df.columns]
            df = df[columns_order]
            
            # 导出为CSV或Excel
            if format == "excel":
                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='历史数据')
                output.seek(0)
                file_content = base64.b64encode(output.read()).decode()
                filename = f"历史数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            else:
                output = io.StringIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                file_content = base64.b64encode(output.getvalue().encode('utf-8-sig')).decode()
                filename = f"历史数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                mime_type = "text/csv"
            
            return {
                "filename": filename,
                "content": file_content,
                "mime_type": mime_type,
                "rows": len(df)
            }
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return {"error": f"导出数据失败: {str(e)}"}


# 创建全局实例
historical_data_service = HistoricalDataService()
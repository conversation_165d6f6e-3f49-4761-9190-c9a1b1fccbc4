"""
历史股票数据服务
专门用于读取和处理backend/data/historical/stocks目录中的CSV文件
"""

import os
import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import glob
import re

logger = logging.getLogger(__name__)


class HistoricalStockService:
    """历史股票数据服务"""
    
    def __init__(self):
        # 获取历史数据目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(os.path.dirname(current_dir))
        self.data_dir = os.path.join(backend_dir, "data", "historical", "stocks")
        
        logger.info(f"历史股票数据服务初始化，数据目录: {self.data_dir}")
        
        # 缓存股票列表
        self._stock_list_cache = None
        self._cache_time = None
        self._cache_duration = 3600  # 缓存1小时
    
    def _get_stock_list_from_files(self) -> List[Dict[str, Any]]:
        """从CSV文件名中提取股票列表"""
        try:
            if not os.path.exists(self.data_dir):
                logger.warning(f"历史数据目录不存在: {self.data_dir}")
                return []
            
            # 获取所有CSV文件
            csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))
            
            if not csv_files:
                logger.warning(f"历史数据目录中没有找到CSV文件: {self.data_dir}")
                return []
            
            stock_list = []
            
            for csv_file in csv_files:
                filename = os.path.basename(csv_file)
                
                # 解析文件名格式：000001_平安银行.csv
                match = re.match(r'(\d{6})_(.+)\.csv', filename)
                if match:
                    symbol = match.group(1)
                    name = match.group(2)
                    
                    # 根据股票代码判断交易所
                    if symbol.startswith('6'):
                        ts_code = f"{symbol}.SH"
                        exchange = "SSE"
                        market = "主板"
                    elif symbol.startswith('0') or symbol.startswith('3'):
                        ts_code = f"{symbol}.SZ"
                        exchange = "SZSE"
                        if symbol.startswith('3'):
                            market = "创业板"
                        else:
                            market = "主板"
                    elif symbol.startswith('8') or symbol.startswith('4'):
                        ts_code = f"{symbol}.BJ"
                        exchange = "BSE"
                        market = "北交所"
                    else:
                        ts_code = f"{symbol}.SZ"
                        exchange = "SZSE"
                        market = "主板"
                    
                    # 判断行业（简单分类）
                    industry = self._guess_industry(name)
                    
                    stock_info = {
                        "symbol": symbol,
                        "ts_code": ts_code,
                        "name": name,
                        "exchange": exchange,
                        "market": market,
                        "industry": industry,
                        "area": "中国",
                        "list_date": "20100101",  # 默认上市日期
                        "file_path": csv_file
                    }
                    
                    stock_list.append(stock_info)
            
            logger.info(f"从历史数据文件中解析出 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"解析历史股票文件失败: {e}")
            return []
    
    def _guess_industry(self, stock_name: str) -> str:
        """根据股票名称猜测行业"""
        industry_keywords = {
            "银行": ["银行"],
            "保险": ["保险", "人寿", "财险"],
            "证券": ["证券", "投资", "信托"],
            "房地产": ["地产", "房地产", "置业", "发展"],
            "医药生物": ["医药", "生物", "制药", "医疗", "健康"],
            "食品饮料": ["食品", "饮料", "酒业", "乳业", "茶叶"],
            "电子": ["电子", "科技", "通信", "软件", "信息"],
            "汽车": ["汽车", "客车", "轿车"],
            "化工": ["化工", "化学", "石化"],
            "钢铁": ["钢铁", "钢材", "金属"],
            "电力": ["电力", "能源", "新能源"],
            "交通运输": ["航空", "港口", "物流", "运输"],
            "建筑材料": ["建材", "水泥", "玻璃"],
            "机械设备": ["机械", "设备", "重工"],
            "纺织服装": ["纺织", "服装", "服饰"]
        }
        
        for industry, keywords in industry_keywords.items():
            for keyword in keywords:
                if keyword in stock_name:
                    return industry
        
        return "其他"
    
    async def get_stock_list(self, page: int = 1, page_size: int = 50, market: str = None) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            # 检查缓存
            now = datetime.now()
            if (self._stock_list_cache and 
                self._cache_time and 
                (now - self._cache_time).seconds < self._cache_duration):
                stock_list = self._stock_list_cache
            else:
                # 重新加载股票列表
                stock_list = self._get_stock_list_from_files()
                self._stock_list_cache = stock_list
                self._cache_time = now
            
            if not stock_list:
                return {"stocks": [], "total": 0, "page": page, "pageSize": page_size}
            
            # 市场筛选
            if market:
                if market.upper() == "SH":
                    stock_list = [s for s in stock_list if s["ts_code"].endswith(".SH")]
                elif market.upper() == "SZ":
                    stock_list = [s for s in stock_list if s["ts_code"].endswith(".SZ")]
                elif market.upper() == "BJ":
                    stock_list = [s for s in stock_list if s["ts_code"].endswith(".BJ")]
            
            # 分页
            total = len(stock_list)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_stocks = stock_list[start_idx:end_idx]
            
            # 为每只股票添加实时行情数据
            for stock in page_stocks:
                quote_data = await self._get_latest_quote(stock["symbol"], stock["file_path"])
                stock.update(quote_data)
            
            return {
                "stocks": page_stocks,
                "total": total,
                "page": page,
                "pageSize": page_size
            }
            
        except Exception as e:
            logger.error(f"获取历史股票列表失败: {e}")
            return {"stocks": [], "total": 0, "page": page, "pageSize": page_size}
    
    async def _get_latest_quote(self, symbol: str, file_path: str) -> Dict[str, Any]:
        """从CSV文件中获取最新行情数据"""
        try:
            # 读取CSV文件的最后几行
            df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                return self._get_default_quote()
            
            # 获取最新一行数据
            latest = df.iloc[-1]
            
            # 解析数据
            current_price = float(latest.get('收盘价', 0))
            open_price = float(latest.get('开盘价', current_price))
            high_price = float(latest.get('最高价', current_price))
            low_price = float(latest.get('最低价', current_price))
            
            # 计算涨跌
            if len(df) > 1:
                prev_close = float(df.iloc[-2].get('收盘价', current_price))
            else:
                prev_close = current_price
            
            change = current_price - prev_close
            change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            
            # 成交量和成交额
            volume = int(latest.get('成交量(手)', 0)) * 100  # 转换为股
            turnover = float(latest.get('成交额(元)', 0))
            
            return {
                "currentPrice": round(current_price, 2),
                "openPrice": round(open_price, 2),
                "highPrice": round(high_price, 2),
                "lowPrice": round(low_price, 2),
                "prevClosePrice": round(prev_close, 2),
                "change": round(change, 2),
                "changePercent": round(change_percent, 2),
                "volume": volume,
                "turnover": turnover,
                "timestamp": datetime.now().timestamp()
            }
            
        except Exception as e:
            logger.error(f"读取股票 {symbol} 的行情数据失败: {e}")
            return self._get_default_quote()
    
    def _get_default_quote(self) -> Dict[str, Any]:
        """获取默认行情数据"""
        return {
            "currentPrice": 0.0,
            "openPrice": 0.0,
            "highPrice": 0.0,
            "lowPrice": 0.0,
            "prevClosePrice": 0.0,
            "change": 0.0,
            "changePercent": 0.0,
            "volume": 0,
            "turnover": 0.0,
            "timestamp": datetime.now().timestamp()
        }
    
    async def get_stock_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取单只股票的行情数据"""
        try:
            # 查找对应的CSV文件
            csv_pattern = os.path.join(self.data_dir, f"{symbol}_*.csv")
            csv_files = glob.glob(csv_pattern)
            
            if not csv_files:
                logger.warning(f"未找到股票 {symbol} 的历史数据文件")
                return None
            
            file_path = csv_files[0]
            quote_data = await self._get_latest_quote(symbol, file_path)
            
            # 添加股票基本信息
            filename = os.path.basename(file_path)
            match = re.match(r'\d{6}_(.+)\.csv', filename)
            stock_name = match.group(1) if match else f"股票{symbol}"
            
            quote_data.update({
                "symbol": symbol,
                "name": stock_name
            })
            
            return quote_data
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 行情失败: {e}")
            return None
    
    async def get_available_symbols(self) -> List[str]:
        """获取所有可用的股票代码"""
        try:
            stock_list = self._get_stock_list_from_files()
            return [stock["symbol"] for stock in stock_list]
        except Exception as e:
            logger.error(f"获取可用股票代码失败: {e}")
            return []


# 创建全局实例
historical_stock_service = HistoricalStockService()

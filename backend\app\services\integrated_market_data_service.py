"""
集成市场数据服务
整合热数据、温数据、冷数据三层存储，提供统一的数据访问接口
基于pythonstock的分层缓存策略优化
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

import pandas as pd
from loguru import logger

from app.core.data_storage import (
    hot_data_manager,
    warm_data_manager,
    cold_data_manager,
    api_rate_limiter
)
from app.services.mock_market_service import mock_market_service
from app.services.data_preloader import data_preloader
from app.services.multi_data_source_manager import multi_data_source_manager
from app.core.config import settings


class IntegratedMarketDataService:
    """集成市场数据服务"""
    
    def __init__(self):
        self.service_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'api_calls': 0,
            'errors': 0
        }

        # 选择数据源
        self.use_real_data = settings.USE_REAL_DATA
        self.data_source = multi_data_source_manager if self.use_real_data else mock_market_service

        logger.info(f"集成市场数据服务初始化: 使用{'真实' if self.use_real_data else '模拟'}数据源")
    
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取实时行情数据
        优先级: 热数据缓存 -> API调用 -> 缓存存储
        """
        try:
            # 1. 尝试从热数据缓存获取
            cached_data = await hot_data_manager.get_realtime_data(symbol)
            if cached_data:
                self.service_stats['cache_hits'] += 1
                logger.debug(f"从热缓存获取实时行情: {symbol}")
                return cached_data
            
            # 2. 缓存未命中，从API获取
            self.service_stats['cache_misses'] += 1
            
            # 检查API调用限制
            if not self.use_real_data:
                await api_rate_limiter.wait_for_rate_limit('mock')

            # 调用数据源获取数据
            quote_data = await self.data_source.get_realtime_quote(symbol)
            self.service_stats['api_calls'] += 1
            
            if quote_data:
                # 存储到热数据缓存
                await hot_data_manager.store_realtime_data(symbol, quote_data)
                logger.debug(f"从API获取并缓存实时行情: {symbol}")
                return quote_data
            
            return None
            
        except Exception as e:
            self.service_stats['errors'] += 1
            logger.error(f"获取实时行情失败 {symbol}: {e}")
            return None
    
    async def get_kline_data(
        self, 
        symbol: str, 
        period: str = 'daily',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100
    ) -> Optional[pd.DataFrame]:
        """
        获取K线数据
        优先级: 温数据缓存 -> 冷数据存储 -> API调用
        """
        try:
            # 处理日期参数
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_dt = datetime.strptime(end_date, '%Y%m%d') - timedelta(days=limit)
                start_date = start_dt.strftime('%Y%m%d')
            
            # 1. 对于日K线，尝试从温数据缓存获取
            if period == 'daily':
                cached_data = await warm_data_manager.load_daily_data(symbol, end_date)
                if cached_data is not None and not cached_data.empty:
                    # 过滤时间范围
                    start_dt = pd.to_datetime(start_date)
                    end_dt = pd.to_datetime(end_date)
                    filtered_data = cached_data[(cached_data.index >= start_dt) & (cached_data.index <= end_dt)]
                    
                    if not filtered_data.empty:
                        self.service_stats['cache_hits'] += 1
                        logger.debug(f"从温缓存获取K线数据: {symbol} {period}")
                        return filtered_data.tail(limit)
            
            # 2. 尝试从冷数据存储获取
            historical_data = await cold_data_manager.load_historical_data(symbol, start_date, end_date)
            if not historical_data.empty:
                self.service_stats['cache_hits'] += 1
                logger.debug(f"从冷存储获取K线数据: {symbol} {period}")
                
                # 如果是日K线且数据较新，存储到温缓存
                if period == 'daily' and self._is_recent_data(historical_data):
                    await warm_data_manager.store_daily_data(symbol, end_date, historical_data)
                
                return historical_data.tail(limit)
            
            # 3. 缓存和存储都未命中，从API获取
            self.service_stats['cache_misses'] += 1
            
            # 检查API调用限制
            if not self.use_real_data:
                await api_rate_limiter.wait_for_rate_limit('mock')

            # 调用数据源获取数据
            kline_data = await self.data_source.get_kline_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                period=period
            )
            self.service_stats['api_calls'] += 1
            
            if kline_data is not None and not kline_data.empty:
                # 存储到相应的缓存层
                if period == 'daily':
                    # 存储到温数据缓存
                    await warm_data_manager.store_daily_data(symbol, end_date, kline_data)
                    
                    # 如果数据量较大，也存储到冷数据
                    if len(kline_data) > 30:
                        await cold_data_manager.store_historical_data(symbol, kline_data)
                
                logger.debug(f"从API获取并缓存K线数据: {symbol} {period} ({len(kline_data)} 条)")
                return kline_data.tail(limit)
            
            return pd.DataFrame()
            
        except Exception as e:
            self.service_stats['errors'] += 1
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_batch_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量获取实时行情"""
        results = {}
        
        # 分批处理，避免API调用过于频繁
        batch_size = 10
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            
            # 并发获取这一批的数据
            tasks = [self.get_realtime_quote(symbol) for symbol in batch_symbols]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for symbol, result in zip(batch_symbols, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"批量获取行情失败 {symbol}: {result}")
                    results[symbol] = None
                else:
                    results[symbol] = result
            
            # 批次间延迟
            if i + batch_size < len(symbols):
                await asyncio.sleep(0.5)
        
        return results
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('mock')
            
            # 调用搜索API
            search_results = await mock_market_service.search_stocks(keyword, limit)
            self.service_stats['api_calls'] += 1
            
            return search_results or []
            
        except Exception as e:
            self.service_stats['errors'] += 1
            logger.error(f"搜索股票失败 {keyword}: {e}")
            return []
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            # 获取主要指数数据
            index_symbols = ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH']
            index_data = await self.get_batch_quotes(index_symbols)
            
            # 构建市场概览数据
            overview = {
                'indices': [],
                'market_stats': {
                    'total_stocks': 4000,  # 模拟数据
                    'rising': 2100,
                    'falling': 1500,
                    'unchanged': 400
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # 处理指数数据
            index_names = {
                '000001.SH': '上证指数',
                '399001.SZ': '深证成指', 
                '399006.SZ': '创业板指',
                '000300.SH': '沪深300'
            }
            
            for symbol, data in index_data.items():
                if data:
                    overview['indices'].append({
                        'symbol': symbol,
                        'name': index_names.get(symbol, symbol),
                        'value': data.get('current_price', 0),
                        'change': data.get('change', 0),
                        'change_percent': data.get('change_percent', 0)
                    })
            
            return overview
            
        except Exception as e:
            self.service_stats['errors'] += 1
            logger.error(f"获取市场概览失败: {e}")
            return {}
    
    async def get_sector_data(self) -> List[Dict[str, Any]]:
        """获取板块数据"""
        try:
            # 模拟板块数据
            sectors = [
                {'name': '银行', 'change_percent': 1.2, 'stocks_count': 42},
                {'name': '科技', 'change_percent': -0.8, 'stocks_count': 156},
                {'name': '医药', 'change_percent': 2.1, 'stocks_count': 89},
                {'name': '地产', 'change_percent': -1.5, 'stocks_count': 67},
                {'name': '汽车', 'change_percent': 0.9, 'stocks_count': 78},
                {'name': '军工', 'change_percent': 3.2, 'stocks_count': 34}
            ]
            
            return sectors
            
        except Exception as e:
            self.service_stats['errors'] += 1
            logger.error(f"获取板块数据失败: {e}")
            return []
    
    def _is_recent_data(self, data: pd.DataFrame) -> bool:
        """判断数据是否为最近的数据"""
        if data.empty:
            return False
        
        latest_date = data.index.max()
        days_ago = (datetime.now() - latest_date).days
        
        return days_ago <= 7  # 7天内的数据认为是最近的
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        total_requests = self.service_stats['cache_hits'] + self.service_stats['cache_misses']
        cache_hit_rate = (self.service_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        # 获取预加载状态
        preload_status = await data_preloader.get_preload_status()
        
        return {
            'service_stats': self.service_stats,
            'cache_hit_rate': f"{cache_hit_rate:.2f}%",
            'preload_status': preload_status,
            'api_rate_limits': {
                api_name: api_rate_limiter.get_remaining_calls(api_name)
                for api_name in ['mock', 'tushare', 'akshare']
            }
        }
    
    async def warm_up_cache(self, symbols: Optional[List[str]] = None):
        """预热缓存"""
        if not symbols:
            # 使用预加载器的热门股票列表
            symbols = data_preloader.popular_symbols[:10]  # 预热前10个热门股票
        
        logger.info(f"开始预热缓存，股票数量: {len(symbols)}")
        
        # 预热实时行情
        await self.get_batch_quotes(symbols)
        
        # 预热K线数据
        for symbol in symbols:
            await self.get_kline_data(symbol, period='daily', limit=30)
            await asyncio.sleep(0.1)
        
        logger.info("缓存预热完成")
    
    async def clear_cache(self, cache_type: str = 'all'):
        """清理缓存"""
        try:
            if cache_type in ['all', 'warm']:
                cleaned_count = await warm_data_manager.cleanup_expired_cache()
                logger.info(f"已清理温数据缓存: {cleaned_count} 个文件")
            
            if cache_type in ['all', 'hot']:
                # 清理热数据缓存 (重置内存缓存)
                hot_data_manager.memory_cache.clear()
                logger.info("已清理热数据缓存")
            
            # 重置统计信息
            if cache_type == 'all':
                self.service_stats = {
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'api_calls': 0,
                    'errors': 0
                }
                logger.info("已重置服务统计信息")
            
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")


# 全局实例
integrated_market_service = IntegratedMarketDataService()


# 导出主要组件
__all__ = [
    'IntegratedMarketDataService',
    'integrated_market_service'
]

"""
集成市场服务
整合多个市场数据源，提供统一的数据接口
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd

from app.services.enhanced_market_service import enhanced_market_service
from app.services.market_data_service import market_data_service
from app.core.config import get_settings
from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger(__name__)
settings = get_settings()


class IntegratedMarketService:
    """集成市场服务"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        self.enhanced_service = enhanced_market_service
        self.data_service = market_data_service
        logger.info("✅ 集成市场服务初始化完成")
    
    async def get_quote(self, symbol: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """获取股票行情"""
        try:
            if use_cache:
                cache_key = f"quote_{symbol}"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            # 优先使用增强服务
            quote = await self.enhanced_service.get_stock_quote(symbol)
            
            if use_cache:
                self._cache_data(cache_key, quote)
            
            return quote
            
        except Exception as e:
            logger.error(f"获取股票行情失败 {symbol}: {e}")
            return None
    
    async def get_quotes(self, symbols: List[str], use_cache: bool = True) -> List[Dict[str, Any]]:
        """批量获取股票行情"""
        quotes = []
        
        for symbol in symbols:
            quote = await self.get_quote(symbol, use_cache)
            if quote:
                quotes.append(quote)
        
        return quotes
    
    async def get_kline_data(
        self,
        symbol: str,
        period: str = "1d",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """获取K线数据"""
        try:
            if use_cache:
                cache_key = f"kline_{symbol}_{period}_{start_date}_{end_date}_{limit}"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            # 使用增强服务获取K线数据
            kline_data = await self.enhanced_service.get_stock_kline(
                symbol, period, start_date, end_date, limit
            )
            
            if use_cache:
                self._cache_data(cache_key, kline_data)
            
            return kline_data
            
        except Exception as e:
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            return []
    
    async def get_market_overview(self, use_cache: bool = True) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            if use_cache:
                cache_key = "market_overview"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            overview = await self.enhanced_service.get_market_overview()
            
            if use_cache:
                self._cache_data(cache_key, overview)
            
            return overview
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {}
    
    async def get_stock_list(
        self,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            if use_cache:
                cache_key = f"stock_list_{market}_{industry}_{page}_{page_size}"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            stock_list = await self.enhanced_service.get_stock_list(
                market, industry, page, page_size
            )
            
            if use_cache:
                self._cache_data(cache_key, stock_list)
            
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return {"stocks": [], "total": 0, "page": page, "page_size": page_size}
    
    async def search_stocks(self, keyword: str, limit: int = 20, use_cache: bool = True) -> List[Dict[str, Any]]:
        """搜索股票"""
        try:
            if use_cache:
                cache_key = f"search_{keyword}_{limit}"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            results = await self.enhanced_service.search_stocks(keyword, limit)
            
            if use_cache:
                self._cache_data(cache_key, results)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索股票失败 {keyword}: {e}")
            return []
    
    async def get_indices(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取主要指数"""
        try:
            if use_cache:
                cache_key = "indices"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            # 主要指数代码
            index_symbols = ["000001.SH", "399001.SZ", "399006.SZ"]
            indices = await self.get_quotes(index_symbols, use_cache)
            
            if use_cache:
                self._cache_data(cache_key, indices)
            
            return indices
            
        except Exception as e:
            logger.error(f"获取指数失败: {e}")
            return []
    
    async def get_sectors(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取板块行情"""
        try:
            if use_cache:
                cache_key = "sectors"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            # 模拟板块数据
            sectors = [
                {"name": "银行", "code": "BK0475", "change_percent": 1.2, "stocks_count": 42},
                {"name": "房地产", "code": "BK0451", "change_percent": -0.8, "stocks_count": 138},
                {"name": "医药生物", "code": "BK0727", "change_percent": 2.1, "stocks_count": 356},
                {"name": "电子", "code": "BK0726", "change_percent": 1.8, "stocks_count": 289},
                {"name": "计算机", "code": "BK0725", "change_percent": 3.2, "stocks_count": 245}
            ]
            
            if use_cache:
                self._cache_data(cache_key, sectors)
            
            return sectors
            
        except Exception as e:
            logger.error(f"获取板块行情失败: {e}")
            return []
    
    async def get_rankings(self, rank_type: str = "change_percent", limit: int = 50, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取股票排行榜"""
        try:
            if use_cache:
                cache_key = f"rankings_{rank_type}_{limit}"
                if self._is_cache_valid(cache_key):
                    return self.cache[cache_key]['data']
            
            # 获取股票列表并排序
            stock_list = await self.get_stock_list(page_size=limit * 2, use_cache=use_cache)
            stocks = stock_list.get("stocks", [])
            
            # 根据排行类型排序
            if rank_type == "change_percent":
                stocks.sort(key=lambda x: x.get("change_percent", 0), reverse=True)
            elif rank_type == "volume":
                stocks.sort(key=lambda x: x.get("volume", 0), reverse=True)
            elif rank_type == "amount":
                stocks.sort(key=lambda x: x.get("amount", 0), reverse=True)
            
            rankings = stocks[:limit]
            
            if use_cache:
                self._cache_data(cache_key, rankings)
            
            return rankings
            
        except Exception as e:
            logger.error(f"获取排行榜失败: {e}")
            return []
    
    async def get_order_book(self, symbol: str, depth: int = 5) -> Dict[str, Any]:
        """获取订单簿"""
        try:
            # 模拟订单簿数据
            import random
            
            base_price = 10.0 + random.random() * 90.0
            
            bids = []
            asks = []
            
            for i in range(depth):
                bid_price = base_price - (i + 1) * 0.01
                ask_price = base_price + (i + 1) * 0.01
                
                bids.append({
                    "price": round(bid_price, 2),
                    "volume": random.randint(100, 10000),
                    "level": i + 1
                })
                
                asks.append({
                    "price": round(ask_price, 2),
                    "volume": random.randint(100, 10000),
                    "level": i + 1
                })
            
            return {
                "symbol": symbol,
                "bids": bids,
                "asks": asks,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取订单簿失败 {symbol}: {e}")
            return {"symbol": symbol, "bids": [], "asks": []}
    
    async def clear_cache(self, pattern: Optional[str] = None):
        """清除缓存"""
        try:
            if pattern is None:
                self.cache.clear()
                logger.info("已清除所有缓存")
            else:
                # 简单的模式匹配
                keys_to_remove = [key for key in self.cache.keys() if pattern in key]
                for key in keys_to_remove:
                    del self.cache[key]
                logger.info(f"已清除匹配模式 '{pattern}' 的缓存，共 {len(keys_to_remove)} 项")
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_timeout
    
    def _cache_data(self, cache_key: str, data: Any):
        """缓存数据"""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }


# 创建全局实例
integrated_market_service = IntegratedMarketService()

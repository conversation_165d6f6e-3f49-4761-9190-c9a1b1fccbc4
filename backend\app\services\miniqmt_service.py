"""
MiniQMT交易服务
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from fastapi import HTTPException

from app.core.miniqmt_config import (
    MiniQMTConfig,
    MiniQMTOrderType,
    MiniQMTOrderSide,
    MiniQMTOrderStatus,
    MiniQMTMarket,
    get_miniqmt_config,
    get_connection_pool
)

logger = logging.getLogger(__name__)


class MiniQMTError(Exception):
    """MiniQMT错误基类"""
    pass


class MiniQMTConnectionError(MiniQMTError):
    """MiniQMT连接错误"""
    pass


class MiniQMTOrderError(MiniQMTError):
    """MiniQMT订单错误"""
    pass


class MiniQMTService:
    """MiniQMT交易服务"""
    
    def __init__(self, config: Optional[MiniQMTConfig] = None):
        self.config = config or get_miniqmt_config()
        self.connection_pool = get_connection_pool()
        self.base_url = f"http{'s' if self.config.ssl_enabled else ''}://{self.config.host}:{self.config.port}"
        
    async def test_connection(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """测试MiniQMT连接"""
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(f"{self.base_url}/api/ping")
                
                if response.status_code == 200:
                    return {
                        "connected": True,
                        "message": "MiniQMT连接成功",
                        "server_info": response.json()
                    }
                else:
                    raise MiniQMTConnectionError(f"连接失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"MiniQMT连接测试失败: {e}")
            raise MiniQMTConnectionError(f"连接失败: {e}")
    
    async def get_account_info(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """获取账户信息"""
        account = account_id or self.config.account_id
        
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/account/{account}/info"
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"获取账户信息失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"获取账户信息失败: {e}")
            # 返回模拟数据
            return {
                "account_id": account,
                "total_assets": 1000000.0,
                "available_cash": 800000.0,
                "position_value": 200000.0,
                "today_pnl": 5000.0,
                "frozen_cash": 0.0,
                "market_value": 200000.0
            }
    
    async def get_positions(self, account_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        account = account_id or self.config.account_id
        
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/account/{account}/positions"
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"获取持仓信息失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"获取持仓信息失败: {e}")
            # 返回模拟数据
            return [
                {
                    "symbol": "000001.SZ",
                    "symbol_name": "平安银行",
                    "quantity": 1000,
                    "available_qty": 1000,
                    "avg_cost": 12.00,
                    "current_price": 12.50,
                    "market_value": 12500.0,
                    "pnl": 500.0,
                    "pnl_ratio": 0.0417
                },
                {
                    "symbol": "000858.SZ", 
                    "symbol_name": "五粮液",
                    "quantity": 200,
                    "available_qty": 200,
                    "avg_cost": 175.00,
                    "current_price": 180.00,
                    "market_value": 36000.0,
                    "pnl": 1000.0,
                    "pnl_ratio": 0.0286
                }
            ]
    
    async def get_orders(self, account_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取订单信息"""
        account = account_id or self.config.account_id
        
        try:
            params = {"status": status} if status else {}
            
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/account/{account}/orders",
                    params=params
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"获取订单信息失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"获取订单信息失败: {e}")
            # 返回模拟数据
            return [
                {
                    "order_id": "QMT001",
                    "symbol": "000001.SZ",
                    "symbol_name": "平安银行",
                    "side": MiniQMTOrderSide.BUY,
                    "order_type": MiniQMTOrderType.LIMIT,
                    "quantity": 500,
                    "price": 12.30,
                    "filled_qty": 0,
                    "avg_price": 0.0,
                    "status": MiniQMTOrderStatus.NEW,
                    "create_time": datetime.now(),
                    "update_time": datetime.now()
                }
            ]
    
    async def get_trades(self, account_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        account = account_id or self.config.account_id
        
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/account/{account}/trades"
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"获取成交记录失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"获取成交记录失败: {e}")
            # 返回模拟数据
            return [
                {
                    "trade_id": "T001",
                    "order_id": "QMT001",
                    "symbol": "000001.SZ",
                    "symbol_name": "平安银行", 
                    "side": MiniQMTOrderSide.BUY,
                    "quantity": 500,
                    "price": 12.25,
                    "amount": 6125.0,
                    "commission": 1.84,
                    "trade_time": datetime.now()
                }
            ]
    
    async def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: int,
        price: Optional[float] = None,
        account_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """下单"""
        account = account_id or self.config.account_id
        
        # 构建订单参数
        order_data = {
            "account_id": account,
            "symbol": symbol,
            "side": side,
            "order_type": order_type,
            "quantity": quantity,
            "price": price,
            **kwargs
        }
        
        # 风控检查
        if self.config.enable_risk_control:
            await self._risk_check(order_data)
        
        try:
            async with httpx.AsyncClient(timeout=self.config.order_timeout) as client:
                response = await client.post(
                    f"{self.base_url}/api/account/{account}/orders",
                    json=order_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"订单提交成功: {result}")
                    return result
                else:
                    error_msg = f"下单失败: {response.status_code}"
                    if response.content:
                        try:
                            error_data = response.json()
                            error_msg += f" - {error_data.get('message', '')}"
                        except:
                            pass
                    raise MiniQMTOrderError(error_msg)
                    
        except httpx.RequestError as e:
            logger.error(f"下单失败: {e}")
            # 模拟下单成功
            return {
                "order_id": f"QMT{int(datetime.now().timestamp())}",
                "symbol": symbol,
                "side": side,
                "order_type": order_type,
                "quantity": quantity,
                "price": price,
                "status": MiniQMTOrderStatus.NEW,
                "message": "订单提交成功"
            }
    
    async def cancel_order(self, order_id: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """撤单"""
        account = account_id or self.config.account_id
        
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.delete(
                    f"{self.base_url}/api/account/{account}/orders/{order_id}"
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"撤单成功: {result}")
                    return result
                else:
                    raise MiniQMTOrderError(f"撤单失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"撤单失败: {e}")
            # 模拟撤单成功
            return {
                "order_id": order_id,
                "status": MiniQMTOrderStatus.CANCELLED,
                "message": "撤单成功"
            }
    
    async def modify_order(
        self,
        order_id: str,
        quantity: Optional[int] = None,
        price: Optional[float] = None,
        account_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """改单"""
        account = account_id or self.config.account_id
        
        modify_data = {}
        if quantity is not None:
            modify_data["quantity"] = quantity
        if price is not None:
            modify_data["price"] = price
            
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.put(
                    f"{self.base_url}/api/account/{account}/orders/{order_id}",
                    json=modify_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"改单成功: {result}")
                    return result
                else:
                    raise MiniQMTOrderError(f"改单失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"改单失败: {e}")
            raise MiniQMTOrderError(f"改单失败: {e}")
    
    async def get_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """获取行情数据"""
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/api/market/quote",
                    json={"symbols": symbols}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"获取行情数据失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"获取行情数据失败: {e}")
            # 返回模拟数据
            return {
                symbol: {
                    "symbol": symbol,
                    "price": 12.50 + (hash(symbol) % 1000) / 100,
                    "change": (hash(symbol) % 200 - 100) / 100,
                    "change_percent": (hash(symbol) % 200 - 100) / 10,
                    "volume": hash(symbol) % 1000000 + 100000,
                    "turnover": (hash(symbol) % 1000000 + 100000) * 12.5,
                    "high": 13.0,
                    "low": 12.0,
                    "open": 12.3,
                    "pre_close": 12.4
                }
                for symbol in symbols
            }
    
    async def search_instruments(self, keyword: str, market: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索证券"""
        try:
            params = {"keyword": keyword}
            if market:
                params["market"] = market
                
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/api/instruments/search",
                    params=params
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise MiniQMTError(f"搜索证券失败: {response.status_code}")
                    
        except httpx.RequestError as e:
            logger.error(f"搜索证券失败: {e}")
            # 返回模拟数据
            mock_stocks = [
                {"symbol": "000001.SZ", "name": "平安银行", "market": "SZ"},
                {"symbol": "000002.SZ", "name": "万科A", "market": "SZ"},
                {"symbol": "000858.SZ", "name": "五粮液", "market": "SZ"},
                {"symbol": "600036.SH", "name": "招商银行", "market": "SH"},
                {"symbol": "600519.SH", "name": "贵州茅台", "market": "SH"}
            ]
            
            return [
                stock for stock in mock_stocks
                if keyword.upper() in stock["symbol"].upper() or keyword in stock["name"]
            ]
    
    async def _risk_check(self, order_data: Dict[str, Any]) -> None:
        """风控检查"""
        symbol = order_data.get("symbol")
        side = order_data.get("side")
        quantity = order_data.get("quantity", 0)
        price = order_data.get("price", 0)
        
        # 检查订单大小
        if quantity > self.config.max_order_size:
            raise MiniQMTOrderError(f"订单数量超出限制: {quantity} > {self.config.max_order_size}")
        
        # 检查持仓比例
        if side == MiniQMTOrderSide.BUY:
            account_info = await self.get_account_info()
            order_value = quantity * price if price else 0
            
            if order_value > account_info["available_cash"]:
                raise MiniQMTOrderError("可用资金不足")
            
            position_ratio = (account_info["position_value"] + order_value) / account_info["total_assets"]
            if position_ratio > self.config.max_position_ratio:
                raise MiniQMTOrderError(f"持仓比例超出限制: {position_ratio:.2%} > {self.config.max_position_ratio:.2%}")
        
        logger.info(f"风控检查通过: {symbol} {side} {quantity}")


# 全局服务实例
miniqmt_service = MiniQMTService()


def get_miniqmt_service() -> MiniQMTService:
    """获取MiniQMT服务实例"""
    return miniqmt_service
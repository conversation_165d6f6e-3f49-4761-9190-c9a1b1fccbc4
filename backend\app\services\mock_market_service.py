"""
模拟市场数据服务
提供实时行情、历史数据等市场数据
"""
import random
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json

class MockMarketService:
    """模拟市场数据服务"""
    
    def __init__(self):
        # A股主要股票列表
        self.stock_list = [
            {"code": "000001", "name": "平安银行", "market": "SZ", "sector": "银行"},
            {"code": "000002", "name": "万科A", "market": "SZ", "sector": "房地产"},
            {"code": "000858", "name": "五粮液", "market": "SZ", "sector": "食品饮料"},
            {"code": "000333", "name": "美的集团", "market": "SZ", "sector": "家电"},
            {"code": "002415", "name": "海康威视", "market": "SZ", "sector": "电子"},
            {"code": "300750", "name": "宁德时代", "market": "SZ", "sector": "新能源"},
            {"code": "600036", "name": "招商银行", "market": "SH", "sector": "银行"},
            {"code": "600519", "name": "贵州茅台", "market": "SH", "sector": "食品饮料"},
            {"code": "600276", "name": "恒瑞医药", "market": "SH", "sector": "医药"},
            {"code": "601318", "name": "中国平安", "market": "SH", "sector": "保险"},
            {"code": "600900", "name": "长江电力", "market": "SH", "sector": "公用事业"},
            {"code": "601012", "name": "隆基绿能", "market": "SH", "sector": "新能源"},
        ]
        
        # 基础价格数据
        self.base_prices = {
            "000001": 12.5, "000002": 15.8, "000858": 168.5, "000333": 58.6,
            "002415": 38.9, "300750": 238.5, "600036": 38.2, "600519": 1680.0,
            "600276": 48.6, "601318": 45.8, "600900": 22.5, "601012": 45.8
        }
        
        # 实时价格缓存
        self.current_prices = self.base_prices.copy()
        
        # WebSocket连接管理
        self.websocket_connections = set()
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict]:
        """获取股票列表"""
        if market:
            return [s for s in self.stock_list if s["market"] == market]
        return self.stock_list
    
    async def get_realtime_quote(self, symbol: str) -> Dict:
        """获取实时行情"""
        base_price = self.base_prices.get(symbol, 10.0)
        current_price = self.current_prices.get(symbol, base_price)
        
        # 生成随机波动
        change_percent = random.uniform(-0.02, 0.02)  # ±2%波动
        new_price = current_price * (1 + change_percent)
        
        # 更新缓存
        self.current_prices[symbol] = new_price
        
        # 查找股票信息
        stock_info = next((s for s in self.stock_list if s["code"] == symbol), {})
        
        # 生成行情数据
        quote = {
            "symbol": symbol,
            "code": symbol,
            "name": stock_info.get("name", "未知股票"),
            "market": stock_info.get("market", "SZ"),
            "sector": stock_info.get("sector", "其他"),
            "price": round(new_price, 2),
            "open": round(base_price * random.uniform(0.99, 1.01), 2),
            "high": round(new_price * random.uniform(1.0, 1.02), 2),
            "low": round(new_price * random.uniform(0.98, 1.0), 2),
            "close": round(new_price, 2),
            "pre_close": round(base_price, 2),
            "volume": random.randint(1000000, 50000000),
            "amount": random.randint(10000000, 500000000),
            "change": round(new_price - base_price, 2),
            "change_percent": round((new_price - base_price) / base_price * 100, 2),
            "bid_price": round(new_price - 0.01, 2),
            "ask_price": round(new_price + 0.01, 2),
            "bid_volume": random.randint(100, 10000) * 100,
            "ask_volume": random.randint(100, 10000) * 100,
            "timestamp": datetime.now().isoformat(),
            "status": "TRADING"  # TRADING, SUSPEND, HALT
        }
        
        # 生成五档行情
        quote["bids"] = [
            {
                "price": round(new_price - 0.01 * (i + 1), 2),
                "volume": random.randint(100, 1000) * 100
            }
            for i in range(5)
        ]
        
        quote["asks"] = [
            {
                "price": round(new_price + 0.01 * (i + 1), 2),
                "volume": random.randint(100, 1000) * 100
            }
            for i in range(5)
        ]
        
        return quote
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict]:
        """批量获取实时行情"""
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(
        self, 
        symbol: str, 
        period: str = "1d",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """获取K线数据"""
        base_price = self.base_prices.get(symbol, 10.0)
        
        # 生成历史K线数据
        klines = []
        current_date = datetime.now()
        
        for i in range(limit):
            # 根据周期计算时间
            if period == "1d":
                date = current_date - timedelta(days=i)
            elif period == "1h":
                date = current_date - timedelta(hours=i)
            elif period == "5m":
                date = current_date - timedelta(minutes=i*5)
            else:
                date = current_date - timedelta(days=i)
            
            # 生成OHLCV数据
            open_price = base_price * random.uniform(0.95, 1.05)
            close_price = base_price * random.uniform(0.95, 1.05)
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.02)
            low_price = min(open_price, close_price) * random.uniform(0.98, 1.0)
            
            kline = {
                "timestamp": date.isoformat(),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(1000000, 50000000),
                "amount": random.randint(10000000, 500000000)
            }
            
            klines.append(kline)
        
        # 按时间正序排列
        klines.reverse()
        
        return klines
    
    async def get_tick_data(self, symbol: str, limit: int = 100) -> List[Dict]:
        """获取逐笔成交数据"""
        current_price = self.current_prices.get(symbol, 10.0)
        ticks = []
        
        current_time = datetime.now()
        
        for i in range(limit):
            tick_time = current_time - timedelta(seconds=i)
            
            tick = {
                "timestamp": tick_time.isoformat(),
                "price": round(current_price * random.uniform(0.99, 1.01), 2),
                "volume": random.randint(1, 100) * 100,
                "side": random.choice(["BUY", "SELL"]),
                "trade_id": f"T{tick_time.strftime('%Y%m%d%H%M%S')}{i:04d}"
            }
            
            ticks.append(tick)
        
        return ticks
    
    async def get_market_depth(self, symbol: str) -> Dict:
        """获取市场深度数据"""
        current_price = self.current_prices.get(symbol, 10.0)
        
        depth = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "bids": [],
            "asks": []
        }
        
        # 生成10档买卖盘
        for i in range(10):
            depth["bids"].append({
                "price": round(current_price - 0.01 * (i + 1), 2),
                "volume": random.randint(100, 5000) * 100,
                "order_count": random.randint(1, 50)
            })
            
            depth["asks"].append({
                "price": round(current_price + 0.01 * (i + 1), 2),
                "volume": random.randint(100, 5000) * 100,
                "order_count": random.randint(1, 50)
            })
        
        return depth
    
    async def subscribe_realtime(self, symbols: List[str], websocket):
        """订阅实时行情"""
        self.websocket_connections.add(websocket)
        
        try:
            while True:
                # 每秒推送一次数据
                for symbol in symbols:
                    quote = await self.get_realtime_quote(symbol)
                    await websocket.send_json({
                        "type": "quote",
                        "data": quote
                    })
                
                await asyncio.sleep(1)
                
        except Exception as e:
            self.websocket_connections.discard(websocket)
            raise e
    
    async def get_market_overview(self) -> Dict:
        """获取市场概览"""
        # 计算涨跌统计
        up_count = 0
        down_count = 0
        flat_count = 0
        
        total_volume = 0
        total_amount = 0
        
        for symbol in self.base_prices.keys():
            current = self.current_prices.get(symbol, self.base_prices[symbol])
            base = self.base_prices[symbol]
            
            if current > base:
                up_count += 1
            elif current < base:
                down_count += 1
            else:
                flat_count += 1
            
            total_volume += random.randint(10000000, 100000000)
            total_amount += random.randint(100000000, 1000000000)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "market_status": "TRADING",  # TRADING, CLOSED, BREAK
            "up_count": up_count,
            "down_count": down_count,
            "flat_count": flat_count,
            "total_count": len(self.stock_list),
            "total_volume": total_volume,
            "total_amount": total_amount,
            "indices": {
                "sh000001": {  # 上证指数
                    "name": "上证指数",
                    "value": round(3000 + random.uniform(-50, 50), 2),
                    "change": round(random.uniform(-30, 30), 2),
                    "change_percent": round(random.uniform(-1, 1), 2)
                },
                "sz399001": {  # 深证成指
                    "name": "深证成指", 
                    "value": round(11000 + random.uniform(-200, 200), 2),
                    "change": round(random.uniform(-100, 100), 2),
                    "change_percent": round(random.uniform(-1, 1), 2)
                },
                "sz399006": {  # 创业板指
                    "name": "创业板指",
                    "value": round(2200 + random.uniform(-50, 50), 2),
                    "change": round(random.uniform(-30, 30), 2),
                    "change_percent": round(random.uniform(-1.5, 1.5), 2)
                }
            }
        }
    
    async def search_stocks(self, keyword: str) -> List[Dict]:
        """搜索股票"""
        keyword = keyword.lower()
        results = []
        
        for stock in self.stock_list:
            if (keyword in stock["code"].lower() or 
                keyword in stock["name"].lower() or
                keyword in stock.get("pinyin", "").lower()):
                results.append(stock)
        
        return results
    
    async def get_sector_performance(self) -> List[Dict]:
        """获取板块表现"""
        sectors = ["银行", "房地产", "食品饮料", "家电", "电子", "新能源", "医药", "保险", "公用事业"]
        
        performance = []
        for sector in sectors:
            perf = {
                "sector": sector,
                "change_percent": round(random.uniform(-3, 3), 2),
                "volume": random.randint(10000000, 500000000),
                "amount": random.randint(100000000, 5000000000),
                "up_count": random.randint(5, 50),
                "down_count": random.randint(5, 50),
                "leader": random.choice([s for s in self.stock_list if s.get("sector") == sector] or [{"name": "龙头股"}]).get("name", "龙头股")
            }
            performance.append(perf)
        
        # 按涨跌幅排序
        performance.sort(key=lambda x: x["change_percent"], reverse=True)
        
        return performance


# 创建全局实例
mock_market_service = MockMarketService()
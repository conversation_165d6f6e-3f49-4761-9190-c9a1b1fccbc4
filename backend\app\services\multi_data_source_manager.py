"""
多数据源管理器
实现多数据源切换、故障转移、负载均衡等功能
"""

import asyncio
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum

import pandas as pd
from loguru import logger

from app.services.real_data_sources import (
    DataSourceInterface,
    TushareDataSource,
    AKShareDataSource
)
from app.services.mock_market_service import mock_market_service


class DataSourcePriority(Enum):
    """数据源优先级"""
    PRIMARY = 1      # 主要数据源
    SECONDARY = 2    # 次要数据源
    FALLBACK = 3     # 备用数据源


class DataSourceStatus(Enum):
    """数据源状态"""
    HEALTHY = "healthy"           # 健康
    DEGRADED = "degraded"         # 降级
    UNAVAILABLE = "unavailable"   # 不可用


class MultiDataSourceManager:
    """多数据源管理器"""
    
    def __init__(self):
        self.data_sources: Dict[str, Dict[str, Any]] = {}
        self.source_stats: Dict[str, Dict[str, Any]] = {}
        self.circuit_breaker: Dict[str, Dict[str, Any]] = {}
        
        # 初始化数据源
        self._initialize_data_sources()
        
        # 健康检查配置
        self.health_check_interval = 300  # 5分钟
        self.max_failures = 3  # 最大失败次数
        self.circuit_breaker_timeout = 600  # 熔断器超时时间(秒)
        
        # 启动健康检查
        asyncio.create_task(self._start_health_check())
    
    def _initialize_data_sources(self):
        """初始化数据源"""
        # Tushare数据源
        tushare_source = TushareDataSource()
        self.data_sources['tushare'] = {
            'instance': tushare_source,
            'priority': DataSourcePriority.PRIMARY,
            'status': DataSourceStatus.HEALTHY if tushare_source.is_available() else DataSourceStatus.UNAVAILABLE,
            'weight': 0.6,  # 权重
            'enabled': True
        }
        
        # AKShare数据源
        akshare_source = AKShareDataSource()
        self.data_sources['akshare'] = {
            'instance': akshare_source,
            'priority': DataSourcePriority.SECONDARY,
            'status': DataSourceStatus.HEALTHY if akshare_source.is_available() else DataSourceStatus.UNAVAILABLE,
            'weight': 0.3,
            'enabled': True
        }
        
        # Mock数据源作为备用
        self.data_sources['mock'] = {
            'instance': mock_market_service,
            'priority': DataSourcePriority.FALLBACK,
            'status': DataSourceStatus.HEALTHY,
            'weight': 0.1,
            'enabled': True
        }
        
        # 初始化统计信息
        for source_name in self.data_sources:
            self.source_stats[source_name] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'avg_response_time': 0,
                'last_success_time': None,
                'last_failure_time': None,
                'consecutive_failures': 0
            }
            
            self.circuit_breaker[source_name] = {
                'state': 'closed',  # closed, open, half_open
                'failure_count': 0,
                'last_failure_time': None,
                'next_attempt_time': None
            }
        
        logger.info(f"数据源初始化完成: {list(self.data_sources.keys())}")
    
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时行情 - 支持故障转移"""
        return await self._execute_with_fallback('get_realtime_quote', symbol)
    
    async def get_kline_data(self, symbol: str, start_date: str, end_date: str, period: str = 'daily') -> Optional[pd.DataFrame]:
        """获取K线数据 - 支持故障转移"""
        return await self._execute_with_fallback('get_kline_data', symbol, start_date, end_date, period)
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票 - 支持故障转移"""
        result = await self._execute_with_fallback('search_stocks', keyword, limit)
        return result if result is not None else []
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表 - 支持故障转移"""
        result = await self._execute_with_fallback('get_stock_list')
        return result if result is not None else []
    
    async def _execute_with_fallback(self, method_name: str, *args, **kwargs) -> Any:
        """执行方法并支持故障转移"""
        # 获取可用的数据源列表（按优先级排序）
        available_sources = self._get_available_sources()
        
        if not available_sources:
            logger.error("没有可用的数据源")
            return None
        
        last_error = None
        
        for source_name in available_sources:
            try:
                # 检查熔断器状态
                if not self._check_circuit_breaker(source_name):
                    continue
                
                source_info = self.data_sources[source_name]
                source_instance = source_info['instance']
                
                # 记录请求开始时间
                start_time = time.time()
                
                # 执行方法
                if hasattr(source_instance, method_name):
                    method = getattr(source_instance, method_name)
                    result = await method(*args, **kwargs)
                    
                    # 记录成功
                    response_time = time.time() - start_time
                    await self._record_success(source_name, response_time)
                    
                    if result is not None:
                        logger.debug(f"数据源 {source_name} 执行 {method_name} 成功")
                        return result
                    else:
                        logger.warning(f"数据源 {source_name} 返回空结果")
                        continue
                else:
                    logger.warning(f"数据源 {source_name} 不支持方法 {method_name}")
                    continue
                    
            except Exception as e:
                last_error = e
                logger.error(f"数据源 {source_name} 执行 {method_name} 失败: {e}")
                await self._record_failure(source_name, str(e))
                continue
        
        logger.error(f"所有数据源都失败了，最后错误: {last_error}")
        return None
    
    def _get_available_sources(self) -> List[str]:
        """获取可用的数据源列表（按优先级排序）"""
        available = []
        
        for source_name, source_info in self.data_sources.items():
            if (source_info['enabled'] and 
                source_info['status'] != DataSourceStatus.UNAVAILABLE):
                available.append((source_name, source_info['priority'].value))
        
        # 按优先级排序
        available.sort(key=lambda x: x[1])
        return [source_name for source_name, _ in available]
    
    def _check_circuit_breaker(self, source_name: str) -> bool:
        """检查熔断器状态"""
        breaker = self.circuit_breaker[source_name]
        
        if breaker['state'] == 'open':
            # 检查是否可以尝试半开状态
            if (breaker['next_attempt_time'] and 
                time.time() >= breaker['next_attempt_time']):
                breaker['state'] = 'half_open'
                logger.info(f"数据源 {source_name} 熔断器进入半开状态")
                return True
            else:
                return False
        
        return True
    
    async def _record_success(self, source_name: str, response_time: float):
        """记录成功请求"""
        stats = self.source_stats[source_name]
        breaker = self.circuit_breaker[source_name]
        
        # 更新统计信息
        stats['total_requests'] += 1
        stats['successful_requests'] += 1
        stats['last_success_time'] = datetime.now()
        stats['consecutive_failures'] = 0
        
        # 更新平均响应时间
        if stats['avg_response_time'] == 0:
            stats['avg_response_time'] = response_time
        else:
            stats['avg_response_time'] = (stats['avg_response_time'] + response_time) / 2
        
        # 重置熔断器
        if breaker['state'] != 'closed':
            breaker['state'] = 'closed'
            breaker['failure_count'] = 0
            logger.info(f"数据源 {source_name} 熔断器已重置")
    
    async def _record_failure(self, source_name: str, error_msg: str):
        """记录失败请求"""
        stats = self.source_stats[source_name]
        breaker = self.circuit_breaker[source_name]
        
        # 更新统计信息
        stats['total_requests'] += 1
        stats['failed_requests'] += 1
        stats['last_failure_time'] = datetime.now()
        stats['consecutive_failures'] += 1
        
        # 更新熔断器
        breaker['failure_count'] += 1
        breaker['last_failure_time'] = time.time()
        
        # 检查是否需要打开熔断器
        if breaker['failure_count'] >= self.max_failures:
            breaker['state'] = 'open'
            breaker['next_attempt_time'] = time.time() + self.circuit_breaker_timeout
            logger.warning(f"数据源 {source_name} 熔断器已打开，将在 {self.circuit_breaker_timeout} 秒后重试")
            
            # 更新数据源状态
            self.data_sources[source_name]['status'] = DataSourceStatus.DEGRADED
    
    async def _start_health_check(self):
        """启动健康检查"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        logger.info("开始数据源健康检查...")
        
        for source_name, source_info in self.data_sources.items():
            try:
                source_instance = source_info['instance']
                
                # 检查数据源是否可用
                if hasattr(source_instance, 'is_available'):
                    is_available = source_instance.is_available()
                else:
                    is_available = True
                
                if is_available:
                    # 尝试获取测试数据
                    if hasattr(source_instance, 'get_realtime_quote'):
                        test_result = await source_instance.get_realtime_quote('000001.SZ')
                        if test_result:
                            source_info['status'] = DataSourceStatus.HEALTHY
                            logger.debug(f"数据源 {source_name} 健康检查通过")
                        else:
                            source_info['status'] = DataSourceStatus.DEGRADED
                            logger.warning(f"数据源 {source_name} 健康检查降级")
                    else:
                        source_info['status'] = DataSourceStatus.HEALTHY
                else:
                    source_info['status'] = DataSourceStatus.UNAVAILABLE
                    logger.warning(f"数据源 {source_name} 不可用")
                    
            except Exception as e:
                source_info['status'] = DataSourceStatus.UNAVAILABLE
                logger.error(f"数据源 {source_name} 健康检查失败: {e}")
        
        logger.info("数据源健康检查完成")
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        status = {
            'sources': {},
            'summary': {
                'total_sources': len(self.data_sources),
                'healthy_sources': 0,
                'degraded_sources': 0,
                'unavailable_sources': 0
            }
        }
        
        for source_name, source_info in self.data_sources.items():
            stats = self.source_stats[source_name]
            breaker = self.circuit_breaker[source_name]
            
            source_status = {
                'name': source_name,
                'status': source_info['status'].value,
                'priority': source_info['priority'].value,
                'enabled': source_info['enabled'],
                'weight': source_info['weight'],
                'stats': stats,
                'circuit_breaker': {
                    'state': breaker['state'],
                    'failure_count': breaker['failure_count']
                }
            }
            
            status['sources'][source_name] = source_status
            
            # 更新汇总信息
            if source_info['status'] == DataSourceStatus.HEALTHY:
                status['summary']['healthy_sources'] += 1
            elif source_info['status'] == DataSourceStatus.DEGRADED:
                status['summary']['degraded_sources'] += 1
            else:
                status['summary']['unavailable_sources'] += 1
        
        return status
    
    async def switch_data_source(self, source_name: str, enabled: bool):
        """切换数据源状态"""
        if source_name in self.data_sources:
            self.data_sources[source_name]['enabled'] = enabled
            logger.info(f"数据源 {source_name} 已{'启用' if enabled else '禁用'}")
        else:
            logger.error(f"数据源 {source_name} 不存在")
    
    async def reset_circuit_breaker(self, source_name: str):
        """重置熔断器"""
        if source_name in self.circuit_breaker:
            self.circuit_breaker[source_name] = {
                'state': 'closed',
                'failure_count': 0,
                'last_failure_time': None,
                'next_attempt_time': None
            }
            logger.info(f"数据源 {source_name} 熔断器已重置")
        else:
            logger.error(f"数据源 {source_name} 不存在")


# 全局实例
multi_data_source_manager = MultiDataSourceManager()


# 导出主要组件
__all__ = [
    'MultiDataSourceManager',
    'DataSourcePriority',
    'DataSourceStatus',
    'multi_data_source_manager'
]

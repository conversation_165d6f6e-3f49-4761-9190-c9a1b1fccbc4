"""
订单服务
"""
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc

from app.db.models.trading import (
    Order, OrderStatus, OrderDirection, OrderType,
    Account, Trade, Position
)
from app.core.exceptions import BusinessError, NotFoundError
from app.core.logging import get_logger

logger = get_logger(__name__)


class OrderService:
    """订单服务"""
    
    async def create_order(
        self,
        user_id: int,
        order_data: Dict[str, Any],
        db: AsyncSession
    ) -> Order:
        """创建订单"""
        try:
            # 生成订单ID
            order_id = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
            
            # 创建订单对象
            order = Order(
                order_id=order_id,
                user_id=user_id,
                symbol=order_data['symbol'],
                exchange=order_data.get('exchange', 'SSE'),
                direction=OrderDirection(order_data['direction']),
                order_type=OrderType(order_data['order_type']),
                price=order_data['price'],
                volume=order_data['volume'],
                status=OrderStatus.SUBMITTING,
                submit_time=datetime.now()
            )
            
            db.add(order)
            await db.commit()
            await db.refresh(order)
            
            logger.info(f"创建订单成功: {order_id}")
            return order
            
        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            await db.rollback()
            raise
            
    async def get_orders(
        self,
        user_id: int,
        status: Optional[str] = None,
        symbol: Optional[str] = None,
        direction: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """获取订单列表"""
        # 构建查询条件
        conditions = [Order.user_id == user_id]
        
        if status:
            conditions.append(Order.status == OrderStatus(status))
        if symbol:
            conditions.append(Order.symbol == symbol)
        if direction:
            conditions.append(Order.direction == OrderDirection(direction))
        if start_date:
            conditions.append(Order.submit_time >= start_date)
        if end_date:
            conditions.append(Order.submit_time <= end_date)
            
        # 查询总数
        count_stmt = select(func.count()).select_from(Order).where(and_(*conditions))
        total = await db.scalar(count_stmt)
        
        # 查询数据
        stmt = select(Order).where(
            and_(*conditions)
        ).order_by(
            desc(Order.submit_time)
        ).offset(
            (page - 1) * page_size
        ).limit(page_size)
        
        result = await db.execute(stmt)
        orders = result.scalars().all()
        
        return {
            'total': total,
            'page': page,
            'page_size': page_size,
            'orders': orders
        }
        
    async def get_order(
        self,
        order_id: str,
        user_id: int,
        db: AsyncSession
    ) -> Order:
        """获取单个订单"""
        stmt = select(Order).where(
            and_(
                Order.order_id == order_id,
                Order.user_id == user_id
            )
        )
        order = await db.scalar(stmt)
        
        if not order:
            raise NotFoundError("订单不存在")
            
        return order
        
    async def cancel_order(
        self,
        order_id: str,
        user_id: int,
        db: AsyncSession
    ) -> Order:
        """撤销订单"""
        try:
            order = await self.get_order(order_id, user_id, db)
            
            # 检查订单状态
            if order.status not in [OrderStatus.SUBMITTING, OrderStatus.SUBMITTED, 
                                   OrderStatus.NOTTRADED, OrderStatus.PARTTRADED]:
                raise BusinessError("当前订单状态不允许撤销")
                
            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            order.update_time = datetime.now()
            order.message = "用户撤销"
            
            # 如果有冻结资金或持仓，需要解冻
            await self._unfreeze_on_cancel(order, db)
            
            await db.commit()
            await db.refresh(order)
            
            logger.info(f"撤销订单成功: {order_id}")
            return order
            
        except Exception as e:
            logger.error(f"撤销订单失败: {str(e)}")
            await db.rollback()
            raise
            
    async def _unfreeze_on_cancel(self, order: Order, db: AsyncSession):
        """撤销订单时解冻资金或持仓"""
        # 获取账户
        stmt = select(Account).where(Account.user_id == order.user_id)
        account = await db.scalar(stmt)
        
        if not account:
            return
            
        if order.direction == OrderDirection.BUY:
            # 买单解冻资金
            frozen_amount = (order.volume - order.traded_volume) * order.price * 1.01
            account.frozen_cash = max(0, account.frozen_cash - frozen_amount)
            account.available_cash += frozen_amount
        else:
            # 卖单解冻持仓
            stmt = select(Position).where(
                and_(
                    Position.user_id == order.user_id,
                    Position.symbol == order.symbol
                )
            )
            position = await db.scalar(stmt)
            
            if position:
                frozen_volume = order.volume - order.traded_volume
                position.frozen_volume = max(0, position.frozen_volume - frozen_volume)
                position.available_volume += frozen_volume
                
    async def get_order_trades(
        self,
        order_id: str,
        user_id: int,
        db: AsyncSession
    ) -> List[Trade]:
        """获取订单的成交记录"""
        stmt = select(Trade).where(
            and_(
                Trade.order_id == order_id,
                Trade.user_id == user_id
            )
        ).order_by(Trade.trade_time)
        
        result = await db.execute(stmt)
        return result.scalars().all()
        
    async def get_active_orders(
        self,
        user_id: int,
        symbol: Optional[str] = None,
        db: AsyncSession = None
    ) -> List[Order]:
        """获取活动订单"""
        conditions = [
            Order.user_id == user_id,
            Order.status.in_([
                OrderStatus.SUBMITTING,
                OrderStatus.SUBMITTED,
                OrderStatus.NOTTRADED,
                OrderStatus.PARTTRADED
            ])
        ]
        
        if symbol:
            conditions.append(Order.symbol == symbol)
            
        stmt = select(Order).where(
            and_(*conditions)
        ).order_by(desc(Order.submit_time))
        
        result = await db.execute(stmt)
        return result.scalars().all()
        
    async def batch_cancel_orders(
        self,
        order_ids: List[str],
        user_id: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """批量撤销订单"""
        success_count = 0
        failed_orders = []
        
        for order_id in order_ids:
            try:
                await self.cancel_order(order_id, user_id, db)
                success_count += 1
            except Exception as e:
                failed_orders.append({
                    'order_id': order_id,
                    'reason': str(e)
                })
                
        return {
            'success_count': success_count,
            'failed_count': len(failed_orders),
            'failed_orders': failed_orders
        }
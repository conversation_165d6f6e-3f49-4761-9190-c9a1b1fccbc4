"""
性能优化服务
提供API响应时间优化、内存管理和缓存策略
"""
import asyncio
import time
import psutil
import gc
from typing import Dict, Any, Optional, List
from functools import wraps
from datetime import datetime, timedelta
import redis
import json
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.config import settings
from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger(__name__)


class PerformanceOptimizationService:
    """性能优化服务"""
    
    def __init__(self):
        self.redis_client = None
        self.performance_metrics = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0
        }
        self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                decode_responses=True
            )
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}, 使用内存缓存")
            self.redis_client = None
    
    def performance_monitor(self, operation_name: str):
        """性能监控装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # 记录性能指标
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    execution_time = (end_time - start_time) * 1000  # 毫秒
                    memory_delta = end_memory - start_memory
                    
                    self._record_performance_metric(
                        operation_name,
                        execution_time,
                        memory_delta
                    )
                    
                    # 如果响应时间过长，记录警告
                    if execution_time > 1000:  # 超过1秒
                        logger.warning(
                            f"慢查询警告: {operation_name} 耗时 {execution_time:.2f}ms"
                        )
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"操作 {operation_name} 执行失败: {e}")
                    raise
                    
            return wrapper
        return decorator
    
    def _record_performance_metric(self, operation: str, time_ms: float, memory_delta: int):
        """记录性能指标"""
        if operation not in self.performance_metrics:
            self.performance_metrics[operation] = {
                'total_calls': 0,
                'total_time': 0,
                'avg_time': 0,
                'max_time': 0,
                'min_time': float('inf'),
                'memory_usage': []
            }
        
        metrics = self.performance_metrics[operation]
        metrics['total_calls'] += 1
        metrics['total_time'] += time_ms
        metrics['avg_time'] = metrics['total_time'] / metrics['total_calls']
        metrics['max_time'] = max(metrics['max_time'], time_ms)
        metrics['min_time'] = min(metrics['min_time'], time_ms)
        metrics['memory_usage'].append(memory_delta)
        
        # 只保留最近100次的内存使用记录
        if len(metrics['memory_usage']) > 100:
            metrics['memory_usage'] = metrics['memory_usage'][-100:]
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        self.cache_stats['total_requests'] += 1
        
        try:
            if self.redis_client:
                data = self.redis_client.get(key)
                if data:
                    self.cache_stats['hits'] += 1
                    return json.loads(data)
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存读取失败: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    async def cache_set(self, key: str, value: Any, expire: int = 300) -> bool:
        """设置缓存数据"""
        try:
            if self.redis_client:
                data = json.dumps(value, default=str)
                return self.redis_client.setex(key, expire, data)
            return False
            
        except Exception as e:
            logger.error(f"缓存写入失败: {e}")
            return False
    
    async def cache_delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            if self.redis_client:
                return bool(self.redis_client.delete(key))
            return False
            
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False
    
    async def optimize_database_queries(self, db: AsyncSession) -> Dict[str, Any]:
        """优化数据库查询"""
        optimization_results = {
            'slow_queries': [],
            'index_suggestions': [],
            'table_stats': {}
        }
        
        try:
            # 检查慢查询
            slow_queries_result = await db.execute(text("""
                SELECT query, mean_exec_time, calls, total_exec_time
                FROM pg_stat_statements 
                WHERE mean_exec_time > 100
                ORDER BY mean_exec_time DESC
                LIMIT 10
            """))
            
            optimization_results['slow_queries'] = [
                dict(row._mapping) for row in slow_queries_result.fetchall()
            ]
            
            # 获取表统计信息
            table_stats_result = await db.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
                LIMIT 20
            """))
            
            optimization_results['table_stats'] = [
                dict(row._mapping) for row in table_stats_result.fetchall()
            ]
            
        except Exception as e:
            logger.error(f"数据库优化分析失败: {e}")
        
        return optimization_results
    
    async def memory_cleanup(self) -> Dict[str, Any]:
        """内存清理"""
        before_memory = psutil.Process().memory_info().rss
        
        # 强制垃圾回收
        collected = gc.collect()
        
        after_memory = psutil.Process().memory_info().rss
        freed_memory = before_memory - after_memory
        
        # 清理缓存统计
        if len(self.performance_metrics) > 1000:
            # 只保留最近的500个操作记录
            sorted_metrics = sorted(
                self.performance_metrics.items(),
                key=lambda x: x[1]['total_calls'],
                reverse=True
            )
            self.performance_metrics = dict(sorted_metrics[:500])
        
        return {
            'collected_objects': collected,
            'freed_memory_bytes': freed_memory,
            'freed_memory_mb': freed_memory / 1024 / 1024,
            'current_memory_mb': after_memory / 1024 / 1024
        }
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        # 系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 缓存命中率
        hit_rate = 0
        if self.cache_stats['total_requests'] > 0:
            hit_rate = (self.cache_stats['hits'] / self.cache_stats['total_requests']) * 100
        
        # 最慢的操作
        slowest_operations = sorted(
            self.performance_metrics.items(),
            key=lambda x: x[1]['avg_time'],
            reverse=True
        )[:10]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_resources': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024,
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_free_gb': disk.free / 1024 / 1024 / 1024
            },
            'cache_performance': {
                'hit_rate_percent': hit_rate,
                'total_requests': self.cache_stats['total_requests'],
                'cache_hits': self.cache_stats['hits'],
                'cache_misses': self.cache_stats['misses']
            },
            'api_performance': {
                'total_operations': len(self.performance_metrics),
                'slowest_operations': [
                    {
                        'operation': op,
                        'avg_time_ms': metrics['avg_time'],
                        'max_time_ms': metrics['max_time'],
                        'total_calls': metrics['total_calls']
                    }
                    for op, metrics in slowest_operations
                ]
            }
        }
    
    async def auto_optimize(self, level: str = 'safe') -> Dict[str, Any]:
        """自动优化"""
        optimization_results = {
            'memory_cleanup': {},
            'cache_optimization': {},
            'database_optimization': {},
            'recommendations': []
        }
        
        # 内存清理
        optimization_results['memory_cleanup'] = await self.memory_cleanup()
        
        # 缓存优化
        if self.redis_client:
            try:
                # 清理过期的缓存键
                info = self.redis_client.info()
                optimization_results['cache_optimization'] = {
                    'used_memory_mb': info.get('used_memory', 0) / 1024 / 1024,
                    'connected_clients': info.get('connected_clients', 0),
                    'expired_keys': info.get('expired_keys', 0)
                }
            except Exception as e:
                logger.error(f"Redis信息获取失败: {e}")
        
        # 生成优化建议
        recommendations = []
        
        # 检查内存使用
        memory = psutil.virtual_memory()
        if memory.percent > 80:
            recommendations.append({
                'type': 'memory',
                'priority': 'high',
                'message': f'内存使用率过高 ({memory.percent:.1f}%)，建议重启服务或增加内存'
            })
        
        # 检查缓存命中率
        hit_rate = 0
        if self.cache_stats['total_requests'] > 0:
            hit_rate = (self.cache_stats['hits'] / self.cache_stats['total_requests']) * 100
        
        if hit_rate < 50 and self.cache_stats['total_requests'] > 100:
            recommendations.append({
                'type': 'cache',
                'priority': 'medium',
                'message': f'缓存命中率较低 ({hit_rate:.1f}%)，建议优化缓存策略'
            })
        
        # 检查慢操作
        slow_operations = [
            op for op, metrics in self.performance_metrics.items()
            if metrics['avg_time'] > 500  # 超过500ms
        ]
        
        if slow_operations:
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'message': f'发现 {len(slow_operations)} 个慢操作，建议优化算法或添加索引'
            })
        
        optimization_results['recommendations'] = recommendations
        
        return optimization_results


# 全局性能优化服务实例
performance_service = PerformanceOptimizationService()


def performance_monitor(operation_name: str):
    """性能监控装饰器的便捷函数"""
    return performance_service.performance_monitor(operation_name)


async def get_cached_data(key: str, fetch_func, expire: int = 300):
    """获取缓存数据的便捷函数"""
    # 尝试从缓存获取
    cached_data = await performance_service.cache_get(key)
    if cached_data is not None:
        return cached_data
    
    # 缓存未命中，获取新数据
    fresh_data = await fetch_func()
    
    # 存入缓存
    await performance_service.cache_set(key, fresh_data, expire)
    
    return fresh_data

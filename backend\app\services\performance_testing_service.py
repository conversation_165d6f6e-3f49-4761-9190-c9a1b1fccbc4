"""
性能测试服务
包含压力测试、负载测试、性能基准测试等
"""
import asyncio
import aiohttp
import time
import statistics
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import psutil
import threading

from app.core.config import get_settings
from app.core.logger import logger

settings = get_settings()


@dataclass
class PerformanceTestResult:
    """性能测试结果"""
    test_name: str
    test_type: str
    start_time: str
    end_time: str
    duration: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    requests_per_second: float
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    error_rate: float
    throughput_mb: float
    cpu_usage: Dict[str, float]
    memory_usage: Dict[str, float]
    errors: List[Dict[str, Any]]


@dataclass
class LoadTestConfig:
    """负载测试配置"""
    target_url: str
    concurrent_users: int
    test_duration: int  # 秒
    ramp_up_time: int   # 秒
    request_method: str = "GET"
    request_headers: Dict[str, str] = None
    request_body: str = None
    think_time: float = 0.0  # 用户思考时间


class PerformanceTestingService:
    """性能测试服务"""
    
    def __init__(self):
        self.test_results = []
        self.active_tests = {}
        self.test_scenarios = {}
        self._init_test_scenarios()
    
    def _init_test_scenarios(self):
        """初始化测试场景"""
        self.test_scenarios = {
            "api_load_test": {
                "name": "API负载测试",
                "description": "测试API在高并发下的性能表现",
                "endpoints": [
                    {"url": "/api/v1/market/stocks", "method": "GET", "weight": 30},
                    {"url": "/api/v1/trading/orders", "method": "GET", "weight": 25},
                    {"url": "/api/v1/auth/login", "method": "POST", "weight": 20},
                    {"url": "/api/v1/monitoring/health", "method": "GET", "weight": 15},
                    {"url": "/api/v1/trading/positions", "method": "GET", "weight": 10}
                ],
                "concurrent_users": [10, 50, 100, 200, 500],
                "test_duration": 300  # 5分钟
            },
            "stress_test": {
                "name": "压力测试",
                "description": "测试系统在极限负载下的表现",
                "endpoints": [
                    {"url": "/api/v1/market/realtime", "method": "GET", "weight": 40},
                    {"url": "/api/v1/trading/order", "method": "POST", "weight": 30},
                    {"url": "/api/v1/data/historical", "method": "GET", "weight": 30}
                ],
                "concurrent_users": [100, 300, 500, 1000, 2000],
                "test_duration": 600  # 10分钟
            },
            "endurance_test": {
                "name": "耐久性测试",
                "description": "测试系统长时间运行的稳定性",
                "endpoints": [
                    {"url": "/api/v1/market/stocks", "method": "GET", "weight": 50},
                    {"url": "/api/v1/monitoring/metrics", "method": "GET", "weight": 50}
                ],
                "concurrent_users": [50],
                "test_duration": 3600  # 1小时
            },
            "spike_test": {
                "name": "峰值测试",
                "description": "测试系统应对突发流量的能力",
                "endpoints": [
                    {"url": "/api/v1/market/realtime", "method": "GET", "weight": 100}
                ],
                "concurrent_users": [10, 100, 500, 100, 10],  # 流量峰值模式
                "test_duration": 60  # 每个阶段1分钟
            }
        }
    
    async def run_load_test(self, config: LoadTestConfig) -> PerformanceTestResult:
        """运行负载测试"""
        test_id = f"load_test_{int(time.time())}"
        start_time = datetime.now()
        
        logger.info(f"🚀 开始负载测试: {config.concurrent_users} 并发用户, {config.test_duration}秒")
        
        # 初始化结果收集器
        results = {
            "response_times": [],
            "success_count": 0,
            "error_count": 0,
            "errors": [],
            "bytes_transferred": 0
        }
        
        # 系统资源监控
        system_monitor = SystemResourceMonitor()
        system_monitor.start()
        
        try:
            # 创建并发任务
            semaphore = asyncio.Semaphore(config.concurrent_users)
            tasks = []
            
            # 计算每个用户的请求数
            requests_per_user = max(1, config.test_duration // (config.think_time + 1))
            
            for user_id in range(config.concurrent_users):
                task = asyncio.create_task(
                    self._simulate_user_load(
                        user_id, config, semaphore, results, requests_per_user
                    )
                )
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks, return_exceptions=True)
            
        finally:
            system_monitor.stop()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 计算性能指标
        response_times = results["response_times"]
        total_requests = len(response_times)
        
        if total_requests > 0:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p50_response_time = statistics.median(response_times)
            p95_response_time = self._percentile(response_times, 95)
            p99_response_time = self._percentile(response_times, 99)
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p50_response_time = p95_response_time = p99_response_time = 0
        
        requests_per_second = total_requests / duration if duration > 0 else 0
        error_rate = (results["error_count"] / total_requests * 100) if total_requests > 0 else 0
        throughput_mb = results["bytes_transferred"] / (1024 * 1024)
        
        # 创建测试结果
        test_result = PerformanceTestResult(
            test_name=f"Load Test - {config.concurrent_users} users",
            test_type="load_test",
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=duration,
            total_requests=total_requests,
            successful_requests=results["success_count"],
            failed_requests=results["error_count"],
            requests_per_second=requests_per_second,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            error_rate=error_rate,
            throughput_mb=throughput_mb,
            cpu_usage=system_monitor.get_cpu_stats(),
            memory_usage=system_monitor.get_memory_stats(),
            errors=results["errors"][:100]  # 只保留前100个错误
        )
        
        self.test_results.append(test_result)
        
        logger.info(f"✅ 负载测试完成: {requests_per_second:.2f} RPS, {error_rate:.2f}% 错误率")
        
        return test_result
    
    async def _simulate_user_load(
        self, 
        user_id: int, 
        config: LoadTestConfig, 
        semaphore: asyncio.Semaphore,
        results: Dict[str, Any],
        requests_per_user: int
    ):
        """模拟单个用户的负载"""
        async with semaphore:
            async with aiohttp.ClientSession() as session:
                for request_num in range(requests_per_user):
                    try:
                        start_time = time.time()
                        
                        # 发送请求
                        async with session.request(
                            method=config.request_method,
                            url=config.target_url,
                            headers=config.request_headers or {},
                            data=config.request_body,
                            timeout=aiohttp.ClientTimeout(total=30)
                        ) as response:
                            content = await response.read()
                            
                            end_time = time.time()
                            response_time = (end_time - start_time) * 1000  # 毫秒
                            
                            results["response_times"].append(response_time)
                            results["bytes_transferred"] += len(content)
                            
                            if response.status < 400:
                                results["success_count"] += 1
                            else:
                                results["error_count"] += 1
                                results["errors"].append({
                                    "user_id": user_id,
                                    "request_num": request_num,
                                    "status_code": response.status,
                                    "error": f"HTTP {response.status}",
                                    "timestamp": datetime.now().isoformat()
                                })
                    
                    except Exception as e:
                        results["error_count"] += 1
                        results["errors"].append({
                            "user_id": user_id,
                            "request_num": request_num,
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    # 用户思考时间
                    if config.think_time > 0:
                        await asyncio.sleep(config.think_time)
    
    async def run_stress_test(self, base_url: str, max_users: int = 1000) -> List[PerformanceTestResult]:
        """运行压力测试"""
        logger.info(f"🔥 开始压力测试: 最大 {max_users} 并发用户")
        
        stress_levels = [50, 100, 200, 500, max_users]
        results = []
        
        for concurrent_users in stress_levels:
            config = LoadTestConfig(
                target_url=f"{base_url}/api/v1/health",
                concurrent_users=concurrent_users,
                test_duration=120,  # 2分钟
                ramp_up_time=30,
                think_time=0.1
            )
            
            result = await self.run_load_test(config)
            results.append(result)
            
            # 检查是否达到系统极限
            if result.error_rate > 50 or result.avg_response_time > 5000:
                logger.warning(f"⚠️ 系统在 {concurrent_users} 并发用户时达到极限")
                break
            
            # 测试间隔，让系统恢复
            await asyncio.sleep(30)
        
        return results
    
    async def run_endurance_test(self, base_url: str, duration_hours: int = 1) -> PerformanceTestResult:
        """运行耐久性测试"""
        logger.info(f"⏱️ 开始耐久性测试: {duration_hours} 小时")
        
        config = LoadTestConfig(
            target_url=f"{base_url}/api/v1/health",
            concurrent_users=50,
            test_duration=duration_hours * 3600,
            ramp_up_time=60,
            think_time=1.0
        )
        
        return await self.run_load_test(config)
    
    async def run_spike_test(self, base_url: str) -> List[PerformanceTestResult]:
        """运行峰值测试"""
        logger.info("📈 开始峰值测试")
        
        spike_pattern = [10, 100, 500, 1000, 500, 100, 10]
        results = []
        
        for concurrent_users in spike_pattern:
            config = LoadTestConfig(
                target_url=f"{base_url}/api/v1/market/realtime",
                concurrent_users=concurrent_users,
                test_duration=60,  # 1分钟
                ramp_up_time=5,
                think_time=0.1
            )
            
            result = await self.run_load_test(config)
            results.append(result)
            
            # 短暂间隔
            await asyncio.sleep(10)
        
        return results
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    async def run_benchmark_test(self, base_url: str) -> Dict[str, Any]:
        """运行性能基准测试"""
        logger.info("📊 开始性能基准测试")
        
        benchmark_results = {}
        
        # API响应时间基准测试
        api_endpoints = [
            "/api/v1/health",
            "/api/v1/market/stocks",
            "/api/v1/trading/orders",
            "/api/v1/monitoring/metrics"
        ]
        
        for endpoint in api_endpoints:
            config = LoadTestConfig(
                target_url=f"{base_url}{endpoint}",
                concurrent_users=10,
                test_duration=60,
                ramp_up_time=10,
                think_time=0.5
            )
            
            result = await self.run_load_test(config)
            benchmark_results[endpoint] = {
                "avg_response_time": result.avg_response_time,
                "p95_response_time": result.p95_response_time,
                "requests_per_second": result.requests_per_second,
                "error_rate": result.error_rate
            }
        
        return benchmark_results
    
    def get_test_results(self, test_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取测试结果"""
        results = self.test_results
        
        if test_type:
            results = [r for r in results if r.test_type == test_type]
        
        return [asdict(result) for result in results]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能测试摘要"""
        if not self.test_results:
            return {"message": "暂无测试结果"}
        
        # 计算总体统计
        total_tests = len(self.test_results)
        total_requests = sum(r.total_requests for r in self.test_results)
        total_errors = sum(r.failed_requests for r in self.test_results)
        
        avg_rps = statistics.mean([r.requests_per_second for r in self.test_results])
        avg_response_time = statistics.mean([r.avg_response_time for r in self.test_results])
        avg_error_rate = statistics.mean([r.error_rate for r in self.test_results])
        
        # 按测试类型分组
        by_type = {}
        for result in self.test_results:
            test_type = result.test_type
            if test_type not in by_type:
                by_type[test_type] = []
            by_type[test_type].append(result)
        
        return {
            "total_tests": total_tests,
            "total_requests": total_requests,
            "total_errors": total_errors,
            "average_rps": round(avg_rps, 2),
            "average_response_time": round(avg_response_time, 2),
            "average_error_rate": round(avg_error_rate, 2),
            "tests_by_type": {
                test_type: len(results) 
                for test_type, results in by_type.items()
            },
            "latest_test": asdict(self.test_results[-1]) if self.test_results else None
        }


class SystemResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.cpu_samples = []
        self.memory_samples = []
        self.monitor_thread = None
    
    def start(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def stop(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                self.cpu_samples.append(cpu_percent)
                self.memory_samples.append(memory_percent)
                
                time.sleep(1)
            except Exception as e:
                logger.error(f"资源监控错误: {e}")
    
    def get_cpu_stats(self) -> Dict[str, float]:
        """获取CPU统计"""
        if not self.cpu_samples:
            return {"avg": 0, "max": 0, "min": 0}
        
        return {
            "avg": statistics.mean(self.cpu_samples),
            "max": max(self.cpu_samples),
            "min": min(self.cpu_samples)
        }
    
    def get_memory_stats(self) -> Dict[str, float]:
        """获取内存统计"""
        if not self.memory_samples:
            return {"avg": 0, "max": 0, "min": 0}
        
        return {
            "avg": statistics.mean(self.memory_samples),
            "max": max(self.memory_samples),
            "min": min(self.memory_samples)
        }


# 全局实例
performance_testing_service = PerformanceTestingService()


async def get_performance_testing_service() -> PerformanceTestingService:
    """获取性能测试服务实例"""
    return performance_testing_service

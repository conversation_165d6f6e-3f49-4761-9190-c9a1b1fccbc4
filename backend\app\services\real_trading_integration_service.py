"""
真实交易接口集成服务
支持多种交易平台：股票、期货、加密货币等
"""
import asyncio
import aiohttp
import json
import time
import hmac
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
import uuid

from app.core.config import get_settings
from app.core.logger import logger

settings = get_settings()


class RealTradingIntegrationService:
    """真实交易接口集成服务"""
    
    def __init__(self):
        self.session = None
        self.trading_platforms = {
            "binance": {
                "enabled": False,
                "api_key": getattr(settings, 'BINANCE_API_KEY', None),
                "secret_key": getattr(settings, 'BINANCE_SECRET_KEY', None),
                "base_url": "https://api.binance.com",
                "testnet_url": "https://testnet.binance.vision",
                "use_testnet": True,
                "asset_type": "crypto"
            },
            "alpaca": {
                "enabled": False,
                "api_key": getattr(settings, 'ALPACA_API_KEY', None),
                "secret_key": getattr(settings, 'ALPACA_SECRET_KEY', None),
                "base_url": "https://api.alpaca.markets",
                "paper_url": "https://paper-api.alpaca.markets",
                "use_paper": True,
                "asset_type": "stock"
            },
            "interactive_brokers": {
                "enabled": False,
                "username": getattr(settings, 'IB_USERNAME', None),
                "password": getattr(settings, 'IB_PASSWORD', None),
                "base_url": "https://localhost:5000/v1/api",
                "asset_type": "multi"
            },
            "futu": {
                "enabled": False,
                "app_id": getattr(settings, 'FUTU_APP_ID', None),
                "app_key": getattr(settings, 'FUTU_APP_KEY', None),
                "base_url": "https://openapi.futunn.com",
                "asset_type": "stock"
            }
        }
        
        # 订单管理
        self.active_orders = {}
        self.order_history = {}
        
        # 风险控制
        self.risk_limits = {
            "daily_loss_limit": Decimal('10000'),
            "position_limit": Decimal('1000000'),
            "order_size_limit": Decimal('100000'),
            "daily_trades_limit": 100
        }
        
        self.daily_stats = {
            "pnl": Decimal('0'),
            "trades_count": 0,
            "last_reset_date": datetime.now().date()
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _reset_daily_stats_if_needed(self):
        """如果需要，重置日统计"""
        today = datetime.now().date()
        if today > self.daily_stats["last_reset_date"]:
            self.daily_stats["pnl"] = Decimal('0')
            self.daily_stats["trades_count"] = 0
            self.daily_stats["last_reset_date"] = today
    
    def _check_risk_limits(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """检查风险限制"""
        self._reset_daily_stats_if_needed()
        
        risks = []
        
        # 检查订单金额
        order_value = Decimal(str(order.get('price', 0))) * Decimal(str(order.get('quantity', 0)))
        if order_value > self.risk_limits["order_size_limit"]:
            risks.append(f"订单金额 {order_value} 超过限额 {self.risk_limits['order_size_limit']}")
        
        # 检查日交易次数
        if self.daily_stats["trades_count"] >= self.risk_limits["daily_trades_limit"]:
            risks.append(f"日交易次数已达限额 {self.risk_limits['daily_trades_limit']}")
        
        # 检查日亏损
        if self.daily_stats["pnl"] < -self.risk_limits["daily_loss_limit"]:
            risks.append(f"日亏损超过限额 {self.risk_limits['daily_loss_limit']}")
        
        return {
            "passed": len(risks) == 0,
            "risks": risks,
            "order_value": float(order_value),
            "daily_pnl": float(self.daily_stats["pnl"]),
            "daily_trades": self.daily_stats["trades_count"]
        }
    
    async def place_order(
        self, 
        platform: str, 
        symbol: str, 
        side: str, 
        order_type: str,
        quantity: float,
        price: Optional[float] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """下单"""
        try:
            # 构建订单对象
            order = {
                "symbol": symbol,
                "side": side,
                "order_type": order_type,
                "quantity": quantity,
                "price": price or 0,
                **kwargs
            }
            
            # 风险检查
            risk_check = self._check_risk_limits(order)
            if not risk_check["passed"]:
                return {
                    "success": False,
                    "message": "订单风险检查失败",
                    "risks": risk_check["risks"]
                }
            
            # 根据平台路由订单
            if platform == "binance":
                return await self._place_binance_order(order)
            elif platform == "alpaca":
                return await self._place_alpaca_order(order)
            elif platform == "interactive_brokers":
                return await self._place_ib_order(order)
            elif platform == "futu":
                return await self._place_futu_order(order)
            else:
                return await self._place_mock_order(order, platform)
                
        except Exception as e:
            logger.error(f"下单失败: {e}")
            return {
                "success": False,
                "message": f"下单失败: {str(e)}"
            }
    
    async def _place_binance_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Binance下单"""
        if not self.trading_platforms["binance"]["enabled"]:
            return await self._place_mock_order(order, "binance")
        
        platform_config = self.trading_platforms["binance"]
        base_url = platform_config["testnet_url"] if platform_config["use_testnet"] else platform_config["base_url"]
        
        # 构建Binance订单参数
        params = {
            "symbol": order["symbol"].replace("/", ""),  # BTCUSDT格式
            "side": order["side"].upper(),
            "type": order["order_type"].upper(),
            "quantity": order["quantity"],
            "timestamp": int(time.time() * 1000)
        }
        
        if order["order_type"].lower() == "limit":
            params["price"] = order["price"]
            params["timeInForce"] = "GTC"
        
        # 生成签名
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        signature = hmac.new(
            platform_config["secret_key"].encode(),
            query_string.encode(),
            hashlib.sha256
        ).hexdigest()
        
        params["signature"] = signature
        
        headers = {
            "X-MBX-APIKEY": platform_config["api_key"]
        }
        
        async with self.session.post(
            f"{base_url}/api/v3/order",
            params=params,
            headers=headers
        ) as response:
            result = await response.json()
            
            if response.status == 200:
                order_id = result["orderId"]
                self.active_orders[str(order_id)] = {
                    **order,
                    "order_id": str(order_id),
                    "platform": "binance",
                    "status": "pending",
                    "create_time": datetime.now().isoformat()
                }
                
                # 更新统计
                self.daily_stats["trades_count"] += 1
                
                return {
                    "success": True,
                    "order_id": str(order_id),
                    "message": "Binance订单提交成功",
                    "platform_response": result
                }
            else:
                return {
                    "success": False,
                    "message": f"Binance下单失败: {result.get('msg', '未知错误')}"
                }
    
    async def _place_alpaca_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Alpaca下单（美股）"""
        if not self.trading_platforms["alpaca"]["enabled"]:
            return await self._place_mock_order(order, "alpaca")
        
        platform_config = self.trading_platforms["alpaca"]
        base_url = platform_config["paper_url"] if platform_config["use_paper"] else platform_config["base_url"]
        
        # 构建Alpaca订单参数
        order_data = {
            "symbol": order["symbol"],
            "qty": order["quantity"],
            "side": order["side"],
            "type": order["order_type"],
            "time_in_force": "day"
        }
        
        if order["order_type"].lower() == "limit":
            order_data["limit_price"] = order["price"]
        
        headers = {
            "APCA-API-KEY-ID": platform_config["api_key"],
            "APCA-API-SECRET-KEY": platform_config["secret_key"],
            "Content-Type": "application/json"
        }
        
        async with self.session.post(
            f"{base_url}/v2/orders",
            json=order_data,
            headers=headers
        ) as response:
            result = await response.json()
            
            if response.status == 201:
                order_id = result["id"]
                self.active_orders[order_id] = {
                    **order,
                    "order_id": order_id,
                    "platform": "alpaca",
                    "status": "pending",
                    "create_time": datetime.now().isoformat()
                }
                
                self.daily_stats["trades_count"] += 1
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "message": "Alpaca订单提交成功",
                    "platform_response": result
                }
            else:
                return {
                    "success": False,
                    "message": f"Alpaca下单失败: {result.get('message', '未知错误')}"
                }
    
    async def _place_ib_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Interactive Brokers下单"""
        if not self.trading_platforms["interactive_brokers"]["enabled"]:
            return await self._place_mock_order(order, "interactive_brokers")
        
        # IB Gateway API调用
        platform_config = self.trading_platforms["interactive_brokers"]
        
        order_data = {
            "conid": await self._get_ib_contract_id(order["symbol"]),
            "orderType": order["order_type"].upper(),
            "side": order["side"].upper(),
            "quantity": order["quantity"]
        }
        
        if order["order_type"].lower() == "limit":
            order_data["price"] = order["price"]
        
        try:
            async with self.session.post(
                f"{platform_config['base_url']}/iserver/account/orders",
                json={"orders": [order_data]}
            ) as response:
                result = await response.json()
                
                if response.status == 200 and result:
                    order_id = result[0].get("order_id", str(uuid.uuid4()))
                    self.active_orders[order_id] = {
                        **order,
                        "order_id": order_id,
                        "platform": "interactive_brokers",
                        "status": "pending",
                        "create_time": datetime.now().isoformat()
                    }
                    
                    self.daily_stats["trades_count"] += 1
                    
                    return {
                        "success": True,
                        "order_id": order_id,
                        "message": "IB订单提交成功",
                        "platform_response": result
                    }
                else:
                    return {
                        "success": False,
                        "message": "IB下单失败"
                    }
        except Exception as e:
            return await self._place_mock_order(order, "interactive_brokers")
    
    async def _place_futu_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """富途下单（港股/美股）"""
        if not self.trading_platforms["futu"]["enabled"]:
            return await self._place_mock_order(order, "futu")
        
        # 富途OpenAPI调用
        platform_config = self.trading_platforms["futu"]
        
        order_data = {
            "code": order["symbol"],
            "trd_side": 1 if order["side"].lower() == "buy" else 2,
            "order_type": 1 if order["order_type"].lower() == "limit" else 2,
            "qty": order["quantity"],
            "price": order.get("price", 0)
        }
        
        # 这里需要实现富途的具体API调用
        # 由于富途API较复杂，这里使用模拟实现
        return await self._place_mock_order(order, "futu")
    
    async def _place_mock_order(self, order: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """模拟下单（用于测试和降级）"""
        order_id = f"mock_{platform}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        self.active_orders[order_id] = {
            **order,
            "order_id": order_id,
            "platform": platform,
            "status": "pending",
            "create_time": datetime.now().isoformat(),
            "mock": True
        }
        
        self.daily_stats["trades_count"] += 1
        
        # 模拟订单处理
        asyncio.create_task(self._simulate_order_execution(order_id))
        
        return {
            "success": True,
            "order_id": order_id,
            "message": f"模拟{platform}订单提交成功",
            "mock": True
        }
    
    async def _simulate_order_execution(self, order_id: str):
        """模拟订单执行"""
        await asyncio.sleep(2)  # 模拟执行延迟
        
        if order_id in self.active_orders:
            order = self.active_orders[order_id]
            
            # 模拟成交
            order["status"] = "filled"
            order["filled_quantity"] = order["quantity"]
            order["avg_price"] = order.get("price", 0) or 10.0
            order["fill_time"] = datetime.now().isoformat()
            
            # 移动到历史订单
            self.order_history[order_id] = order
            del self.active_orders[order_id]
            
            logger.info(f"模拟订单 {order_id} 执行完成")
    
    async def cancel_order(self, platform: str, order_id: str) -> Dict[str, Any]:
        """撤单"""
        try:
            if platform == "binance":
                return await self._cancel_binance_order(order_id)
            elif platform == "alpaca":
                return await self._cancel_alpaca_order(order_id)
            elif platform == "interactive_brokers":
                return await self._cancel_ib_order(order_id)
            elif platform == "futu":
                return await self._cancel_futu_order(order_id)
            else:
                return await self._cancel_mock_order(order_id)
                
        except Exception as e:
            logger.error(f"撤单失败: {e}")
            return {
                "success": False,
                "message": f"撤单失败: {str(e)}"
            }
    
    async def _cancel_mock_order(self, order_id: str) -> Dict[str, Any]:
        """撤销模拟订单"""
        if order_id in self.active_orders:
            order = self.active_orders[order_id]
            order["status"] = "cancelled"
            order["cancel_time"] = datetime.now().isoformat()
            
            self.order_history[order_id] = order
            del self.active_orders[order_id]
            
            return {
                "success": True,
                "message": "订单已撤销",
                "order_id": order_id
            }
        else:
            return {
                "success": False,
                "message": "订单不存在或已完成"
            }
    
    async def get_positions(self, platform: str) -> Dict[str, Any]:
        """获取持仓"""
        try:
            if platform == "binance":
                return await self._get_binance_positions()
            elif platform == "alpaca":
                return await self._get_alpaca_positions()
            elif platform == "interactive_brokers":
                return await self._get_ib_positions()
            elif platform == "futu":
                return await self._get_futu_positions()
            else:
                return await self._get_mock_positions(platform)
                
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return await self._get_mock_positions(platform)
    
    async def _get_mock_positions(self, platform: str) -> Dict[str, Any]:
        """获取模拟持仓"""
        positions = [
            {
                "symbol": "BTCUSDT" if platform == "binance" else "AAPL",
                "quantity": 1.5 if platform == "binance" else 100,
                "avg_price": 45000 if platform == "binance" else 150,
                "current_price": 46000 if platform == "binance" else 155,
                "unrealized_pnl": 1500 if platform == "binance" else 500,
                "platform": platform
            }
        ]
        
        return {
            "success": True,
            "data": {
                "positions": positions,
                "total_value": sum(pos["quantity"] * pos["current_price"] for pos in positions),
                "total_pnl": sum(pos["unrealized_pnl"] for pos in positions)
            }
        }
    
    async def get_account_info(self, platform: str) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            if platform == "binance":
                return await self._get_binance_account()
            elif platform == "alpaca":
                return await self._get_alpaca_account()
            elif platform == "interactive_brokers":
                return await self._get_ib_account()
            elif platform == "futu":
                return await self._get_futu_account()
            else:
                return await self._get_mock_account(platform)
                
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return await self._get_mock_account(platform)
    
    async def _get_mock_account(self, platform: str) -> Dict[str, Any]:
        """获取模拟账户信息"""
        return {
            "success": True,
            "data": {
                "platform": platform,
                "account_id": f"mock_{platform}_account",
                "total_balance": 100000.0,
                "available_balance": 95000.0,
                "currency": "USDT" if platform == "binance" else "USD",
                "margin_level": 1.5,
                "daily_pnl": float(self.daily_stats["pnl"]),
                "total_trades_today": self.daily_stats["trades_count"]
            }
        }
    
    async def _get_ib_contract_id(self, symbol: str) -> int:
        """获取IB合约ID"""
        # 这里应该调用IB API获取合约ID
        # 简化实现，返回模拟ID
        return hash(symbol) % 1000000
    
    def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """获取订单状态"""
        order = self.active_orders.get(order_id) or self.order_history.get(order_id)
        
        if order:
            return {
                "success": True,
                "data": order
            }
        else:
            return {
                "success": False,
                "message": "订单不存在"
            }
    
    def get_active_orders(self, platform: Optional[str] = None) -> Dict[str, Any]:
        """获取活跃订单"""
        orders = list(self.active_orders.values())
        
        if platform:
            orders = [order for order in orders if order.get("platform") == platform]
        
        return {
            "success": True,
            "data": {
                "orders": orders,
                "total": len(orders)
            }
        }
    
    def get_trading_platforms_status(self) -> Dict[str, Any]:
        """获取交易平台状态"""
        status = {}
        
        for platform, config in self.trading_platforms.items():
            status[platform] = {
                "enabled": config["enabled"],
                "asset_type": config["asset_type"],
                "has_credentials": bool(config.get("api_key") or config.get("username")),
                "use_testnet": config.get("use_testnet", False),
                "use_paper": config.get("use_paper", False)
            }
        
        return {
            "platforms": status,
            "active_orders_count": len(self.active_orders),
            "daily_stats": {
                "pnl": float(self.daily_stats["pnl"]),
                "trades_count": self.daily_stats["trades_count"],
                "last_reset_date": self.daily_stats["last_reset_date"].isoformat()
            },
            "risk_limits": {k: float(v) for k, v in self.risk_limits.items()}
        }


# 全局实例
real_trading_service = RealTradingIntegrationService()


async def get_real_trading_service() -> RealTradingIntegrationService:
    """获取真实交易服务实例"""
    return real_trading_service

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import pandas as pd
import redis.asyncio as redis
import tushare as ts
from cachetools import TTLCache

from app.core.config import settings

logger = logging.getLogger(__name__)

# 缓存配置
CACHE_DIR = "cache"
STOCK_LIST_CACHE_FILE = os.path.join(CACHE_DIR, "stock_list.pkl")
CACHE_TTL_SECONDS = 60 * 60 * 4  # 4小时

os.makedirs(CACHE_DIR, exist_ok=True)


class RealtimeDataService:
    """Tushare实时数据服务"""

    def __init__(self):
        # 初始化数据服务
        self.ts_pro = None
        self.mock_service = None
        self.use_mock = False
        
        # 尝试初始化Tushare Pro接口
        if settings.TUSHARE_TOKEN and settings.TUSHARE_TOKEN != "test_token":
            try:
                ts.set_token(settings.TUSHARE_TOKEN)
                self.ts_pro = ts.pro_api()
                # 简单测试连接
                test_df = self.ts_pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241201')
                if not test_df.empty:
                    logger.info("✅ Tushare Pro API初始化成功")
                else:
                    raise Exception("API返回空数据")
            except Exception as e:
                logger.warning(f"⚠️ Tushare Pro API初始化失败: {e}")
                logger.info("🔄 切换到模拟数据模式")
                self.use_mock = True
                self.mock_service = self._create_mock_service()
        else:
            logger.info("⚠️ 未配置TUSHARE_TOKEN，使用模拟数据模式")
            self.use_mock = True
            self.mock_service = self._create_mock_service()

        # 初始化Redis连接
        self.redis = None

        # 当前订阅的股票列表
        self.subscribed_symbols = set()

        # 指数代码映射
        self.index_mapping = {
            "000001.SH": {"name": "上证指数", "symbol": "SH000001"},
            "399001.SZ": {"name": "深证成指", "symbol": "SZ399001"},
            "399006.SZ": {"name": "创业板指", "symbol": "SZ399006"},
            "000688.SH": {"name": "科创50", "symbol": "SH000688"},
        }

    def _create_mock_service(self):
        """创建模拟Tushare服务"""
        from app.services.mock_market_service import mock_market_service
        return mock_market_service

    async def _init_redis(self):
        """初始化Redis连接"""
        if self.redis is None:
            try:
                self.redis = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD,
                    decode_responses=True,
                )
                # 测试连接
                await self.redis.ping()
                logger.info("✅ Redis连接初始化成功")
            except Exception as e:
                logger.warning(f"Redis 不可用，已降级为本地模式: {e}")
                # 置空 redis 对象，后续发布操作将跳过
                self.redis = None

    async def start(self):
        """启动实时数据服务"""
        logger.info("🚀 启动Tushare实时数据服务...")
        await self._init_redis()

        if settings.USE_REALTIME_DATA:
            # 创建实时数据获取任务
            asyncio.create_task(self._fetch_realtime_data())
            logger.info("📡 实时数据获取任务已启动")

    async def subscribe(self, symbols: List[str]):
        """订阅新的股票代码"""
        new_symbols = set(symbols) - self.subscribed_symbols
        if new_symbols:
            logger.info(f"📡 订阅新股票代码: {', '.join(new_symbols)}")
            self.subscribed_symbols.update(new_symbols)

    async def unsubscribe(self, symbols: List[str]):
        """取消订阅股票代码"""
        removed_symbols = set(symbols) & self.subscribed_symbols
        if removed_symbols:
            self.subscribed_symbols -= set(symbols)
            logger.info(f"📡 取消订阅股票代码: {', '.join(removed_symbols)}")

    async def _fetch_realtime_data(self):
        """实时数据获取循环"""
        while True:
            try:
                if not self.subscribed_symbols:
                    await asyncio.sleep(5)
                    continue

                # 获取实时行情数据
                await self._fetch_stock_quotes(list(self.subscribed_symbols))

                # 获取指数数据
                await self._fetch_index_quotes()

                # 控制查询频率 (Tushare限制)
                await asyncio.sleep(3)

            except Exception as e:
                logger.error(f"获取实时数据失败: {e}", exc_info=True)
                await asyncio.sleep(10)

    async def _fetch_stock_quotes(self, symbols: List[str]):
        """获取股票实时行情"""
        if not self.ts_pro:
            logger.warning("TuShare API未初始化，跳过股票行情获取")
            return

        try:
            # 使用Tushare获取实时行情
            for symbol in symbols:
                try:
                    # 获取单个股票的实时数据
                    df = self.ts_pro.daily(ts_code=symbol, limit=1)
                    if not df.empty:
                        tick = self._format_stock_tick(df.iloc[0])
                        await self._publish_to_redis(tick)
                except Exception as e:
                    logger.warning(f"获取股票 {symbol} 行情失败: {e}")

        except Exception as e:
            logger.error(f"批量获取股票行情失败: {e}")

    async def _fetch_index_quotes(self):
        """获取指数实时行情"""
        if not self.ts_pro:
            logger.warning("TuShare API未初始化，跳过指数行情获取")
            return

        try:
            for ts_code, info in self.index_mapping.items():
                try:
                    df = self.ts_pro.index_daily(ts_code=ts_code, limit=1)
                    if not df.empty:
                        tick = self._format_index_tick(df.iloc[0], info)
                        await self._publish_to_redis(tick, is_index=True)
                except Exception as e:
                    logger.warning(f"获取指数 {ts_code} 行情失败: {e}")

        except Exception as e:
            logger.error(f"批量获取指数行情失败: {e}")

    def _format_stock_tick(self, row) -> Dict:
        """格式化股票行情数据"""
        return {
            "symbol": row["ts_code"],
            "name": "",  # 需要从股票基本信息获取
            "price": float(row["close"]),
            "prev_close": float(row["pre_close"]),
            "open": float(row["open"]),
            "high": float(row["high"]),
            "low": float(row["low"]),
            "volume": float(row["vol"]) * 100,  # 转换为股
            "amount": float(row["amount"]) * 1000,  # 转换为元
            "change": float(row["change"]),
            "change_percent": float(row["pct_chg"]),
            "time": row["trade_date"],
            "type": "stock",
        }

    def _format_index_tick(self, row, info: Dict) -> Dict:
        """格式化指数行情数据"""
        return {
            "symbol": info["symbol"],
            "name": info["name"],
            "price": float(row["close"]),
            "prev_close": float(row["pre_close"]),
            "open": float(row["open"]),
            "high": float(row["high"]),
            "low": float(row["low"]),
            "volume": float(row["vol"]) if row["vol"] else 0,
            "amount": float(row["amount"]) if row["amount"] else 0,
            "change": float(row["change"]),
            "change_percent": float(row["pct_chg"]),
            "time": row["trade_date"],
            "type": "index",
        }

    async def _publish_to_redis(self, tick: Dict, is_index: bool = False):
        """发布行情数据到Redis频道"""
        try:
            await self._init_redis()

            # 如果Redis不可用则直接跳过
            if self.redis is None:
                return

            # 发布到全局行情频道
            if is_index:
                await self.redis.publish("market:indices", json.dumps(tick))
            else:
                await self.redis.publish("market:ticks", json.dumps(tick))

            # 发布到个股专属频道
            await self.redis.publish(
                f'market:symbol:{tick["symbol"]}', json.dumps(tick)
            )

        except Exception as e:
            logger.error(f"发布到Redis失败: {e}", exc_info=True)

    async def get_index_data(self) -> List[Dict[str, Any]]:
        """获取主要指数数据"""
        try:
            index_data = []

            for ts_code, info in self.index_mapping.items():
                try:
                    # 获取最新指数数据
                    df = self.ts_pro.index_daily(ts_code=ts_code, limit=1)
                    if not df.empty:
                        row = df.iloc[0]
                        data = {
                            "name": info["name"],
                            "currentPrice": float(row["close"]),
                            "change": float(row["change"]),
                            "changePercent": float(row["pct_chg"]),
                            "volume": float(row["vol"]) if row["vol"] else 0,
                            "turnover": float(row["amount"]) if row["amount"] else 0,
                            "open": float(row["open"]),
                            "high": float(row["high"]),
                            "low": float(row["low"]),
                            "prevClose": float(row["pre_close"]),
                        }
                    else:
                        # 如果日线数据为空，尝试获取指数基本信息作为备用
                        df_basic = self.ts_pro.index_basic(ts_code=ts_code)
                        if not df_basic.empty:
                            # 此处仅为示例，Tushare可能不直接提供价格
                            data["currentPrice"] = 0.0
                except Exception as e:
                    logger.warning(f"获取指数 {ts_code} 数据失败: {e}")
                    # 当接口无权限时，优雅地处理
                    logger.warning(f"获取指数 {ts_code} 数据失败: {e}")
                    data = {
                        "name": info["name"],
                        "currentPrice": 0,
                        "change": 0,
                        "changePercent": 0,
                        "volume": 0,
                        "turnover": 0,
                        "open": 0,
                        "high": 0,
                        "low": 0,
                        "prevClose": 0,
                    }

                index_data.append(data)

            return index_data

        except Exception as e:
            logger.error(f"获取指数数据失败: {e}", exc_info=True)
            # 返回空数据结构
            return [
                {
                    "name": info["name"],
                    "currentPrice": 0,
                    "change": 0,
                    "changePercent": 0,
                    "volume": 0,
                    "turnover": 0,
                    "open": 0,
                    "high": 0,
                    "low": 0,
                    "prevClose": 0,
                }
                for info in self.index_mapping.values()
            ]

    def _load_stocks_from_file(self) -> Optional[pd.DataFrame]:
        """从文件加载股票列表缓存"""
        if os.path.exists(STOCK_LIST_CACHE_FILE):
            last_mod_time = os.path.getmtime(STOCK_LIST_CACHE_FILE)
            if (time.time() - last_mod_time) < CACHE_TTL_SECONDS:
                logger.info(f"✅ 从文件缓存加载股票列表: {STOCK_LIST_CACHE_FILE}")
                return pd.read_pickle(STOCK_LIST_CACHE_FILE)
        return None

    def _save_stocks_to_file(self, df: pd.DataFrame):
        """保存股票列表到文件缓存"""
        logger.info(f"💾 保存股票列表到文件缓存: {STOCK_LIST_CACHE_FILE}")
        df.to_pickle(STOCK_LIST_CACHE_FILE)

    async def get_stock_list(
        self, page: int = 1, page_size: int = 20, market: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取股票列表，带文件缓存"""
        try:
            df_basic = self._load_stocks_from_file()

            if df_basic is None:
                logger.info("🔄 从API获取股票列表")
                
                if self.use_mock:
                    # 使用Mock服务
                    df_basic = await self.mock_service.get_stock_basic(
                        exchange="", list_status="L"
                    )
                else:
                    # 使用真实Tushare API
                    df_basic = self.ts_pro.stock_basic(
                        exchange="",  # 获取所有市场的，方便筛选
                        list_status="L",
                        fields="ts_code,symbol,name,area,industry,market,list_date",
                    )
                
                if not df_basic.empty:
                    self._save_stocks_to_file(df_basic)

            if df_basic.empty:
                return {"stocks": [], "total": 0, "page": page, "pageSize": page_size}

            # 市场筛选
            if market:
                if market.upper() == "SH":
                    df_basic = df_basic[df_basic["ts_code"].str.endswith(".SH")]
                elif market.upper() == "SZ":
                    df_basic = df_basic[df_basic["ts_code"].str.endswith(".SZ")]

            # 分页
            total = len(df_basic)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            df_page = df_basic.iloc[start_idx:end_idx]

            # 获取这些股票的最新行情
            stocks = []
            for _, row in df_page.iterrows():
                try:
                    # 获取最新行情
                    if self.use_mock:
                        df_quote = await self.mock_service.get_daily_data(row["ts_code"], limit=1)
                    else:
                        df_quote = self.ts_pro.daily(ts_code=row["ts_code"], limit=1)

                    if not df_quote.empty:
                        quote = df_quote.iloc[0]
                        stock_data = {
                            "symbol": row["symbol"],
                            "ts_code": row["ts_code"],
                            "name": row["name"],
                            "industry": row["industry"] or "未知",
                            "area": row["area"] or "未知",
                            "market": row["market"],
                            "listDate": row["list_date"],
                            "price": float(quote["close"]),
                            "change": float(quote["change"]),
                            "changePercent": float(quote["pct_chg"]),
                            "volume": float(quote["vol"]) * 100,  # 转换为股
                            "amount": float(quote["amount"]) * 1000,  # 转换为元
                            "open": float(quote["open"]),
                            "high": float(quote["high"]),
                            "low": float(quote["low"]),
                            "prevClose": float(quote["pre_close"]),
                        }
                    else:
                        # 没有行情数据时的默认值
                        stock_data = {
                            "symbol": row["symbol"],
                            "ts_code": row["ts_code"],
                            "name": row["name"],
                            "industry": row["industry"] or "未知",
                            "area": row["area"] or "未知",
                            "market": row["market"],
                            "listDate": row["list_date"],
                            "price": 0,
                            "change": 0,
                            "changePercent": 0,
                            "volume": 0,
                            "amount": 0,
                            "open": 0,
                            "high": 0,
                            "low": 0,
                            "prevClose": 0,
                        }

                    stocks.append(stock_data)

                except Exception as e:
                    logger.warning(f"获取股票 {row['ts_code']} 行情失败: {e}")
                    # 添加基本信息，行情数据为0
                    stocks.append(
                        {
                            "symbol": row["symbol"],
                            "ts_code": row["ts_code"],
                            "name": row["name"],
                            "industry": row["industry"] or "未知",
                            "area": row["area"] or "未知",
                            "market": row["market"],
                            "listDate": row["list_date"],
                            "price": 0,
                            "change": 0,
                            "changePercent": 0,
                            "volume": 0,
                            "amount": 0,
                            "open": 0,
                            "high": 0,
                            "low": 0,
                            "prevClose": 0,
                        }
                    )

            return {
                "total": total,
                "page": page,
                "pageSize": page_size,
                "stocks": stocks,
            }

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            # 返回空结果而不是抛出异常
            return {
                "total": 0,
                "page": page,
                "pageSize": page_size,
                "stocks": [],
                "error": f"获取股票列表失败: {str(e)}"
            }

    async def get_kline_data(
        self, symbol: str, period: str = "D", limit: int = 250
    ) -> List[Dict]:
        """获取K线数据"""
        try:
            # 计算开始日期
            days_map = {"D": 365, "W": 365 * 2, "M": 365 * 5}
            days = days_map.get(period, 365)
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y%m%d")

            # 获取K线数据
            if self.use_mock:
                # 使用Mock服务，只支持日线
                df = await self.mock_service.get_daily_data(symbol, limit=limit)
            else:
                # 使用真实API
                if period == "D":
                    df = self.ts_pro.daily(ts_code=symbol, start_date=start_date)
                elif period == "W":
                    df = self.ts_pro.weekly(ts_code=symbol, start_date=start_date)
                elif period == "M":
                    df = self.ts_pro.monthly(ts_code=symbol, start_date=start_date)
                else:
                    df = self.ts_pro.daily(ts_code=symbol, start_date=start_date)

            # 限制数据量
            if len(df) > limit:
                df = df.tail(limit)

            # 转换为前端需要的格式
            kline_data = []
            for _, row in df.iterrows():
                kline_data.append(
                    {
                        "date": row["trade_date"],
                        "open": float(row["open"]),
                        "high": float(row["high"]),
                        "low": float(row["low"]),
                        "close": float(row["close"]),
                        "volume": float(row["vol"]) * 100,  # 转换为股
                        "amount": float(row["amount"]) * 1000,  # 转换为元
                    }
                )

            # 按日期排序（从旧到新）
            kline_data.sort(key=lambda x: x["date"])

            return kline_data

        except Exception as e:
            logger.error(f"获取K线数据失败: {e}", exc_info=True)
            return []


# 全局实例
_realtime_service = None


def get_realtime_service() -> RealtimeDataService:
    """获取实时数据服务单例"""
    global _realtime_service
    if _realtime_service is None:
        _realtime_service = RealtimeDataService()
    return _realtime_service

"""
风险控制服务
提供多层级风险检查和实时风险监控
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


class RiskLevel(str, Enum):
    """风险等级"""
    LOW = "low"           # 低风险
    MEDIUM = "medium"     # 中风险
    HIGH = "high"         # 高风险
    CRITICAL = "critical" # 严重风险


class RiskType(str, Enum):
    """风险类型"""
    CAPITAL = "capital"           # 资金风险
    POSITION = "position"         # 持仓风险
    CONCENTRATION = "concentration" # 集中度风险
    FREQUENCY = "frequency"       # 频率风险
    VOLATILITY = "volatility"     # 波动率风险
    DRAWDOWN = "drawdown"         # 回撤风险


@dataclass
class RiskCheckResult:
    """风险检查结果"""
    passed: bool
    risk_level: RiskLevel
    risk_type: RiskType
    message: str
    details: Dict = None
    suggestions: List[str] = None


@dataclass
class RiskLimit:
    """风险限制配置"""
    # 资金风险限制
    max_single_order_amount: float = 100000.0      # 单笔最大金额
    max_daily_trade_amount: float = 1000000.0       # 日交易限额
    max_position_ratio: float = 0.8                 # 最大持仓比例
    min_available_ratio: float = 0.2                # 最小可用资金比例
    
    # 持仓风险限制
    max_single_position_ratio: float = 0.3          # 单个品种最大持仓比例
    max_sector_concentration: float = 0.5           # 行业集中度限制
    max_leverage_ratio: float = 3.0                 # 最大杠杆比例
    
    # 交易频率限制
    max_orders_per_minute: int = 60                 # 每分钟最大订单数
    max_orders_per_hour: int = 1000                 # 每小时最大订单数
    max_orders_per_day: int = 10000                 # 每日最大订单数
    
    # 损失限制
    max_daily_loss: float = 50000.0                 # 日最大亏损
    max_weekly_loss: float = 200000.0               # 周最大亏损
    max_monthly_loss: float = 500000.0              # 月最大亏损
    max_drawdown_ratio: float = 0.15                # 最大回撤比例
    
    # 波动率限制
    max_volatility_threshold: float = 0.05          # 最大波动率阈值
    volatility_check_period: int = 30               # 波动率检查周期(天)


class RiskControlService:
    """风险控制服务"""
    
    def __init__(self, risk_limits: Optional[RiskLimit] = None):
        self.risk_limits = risk_limits or RiskLimit()
        self.trading_records: List[Dict] = []
        self.order_history: List[Dict] = []
        self.position_history: List[Dict] = []
        self.risk_alerts: List[Dict] = []
        
        # 实时监控数据
        self.current_positions: Dict[str, Dict] = {}
        self.daily_stats: Dict = {
            "trade_count": 0,
            "trade_amount": 0.0,
            "pnl": 0.0,
            "max_drawdown": 0.0,
            "start_balance": 0.0
        }
        
        logger.info("Risk Control Service initialized")
    
    async def check_order_risk(self, order_data: Dict, account_data: Dict) -> RiskCheckResult:
        """综合订单风险检查"""
        try:
            # 执行多层风险检查
            checks = [
                self._check_capital_risk(order_data, account_data),
                self._check_position_risk(order_data, account_data),
                self._check_frequency_risk(order_data),
                self._check_concentration_risk(order_data, account_data),
                self._check_volatility_risk(order_data)
            ]
            
            # 等待所有检查完成
            results = await asyncio.gather(*checks, return_exceptions=True)
            
            # 分析检查结果
            failed_checks = []
            highest_risk_level = RiskLevel.LOW
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Risk check error: {result}")
                    continue
                
                if not result.passed:
                    failed_checks.append(result)
                
                # 更新最高风险等级
                if self._get_risk_level_priority(result.risk_level) > self._get_risk_level_priority(highest_risk_level):
                    highest_risk_level = result.risk_level
            
            # 生成最终结果
            if failed_checks:
                return RiskCheckResult(
                    passed=False,
                    risk_level=highest_risk_level,
                    risk_type=failed_checks[0].risk_type,
                    message=f"风险检查失败: {failed_checks[0].message}",
                    details={
                        "failed_checks": len(failed_checks),
                        "total_checks": len(results),
                        "failures": [check.message for check in failed_checks]
                    },
                    suggestions=self._generate_risk_suggestions(failed_checks)
                )
            else:
                return RiskCheckResult(
                    passed=True,
                    risk_level=highest_risk_level,
                    risk_type=RiskType.CAPITAL,
                    message="风险检查通过",
                    details={"all_checks_passed": True}
                )
                
        except Exception as e:
            logger.error(f"Order risk check failed: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.CAPITAL,
                message=f"风险检查异常: {str(e)}"
            )
    
    async def _check_capital_risk(self, order_data: Dict, account_data: Dict) -> RiskCheckResult:
        """检查资金风险"""
        try:
            order_amount = order_data.get("price", 0) * order_data.get("volume", 0)
            available_funds = account_data.get("available", 0)
            total_assets = account_data.get("total_asset", 0)
            
            # 检查单笔订单金额
            if order_amount > self.risk_limits.max_single_order_amount:
                return RiskCheckResult(
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    risk_type=RiskType.CAPITAL,
                    message=f"单笔订单金额{order_amount:.2f}超过限额{self.risk_limits.max_single_order_amount:.2f}"
                )
            
            # 检查可用资金
            if order_data.get("direction") == "BUY" and order_amount > available_funds:
                return RiskCheckResult(
                    passed=False,
                    risk_level=RiskLevel.CRITICAL,
                    risk_type=RiskType.CAPITAL,
                    message=f"可用资金不足，需要{order_amount:.2f}，可用{available_funds:.2f}"
                )
            
            # 检查资金使用比例
            if total_assets > 0:
                usage_ratio = (total_assets - available_funds + order_amount) / total_assets
                if usage_ratio > self.risk_limits.max_position_ratio:
                    return RiskCheckResult(
                        passed=False,
                        risk_level=RiskLevel.MEDIUM,
                        risk_type=RiskType.CAPITAL,
                        message=f"资金使用比例{usage_ratio:.2%}超过限制{self.risk_limits.max_position_ratio:.2%}"
                    )
            
            # 检查日交易限额
            daily_amount = self.daily_stats.get("trade_amount", 0) + order_amount
            if daily_amount > self.risk_limits.max_daily_trade_amount:
                return RiskCheckResult(
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    risk_type=RiskType.CAPITAL,
                    message=f"日交易金额{daily_amount:.2f}超过限额{self.risk_limits.max_daily_trade_amount:.2f}"
                )
            
            return RiskCheckResult(
                passed=True,
                risk_level=RiskLevel.LOW,
                risk_type=RiskType.CAPITAL,
                message="资金风险检查通过"
            )
            
        except Exception as e:
            logger.error(f"Capital risk check error: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.CAPITAL,
                message=f"资金风险检查异常: {str(e)}"
            )
    
    async def _check_position_risk(self, order_data: Dict, account_data: Dict) -> RiskCheckResult:
        """检查持仓风险"""
        try:
            symbol = order_data.get("symbol")
            direction = order_data.get("direction")
            volume = order_data.get("volume", 0)
            
            # 获取当前持仓
            current_position = self.current_positions.get(f"{symbol}_{direction}", {})
            current_volume = current_position.get("volume", 0)
            
            # 计算新的持仓量
            new_volume = current_volume + volume
            
            # 检查单个品种持仓比例
            total_assets = account_data.get("total_asset", 0)
            if total_assets > 0:
                position_value = new_volume * order_data.get("price", 0)
                position_ratio = position_value / total_assets
                
                if position_ratio > self.risk_limits.max_single_position_ratio:
                    return RiskCheckResult(
                        passed=False,
                        risk_level=RiskLevel.MEDIUM,
                        risk_type=RiskType.POSITION,
                        message=f"单品种持仓比例{position_ratio:.2%}超过限制{self.risk_limits.max_single_position_ratio:.2%}"
                    )
            
            return RiskCheckResult(
                passed=True,
                risk_level=RiskLevel.LOW,
                risk_type=RiskType.POSITION,
                message="持仓风险检查通过"
            )
            
        except Exception as e:
            logger.error(f"Position risk check error: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.POSITION,
                message=f"持仓风险检查异常: {str(e)}"
            )
    
    async def _check_frequency_risk(self, order_data: Dict) -> RiskCheckResult:
        """检查交易频率风险"""
        try:
            now = datetime.now()
            
            # 统计最近1分钟的订单数
            minute_ago = now - timedelta(minutes=1)
            recent_orders = [
                order for order in self.order_history
                if order.get("timestamp", datetime.min) > minute_ago
            ]
            
            if len(recent_orders) >= self.risk_limits.max_orders_per_minute:
                return RiskCheckResult(
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    risk_type=RiskType.FREQUENCY,
                    message=f"1分钟内订单数{len(recent_orders)}超过限制{self.risk_limits.max_orders_per_minute}"
                )
            
            # 统计今日订单数
            today = now.replace(hour=0, minute=0, second=0, microsecond=0)
            daily_orders = [
                order for order in self.order_history
                if order.get("timestamp", datetime.min) > today
            ]
            
            if len(daily_orders) >= self.risk_limits.max_orders_per_day:
                return RiskCheckResult(
                    passed=False,
                    risk_level=RiskLevel.MEDIUM,
                    risk_type=RiskType.FREQUENCY,
                    message=f"今日订单数{len(daily_orders)}超过限制{self.risk_limits.max_orders_per_day}"
                )
            
            return RiskCheckResult(
                passed=True,
                risk_level=RiskLevel.LOW,
                risk_type=RiskType.FREQUENCY,
                message="交易频率检查通过"
            )
            
        except Exception as e:
            logger.error(f"Frequency risk check error: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.FREQUENCY,
                message=f"频率风险检查异常: {str(e)}"
            )
    
    async def _check_concentration_risk(self, order_data: Dict, account_data: Dict) -> RiskCheckResult:
        """检查集中度风险"""
        try:
            # 简化的集中度检查
            # 在实际应用中，这里会检查行业集中度、地区集中度等
            
            return RiskCheckResult(
                passed=True,
                risk_level=RiskLevel.LOW,
                risk_type=RiskType.CONCENTRATION,
                message="集中度风险检查通过"
            )
            
        except Exception as e:
            logger.error(f"Concentration risk check error: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.CONCENTRATION,
                message=f"集中度风险检查异常: {str(e)}"
            )
    
    async def _check_volatility_risk(self, order_data: Dict) -> RiskCheckResult:
        """检查波动率风险"""
        try:
            # 简化的波动率检查
            # 在实际应用中，这里会分析历史价格波动率
            
            return RiskCheckResult(
                passed=True,
                risk_level=RiskLevel.LOW,
                risk_type=RiskType.VOLATILITY,
                message="波动率风险检查通过"
            )
            
        except Exception as e:
            logger.error(f"Volatility risk check error: {e}")
            return RiskCheckResult(
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                risk_type=RiskType.VOLATILITY,
                message=f"波动率风险检查异常: {str(e)}"
            )
    
    def _get_risk_level_priority(self, risk_level: RiskLevel) -> int:
        """获取风险等级优先级"""
        priority_map = {
            RiskLevel.LOW: 1,
            RiskLevel.MEDIUM: 2,
            RiskLevel.HIGH: 3,
            RiskLevel.CRITICAL: 4
        }
        return priority_map.get(risk_level, 0)
    
    def _generate_risk_suggestions(self, failed_checks: List[RiskCheckResult]) -> List[str]:
        """生成风险建议"""
        suggestions = []
        
        for check in failed_checks:
            if check.risk_type == RiskType.CAPITAL:
                suggestions.append("建议减少订单金额或增加资金")
            elif check.risk_type == RiskType.POSITION:
                suggestions.append("建议分散持仓或减少单一品种仓位")
            elif check.risk_type == RiskType.FREQUENCY:
                suggestions.append("建议降低交易频率，避免过度交易")
            elif check.risk_type == RiskType.CONCENTRATION:
                suggestions.append("建议分散投资，避免过度集中")
            elif check.risk_type == RiskType.VOLATILITY:
                suggestions.append("建议关注市场波动，适当降低仓位")
        
        return list(set(suggestions))  # 去重
    
    async def update_trading_record(self, trade_data: Dict):
        """更新交易记录"""
        try:
            self.trading_records.append({
                **trade_data,
                "timestamp": datetime.now()
            })
            
            # 更新日统计
            self.daily_stats["trade_count"] += 1
            self.daily_stats["trade_amount"] += trade_data.get("amount", 0)
            
            # 更新持仓
            await self._update_position(trade_data)
            
        except Exception as e:
            logger.error(f"Update trading record error: {e}")
    
    async def _update_position(self, trade_data: Dict):
        """更新持仓记录"""
        try:
            symbol = trade_data.get("symbol")
            direction = trade_data.get("direction")
            volume = trade_data.get("volume", 0)
            price = trade_data.get("price", 0)
            
            position_key = f"{symbol}_{direction}"
            
            if position_key in self.current_positions:
                # 更新现有持仓
                position = self.current_positions[position_key]
                total_volume = position["volume"] + volume
                total_cost = position["avg_price"] * position["volume"] + price * volume
                position["avg_price"] = total_cost / total_volume if total_volume > 0 else 0
                position["volume"] = total_volume
            else:
                # 创建新持仓
                self.current_positions[position_key] = {
                    "symbol": symbol,
                    "direction": direction,
                    "volume": volume,
                    "avg_price": price,
                    "market_value": volume * price
                }
                
        except Exception as e:
            logger.error(f"Update position error: {e}")
    
    async def get_risk_report(self) -> Dict:
        """获取风险报告"""
        try:
            return {
                "timestamp": datetime.now().isoformat(),
                "risk_limits": {
                    "max_single_order_amount": self.risk_limits.max_single_order_amount,
                    "max_daily_trade_amount": self.risk_limits.max_daily_trade_amount,
                    "max_position_ratio": self.risk_limits.max_position_ratio,
                    "max_daily_loss": self.risk_limits.max_daily_loss
                },
                "current_stats": self.daily_stats,
                "position_count": len(self.current_positions),
                "recent_alerts": self.risk_alerts[-10:],  # 最近10条告警
                "risk_status": "normal"  # 简化状态
            }
        except Exception as e:
            logger.error(f"Get risk report error: {e}")
            return {"error": str(e)}
    
    def add_risk_alert(self, alert_data: Dict):
        """添加风险告警"""
        try:
            self.risk_alerts.append({
                **alert_data,
                "timestamp": datetime.now(),
                "id": len(self.risk_alerts) + 1
            })
            
            # 保留最近1000条告警
            if len(self.risk_alerts) > 1000:
                self.risk_alerts = self.risk_alerts[-1000:]
                
        except Exception as e:
            logger.error(f"Add risk alert error: {e}")


# 全局风险控制服务实例
_risk_service: Optional[RiskControlService] = None


def get_risk_service() -> RiskControlService:
    """获取风险控制服务实例"""
    global _risk_service
    if _risk_service is None:
        _risk_service = RiskControlService()
    return _risk_service


async def initialize_risk_service(risk_limits: Optional[RiskLimit] = None) -> RiskControlService:
    """初始化风险控制服务"""
    global _risk_service
    _risk_service = RiskControlService(risk_limits)
    return _risk_service

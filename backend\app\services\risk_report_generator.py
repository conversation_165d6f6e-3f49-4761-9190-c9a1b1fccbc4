"""
风险报告生成器
生成日报、周报、月报等风险分析报告
"""
import io
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from jinja2 import Environment, FileSystemLoader
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import (
    Image,
    PageBreak,
    Paragraph,
    SimpleDocTemplate,
    Spacer,
    Table,
    TableStyle,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.trading import Order, Position, Trade
from app.db.models.user import User
from app.services.market_service import MarketService
from app.services.risk_service import RiskService

logger = logging.getLogger(__name__)


class RiskReportGenerator:
    """风险报告生成器"""
    
    def __init__(
        self,
        db: AsyncSession,
        risk_service: RiskService,
        market_service: MarketService,
        template_dir: str = "templates/reports"
    ):
        self.db = db
        self.risk_service = risk_service
        self.market_service = market_service
        self.template_env = Environment(loader=FileSystemLoader(template_dir))
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    async def generate_daily_report(
        self,
        user_id: int,
        report_date: Optional[datetime] = None
    ) -> bytes:
        """
        生成日度风险报告
        
        Args:
            user_id: 用户ID
            report_date: 报告日期，默认为当天
            
        Returns:
            PDF报告内容
        """
        if report_date is None:
            report_date = datetime.now().date()
            
        logger.info(f"生成用户 {user_id} 的日度风险报告: {report_date}")
        
        # 收集报告数据
        report_data = await self._collect_daily_data(user_id, report_date)
        
        # 生成图表
        charts = await self._generate_daily_charts(report_data)
        
        # 生成PDF
        pdf_buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            pdf_buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18,
        )
        
        # 构建PDF内容
        story = []
        styles = getSampleStyleSheet()
        
        # 标题
        title = f"Daily Risk Report - {report_date}"
        story.append(Paragraph(title, styles['Title']))
        story.append(Spacer(1, 12))
        
        # 摘要
        summary = self._generate_summary(report_data)
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        story.append(Paragraph(summary, styles['Normal']))
        story.append(Spacer(1, 12))
        
        # 风险指标表
        story.append(Paragraph("Risk Metrics", styles['Heading2']))
        risk_table = self._create_risk_metrics_table(report_data['risk_metrics'])
        story.append(risk_table)
        story.append(Spacer(1, 12))
        
        # 持仓分析
        story.append(Paragraph("Position Analysis", styles['Heading2']))
        position_table = self._create_position_table(report_data['positions'])
        story.append(position_table)
        story.append(PageBreak())
        
        # 交易分析
        story.append(Paragraph("Trading Analysis", styles['Heading2']))
        trading_table = self._create_trading_table(report_data['trades'])
        story.append(trading_table)
        story.append(Spacer(1, 12))
        
        # 图表
        for chart_name, chart_buffer in charts.items():
            story.append(Paragraph(chart_name, styles['Heading3']))
            img = Image(chart_buffer, width=6*inch, height=4*inch)
            story.append(img)
            story.append(Spacer(1, 12))
            
        # 风险警报
        if report_data['alerts']:
            story.append(Paragraph("Risk Alerts", styles['Heading2']))
            alerts_table = self._create_alerts_table(report_data['alerts'])
            story.append(alerts_table)
            
        # 建议
        story.append(PageBreak())
        story.append(Paragraph("Recommendations", styles['Heading2']))
        recommendations = self._generate_recommendations(report_data)
        for rec in recommendations:
            story.append(Paragraph(f"• {rec}", styles['Normal']))
            
        # 生成PDF
        doc.build(story)
        pdf_buffer.seek(0)
        
        return pdf_buffer.read()
    
    async def generate_portfolio_report(
        self,
        user_id: int,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        生成投资组合分析报告
        
        Args:
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            报告数据
        """
        logger.info(f"生成投资组合分析报告: {start_date} 至 {end_date}")
        
        # 获取持仓数据
        positions = await self._get_positions(user_id, end_date)
        
        # 计算组合指标
        portfolio_metrics = await self._calculate_portfolio_metrics(
            positions, start_date, end_date
        )
        
        # 资产配置分析
        allocation_analysis = self._analyze_allocation(positions)
        
        # 风险贡献分析
        risk_contribution = await self._calculate_risk_contribution(positions)
        
        # 相关性分析
        correlation_matrix = await self._calculate_correlation_matrix(
            positions, start_date, end_date
        )
        
        # 情景分析
        scenario_analysis = await self._perform_scenario_analysis(positions)
        
        # 归因分析
        attribution_analysis = await self._perform_attribution_analysis(
            positions, start_date, end_date
        )
        
        return {
            'period': {'start': start_date, 'end': end_date},
            'portfolio_metrics': portfolio_metrics,
            'allocation': allocation_analysis,
            'risk_contribution': risk_contribution,
            'correlation': correlation_matrix,
            'scenarios': scenario_analysis,
            'attribution': attribution_analysis,
            'positions': [self._position_to_dict(p) for p in positions]
        }
    
    async def generate_risk_dashboard_data(
        self,
        user_id: int
    ) -> Dict[str, Any]:
        """
        生成风险仪表板数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            仪表板数据
        """
        # 实时风险指标
        current_metrics = await self.risk_service.calculate_risk_metrics(user_id)
        
        # 历史风险趋势
        risk_history = await self._get_risk_history(user_id, days=30)
        
        # 风险热力图
        risk_heatmap = await self._generate_risk_heatmap(user_id)
        
        # 压力测试结果
        stress_test_results = await self._get_latest_stress_test(user_id)
        
        # 风险预警
        risk_warnings = await self._get_risk_warnings(user_id)
        
        return {
            'current_metrics': current_metrics,
            'risk_history': risk_history,
            'risk_heatmap': risk_heatmap,
            'stress_test': stress_test_results,
            'warnings': risk_warnings,
            'last_updated': datetime.now()
        }
    
    async def _collect_daily_data(
        self,
        user_id: int,
        report_date: datetime
    ) -> Dict[str, Any]:
        """收集日报数据"""
        # 获取用户信息
        user = await self.db.get(User, user_id)
        
        # 获取当日持仓
        positions = await self._get_positions(user_id, report_date)
        
        # 获取当日交易
        trades = await self._get_trades(user_id, report_date)
        
        # 计算风险指标
        risk_metrics = await self.risk_service.calculate_risk_metrics(user_id)
        
        # 获取风险警报
        alerts = await self._get_risk_alerts(user_id, report_date)
        
        # 计算收益统计
        pnl_stats = await self._calculate_pnl_stats(user_id, report_date)
        
        return {
            'user': user,
            'report_date': report_date,
            'positions': positions,
            'trades': trades,
            'risk_metrics': risk_metrics,
            'alerts': alerts,
            'pnl_stats': pnl_stats
        }
    
    async def _generate_daily_charts(
        self,
        report_data: Dict[str, Any]
    ) -> Dict[str, io.BytesIO]:
        """生成日报图表"""
        charts = {}
        
        # 1. 持仓分布饼图
        if report_data['positions']:
            fig, ax = plt.subplots(figsize=(8, 6))
            positions_df = pd.DataFrame([
                {'symbol': p.symbol, 'value': p.market_value}
                for p in report_data['positions']
            ])
            ax.pie(
                positions_df['value'],
                labels=positions_df['symbol'],
                autopct='%1.1f%%'
            )
            ax.set_title('Position Distribution')
            
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight')
            buffer.seek(0)
            charts['Position Distribution'] = buffer
            plt.close()
            
        # 2. 风险指标雷达图
        fig, ax = plt.subplots(figsize=(8, 6), subplot_kw=dict(projection='polar'))
        
        metrics = report_data['risk_metrics']
        categories = list(metrics.keys())[:6]  # 取前6个指标
        values = [metrics[cat] for cat in categories]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False)
        values = np.concatenate((values, [values[0]]))
        angles = np.concatenate((angles, [angles[0]]))
        
        ax.plot(angles, values, 'o-', linewidth=2)
        ax.fill(angles, values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_title('Risk Metrics Radar')
        
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', bbox_inches='tight')
        buffer.seek(0)
        charts['Risk Metrics'] = buffer
        plt.close()
        
        # 3. PnL趋势图
        if report_data['pnl_stats'].get('history'):
            fig, ax = plt.subplots(figsize=(10, 6))
            pnl_df = pd.DataFrame(report_data['pnl_stats']['history'])
            pnl_df['date'] = pd.to_datetime(pnl_df['date'])
            pnl_df.set_index('date', inplace=True)
            
            ax.plot(pnl_df.index, pnl_df['cumulative_pnl'], label='Cumulative PnL')
            ax.fill_between(
                pnl_df.index,
                pnl_df['cumulative_pnl'],
                0,
                where=pnl_df['cumulative_pnl'] > 0,
                alpha=0.3,
                color='green',
                label='Profit'
            )
            ax.fill_between(
                pnl_df.index,
                pnl_df['cumulative_pnl'],
                0,
                where=pnl_df['cumulative_pnl'] < 0,
                alpha=0.3,
                color='red',
                label='Loss'
            )
            
            ax.set_xlabel('Date')
            ax.set_ylabel('PnL')
            ax.set_title('Profit & Loss Trend')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight')
            buffer.seek(0)
            charts['PnL Trend'] = buffer
            plt.close()
            
        return charts
    
    def _generate_summary(self, report_data: Dict[str, Any]) -> str:
        """生成报告摘要"""
        metrics = report_data['risk_metrics']
        pnl = report_data['pnl_stats']
        
        summary_parts = []
        
        # 账户概况
        summary_parts.append(
            f"As of {report_data['report_date']}, the portfolio consists of "
            f"{len(report_data['positions'])} positions with a total market value of "
            f"${metrics.get('total_market_value', 0):,.2f}."
        )
        
        # 收益情况
        daily_pnl = pnl.get('daily_pnl', 0)
        if daily_pnl > 0:
            summary_parts.append(
                f"The portfolio generated a profit of ${daily_pnl:,.2f} "
                f"({pnl.get('daily_return', 0):.2%}) for the day."
            )
        else:
            summary_parts.append(
                f"The portfolio incurred a loss of ${abs(daily_pnl):,.2f} "
                f"({pnl.get('daily_return', 0):.2%}) for the day."
            )
            
        # 风险状况
        var = metrics.get('var_95', 0)
        summary_parts.append(
            f"The 95% VaR is ${var:,.2f}, indicating the maximum expected loss "
            f"under normal market conditions."
        )
        
        # 警报情况
        if report_data['alerts']:
            summary_parts.append(
                f"There are {len(report_data['alerts'])} active risk alerts "
                f"requiring attention."
            )
            
        return " ".join(summary_parts)
    
    def _create_risk_metrics_table(
        self,
        metrics: Dict[str, Any]
    ) -> Table:
        """创建风险指标表格"""
        data = [
            ['Metric', 'Value', 'Status'],
            ['Total Value', f"${metrics.get('total_market_value', 0):,.2f}", 'Normal'],
            ['VaR (95%)', f"${metrics.get('var_95', 0):,.2f}", 'Normal'],
            ['Max Drawdown', f"{metrics.get('max_drawdown', 0):.2%}", 'Normal'],
            ['Sharpe Ratio', f"{metrics.get('sharpe_ratio', 0):.2f}", 'Normal'],
            ['Leverage', f"{metrics.get('leverage', 0):.1f}x", 'Normal'],
            ['Concentration', f"{metrics.get('concentration', 0):.2%}", 'Normal'],
        ]
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        return table
    
    def _create_position_table(self, positions: List[Position]) -> Table:
        """创建持仓表格"""
        data = [
            ['Symbol', 'Quantity', 'Avg Cost', 'Market Price', 'Market Value', 'P&L', 'P&L %']
        ]
        
        for pos in positions[:10]:  # 只显示前10个
            pnl = pos.unrealized_pnl
            pnl_pct = pnl / pos.cost_basis if pos.cost_basis > 0 else 0
            
            data.append([
                pos.symbol,
                f"{pos.quantity:,}",
                f"${pos.average_cost:.2f}",
                f"${pos.last_price:.2f}",
                f"${pos.market_value:,.2f}",
                f"${pnl:,.2f}",
                f"{pnl_pct:.2%}"
            ])
            
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        return table
    
    def _create_trading_table(self, trades: List[Trade]) -> Table:
        """创建交易表格"""
        data = [
            ['Time', 'Symbol', 'Side', 'Quantity', 'Price', 'Amount', 'Fees']
        ]
        
        for trade in trades[:20]:  # 只显示前20笔
            data.append([
                trade.executed_at.strftime('%H:%M:%S'),
                trade.symbol,
                trade.side,
                f"{trade.quantity:,}",
                f"${trade.price:.2f}",
                f"${trade.quantity * trade.price:,.2f}",
                f"${trade.commission:.2f}"
            ])
            
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        return table
    
    def _create_alerts_table(self, alerts: List[Dict[str, Any]]) -> Table:
        """创建警报表格"""
        data = [
            ['Level', 'Type', 'Message', 'Time']
        ]
        
        for alert in alerts:
            data.append([
                alert['level'],
                alert['type'],
                alert['message'],
                alert['timestamp'].strftime('%H:%M:%S')
            ])
            
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        return table
    
    def _generate_recommendations(
        self,
        report_data: Dict[str, Any]
    ) -> List[str]:
        """生成投资建议"""
        recommendations = []
        metrics = report_data['risk_metrics']
        
        # 基于风险指标的建议
        if metrics.get('leverage', 0) > 5:
            recommendations.append(
                "Consider reducing leverage to manage risk exposure"
            )
            
        if metrics.get('concentration', 0) > 0.3:
            recommendations.append(
                "Diversify portfolio to reduce concentration risk"
            )
            
        if metrics.get('max_drawdown', 0) > 0.15:
            recommendations.append(
                "Review stop-loss strategies to limit drawdown"
            )
            
        # 基于警报的建议
        critical_alerts = [
            a for a in report_data['alerts'] 
            if a.get('level') == 'critical'
        ]
        if critical_alerts:
            recommendations.append(
                f"Address {len(critical_alerts)} critical risk alerts immediately"
            )
            
        # 基于市场条件的建议
        if metrics.get('market_volatility', 0) > 0.3:
            recommendations.append(
                "Consider reducing position sizes in high volatility environment"
            )
            
        if not recommendations:
            recommendations.append(
                "Portfolio risk metrics are within acceptable ranges"
            )
            
        return recommendations
    
    async def _get_positions(
        self,
        user_id: int,
        as_of_date: datetime
    ) -> List[Position]:
        """获取持仓数据"""
        query = select(Position).where(
            Position.user_id == user_id,
            Position.quantity > 0
        )
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def _get_trades(
        self,
        user_id: int,
        trade_date: datetime
    ) -> List[Trade]:
        """获取交易数据"""
        start_of_day = trade_date.replace(hour=0, minute=0, second=0)
        end_of_day = trade_date.replace(hour=23, minute=59, second=59)
        
        query = select(Trade).where(
            Trade.user_id == user_id,
            Trade.executed_at >= start_of_day,
            Trade.executed_at <= end_of_day
        )
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def _get_risk_alerts(
        self,
        user_id: int,
        alert_date: datetime
    ) -> List[Dict[str, Any]]:
        """获取风险警报"""
        # 这里应该从数据库查询
        # 简化实现，返回模拟数据
        return []
    
    async def _calculate_pnl_stats(
        self,
        user_id: int,
        calc_date: datetime
    ) -> Dict[str, Any]:
        """计算收益统计"""
        # 这里应该从数据库查询历史数据
        # 简化实现，返回模拟数据
        return {
            'daily_pnl': 1500.0,
            'daily_return': 0.015,
            'mtd_pnl': 15000.0,
            'ytd_pnl': 150000.0,
            'history': []
        }
    
    def _position_to_dict(self, position: Position) -> Dict[str, Any]:
        """将持仓对象转换为字典"""
        return {
            'symbol': position.symbol,
            'quantity': position.quantity,
            'average_cost': position.average_cost,
            'market_value': position.market_value,
            'unrealized_pnl': position.unrealized_pnl,
            'weight': 0  # 需要计算
        }
    
    async def _calculate_portfolio_metrics(
        self,
        positions: List[Position],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """计算组合指标"""
        # 这里应该实现完整的组合分析
        # 简化实现
        return {
            'total_return': 0.15,
            'annualized_return': 0.18,
            'volatility': 0.12,
            'sharpe_ratio': 1.5,
            'max_drawdown': -0.08,
            'calmar_ratio': 2.25,
            'beta': 0.95,
            'alpha': 0.03
        }
    
    def _analyze_allocation(
        self,
        positions: List[Position]
    ) -> Dict[str, Any]:
        """分析资产配置"""
        total_value = sum(p.market_value for p in positions)
        
        # 按资产类别分组
        allocation_by_type = {}
        allocation_by_sector = {}
        
        for position in positions:
            # 这里需要资产分类信息
            asset_type = 'equity'  # 简化处理
            sector = 'technology'  # 简化处理
            
            weight = position.market_value / total_value if total_value > 0 else 0
            
            allocation_by_type[asset_type] = allocation_by_type.get(asset_type, 0) + weight
            allocation_by_sector[sector] = allocation_by_sector.get(sector, 0) + weight
            
        return {
            'by_asset_type': allocation_by_type,
            'by_sector': allocation_by_sector,
            'concentration': max(allocation_by_type.values()) if allocation_by_type else 0
        }
    
    async def _calculate_risk_contribution(
        self,
        positions: List[Position]
    ) -> Dict[str, float]:
        """计算风险贡献"""
        # 这里应该使用风险模型计算
        # 简化实现
        risk_contributions = {}
        
        for position in positions:
            # 简化的风险贡献计算
            risk_contributions[position.symbol] = position.market_value * 0.01
            
        return risk_contributions
    
    async def _calculate_correlation_matrix(
        self,
        positions: List[Position],
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """计算相关性矩阵"""
        # 获取价格数据
        symbols = [p.symbol for p in positions]
        price_data = {}
        
        for symbol in symbols:
            prices = await self.market_service.get_historical_prices(
                symbol, start_date, end_date
            )
            price_data[symbol] = prices
            
        # 计算收益率
        returns_df = pd.DataFrame(price_data).pct_change().dropna()
        
        # 计算相关性矩阵
        correlation_matrix = returns_df.corr()
        
        return correlation_matrix
    
    async def _perform_scenario_analysis(
        self,
        positions: List[Position]
    ) -> List[Dict[str, Any]]:
        """执行情景分析"""
        scenarios = []
        
        # 定义情景
        scenario_definitions = [
            {'name': 'Market Crash', 'market_change': -0.20, 'vol_change': 2.0},
            {'name': 'Bull Market', 'market_change': 0.15, 'vol_change': 0.8},
            {'name': 'High Inflation', 'market_change': -0.05, 'vol_change': 1.5},
            {'name': 'Recession', 'market_change': -0.30, 'vol_change': 2.5},
        ]
        
        total_value = sum(p.market_value for p in positions)
        
        for scenario_def in scenario_definitions:
            # 简化的情景分析
            impact = total_value * scenario_def['market_change']
            
            scenarios.append({
                'name': scenario_def['name'],
                'probability': 0.1,  # 简化处理
                'impact': impact,
                'impact_pct': scenario_def['market_change'],
                'description': f"Portfolio value change: ${impact:,.2f}"
            })
            
        return scenarios
    
    async def _perform_attribution_analysis(
        self,
        positions: List[Position],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """执行归因分析"""
        # 这里应该实现完整的业绩归因
        # 简化实现
        return {
            'total_return': 0.15,
            'allocation_effect': 0.03,
            'selection_effect': 0.08,
            'interaction_effect': 0.04,
            'by_sector': {
                'technology': 0.06,
                'finance': 0.04,
                'healthcare': 0.03,
                'others': 0.02
            }
        }
    
    async def _get_risk_history(
        self,
        user_id: int,
        days: int
    ) -> List[Dict[str, Any]]:
        """获取历史风险数据"""
        # 这里应该从数据库查询
        # 简化实现
        history = []
        
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i)
            history.append({
                'date': date,
                'var_95': 10000 + np.random.normal(0, 1000),
                'sharpe_ratio': 1.5 + np.random.normal(0, 0.2),
                'max_drawdown': -0.08 + np.random.normal(0, 0.02)
            })
            
        return history
    
    async def _generate_risk_heatmap(
        self,
        user_id: int
    ) -> Dict[str, Any]:
        """生成风险热力图"""
        # 这里应该计算各种风险因子
        # 简化实现
        risk_factors = [
            'Market Risk', 'Credit Risk', 'Liquidity Risk',
            'Operational Risk', 'Concentration Risk'
        ]
        
        time_periods = ['1D', '1W', '1M', '3M', '1Y']
        
        heatmap_data = []
        for factor in risk_factors:
            row = []
            for period in time_periods:
                # 生成模拟风险值
                risk_value = np.random.uniform(0, 1)
                row.append(risk_value)
            heatmap_data.append(row)
            
        return {
            'factors': risk_factors,
            'periods': time_periods,
            'data': heatmap_data
        }
    
    async def _get_latest_stress_test(
        self,
        user_id: int
    ) -> Dict[str, Any]:
        """获取最新压力测试结果"""
        # 这里应该从数据库查询
        # 简化实现
        return {
            'test_date': datetime.now(),
            'scenarios': [
                {
                    'name': 'Severe Market Downturn',
                    'probability': 0.05,
                    'impact': -150000,
                    'var_breach': True
                },
                {
                    'name': 'Interest Rate Shock',
                    'probability': 0.1,
                    'impact': -80000,
                    'var_breach': False
                }
            ],
            'overall_risk_score': 7.5
        }
    
    async def _get_risk_warnings(
        self,
        user_id: int
    ) -> List[Dict[str, Any]]:
        """获取风险预警"""
        # 这里应该基于实时数据生成预警
        # 简化实现
        warnings = []
        
        # 检查各种风险条件
        if np.random.random() > 0.7:
            warnings.append({
                'level': 'high',
                'type': 'concentration',
                'message': 'Portfolio concentration exceeds threshold',
                'timestamp': datetime.now()
            })
            
        return warnings
# 风控服务
from datetime import date, datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.trading import Account, Order, Position, Trade
from app.db.models.user import User
from app.monitoring.risk_metrics import record_failure
from app.schemas.trading import (

    Direction,
    Offset,
    OrderRequest,
    OrderStatus,
    RiskCheckResult,
    RiskLimitData,

)
from app.utils.exceptions import DataNotFoundError


class RiskService:
    """风控服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def check_order_risk(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查订单风险"""

        # 1. 检查用户状态
        user_check = await self._check_user_status(user_id)
        if not user_check.passed:
            return user_check

        # 2. 检查资金充足性
        fund_check = await self._check_fund_sufficiency(user_id, order_request)
        if not fund_check.passed:
            record_failure("fund")
            return fund_check

        # 3. 检查持仓限制
        position_check = await self._check_position_limit(user_id, order_request)
        if not position_check.passed:
            return position_check

        # 3.1 检查动态保证金比例
        margin_check = await self._check_dynamic_margin(user_id, order_request)
        if not margin_check.passed:
            return margin_check

        # 3.2 检查止损保护
        stop_loss_check = await self._check_stop_loss(user_id, order_request)
        if not stop_loss_check.passed:
            return stop_loss_check

        # 4. 检查单笔委托限制
        order_size_check = await self._check_order_size_limit(user_id, order_request)
        if not order_size_check.passed:
            return order_size_check

        # 5. 检查日交易限制
        daily_limit_check = await self._check_daily_trading_limit(
            user_id, order_request
        )
        if not daily_limit_check.passed:
            return daily_limit_check

        # 6. 检查禁止交易合约
        symbol_check = await self._check_forbidden_symbols(
            user_id, order_request.symbol
        )
        if not symbol_check.passed:
            return symbol_check

        return RiskCheckResult(passed=True, message="风控检查通过")

    async def _check_user_status(self, user_id: int) -> RiskCheckResult:
        """检查用户状态"""
        result = await self.db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            record_failure("user_status")
            return RiskCheckResult(passed=False, message="用户不存在")

        if not user.is_active:
            return RiskCheckResult(passed=False, message="用户账户已禁用")

        # TODO: 检查用户是否有交易权限

        return RiskCheckResult(passed=True, message="用户状态正常")

    async def _check_fund_sufficiency(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查资金充足性"""
        # 获取账户信息
        result = await self.db.execute(
            select(Account).where(Account.user_id == user_id)
        )
        account = result.scalar_one_or_none()

        if not account:
            return RiskCheckResult(passed=False, message="账户信息不存在")

        # 计算所需保证金（简化计算）
        required_margin = self._calculate_required_margin(order_request)

        if account.available_cash < required_margin:
            return RiskCheckResult(
                passed=False,
                message=f"资金不足，可用资金: {account.available_cash}, 所需保证金: {required_margin}",
            )

        return RiskCheckResult(passed=True, message="资金充足")

    async def _check_position_limit(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查持仓限制"""
        # 获取当前持仓
        result = await self.db.execute(
            select(Position).where(
                and_(
                    Position.user_id == user_id, Position.symbol_code == order_request.symbol
                )
            )
        )
        positions = result.scalars().all()

        current_volume = sum(pos.quantity for pos in positions)

        # 计算开仓后的持仓量
        if order_request.offset == Offset.OPEN:
            new_volume = current_volume + order_request.volume
        else:
            new_volume = max(0, current_volume - order_request.volume)

        # 获取持仓限制（这里使用默认值，实际应该从配置中获取）
        max_position = await self._get_max_position_limit(user_id, order_request.symbol)

        if new_volume > max_position:
            return RiskCheckResult(
                passed=False,
                message=f"持仓超限，当前持仓: {current_volume}, 最大持仓: {max_position}",
            )

        return RiskCheckResult(passed=True, message="持仓检查通过")

    async def _check_order_size_limit(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查单笔委托限制"""
        max_order_size = await self._get_max_order_size_limit(
            user_id, order_request.symbol
        )

        if order_request.volume > max_order_size:
            return RiskCheckResult(
                passed=False,
                message=f"单笔委托超限，委托量: {order_request.volume}, 最大委托: {max_order_size}",
            )

        return RiskCheckResult(passed=True, message="单笔委托检查通过")

    async def _check_daily_trading_limit(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查日交易限制"""
        today = date.today()

        # 查询今日交易量
        result = await self.db.execute(
            select(func.sum(Trade.quantity)).where(
                and_(
                    Trade.user_id == user_id,
                    Trade.symbol_code == order_request.symbol,
                    func.date(Trade.trade_time) == today,
                )
            )
        )
        daily_volume = result.scalar() or 0

        # 获取日交易限制
        max_daily_volume = await self._get_max_daily_volume_limit(
            user_id, order_request.symbol
        )

        if daily_volume + order_request.volume > max_daily_volume:
            return RiskCheckResult(
                passed=False,
                message=f"日交易量超限，今日已交易: {daily_volume}, 最大日交易量: {max_daily_volume}",
            )

        return RiskCheckResult(passed=True, message="日交易限制检查通过")

    async def _check_forbidden_symbols(
        self, user_id: int, symbol: str
    ) -> RiskCheckResult:
        """检查禁止交易合约"""
        forbidden_symbols = await self._get_forbidden_symbols(user_id)

        if symbol in forbidden_symbols:
            return RiskCheckResult(passed=False, message=f"合约 {symbol} 被禁止交易")

        return RiskCheckResult(passed=True, message="合约检查通过")

    def _calculate_required_margin(self, order_request: OrderRequest) -> float:
        """计算所需保证金（简化版本）"""
        # 这里使用简化的保证金计算
        # 实际应该根据合约规格、保证金率等计算
        if order_request.price:
            return order_request.volume * order_request.price * 0.1  # 假设10%保证金率
        else:
            return order_request.volume * 100 * 0.1  # 市价单使用估算价格

    async def _get_max_position_limit(self, user_id: int, symbol: str) -> float:
        """获取最大持仓限制"""
        # 这里返回默认值，实际应该从数据库或配置中获取
        return 1000.0

    async def _get_max_order_size_limit(self, user_id: int, symbol: str) -> float:
        """获取最大单笔委托限制"""
        # 这里返回默认值，实际应该从数据库或配置中获取
        return 100.0

    async def _get_max_daily_volume_limit(self, user_id: int, symbol: str) -> float:
        """获取最大日交易量限制"""
        # 这里返回默认值，实际应该从数据库或配置中获取
        return 10000.0

    async def _get_forbidden_symbols(self, user_id: int) -> List[str]:
        """获取禁止交易的合约列表"""
        # 这里返回空列表，实际应该从数据库或配置中获取
        return []

    async def get_risk_limits(self, user_id: int) -> RiskLimitData:
        """获取风控限制"""
        return RiskLimitData(
            max_position=await self._get_max_position_limit(user_id, ""),
            max_order_size=await self._get_max_order_size_limit(user_id, ""),
            max_daily_loss=50000.0,  # 默认值
            max_total_loss=100000.0,  # 默认值
            allowed_symbols=[],
            forbidden_symbols=await self._get_forbidden_symbols(user_id),
        )

    async def get_user_risk_limits(self, user_id: int) -> RiskLimitData:
        """获取用户风控限制（trading API需要的方法名）"""
        return await self.get_risk_limits(user_id)

    async def update_risk_limits(
        self, user_id: int, risk_limits: RiskLimitData
    ) -> RiskLimitData:
        """更新风控限制"""
        from app.db.models.risk import RiskLimit
        from datetime import datetime
        
        try:
            # 查找现有风控记录
            result = await self.db.execute(
                select(RiskLimit).where(RiskLimit.user_id == user_id)
            )
            risk_limit_record = result.scalar_one_or_none()
            
            if risk_limit_record:
                # 更新现有记录
                risk_limit_record.daily_loss_limit = risk_limits.daily_loss_limit
                risk_limit_record.single_trade_limit = risk_limits.single_trade_limit
                risk_limit_record.position_limit = risk_limits.position_limit
                risk_limit_record.leverage_limit = risk_limits.leverage_limit
                risk_limit_record.updated_at = datetime.utcnow()
            else:
                # 创建新记录
                risk_limit_record = RiskLimit(
                    user_id=user_id,
                    daily_loss_limit=risk_limits.daily_loss_limit,
                    single_trade_limit=risk_limits.single_trade_limit,
                    position_limit=risk_limits.position_limit,
                    leverage_limit=risk_limits.leverage_limit,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(risk_limit_record)
            
            await self.db.commit()
            
            # 触发风控限制更新事件
            from app.monitoring.alerting import trigger_custom_alert, AlertSeverity
            trigger_custom_alert(
                f"risk_limits_updated_{user_id}",
                f"用户 {user_id} 风控限制已更新",
                AlertSeverity.LOW,
                {
                    "user_id": user_id,
                    "daily_loss_limit": risk_limits.daily_loss_limit,
                    "single_trade_limit": risk_limits.single_trade_limit
                }
            )
            
            return risk_limits
            
        except Exception as e:
            logger.error(f"更新风控限制失败 user_id={user_id}: {e}")
            await self.db.rollback()
            raise

    async def update_user_risk_limits(
        self, user_id: int, risk_limits: RiskLimitData
    ) -> RiskLimitData:
        """更新用户风控限制（trading API需要的方法名）"""
        return await self.update_risk_limits(user_id, risk_limits)

    async def check_daily_loss_limit(self, user_id: int) -> RiskCheckResult:
        """检查日亏损限制"""
        today = date.today()

        # 计算今日盈亏
        result = await self.db.execute(
            select(func.sum(Trade.quantity * Trade.price)).where(
                and_(Trade.user_id == user_id, func.date(Trade.trade_time) == today)
            )
        )
        daily_pnl = result.scalar() or 0

        max_daily_loss = 50000.0  # 默认值，应该从配置获取

        if daily_pnl < -max_daily_loss:
            return RiskCheckResult(
                passed=False, message=f"日亏损超限: {abs(daily_pnl)}"
            )

        return RiskCheckResult(passed=True, message="日亏损检查通过")

    async def check_total_loss_limit(self, user_id: int) -> RiskCheckResult:
        """检查总亏损限制"""
        # 计算总盈亏
        result = await self.db.execute(
            select(func.sum(Trade.quantity * Trade.price)).where(Trade.user_id == user_id)
        )
        total_pnl = result.scalar() or 0

        max_total_loss = 100000.0  # 默认值，应该从配置获取

        if total_pnl < -max_total_loss:
            return RiskCheckResult(False, f"总亏损超限: {abs(total_pnl)}")

        return RiskCheckResult(True, "总亏损检查通过")

    # ---------------------------------------------------------------------
    # 新增动态保证金与止损检查
    # ---------------------------------------------------------------------

    async def _check_dynamic_margin(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查动态保证金比例限制
        根据账户净值动态调整可用杠杆，示例实现：
        - 净值 < 100k: 最大杠杆 2x
        - 100k ≤ 净值 < 500k: 最大杠杆 3x
        - 净值 ≥ 500k: 最大杠杆 5x
        """
        # 获取账户信息
        result = await self.db.execute(
            select(Account).where(Account.user_id == user_id)
        )
        account: Account = result.scalar_one_or_none()
        if not account:
            return RiskCheckResult(passed=False, message="账户信息不存在")

        # 计算当前杠杆（简化：总市值 / 净资产）
        current_leverage = (
            account.market_value + account.available_cash + account.frozen_cash
        ) / max(account.total_assets, 1)

        # 根据净值设定最大杠杆
        if account.total_assets < 100_000:
            max_leverage = 2
        elif account.total_assets < 500_000:
            max_leverage = 3
        else:
            max_leverage = 5

        if current_leverage > max_leverage:
            return RiskCheckResult(
                passed=False,
                message=f"杠杆率超限，当前:{current_leverage:.2f}x，最大:{max_leverage}x",
            )

        return RiskCheckResult(passed=True, message="杠杆率检查通过")

    async def _check_stop_loss(
        self, user_id: int, order_request: OrderRequest
    ) -> RiskCheckResult:
        """检查止损保护，防止在止损触发后继续开仓同方向"""
        # 查询当天是否触发过强平或止损事件
        # 这里简化为检查账户当日亏损是否超过阈值
        result = await self.db.execute(
            select(Account).where(Account.user_id == user_id)
        )
        account: Account = result.scalar_one_or_none()
        if not account:
            return RiskCheckResult(passed=False, message="账户信息不存在")

        daily_loss_limit = 0.1  # 10% 止损线
        if account.daily_pnl is not None and account.daily_pnl < -abs(
            account.total_assets * daily_loss_limit
        ):
            return RiskCheckResult(
                passed=False, message="当日亏损超出止损线，禁止继续开仓"
            )
        return RiskCheckResult(passed=True, message="止损检查通过")

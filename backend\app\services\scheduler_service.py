"""
定时任务调度服务
使用APScheduler实现定时数据抓取任务
"""

import asyncio
import logging
from datetime import datetime, time
from typing import Optional, Dict, List
import pytz

try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    from apscheduler.triggers.interval import IntervalTrigger
    from apscheduler.jobstores.memory import MemoryJobStore
    from apscheduler.executors.asyncio import AsyncIOExecutor
    SCHEDULER_AVAILABLE = True
except ImportError:
    SCHEDULER_AVAILABLE = False
    logging.warning("APScheduler未安装，请运行: pip install apscheduler")

from app.services.data_crawler_service import run_daily_data_crawl, get_crawler_status

logger = logging.getLogger(__name__)

class SchedulerService:
    """定时任务调度服务"""
    
    def __init__(self):
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.is_running = False
        self.jobs = {}
        
        if SCHEDULER_AVAILABLE:
            # 配置调度器
            jobstores = {
                'default': MemoryJobStore()
            }
            executors = {
                'default': AsyncIOExecutor()
            }
            job_defaults = {
                'coalesce': False,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5分钟容错时间
            }
            
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone=pytz.timezone('Asia/Shanghai')
            )
        else:
            logger.error("APScheduler不可用，定时任务功能将被禁用")
    
    async def start(self):
        """启动调度器"""
        if not self.scheduler:
            logger.error("调度器未初始化")
            return
        
        if self.is_running:
            logger.warning("调度器已在运行")
            return
        
        try:
            self.scheduler.start()
            self.is_running = True
            
            # 添加默认任务
            await self._setup_default_jobs()
            
            logger.info("定时任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
    
    async def stop(self):
        """停止调度器"""
        if not self.scheduler or not self.is_running:
            return
        
        try:
            self.scheduler.shutdown(wait=False)
            self.is_running = False
            logger.info("定时任务调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    async def _setup_default_jobs(self):
        """设置默认任务"""
        # 每日18点数据抓取任务
        await self.add_daily_crawl_job()
        
        # 每小时清理任务
        await self.add_cleanup_job()
        
        # 每天凌晨2点的维护任务
        await self.add_maintenance_job()
    
    async def add_daily_crawl_job(self, hour: int = 18, minute: int = 0):
        """添加每日数据抓取任务"""
        if not self.scheduler:
            return
        
        job_id = "daily_data_crawl"
        
        try:
            # 移除已存在的任务
            if job_id in self.jobs:
                self.scheduler.remove_job(job_id)
            
            # 添加新任务
            job = self.scheduler.add_job(
                func=self._run_daily_crawl_wrapper,
                trigger=CronTrigger(hour=hour, minute=minute),
                id=job_id,
                name="每日数据抓取",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                "job": job,
                "description": f"每日{hour:02d}:{minute:02d}执行数据抓取",
                "last_run": None,
                "next_run": job.next_run_time,
                "status": "scheduled"
            }
            
            logger.info(f"添加每日数据抓取任务: {hour:02d}:{minute:02d}")
            
        except Exception as e:
            logger.error(f"添加每日抓取任务失败: {e}")
    
    async def add_cleanup_job(self, interval_hours: int = 1):
        """添加清理任务"""
        if not self.scheduler:
            return
        
        job_id = "hourly_cleanup"
        
        try:
            if job_id in self.jobs:
                self.scheduler.remove_job(job_id)
            
            job = self.scheduler.add_job(
                func=self._run_cleanup_wrapper,
                trigger=IntervalTrigger(hours=interval_hours),
                id=job_id,
                name="定期清理任务",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                "job": job,
                "description": f"每{interval_hours}小时执行清理任务",
                "last_run": None,
                "next_run": job.next_run_time,
                "status": "scheduled"
            }
            
            logger.info(f"添加清理任务: 每{interval_hours}小时执行")
            
        except Exception as e:
            logger.error(f"添加清理任务失败: {e}")
    
    async def add_maintenance_job(self, hour: int = 2, minute: int = 0):
        """添加维护任务"""
        if not self.scheduler:
            return
        
        job_id = "daily_maintenance"
        
        try:
            if job_id in self.jobs:
                self.scheduler.remove_job(job_id)
            
            job = self.scheduler.add_job(
                func=self._run_maintenance_wrapper,
                trigger=CronTrigger(hour=hour, minute=minute),
                id=job_id,
                name="每日维护任务",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                "job": job,
                "description": f"每日{hour:02d}:{minute:02d}执行维护任务",
                "last_run": None,
                "next_run": job.next_run_time,
                "status": "scheduled"
            }
            
            logger.info(f"添加维护任务: {hour:02d}:{minute:02d}")
            
        except Exception as e:
            logger.error(f"添加维护任务失败: {e}")
    
    async def _run_daily_crawl_wrapper(self):
        """数据抓取任务包装器"""
        job_id = "daily_data_crawl"
        
        try:
            logger.info("开始执行每日数据抓取任务")
            self.jobs[job_id]["status"] = "running"
            self.jobs[job_id]["last_run"] = datetime.now()
            
            # 执行数据抓取（300天数据，约15分钟完成）
            await run_daily_data_crawl(days=300)
            
            self.jobs[job_id]["status"] = "completed"
            logger.info("每日数据抓取任务完成")
            
        except Exception as e:
            self.jobs[job_id]["status"] = "failed"
            logger.error(f"每日数据抓取任务失败: {e}")
        finally:
            # 更新下次运行时间
            if job_id in self.jobs and self.jobs[job_id]["job"]:
                self.jobs[job_id]["next_run"] = self.jobs[job_id]["job"].next_run_time
    
    async def _run_cleanup_wrapper(self):
        """清理任务包装器"""
        job_id = "hourly_cleanup"
        
        try:
            logger.info("开始执行清理任务")
            self.jobs[job_id]["status"] = "running"
            self.jobs[job_id]["last_run"] = datetime.now()
            
            # 执行清理操作
            from app.services.tushare_data_service import tushare_service
            from app.services.akshare_data_service import akshare_service
            
            async with tushare_service:
                await tushare_service.cleanup_old_cache(days_to_keep=7)
            
            await akshare_service.cleanup_old_cache(days_to_keep=7)
            
            self.jobs[job_id]["status"] = "completed"
            logger.info("清理任务完成")
            
        except Exception as e:
            self.jobs[job_id]["status"] = "failed"
            logger.error(f"清理任务失败: {e}")
        finally:
            if job_id in self.jobs and self.jobs[job_id]["job"]:
                self.jobs[job_id]["next_run"] = self.jobs[job_id]["job"].next_run_time
    
    async def _run_maintenance_wrapper(self):
        """维护任务包装器"""
        job_id = "daily_maintenance"
        
        try:
            logger.info("开始执行维护任务")
            self.jobs[job_id]["status"] = "running"
            self.jobs[job_id]["last_run"] = datetime.now()
            
            # 执行维护操作
            # 1. 清理旧日志
            await self._cleanup_old_logs()
            
            # 2. 数据库维护（如果需要）
            # await self._database_maintenance()
            
            # 3. 系统状态检查
            await self._system_health_check()
            
            self.jobs[job_id]["status"] = "completed"
            logger.info("维护任务完成")
            
        except Exception as e:
            self.jobs[job_id]["status"] = "failed"
            logger.error(f"维护任务失败: {e}")
        finally:
            if job_id in self.jobs and self.jobs[job_id]["job"]:
                self.jobs[job_id]["next_run"] = self.jobs[job_id]["job"].next_run_time
    
    async def _cleanup_old_logs(self, days_to_keep: int = 30):
        """清理旧日志"""
        from pathlib import Path
        
        log_dirs = [
            Path("logs/crawler"),
            Path("logs/app"),
            Path("logs/error")
        ]
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cleaned_count = 0
        
        for log_dir in log_dirs:
            if log_dir.exists():
                for log_file in log_dir.glob("*.log"):
                    try:
                        file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
                        if file_date < cutoff_date:
                            log_file.unlink()
                            cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"删除日志文件失败: {e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了{cleaned_count}个旧日志文件")
    
    async def _system_health_check(self):
        """系统健康检查"""
        try:
            # 检查数据抓取器状态
            crawler_status = await get_crawler_status()
            
            # 检查磁盘空间
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free // (1024**3)
            
            if free_gb < 5:  # 少于5GB
                logger.warning(f"磁盘空间不足: {free_gb}GB")
            
            logger.info(f"系统健康检查完成 - 磁盘剩余: {free_gb}GB")
            
        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")
    
    def get_job_status(self, job_id: str = None) -> Dict:
        """获取任务状态"""
        if job_id:
            return self.jobs.get(job_id, {})
        
        return {
            "scheduler_running": self.is_running,
            "total_jobs": len(self.jobs),
            "jobs": {
                job_id: {
                    "description": job_info["description"],
                    "last_run": job_info["last_run"].isoformat() if job_info["last_run"] else None,
                    "next_run": job_info["next_run"].isoformat() if job_info["next_run"] else None,
                    "status": job_info["status"]
                }
                for job_id, job_info in self.jobs.items()
            }
        }
    
    async def trigger_job(self, job_id: str):
        """手动触发任务"""
        if not self.scheduler or job_id not in self.jobs:
            return False
        
        try:
            job = self.jobs[job_id]["job"]
            job.modify(next_run_time=datetime.now())
            logger.info(f"手动触发任务: {job_id}")
            return True
        except Exception as e:
            logger.error(f"触发任务失败: {e}")
            return False

# 创建全局实例
scheduler_service = SchedulerService()

# 导出主要接口
async def start_scheduler():
    """启动调度器"""
    await scheduler_service.start()

async def stop_scheduler():
    """停止调度器"""
    await scheduler_service.stop()

async def get_scheduler_status():
    """获取调度器状态"""
    return scheduler_service.get_job_status()

async def trigger_daily_crawl():
    """手动触发数据抓取"""
    return await scheduler_service.trigger_job("daily_data_crawl")

"""
简化版交易服务
用于开发测试，使用内存存储
"""

import asyncio
import logging
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional
import random

from app.schemas.trading import (

    OrderData, OrderRequest, OrderStatus, OrderType,
    Direction, Offset, PositionData, TradeData,
    AccountData

)

logger = logging.getLogger(__name__)

class SimpleTradingService:
    """简化版交易服务 - 使用内存存储"""
    
    def __init__(self):
        # 内存存储
        self.orders: Dict[str, OrderData] = {}
        self.positions: Dict[str, Dict[str, PositionData]] = {}  # user_id -> {symbol -> position}
        self.trades: Dict[str, List[TradeData]] = {}  # order_id -> trades
        self.accounts: Dict[str, AccountData] = {}  # user_id -> account
        
        # 初始化一些测试账户
        self._init_test_accounts()
        
    def _init_test_accounts(self):
        """初始化测试账户"""
        # 为admin用户创建账户
        self.accounts["admin_user"] = AccountData(
            account_id="admin_user",
            balance=1000000.00,  # 100万初始资金
            available=1000000.00,
            frozen=0.00,
            margin=0.00,
            close_profit=0.00,
            holding_profit=0.00,
            trading_day=datetime.now().strftime("%Y%m%d")
        )
    
    async def submit_order(self, user_id: str, order_request: OrderRequest) -> OrderData:
        """提交订单"""
        # 生成订单ID
        order_id = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(1000, 9999)}"
        
        # 检查账户
        account = self.accounts.get(user_id)
        if not account:
            raise ValueError("账户不存在")
        
        # 计算所需资金
        required_amount = Decimal(str(order_request.price)) * order_request.volume
        if account.available < required_amount:
            raise ValueError("可用资金不足")
        
        # 创建订单
        order = OrderData(
            order_id=order_id,
            user_id=user_id,
            symbol=order_request.symbol,
            exchange=order_request.exchange or "SSE",
            direction=order_request.direction,
            offset=order_request.offset,
            order_type=order_request.order_type,
            status=OrderStatus.SUBMITTED,
            price=Decimal(str(order_request.price)),
            volume=order_request.volume,
            traded_volume=0,
            limit_price=Decimal(str(order_request.price)),
            order_time=datetime.now(),
            update_time=datetime.now(),
            status_msg="订单已提交",
            frozen_margin=Decimal("0"),
            frozen_commission=Decimal("0")
        )
        
        # 冻结资金
        account.available -= required_amount
        account.frozen += required_amount
        
        # 保存订单
        self.orders[order_id] = order
        
        # 模拟订单执行
        asyncio.create_task(self._simulate_order_execution(order_id))
        
        logger.info(f"订单已提交: {order_id}")
        return order
    
    async def cancel_order(self, user_id: str, order_id: str) -> bool:
        """撤销订单"""
        order = self.orders.get(order_id)
        if not order:
            raise ValueError("订单不存在")
        
        if order.user_id != user_id:
            raise ValueError("无权操作此订单")
        
        if order.status in [OrderStatus.ALL_FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
            raise ValueError(f"订单状态不允许撤销: {order.status}")
        
        # 更新订单状态
        order.status = OrderStatus.CANCELLED
        order.status_msg = "用户撤销"
        order.update_time = datetime.now()
        
        # 解冻资金
        account = self.accounts.get(user_id)
        if account:
            unfilled_volume = order.volume - order.traded_volume
            unfilled_amount = order.price * unfilled_volume
            account.frozen -= unfilled_amount
            account.available += unfilled_amount
        
        logger.info(f"订单已撤销: {order_id}")
        return True
    
    async def get_orders(
        self, 
        user_id: str, 
        status: Optional[OrderStatus] = None,
        symbol: Optional[str] = None,
        limit: int = 100
    ) -> List[OrderData]:
        """查询订单"""
        orders = []
        
        for order in self.orders.values():
            if order.user_id != user_id:
                continue
                
            if status and order.status != status:
                continue
                
            if symbol and order.symbol != symbol:
                continue
                
            orders.append(order)
        
        # 按时间倒序排序
        orders.sort(key=lambda x: x.order_time, reverse=True)
        
        return orders[:limit]
    
    async def get_positions(self, user_id: str) -> List[PositionData]:
        """查询持仓"""
        user_positions = self.positions.get(user_id, {})
        return list(user_positions.values())
    
    async def get_account(self, user_id: str) -> Optional[AccountData]:
        """查询账户信息"""
        return self.accounts.get(user_id)
    
    async def get_trades(
        self,
        user_id: str,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        limit: int = 100
    ) -> List[TradeData]:
        """查询成交记录"""
        trades = []
        
        for oid, order_trades in self.trades.items():
            # 检查订单所有者
            order = self.orders.get(oid)
            if not order or order.user_id != user_id:
                continue
            
            if order_id and oid != order_id:
                continue
                
            if symbol and order.symbol != symbol:
                continue
            
            trades.extend(order_trades)
        
        # 按时间倒序排序
        trades.sort(key=lambda x: x.trade_time, reverse=True)
        
        return trades[:limit]
    
    async def _simulate_order_execution(self, order_id: str):
        """模拟订单执行"""
        await asyncio.sleep(random.uniform(0.5, 2))  # 模拟延迟
        
        order = self.orders.get(order_id)
        if not order or order.status != OrderStatus.SUBMITTED:
            return
        
        # 模拟成交
        success_rate = 0.8  # 80%成交率
        if random.random() < success_rate:
            # 全部成交
            trade_id = f"TRD{datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(1000, 9999)}"
            
            trade = TradeData(
                trade_id=trade_id,
                order_id=order_id,
                symbol=order.symbol,
                exchange=order.exchange,
                direction=order.direction,
                offset=order.offset,
                price=order.price,
                volume=order.volume,
                trade_time=datetime.now(),
                commission=order.price * order.volume * Decimal("0.0003")  # 万3手续费
            )
            
            # 保存成交记录
            if order_id not in self.trades:
                self.trades[order_id] = []
            self.trades[order_id].append(trade)
            
            # 更新订单状态
            order.traded_volume = order.volume
            order.status = OrderStatus.ALL_FILLED
            order.status_msg = "全部成交"
            order.update_time = datetime.now()
            
            # 更新持仓
            await self._update_position(order, trade)
            
            # 更新账户
            await self._update_account(order, trade)
            
            logger.info(f"订单成交: {order_id}, 成交价: {trade.price}, 成交量: {trade.volume}")
        else:
            # 模拟拒绝
            order.status = OrderStatus.REJECTED
            order.status_msg = "交易所拒绝"
            order.update_time = datetime.now()
            
            # 解冻资金
            account = self.accounts.get(order.user_id)
            if account:
                frozen_amount = order.price * order.volume
                account.frozen -= frozen_amount
                account.available += frozen_amount
            
            logger.info(f"订单被拒绝: {order_id}")
    
    async def _update_position(self, order: OrderData, trade: TradeData):
        """更新持仓"""
        user_id = order.user_id
        symbol = order.symbol
        
        if user_id not in self.positions:
            self.positions[user_id] = {}
        
        if symbol not in self.positions[user_id]:
            # 新建持仓
            self.positions[user_id][symbol] = PositionData(
                user_id=user_id,
                symbol=symbol,
                exchange=order.exchange,
                direction="long" if order.direction == Direction.BUY else "short",
                volume=0,
                frozen_volume=0,
                available_volume=0,
                avg_price=Decimal("0"),
                position_profit=Decimal("0"),
                last_price=trade.price,
                market_value=Decimal("0"),
                margin=Decimal("0"),
                update_time=datetime.now()
            )
        
        position = self.positions[user_id][symbol]
        
        if order.direction == Direction.BUY:
            # 买入增加持仓
            total_cost = position.avg_price * position.volume + trade.price * trade.volume
            position.volume += trade.volume
            position.available_volume += trade.volume
            position.avg_price = total_cost / position.volume if position.volume > 0 else Decimal("0")
        else:
            # 卖出减少持仓
            position.volume -= trade.volume
            position.available_volume -= trade.volume
            
            # 计算平仓盈亏
            close_profit = (trade.price - position.avg_price) * trade.volume
            account = self.accounts.get(user_id)
            if account:
                account.close_profit += close_profit
        
        # 更新市值
        position.market_value = position.volume * position.last_price
        position.update_time = datetime.now()
        
        # 如果持仓为0，删除记录
        if position.volume == 0:
            del self.positions[user_id][symbol]
    
    async def _update_account(self, order: OrderData, trade: TradeData):
        """更新账户"""
        account = self.accounts.get(order.user_id)
        if not account:
            return
        
        # 解冻资金
        frozen_amount = order.price * trade.volume
        account.frozen -= frozen_amount
        
        if order.direction == Direction.BUY:
            # 买入扣除资金
            account.balance -= frozen_amount + trade.commission
        else:
            # 卖出增加资金
            trade_amount = trade.price * trade.volume
            account.balance += trade_amount - trade.commission
            account.available += trade_amount - trade.commission
        
        # 累计手续费
        account.commission += trade.commission
        
        # 更新风险度
        if account.balance > 0:
            used_amount = sum(
                pos.market_value 
                for pos in self.positions.get(order.user_id, {}).values()
            )
            account.risk_degree = (used_amount / account.balance) * 100
        
        account.update_time = datetime.now()

# 全局实例
simple_trading_service = SimpleTradingService()
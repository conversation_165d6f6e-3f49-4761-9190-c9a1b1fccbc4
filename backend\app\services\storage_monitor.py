"""
存储空间监控模块
监控数据存储空间使用情况，实现智能清理和告警
"""

import asyncio
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from loguru import logger

from app.core.data_storage import StorageConfig
from app.core.monitoring import system_monitor, AlertLevel


class StorageSpaceMonitor:
    """存储空间监控器"""
    
    def __init__(self):
        self.monitoring_enabled = True
        self.check_interval = 300  # 5分钟检查一次
        self.cleanup_enabled = True
        
        # 存储路径配置
        self.storage_paths = {
            'data_root': StorageConfig.DATA_ROOT,
            'daily_cache': StorageConfig.DAILY_CACHE_DIR,
            'historical': StorageConfig.HISTORICAL_DIR,
            'realtime': StorageConfig.REALTIME_DIR
        }
        
        # 清理策略配置
        self.cleanup_policies = {
            'daily_cache': {
                'max_age_days': StorageConfig.DAILY_CACHE_DAYS,
                'max_size_mb': 1000,  # 1GB
                'file_pattern': '*.pkl.gz'
            },
            'realtime': {
                'max_age_hours': 1,
                'max_size_mb': 100,   # 100MB
                'file_pattern': '*'
            },
            'historical': {
                'max_age_years': StorageConfig.HISTORICAL_YEARS,
                'max_size_gb': 10,    # 10GB
                'file_pattern': '*.parquet'
            }
        }
        
        # 告警阈值
        self.alert_thresholds = {
            'disk_usage_warning': 80,    # 磁盘使用率80%告警
            'disk_usage_critical': 95,   # 磁盘使用率95%严重告警
            'directory_size_warning': 500,  # 目录大小500MB告警
            'directory_size_critical': 1000  # 目录大小1GB严重告警
        }
        
        # 启动监控任务
        asyncio.create_task(self._start_monitoring())
    
    async def _start_monitoring(self):
        """启动存储监控"""
        logger.info("启动存储空间监控...")
        
        while self.monitoring_enabled:
            try:
                await self._check_storage_usage()
                await self._check_directory_sizes()
                
                if self.cleanup_enabled:
                    await self._perform_cleanup()
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"存储监控异常: {e}")
                await asyncio.sleep(60)
    
    async def _check_storage_usage(self):
        """检查存储使用情况"""
        try:
            # 检查数据根目录所在磁盘
            disk_usage = shutil.disk_usage(StorageConfig.DATA_ROOT)
            
            total_gb = disk_usage.total / (1024**3)
            used_gb = disk_usage.used / (1024**3)
            free_gb = disk_usage.free / (1024**3)
            usage_percent = (used_gb / total_gb) * 100
            
            # 记录磁盘使用指标
            system_monitor.record_metric("storage.disk.total_gb", total_gb)
            system_monitor.record_metric("storage.disk.used_gb", used_gb)
            system_monitor.record_metric("storage.disk.free_gb", free_gb)
            system_monitor.record_metric("storage.disk.usage_percent", usage_percent)
            
            # 检查告警阈值
            if usage_percent >= self.alert_thresholds['disk_usage_critical']:
                logger.critical(f"磁盘使用率严重告警: {usage_percent:.1f}% (阈值: {self.alert_thresholds['disk_usage_critical']}%)")
            elif usage_percent >= self.alert_thresholds['disk_usage_warning']:
                logger.warning(f"磁盘使用率告警: {usage_percent:.1f}% (阈值: {self.alert_thresholds['disk_usage_warning']}%)")
            
            logger.debug(f"磁盘使用情况: {used_gb:.2f}GB / {total_gb:.2f}GB ({usage_percent:.1f}%)")
            
        except Exception as e:
            logger.error(f"检查磁盘使用情况失败: {e}")
    
    async def _check_directory_sizes(self):
        """检查目录大小"""
        for path_name, path in self.storage_paths.items():
            try:
                if not path.exists():
                    continue
                
                # 计算目录大小
                total_size = self._get_directory_size(path)
                size_mb = total_size / (1024**2)
                
                # 记录目录大小指标
                system_monitor.record_metric(f"storage.{path_name}.size_mb", size_mb)
                
                # 检查告警阈值
                if size_mb >= self.alert_thresholds['directory_size_critical']:
                    logger.critical(f"目录大小严重告警: {path_name} {size_mb:.1f}MB")
                elif size_mb >= self.alert_thresholds['directory_size_warning']:
                    logger.warning(f"目录大小告警: {path_name} {size_mb:.1f}MB")
                
                # 统计文件数量
                file_count = len(list(path.rglob('*'))) if path.is_dir() else 0
                system_monitor.record_metric(f"storage.{path_name}.file_count", file_count)
                
                logger.debug(f"目录 {path_name}: {size_mb:.2f}MB, {file_count} 个文件")
                
            except Exception as e:
                logger.error(f"检查目录大小失败 {path_name}: {e}")
    
    def _get_directory_size(self, path: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        
        try:
            if path.is_file():
                return path.stat().st_size
            
            for item in path.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
                    
        except Exception as e:
            logger.error(f"计算目录大小失败 {path}: {e}")
        
        return total_size
    
    async def _perform_cleanup(self):
        """执行清理操作"""
        logger.debug("开始存储清理...")
        
        total_cleaned_size = 0
        total_cleaned_files = 0
        
        for path_name, path in self.storage_paths.items():
            if path_name not in self.cleanup_policies:
                continue
            
            try:
                policy = self.cleanup_policies[path_name]
                cleaned_size, cleaned_files = await self._cleanup_directory(path, policy)
                
                total_cleaned_size += cleaned_size
                total_cleaned_files += cleaned_files
                
                if cleaned_files > 0:
                    logger.info(f"清理目录 {path_name}: {cleaned_files} 个文件, {cleaned_size / (1024**2):.2f}MB")
                
            except Exception as e:
                logger.error(f"清理目录失败 {path_name}: {e}")
        
        if total_cleaned_files > 0:
            logger.info(f"存储清理完成: 总计 {total_cleaned_files} 个文件, {total_cleaned_size / (1024**2):.2f}MB")
            
            # 记录清理指标
            system_monitor.record_metric("storage.cleanup.files_cleaned", total_cleaned_files)
            system_monitor.record_metric("storage.cleanup.size_cleaned_mb", total_cleaned_size / (1024**2))
    
    async def _cleanup_directory(self, directory: Path, policy: Dict[str, Any]) -> tuple[int, int]:
        """清理目录"""
        if not directory.exists():
            return 0, 0
        
        cleaned_size = 0
        cleaned_files = 0
        current_time = datetime.now()
        
        try:
            # 按文件模式查找文件
            file_pattern = policy.get('file_pattern', '*')
            files = list(directory.rglob(file_pattern))
            
            for file_path in files:
                if not file_path.is_file():
                    continue
                
                # 检查文件年龄
                file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                should_delete = False
                
                # 按年龄清理
                if 'max_age_days' in policy:
                    if file_age.days > policy['max_age_days']:
                        should_delete = True
                
                if 'max_age_hours' in policy:
                    if file_age.total_seconds() > policy['max_age_hours'] * 3600:
                        should_delete = True
                
                if 'max_age_years' in policy:
                    if file_age.days > policy['max_age_years'] * 365:
                        should_delete = True
                
                # 删除文件
                if should_delete:
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        
                        cleaned_size += file_size
                        cleaned_files += 1
                        
                        logger.debug(f"删除过期文件: {file_path}")
                        
                    except Exception as e:
                        logger.error(f"删除文件失败 {file_path}: {e}")
            
            # 检查目录大小限制
            if cleaned_files == 0:  # 如果没有按年龄清理，检查大小限制
                await self._cleanup_by_size(directory, policy)
            
        except Exception as e:
            logger.error(f"清理目录异常 {directory}: {e}")
        
        return cleaned_size, cleaned_files
    
    async def _cleanup_by_size(self, directory: Path, policy: Dict[str, Any]):
        """按大小清理目录"""
        try:
            # 检查大小限制
            max_size_bytes = 0
            if 'max_size_mb' in policy:
                max_size_bytes = policy['max_size_mb'] * 1024 * 1024
            elif 'max_size_gb' in policy:
                max_size_bytes = policy['max_size_gb'] * 1024 * 1024 * 1024
            
            if max_size_bytes == 0:
                return
            
            current_size = self._get_directory_size(directory)
            
            if current_size <= max_size_bytes:
                return
            
            # 获取文件列表并按修改时间排序（最旧的先删除）
            files = []
            file_pattern = policy.get('file_pattern', '*')
            
            for file_path in directory.rglob(file_pattern):
                if file_path.is_file():
                    files.append((file_path, file_path.stat().st_mtime))
            
            files.sort(key=lambda x: x[1])  # 按修改时间排序
            
            # 删除最旧的文件直到满足大小限制
            for file_path, _ in files:
                if current_size <= max_size_bytes:
                    break
                
                try:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    current_size -= file_size
                    
                    logger.debug(f"按大小限制删除文件: {file_path}")
                    
                except Exception as e:
                    logger.error(f"删除文件失败 {file_path}: {e}")
            
        except Exception as e:
            logger.error(f"按大小清理失败 {directory}: {e}")
    
    def get_storage_summary(self) -> Dict[str, Any]:
        """获取存储摘要"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'monitoring_enabled': self.monitoring_enabled,
            'cleanup_enabled': self.cleanup_enabled,
            'paths': {},
            'disk_usage': {}
        }
        
        try:
            # 磁盘使用情况
            disk_usage = shutil.disk_usage(StorageConfig.DATA_ROOT)
            summary['disk_usage'] = {
                'total_gb': round(disk_usage.total / (1024**3), 2),
                'used_gb': round(disk_usage.used / (1024**3), 2),
                'free_gb': round(disk_usage.free / (1024**3), 2),
                'usage_percent': round((disk_usage.used / disk_usage.total) * 100, 2)
            }
            
            # 各目录情况
            for path_name, path in self.storage_paths.items():
                if path.exists():
                    size_bytes = self._get_directory_size(path)
                    file_count = len(list(path.rglob('*'))) if path.is_dir() else 0
                    
                    summary['paths'][path_name] = {
                        'path': str(path),
                        'size_mb': round(size_bytes / (1024**2), 2),
                        'file_count': file_count,
                        'exists': True
                    }
                else:
                    summary['paths'][path_name] = {
                        'path': str(path),
                        'size_mb': 0,
                        'file_count': 0,
                        'exists': False
                    }
            
        except Exception as e:
            logger.error(f"获取存储摘要失败: {e}")
            summary['error'] = str(e)
        
        return summary
    
    async def force_cleanup(self) -> Dict[str, Any]:
        """强制执行清理"""
        logger.info("开始强制存储清理...")
        
        start_time = datetime.now()
        await self._perform_cleanup()
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        return {
            'message': '强制清理完成',
            'duration_seconds': duration,
            'timestamp': end_time.isoformat()
        }
    
    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        logger.info("存储监控已启用")
    
    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        logger.info("存储监控已禁用")
    
    def enable_cleanup(self):
        """启用自动清理"""
        self.cleanup_enabled = True
        logger.info("存储自动清理已启用")
    
    def disable_cleanup(self):
        """禁用自动清理"""
        self.cleanup_enabled = False
        logger.info("存储自动清理已禁用")


# 全局实例
storage_monitor = StorageSpaceMonitor()


# 导出主要组件
__all__ = [
    'StorageSpaceMonitor',
    'storage_monitor'
]

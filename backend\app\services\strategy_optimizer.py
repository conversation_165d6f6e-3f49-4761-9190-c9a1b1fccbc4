"""
策略优化器
提供网格搜索、遗传算法等参数优化方法
"""
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.model_selection import ParameterGrid

from app.services.backtest_engine import BacktestEngine, BacktestResult

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """优化结果"""
    best_params: Dict[str, Any]
    best_score: float
    all_results: List[Dict[str, Any]]
    optimization_stats: Dict[str, Any]


class StrategyOptimizer:
    """策略参数优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        data_loader: Callable,
        optimization_metric: str = "sharpe_ratio",
        n_jobs: int = -1
    ):
        """
        初始化策略优化器
        
        Args:
            strategy_class: 策略类
            data_loader: 数据加载函数
            optimization_metric: 优化目标指标
            n_jobs: 并行任务数，-1表示使用所有CPU
        """
        self.strategy_class = strategy_class
        self.data_loader = data_loader
        self.optimization_metric = optimization_metric
        self.n_jobs = n_jobs if n_jobs > 0 else None
        
    def grid_search(
        self,
        param_grid: Dict[str, List[Any]],
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        initial_capital: float = 1_000_000,
        commission_rate: float = 0.0003,
        slippage: float = 0.0,
        validation_split: float = 0.2
    ) -> OptimizationResult:
        """
        网格搜索优化
        
        Args:
            param_grid: 参数网格
            symbol: 标的代码
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点
            validation_split: 验证集比例
            
        Returns:
            优化结果
        """
        logger.info(f"开始网格搜索优化，参数组合数: {len(ParameterGrid(param_grid))}")
        
        # 分割训练集和验证集
        total_days = (end_date - start_date).days
        train_days = int(total_days * (1 - validation_split))
        train_end = start_date + pd.Timedelta(days=train_days)
        
        # 生成所有参数组合
        param_combinations = list(ParameterGrid(param_grid))
        
        # 并行执行回测
        results = []
        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            # 提交任务
            future_to_params = {
                executor.submit(
                    self._run_single_backtest,
                    params,
                    symbol,
                    start_date,
                    train_end,
                    initial_capital,
                    commission_rate,
                    slippage
                ): params
                for params in param_combinations
            }
            
            # 收集结果
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    result = future.result()
                    results.append({
                        'params': params,
                        'train_metrics': result.metrics,
                        'train_score': result.metrics[self.optimization_metric]
                    })
                except Exception as e:
                    logger.error(f"参数 {params} 回测失败: {e}")
                    
        # 按训练集得分排序
        results.sort(key=lambda x: x['train_score'], reverse=True)
        
        # 使用最佳参数在验证集上测试
        best_params = results[0]['params']
        validation_result = self._run_single_backtest(
            best_params,
            symbol,
            train_end,
            end_date,
            initial_capital,
            commission_rate,
            slippage
        )
        
        # 计算优化统计
        train_scores = [r['train_score'] for r in results]
        optimization_stats = {
            'total_combinations': len(param_combinations),
            'best_train_score': results[0]['train_score'],
            'validation_score': validation_result.metrics[self.optimization_metric],
            'overfit_ratio': (
                results[0]['train_score'] / validation_result.metrics[self.optimization_metric] 
                if validation_result.metrics[self.optimization_metric] != 0 else np.inf
            ),
            'score_mean': np.mean(train_scores),
            'score_std': np.std(train_scores),
            'score_distribution': {
                'q25': np.percentile(train_scores, 25),
                'q50': np.percentile(train_scores, 50),
                'q75': np.percentile(train_scores, 75),
            }
        }
        
        return OptimizationResult(
            best_params=best_params,
            best_score=validation_result.metrics[self.optimization_metric],
            all_results=results[:10],  # 返回前10个结果
            optimization_stats=optimization_stats
        )
    
    def walk_forward_optimization(
        self,
        param_grid: Dict[str, List[Any]],
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        window_size: int = 252,  # 训练窗口大小（天）
        step_size: int = 63,     # 步进大小（天）
        initial_capital: float = 1_000_000,
        commission_rate: float = 0.0003,
        slippage: float = 0.0
    ) -> OptimizationResult:
        """
        滚动窗口优化（Walk Forward Optimization）
        
        Args:
            param_grid: 参数网格
            symbol: 标的代码
            start_date: 开始日期
            end_date: 结束日期
            window_size: 训练窗口大小
            step_size: 步进大小
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点
            
        Returns:
            优化结果
        """
        logger.info("开始滚动窗口优化")
        
        # 生成滚动窗口
        windows = []
        current_start = start_date
        
        while current_start + pd.Timedelta(days=window_size + step_size) <= end_date:
            train_start = current_start
            train_end = current_start + pd.Timedelta(days=window_size)
            test_start = train_end
            test_end = test_start + pd.Timedelta(days=step_size)
            
            windows.append({
                'train': (train_start, train_end),
                'test': (test_start, test_end)
            })
            
            current_start += pd.Timedelta(days=step_size)
            
        logger.info(f"生成 {len(windows)} 个滚动窗口")
        
        # 对每个窗口进行优化和验证
        window_results = []
        cumulative_equity = initial_capital
        
        for i, window in enumerate(windows):
            logger.info(f"处理窗口 {i+1}/{len(windows)}")
            
            # 在训练集上优化
            train_result = self.grid_search(
                param_grid,
                symbol,
                window['train'][0],
                window['train'][1],
                cumulative_equity,
                commission_rate,
                slippage,
                validation_split=0  # 不使用验证集
            )
            
            # 在测试集上验证
            test_result = self._run_single_backtest(
                train_result.best_params,
                symbol,
                window['test'][0],
                window['test'][1],
                cumulative_equity,
                commission_rate,
                slippage
            )
            
            # 更新累积资金
            cumulative_equity = test_result.equity_curve.iloc[-1]
            
            window_results.append({
                'window': i,
                'train_period': window['train'],
                'test_period': window['test'],
                'best_params': train_result.best_params,
                'train_score': train_result.best_score,
                'test_score': test_result.metrics[self.optimization_metric],
                'test_metrics': test_result.metrics
            })
            
        # 计算总体表现
        total_return = (cumulative_equity / initial_capital) - 1
        test_scores = [r['test_score'] for r in window_results]
        
        optimization_stats = {
            'total_windows': len(windows),
            'total_return': total_return,
            'average_test_score': np.mean(test_scores),
            'test_score_std': np.std(test_scores),
            'consistency_ratio': np.mean(test_scores) / (np.std(test_scores) + 1e-9),
            'parameter_stability': self._calculate_parameter_stability(window_results)
        }
        
        # 选择最稳定的参数组合
        param_frequency = {}
        for result in window_results:
            param_str = str(result['best_params'])
            param_frequency[param_str] = param_frequency.get(param_str, 0) + 1
            
        most_frequent_params = eval(max(param_frequency, key=param_frequency.get))
        
        return OptimizationResult(
            best_params=most_frequent_params,
            best_score=np.mean(test_scores),
            all_results=window_results,
            optimization_stats=optimization_stats
        )
    
    def monte_carlo_optimization(
        self,
        param_ranges: Dict[str, Tuple[float, float]],
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        n_iterations: int = 1000,
        initial_capital: float = 1_000_000,
        commission_rate: float = 0.0003,
        slippage: float = 0.0
    ) -> OptimizationResult:
        """
        蒙特卡洛随机搜索优化
        
        Args:
            param_ranges: 参数范围字典 {参数名: (最小值, 最大值)}
            symbol: 标的代码
            start_date: 开始日期
            end_date: 结束日期
            n_iterations: 迭代次数
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点
            
        Returns:
            优化结果
        """
        logger.info(f"开始蒙特卡洛优化，迭代次数: {n_iterations}")
        
        # 生成随机参数组合
        param_combinations = []
        for _ in range(n_iterations):
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if isinstance(min_val, int) and isinstance(max_val, int):
                    params[param_name] = np.random.randint(min_val, max_val + 1)
                else:
                    params[param_name] = np.random.uniform(min_val, max_val)
            param_combinations.append(params)
            
        # 并行执行回测
        results = []
        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            future_to_params = {
                executor.submit(
                    self._run_single_backtest,
                    params,
                    symbol,
                    start_date,
                    end_date,
                    initial_capital,
                    commission_rate,
                    slippage
                ): params
                for params in param_combinations
            }
            
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    result = future.result()
                    results.append({
                        'params': params,
                        'metrics': result.metrics,
                        'score': result.metrics[self.optimization_metric]
                    })
                except Exception as e:
                    logger.error(f"参数 {params} 回测失败: {e}")
                    
        # 按得分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        # 计算优化统计
        scores = [r['score'] for r in results]
        optimization_stats = {
            'total_iterations': n_iterations,
            'successful_iterations': len(results),
            'best_score': results[0]['score'],
            'score_mean': np.mean(scores),
            'score_std': np.std(scores),
            'convergence_rate': self._calculate_convergence_rate(scores),
            'parameter_importance': self._calculate_parameter_importance(results, param_ranges)
        }
        
        return OptimizationResult(
            best_params=results[0]['params'],
            best_score=results[0]['score'],
            all_results=results[:20],  # 返回前20个结果
            optimization_stats=optimization_stats
        )
    
    def _run_single_backtest(
        self,
        params: Dict[str, Any],
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        initial_capital: float,
        commission_rate: float,
        slippage: float
    ) -> BacktestResult:
        """运行单次回测"""
        # 创建策略实例
        strategy = self.strategy_class(**params)
        
        # 创建回测引擎
        engine = BacktestEngine(
            data_loader=self.data_loader,
            strategy_callable=strategy.generate_signals,
            symbol=symbol,
            start=start_date,
            end=end_date,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            slippage=slippage
        )
        
        # 运行回测
        return engine.run()
    
    def _calculate_parameter_stability(
        self, 
        window_results: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """计算参数稳定性"""
        param_changes = {}
        
        if len(window_results) < 2:
            return {}
            
        # 获取所有参数名
        param_names = list(window_results[0]['best_params'].keys())
        
        for param_name in param_names:
            values = [r['best_params'][param_name] for r in window_results]
            
            # 计算变化率
            if all(isinstance(v, (int, float)) for v in values):
                # 数值参数：计算变异系数
                stability = 1 - (np.std(values) / (np.mean(values) + 1e-9))
            else:
                # 类别参数：计算出现频率
                unique_values = len(set(values))
                stability = 1 - (unique_values - 1) / len(values)
                
            param_changes[param_name] = stability
            
        return param_changes
    
    def _calculate_convergence_rate(self, scores: List[float]) -> float:
        """计算收敛速度"""
        if len(scores) < 10:
            return 0.0
            
        # 计算移动平均
        window_size = max(len(scores) // 10, 5)
        moving_avg = pd.Series(scores).rolling(window_size).mean().dropna()
        
        if len(moving_avg) < 2:
            return 0.0
            
        # 计算趋势斜率
        x = np.arange(len(moving_avg))
        slope, _, r_value, _, _ = stats.linregress(x, moving_avg)
        
        # 返回R²值作为收敛度量
        return r_value ** 2
    
    def _calculate_parameter_importance(
        self, 
        results: List[Dict[str, Any]], 
        param_ranges: Dict[str, Tuple[float, float]]
    ) -> Dict[str, float]:
        """计算参数重要性（基于相关性）"""
        importance = {}
        
        # 提取参数值和得分
        param_names = list(param_ranges.keys())
        scores = [r['score'] for r in results]
        
        for param_name in param_names:
            values = [r['params'][param_name] for r in results]
            
            # 只对数值参数计算相关性
            if all(isinstance(v, (int, float)) for v in values):
                correlation = abs(np.corrcoef(values, scores)[0, 1])
                importance[param_name] = correlation if not np.isnan(correlation) else 0.0
            else:
                importance[param_name] = 0.0
                
        # 归一化
        total_importance = sum(importance.values())
        if total_importance > 0:
            importance = {k: v/total_importance for k, v in importance.items()}
            
        return importance
#!/usr/bin/env python3
"""
策略模板库
提供常用的量化交易策略模板
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from loguru import logger


class StrategyTemplates:
    """策略模板管理器"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
        logger.info(f"策略模板库初始化完成，共 {len(self.templates)} 个模板")
    
    def _initialize_templates(self) -> Dict[str, Dict[str, Any]]:
        """初始化策略模板"""
        return {
            "ma_crossover": {
                "name": "移动平均线交叉策略",
                "description": "基于短期和长期移动平均线交叉的经典趋势跟踪策略",
                "category": "趋势跟踪",
                "risk_level": "中等",
                "parameters": {
                    "short_period": {
                        "type": "int",
                        "default": 5,
                        "min": 3,
                        "max": 20,
                        "description": "短期移动平均线周期"
                    },
                    "long_period": {
                        "type": "int", 
                        "default": 20,
                        "min": 10,
                        "max": 100,
                        "description": "长期移动平均线周期"
                    },
                    "symbol": {
                        "type": "str",
                        "default": "000001.SZ",
                        "description": "交易标的"
                    },
                    "position_size": {
                        "type": "float",
                        "default": 0.1,
                        "min": 0.01,
                        "max": 1.0,
                        "description": "仓位大小（占总资金比例）"
                    }
                },
                "code": '''
async def execute(context):
    """移动平均线交叉策略执行函数"""
    from app.services.technical_indicators import technical_indicators
    
    # 获取参数
    symbol = context['config']['symbol']
    short_period = context['config']['short_period']
    long_period = context['config']['long_period']
    position_size = context['config']['position_size']
    
    # 获取历史数据
    market_data = context.get('market_data', {})
    if not market_data or symbol not in market_data:
        return []
    
    ohlcv = market_data[symbol]
    close_prices = ohlcv.get('close', [])
    
    if len(close_prices) < long_period:
        return []
    
    # 计算移动平均线
    short_ma = technical_indicators.sma(close_prices, short_period)
    long_ma = technical_indicators.sma(close_prices, long_period)
    
    if len(short_ma) < 2 or len(long_ma) < 2:
        return []
    
    # 获取当前和前一个值
    current_short = short_ma[-1]
    current_long = long_ma[-1]
    prev_short = short_ma[-2]
    prev_long = long_ma[-2]
    
    signals = []
    current_position = context.get('positions', {}).get(symbol, 0)
    
    # 金叉：短期均线上穿长期均线，买入信号
    if current_short > current_long and prev_short <= prev_long and current_position <= 0:
        signals.append({
            'symbol': symbol,
            'direction': 'BUY',
            'quantity': int(10000 * position_size / close_prices[-1]),
            'type': 'MARKET',
            'reason': f'金叉买入信号: 短期MA({current_short:.2f}) > 长期MA({current_long:.2f})'
        })
    
    # 死叉：短期均线下穿长期均线，卖出信号
    elif current_short < current_long and prev_short >= prev_long and current_position > 0:
        signals.append({
            'symbol': symbol,
            'direction': 'SELL',
            'quantity': abs(current_position),
            'type': 'MARKET',
            'reason': f'死叉卖出信号: 短期MA({current_short:.2f}) < 长期MA({current_long:.2f})'
        })
    
    return signals
'''
            },
            
            "rsi_reversal": {
                "name": "RSI反转策略",
                "description": "基于RSI指标的超买超卖反转策略",
                "category": "均值回归",
                "risk_level": "中等",
                "parameters": {
                    "rsi_period": {
                        "type": "int",
                        "default": 14,
                        "min": 5,
                        "max": 30,
                        "description": "RSI计算周期"
                    },
                    "oversold_threshold": {
                        "type": "float",
                        "default": 30,
                        "min": 10,
                        "max": 40,
                        "description": "超卖阈值"
                    },
                    "overbought_threshold": {
                        "type": "float",
                        "default": 70,
                        "min": 60,
                        "max": 90,
                        "description": "超买阈值"
                    },
                    "symbol": {
                        "type": "str",
                        "default": "000001.SZ",
                        "description": "交易标的"
                    }
                },
                "code": '''
async def execute(context):
    """RSI反转策略执行函数"""
    from app.services.technical_indicators import technical_indicators
    
    # 获取参数
    symbol = context['config']['symbol']
    rsi_period = context['config']['rsi_period']
    oversold = context['config']['oversold_threshold']
    overbought = context['config']['overbought_threshold']
    
    # 获取历史数据
    market_data = context.get('market_data', {})
    if not market_data or symbol not in market_data:
        return []
    
    close_prices = market_data[symbol].get('close', [])
    
    if len(close_prices) < rsi_period + 1:
        return []
    
    # 计算RSI
    rsi_values = technical_indicators.rsi(close_prices, rsi_period)
    
    if len(rsi_values) < 2:
        return []
    
    current_rsi = rsi_values[-1]
    prev_rsi = rsi_values[-2]
    current_position = context.get('positions', {}).get(symbol, 0)
    
    signals = []
    
    # 超卖反转：RSI从超卖区域向上突破
    if current_rsi > oversold and prev_rsi <= oversold and current_position <= 0:
        signals.append({
            'symbol': symbol,
            'direction': 'BUY',
            'quantity': 100,
            'type': 'MARKET',
            'reason': f'RSI超卖反转买入: {current_rsi:.2f}'
        })
    
    # 超买反转：RSI从超买区域向下突破
    elif current_rsi < overbought and prev_rsi >= overbought and current_position > 0:
        signals.append({
            'symbol': symbol,
            'direction': 'SELL',
            'quantity': abs(current_position),
            'type': 'MARKET',
            'reason': f'RSI超买反转卖出: {current_rsi:.2f}'
        })
    
    return signals
'''
            },
            
            "bollinger_breakout": {
                "name": "布林带突破策略",
                "description": "基于布林带上下轨突破的趋势跟踪策略",
                "category": "突破",
                "risk_level": "高",
                "parameters": {
                    "bb_period": {
                        "type": "int",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "布林带周期"
                    },
                    "bb_std": {
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 3.0,
                        "description": "布林带标准差倍数"
                    },
                    "symbol": {
                        "type": "str",
                        "default": "000001.SZ",
                        "description": "交易标的"
                    }
                },
                "code": '''
async def execute(context):
    """布林带突破策略执行函数"""
    from app.services.technical_indicators import technical_indicators
    
    # 获取参数
    symbol = context['config']['symbol']
    bb_period = context['config']['bb_period']
    bb_std = context['config']['bb_std']
    
    # 获取历史数据
    market_data = context.get('market_data', {})
    if not market_data or symbol not in market_data:
        return []
    
    close_prices = market_data[symbol].get('close', [])
    
    if len(close_prices) < bb_period:
        return []
    
    # 计算布林带
    bb = technical_indicators.bollinger_bands(close_prices, bb_period, bb_std)
    
    if not bb['upper'] or len(bb['upper']) < 2:
        return []
    
    current_price = close_prices[-1]
    prev_price = close_prices[-2]
    current_upper = bb['upper'][-1]
    current_lower = bb['lower'][-1]
    prev_upper = bb['upper'][-2] if len(bb['upper']) > 1 else current_upper
    prev_lower = bb['lower'][-2] if len(bb['lower']) > 1 else current_lower
    
    current_position = context.get('positions', {}).get(symbol, 0)
    signals = []
    
    # 向上突破上轨
    if current_price > current_upper and prev_price <= prev_upper and current_position <= 0:
        signals.append({
            'symbol': symbol,
            'direction': 'BUY',
            'quantity': 100,
            'type': 'MARKET',
            'reason': f'突破布林带上轨: {current_price:.2f} > {current_upper:.2f}'
        })
    
    # 向下突破下轨
    elif current_price < current_lower and prev_price >= prev_lower and current_position >= 0:
        signals.append({
            'symbol': symbol,
            'direction': 'SELL',
            'quantity': 100,
            'type': 'MARKET',
            'reason': f'跌破布林带下轨: {current_price:.2f} < {current_lower:.2f}'
        })
    
    return signals
'''
            },
            
            "macd_divergence": {
                "name": "MACD背离策略",
                "description": "基于MACD指标与价格背离的反转策略",
                "category": "背离",
                "risk_level": "中高",
                "parameters": {
                    "fast_period": {
                        "type": "int",
                        "default": 12,
                        "min": 5,
                        "max": 20,
                        "description": "MACD快线周期"
                    },
                    "slow_period": {
                        "type": "int",
                        "default": 26,
                        "min": 15,
                        "max": 40,
                        "description": "MACD慢线周期"
                    },
                    "signal_period": {
                        "type": "int",
                        "default": 9,
                        "min": 5,
                        "max": 15,
                        "description": "MACD信号线周期"
                    },
                    "symbol": {
                        "type": "str",
                        "default": "000001.SZ",
                        "description": "交易标的"
                    }
                },
                "code": '''
async def execute(context):
    """MACD背离策略执行函数"""
    from app.services.technical_indicators import technical_indicators
    
    # 获取参数
    symbol = context['config']['symbol']
    fast_period = context['config']['fast_period']
    slow_period = context['config']['slow_period']
    signal_period = context['config']['signal_period']
    
    # 获取历史数据
    market_data = context.get('market_data', {})
    if not market_data or symbol not in market_data:
        return []
    
    close_prices = market_data[symbol].get('close', [])
    
    if len(close_prices) < slow_period + signal_period:
        return []
    
    # 计算MACD
    macd_data = technical_indicators.macd(close_prices, fast_period, slow_period, signal_period)
    
    if not macd_data['macd'] or len(macd_data['macd']) < 2:
        return []
    
    macd_line = macd_data['macd']
    signal_line = macd_data['signal']
    
    if not signal_line or len(signal_line) < 2:
        return []
    
    current_macd = macd_line[-1]
    current_signal = signal_line[-1]
    prev_macd = macd_line[-2]
    prev_signal = signal_line[-2]
    
    current_position = context.get('positions', {}).get(symbol, 0)
    signals = []
    
    # MACD金叉：MACD线上穿信号线
    if current_macd > current_signal and prev_macd <= prev_signal and current_position <= 0:
        signals.append({
            'symbol': symbol,
            'direction': 'BUY',
            'quantity': 100,
            'type': 'MARKET',
            'reason': f'MACD金叉买入: MACD({current_macd:.4f}) > Signal({current_signal:.4f})'
        })
    
    # MACD死叉：MACD线下穿信号线
    elif current_macd < current_signal and prev_macd >= prev_signal and current_position > 0:
        signals.append({
            'symbol': symbol,
            'direction': 'SELL',
            'quantity': abs(current_position),
            'type': 'MARKET',
            'reason': f'MACD死叉卖出: MACD({current_macd:.4f}) < Signal({current_signal:.4f})'
        })
    
    return signals
'''
            },
            
            "grid_trading": {
                "name": "网格交易策略",
                "description": "在震荡市场中通过网格买卖获利的策略",
                "category": "网格",
                "risk_level": "中等",
                "parameters": {
                    "grid_size": {
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "网格间距（百分比）"
                    },
                    "grid_count": {
                        "type": "int",
                        "default": 10,
                        "min": 5,
                        "max": 20,
                        "description": "网格数量"
                    },
                    "base_price": {
                        "type": "float",
                        "default": 0,
                        "description": "基准价格（0为自动计算）"
                    },
                    "symbol": {
                        "type": "str",
                        "default": "000001.SZ",
                        "description": "交易标的"
                    }
                },
                "code": '''
async def execute(context):
    """网格交易策略执行函数"""
    
    # 获取参数
    symbol = context['config']['symbol']
    grid_size = context['config']['grid_size']
    grid_count = context['config']['grid_count']
    base_price = context['config']['base_price']
    
    # 获取历史数据
    market_data = context.get('market_data', {})
    if not market_data or symbol not in market_data:
        return []
    
    close_prices = market_data[symbol].get('close', [])
    
    if not close_prices:
        return []
    
    current_price = close_prices[-1]
    
    # 如果没有设置基准价格，使用当前价格
    if base_price == 0:
        base_price = current_price
    
    # 计算网格价位
    grid_levels = []
    for i in range(-grid_count//2, grid_count//2 + 1):
        level_price = base_price * (1 + i * grid_size)
        grid_levels.append(level_price)
    
    current_position = context.get('positions', {}).get(symbol, 0)
    signals = []
    
    # 找到当前价格对应的网格位置
    for i, level in enumerate(grid_levels):
        if abs(current_price - level) / level < grid_size / 2:
            # 在网格点附近，根据位置决定买卖
            if i < len(grid_levels) // 2 and current_position <= 0:
                # 下方网格，买入
                signals.append({
                    'symbol': symbol,
                    'direction': 'BUY',
                    'quantity': 100,
                    'type': 'LIMIT',
                    'price': level,
                    'reason': f'网格买入: 价格{current_price:.2f}接近网格{level:.2f}'
                })
            elif i > len(grid_levels) // 2 and current_position > 0:
                # 上方网格，卖出
                signals.append({
                    'symbol': symbol,
                    'direction': 'SELL',
                    'quantity': 100,
                    'type': 'LIMIT',
                    'price': level,
                    'reason': f'网格卖出: 价格{current_price:.2f}接近网格{level:.2f}'
                })
            break
    
    return signals
'''
            }
        }
    
    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取策略模板"""
        return self.templates.get(template_id)
    
    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略模板"""
        return self.templates
    
    def get_templates_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """按分类获取策略模板"""
        return {
            template_id: template
            for template_id, template in self.templates.items()
            if template.get("category") == category
        }
    
    def get_template_categories(self) -> List[str]:
        """获取所有策略分类"""
        categories = set()
        for template in self.templates.values():
            if template.get("category"):
                categories.add(template["category"])
        return sorted(list(categories))
    
    def create_strategy_from_template(self, template_id: str, custom_params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """从模板创建策略"""
        template = self.get_template(template_id)
        if not template:
            return None
        
        strategy = {
            "name": template["name"],
            "description": template["description"],
            "category": template.get("category", "未分类"),
            "risk_level": template.get("risk_level", "未知"),
            "code": template["code"],
            "config": {}
        }
        
        # 设置默认参数
        for param_name, param_info in template["parameters"].items():
            strategy["config"][param_name] = param_info["default"]
        
        # 应用自定义参数
        if custom_params:
            strategy["config"].update(custom_params)
        
        return strategy


# 全局策略模板管理器实例
strategy_templates = StrategyTemplates()

#!/usr/bin/env python3
"""
技术指标计算服务
提供常用技术指标的计算功能
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from loguru import logger


class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def sma(data: List[float], period: int) -> List[float]:
        """简单移动平均线 (Simple Moving Average)"""
        if len(data) < period:
            return []
        
        result = []
        for i in range(period - 1, len(data)):
            avg = sum(data[i - period + 1:i + 1]) / period
            result.append(avg)
        
        return result
    
    @staticmethod
    def ema(data: List[float], period: int) -> List[float]:
        """指数移动平均线 (Exponential Moving Average)"""
        if len(data) < period:
            return []
        
        multiplier = 2 / (period + 1)
        result = []
        
        # 第一个EMA值使用SMA
        sma_value = sum(data[:period]) / period
        result.append(sma_value)
        
        # 后续EMA值
        for i in range(period, len(data)):
            ema_value = (data[i] * multiplier) + (result[-1] * (1 - multiplier))
            result.append(ema_value)
        
        return result
    
    @staticmethod
    def rsi(data: List[float], period: int = 14) -> List[float]:
        """相对强弱指数 (Relative Strength Index)"""
        if len(data) < period + 1:
            return []
        
        # 计算价格变化
        changes = [data[i] - data[i-1] for i in range(1, len(data))]
        
        # 分离上涨和下跌
        gains = [change if change > 0 else 0 for change in changes]
        losses = [-change if change < 0 else 0 for change in changes]
        
        result = []
        
        # 计算初始平均收益和损失
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        for i in range(period, len(changes)):
            if avg_loss == 0:
                rsi_value = 100
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
            
            result.append(rsi_value)
            
            # 更新平均收益和损失
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
        
        return result
    
    @staticmethod
    def macd(data: List[float], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, List[float]]:
        """MACD指标 (Moving Average Convergence Divergence)"""
        if len(data) < slow_period:
            return {"macd": [], "signal": [], "histogram": []}
        
        # 计算快线和慢线EMA
        fast_ema = TechnicalIndicators.ema(data, fast_period)
        slow_ema = TechnicalIndicators.ema(data, slow_period)
        
        # 对齐数据长度
        min_length = min(len(fast_ema), len(slow_ema))
        fast_ema = fast_ema[-min_length:]
        slow_ema = slow_ema[-min_length:]
        
        # 计算MACD线
        macd_line = [fast_ema[i] - slow_ema[i] for i in range(min_length)]
        
        # 计算信号线
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)
        
        # 计算柱状图
        histogram = []
        signal_start = len(macd_line) - len(signal_line)
        for i in range(len(signal_line)):
            histogram.append(macd_line[signal_start + i] - signal_line[i])
        
        return {
            "macd": macd_line,
            "signal": signal_line,
            "histogram": histogram
        }
    
    @staticmethod
    def bollinger_bands(data: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, List[float]]:
        """布林带 (Bollinger Bands)"""
        if len(data) < period:
            return {"upper": [], "middle": [], "lower": []}
        
        middle_band = TechnicalIndicators.sma(data, period)
        
        upper_band = []
        lower_band = []
        
        for i in range(period - 1, len(data)):
            # 计算标准差
            subset = data[i - period + 1:i + 1]
            std = np.std(subset)
            
            middle = middle_band[i - period + 1]
            upper_band.append(middle + (std_dev * std))
            lower_band.append(middle - (std_dev * std))
        
        return {
            "upper": upper_band,
            "middle": middle_band,
            "lower": lower_band
        }
    
    @staticmethod
    def kdj(high: List[float], low: List[float], close: List[float], 
            k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Dict[str, List[float]]:
        """KDJ指标"""
        if len(high) < k_period or len(low) < k_period or len(close) < k_period:
            return {"k": [], "d": [], "j": []}
        
        rsv = []  # 未成熟随机值
        
        for i in range(k_period - 1, len(close)):
            highest = max(high[i - k_period + 1:i + 1])
            lowest = min(low[i - k_period + 1:i + 1])
            
            if highest == lowest:
                rsv_value = 50
            else:
                rsv_value = (close[i] - lowest) / (highest - lowest) * 100
            
            rsv.append(rsv_value)
        
        # 计算K值
        k_values = []
        k_value = 50  # 初始K值
        
        for rsv_value in rsv:
            k_value = (2 * k_value + rsv_value) / 3
            k_values.append(k_value)
        
        # 计算D值
        d_values = []
        d_value = 50  # 初始D值
        
        for k_value in k_values:
            d_value = (2 * d_value + k_value) / 3
            d_values.append(d_value)
        
        # 计算J值
        j_values = [3 * k_values[i] - 2 * d_values[i] for i in range(len(k_values))]
        
        return {
            "k": k_values,
            "d": d_values,
            "j": j_values
        }
    
    @staticmethod
    def stochastic(high: List[float], low: List[float], close: List[float], 
                   k_period: int = 14, d_period: int = 3) -> Dict[str, List[float]]:
        """随机指标 (Stochastic Oscillator)"""
        if len(high) < k_period:
            return {"k": [], "d": []}
        
        k_values = []
        
        for i in range(k_period - 1, len(close)):
            highest = max(high[i - k_period + 1:i + 1])
            lowest = min(low[i - k_period + 1:i + 1])
            
            if highest == lowest:
                k_value = 50
            else:
                k_value = (close[i] - lowest) / (highest - lowest) * 100
            
            k_values.append(k_value)
        
        # 计算%D (K值的移动平均)
        d_values = TechnicalIndicators.sma(k_values, d_period)
        
        return {
            "k": k_values,
            "d": d_values
        }
    
    @staticmethod
    def williams_r(high: List[float], low: List[float], close: List[float], period: int = 14) -> List[float]:
        """威廉指标 (Williams %R)"""
        if len(high) < period:
            return []
        
        result = []
        
        for i in range(period - 1, len(close)):
            highest = max(high[i - period + 1:i + 1])
            lowest = min(low[i - period + 1:i + 1])
            
            if highest == lowest:
                wr_value = -50
            else:
                wr_value = (highest - close[i]) / (highest - lowest) * -100
            
            result.append(wr_value)
        
        return result
    
    @staticmethod
    def atr(high: List[float], low: List[float], close: List[float], period: int = 14) -> List[float]:
        """平均真实波幅 (Average True Range)"""
        if len(high) < 2 or len(low) < 2 or len(close) < 2:
            return []
        
        true_ranges = []
        
        for i in range(1, len(close)):
            tr1 = high[i] - low[i]
            tr2 = abs(high[i] - close[i-1])
            tr3 = abs(low[i] - close[i-1])
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # 计算ATR (真实波幅的移动平均)
        return TechnicalIndicators.sma(true_ranges, period)
    
    @staticmethod
    def obv(close: List[float], volume: List[float]) -> List[float]:
        """能量潮 (On Balance Volume)"""
        if len(close) != len(volume) or len(close) < 2:
            return []
        
        obv_values = [0]  # 第一个值为0
        
        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv_value = obv_values[-1] + volume[i]
            elif close[i] < close[i-1]:
                obv_value = obv_values[-1] - volume[i]
            else:
                obv_value = obv_values[-1]
            
            obv_values.append(obv_value)
        
        return obv_values[1:]  # 去掉第一个0值
    
    @staticmethod
    def calculate_all_indicators(ohlcv_data: Dict[str, List[float]]) -> Dict[str, Any]:
        """计算所有技术指标"""
        try:
            high = ohlcv_data.get("high", [])
            low = ohlcv_data.get("low", [])
            close = ohlcv_data.get("close", [])
            volume = ohlcv_data.get("volume", [])
            
            if not close:
                return {}
            
            indicators = {}
            
            # 移动平均线
            indicators["sma_5"] = TechnicalIndicators.sma(close, 5)
            indicators["sma_10"] = TechnicalIndicators.sma(close, 10)
            indicators["sma_20"] = TechnicalIndicators.sma(close, 20)
            indicators["sma_60"] = TechnicalIndicators.sma(close, 60)
            
            indicators["ema_12"] = TechnicalIndicators.ema(close, 12)
            indicators["ema_26"] = TechnicalIndicators.ema(close, 26)
            
            # 趋势指标
            indicators["macd"] = TechnicalIndicators.macd(close)
            
            # 震荡指标
            indicators["rsi"] = TechnicalIndicators.rsi(close)
            
            if high and low:
                indicators["kdj"] = TechnicalIndicators.kdj(high, low, close)
                indicators["stochastic"] = TechnicalIndicators.stochastic(high, low, close)
                indicators["williams_r"] = TechnicalIndicators.williams_r(high, low, close)
                indicators["atr"] = TechnicalIndicators.atr(high, low, close)
            
            # 布林带
            indicators["bollinger"] = TechnicalIndicators.bollinger_bands(close)
            
            # 成交量指标
            if volume:
                indicators["obv"] = TechnicalIndicators.obv(close, volume)
            
            logger.info(f"计算了 {len(indicators)} 个技术指标")
            return indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}


# 全局技术指标计算器实例
technical_indicators = TechnicalIndicators()

"""
交易服务实现
提供订单管理、持仓管理、账户管理等功能
"""
from datetime import datetime
from typing import List, Optional
import uuid
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.db.models.trading import Order, Trade, Position, Account, OrderStatus, OrderSide, OrderType, PositionSide
from app.db.models.user import User
from app.schemas.trading import (
    OrderRequest, OrderData, OrderQueryRequest, TradeQueryRequest, 
    PositionQueryRequest, Direction, Offset, AccountData, TradeData, PositionData
)
from app.core.exceptions import BusinessException
import logging

logger = logging.getLogger(__name__)


class TradingService:
    """交易服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_order(self, order_data: OrderData) -> Order:
        """创建订单"""
        try:
            # 创建订单对象
            order = Order(
                order_id=order_data.order_id,
                user_id=order_data.user_id,
                symbol_code=order_data.symbol,
                side=OrderSide.BUY if order_data.direction in ['BUY', 'long'] else OrderSide.SELL,
                order_type=OrderType.LIMIT if order_data.order_type == 'limit' else OrderType.MARKET,
                quantity=int(order_data.volume),
                price=order_data.price,
                status=OrderStatus.SUBMITTED,
                submit_time=order_data.order_time or datetime.utcnow()
            )
            
            self.db.add(order)
            await self.db.commit()
            await self.db.refresh(order)

            logger.info(f"订单创建成功: {order.order_id}")
            return order
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建订单失败: {str(e)}")
            raise BusinessException("创建订单失败", details=str(e))
    
    async def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        result = await self.db.execute(
            select(Order).where(Order.order_id == order_id)
        )
        return result.scalar_one_or_none()
    
    async def get_orders(self, query_request: OrderQueryRequest) -> tuple[List[Order], int]:
        """获取用户订单列表"""
        query = select(Order).where(Order.user_id == query_request.user_id)
        
        if query_request.status:
            query = query.where(Order.status == query_request.status)
        if query_request.symbol:
            query = query.where(Order.symbol_code == query_request.symbol)
        if query_request.start_time:
            query = query.where(Order.submit_time >= query_request.start_time)
        if query_request.end_time:
            query = query.where(Order.submit_time <= query_request.end_time)
            
        # 获取总数
        count_result = await self.db.execute(select(func.count()).select_from(query.subquery()))
        total = count_result.scalar()
        
        # 分页查询
        query = query.order_by(Order.submit_time.desc())
        query = query.offset(query_request.skip).limit(query_request.limit)
        
        result = await self.db.execute(query)
        orders = result.scalars().all()
        
        return orders, total
    
    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            order = await self.get_order_by_id(order_id)
            if not order:
                return False
            
            # 检查订单状态是否可取消
            if order.status not in [OrderStatus.SUBMITTED, OrderStatus.PARTIAL]:
                raise BusinessException("订单状态不允许取消")
            
            order.status = OrderStatus.CANCELLED
            order.cancel_time = datetime.utcnow()
            order.updated_at = datetime.utcnow()
            
            await self.db.commit()
            logger.info(f"订单已取消: {order.order_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"取消订单失败: {str(e)}")
            return False
    
    async def get_positions(self, query_request: Optional[PositionQueryRequest] = None, user_id: Optional[int] = None, symbol: Optional[str] = None) -> List[Position]:
        """获取用户持仓"""
        if query_request:
            query = select(Position).where(Position.user_id == query_request.user_id)
            if query_request.symbol:
                query = query.where(Position.symbol_code == query_request.symbol)
            if query_request.direction:
                query = query.where(Position.side == query_request.direction)
        else:
            query = select(Position).where(Position.user_id == user_id)
            if symbol:
                query = query.where(Position.symbol_code == symbol)
                
        query = query.where(Position.quantity > 0)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_position(self, user_id: int, symbol: str) -> Optional[Position]:
        """获取指定持仓"""
        result = await self.db.execute(
            select(Position).where(
                and_(
                    Position.user_id == user_id,
                    Position.symbol_code == symbol
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def update_position(self, user_id: int, symbol: str, trade: Trade):
        """更新持仓"""
        position = await self.get_position(user_id, symbol)
        
        if not position:
            # 创建新持仓
            position = Position(
                user_id=user_id,
                symbol_code=symbol,
                side=PositionSide.LONG,
                quantity=0,
                available_quantity=0,
                avg_cost=0
            )
            self.db.add(position)
        
        # 使用模型的业务方法更新持仓
        position.apply_trade(trade)
        await self.db.commit()
    
    async def get_account(self, user_id: int) -> Optional[Account]:
        """获取账户信息"""
        result = await self.db.execute(
            select(Account).where(Account.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def create_default_account(self, user_id: int) -> Account:
        """创建默认账户"""
        account_id = f"ACC{user_id:06d}"
        
        account = Account(
            user_id=user_id,
            account_id=account_id,
            account_name=f"用户{user_id}账户",
            total_assets=1000000.0,  # 默认100万初始资金
            available_cash=1000000.0,
            frozen_cash=0.0,
            market_value=0.0,
            is_active=True,
            is_tradable=True
        )
        
        self.db.add(account)
        await self.db.commit()
        await self.db.refresh(account)
        
        return account
    
    async def update_account_assets(self, user_id: int):
        """更新账户资产"""
        account = await self.get_account(user_id)
        if not account:
            return
        
        # 计算持仓市值
        positions = await self.get_positions(user_id=user_id)
        market_value = sum(float(p.market_value or 0) for p in positions)
        
        # 更新账户数据
        account.market_value = market_value
        account.total_assets = account.available_cash + account.frozen_cash + market_value
        
        # 计算盈亏
        total_pnl = sum(float(p.total_pnl) for p in positions)
        account.total_pnl = total_pnl
        
        account.updated_at = datetime.utcnow()
        await self.db.commit()
    
    async def check_order_risk(self, user_id: int, order_data: OrderRequest) -> dict:
        """检查订单风险"""
        account = await self.get_account(user_id)
        if not account:
            return {"passed": False, "message": "账户不存在"}
        
        # 检查资金是否充足
        if order_data.direction in [Direction.BUY, 'BUY', 'long']:
            required_amount = order_data.volume * (order_data.price or 0) * 1.002  # 加手续费
            if float(account.available_cash) < required_amount:
                return {"passed": False, "message": "可用资金不足"}
        else:
            # 检查持仓是否充足
            position = await self.get_position(user_id, order_data.symbol)
            if not position or position.available_quantity < order_data.volume:
                return {"passed": False, "message": "可用持仓不足"}
        
        return {"passed": True, "message": "风控检查通过"}
    
    async def execute_order(self, order: Order):
        """执行订单（模拟成交）"""
        try:
            # 生成成交记录
            trade_id = f"TRD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
            
            trade = Trade(
                trade_id=trade_id,
                order_id=order.id,
                user_id=order.user_id,
                symbol_code=order.symbol_code,
                side=order.side,
                quantity=order.quantity,
                price=order.price,
                turnover=float(order.price) * order.quantity,
                commission=float(order.price) * order.quantity * 0.0003,  # 万三手续费
                trade_time=datetime.utcnow()
            )
            
            self.db.add(trade)
            
            # 更新订单状态
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.avg_fill_price = order.price
            order.fill_time = datetime.utcnow()
            order.updated_at = datetime.utcnow()
            
            # 更新持仓
            await self.update_position(order.user_id, order.symbol_code, trade)
            
            # 更新账户
            account = await self.get_account(order.user_id)
            if account:
                account.apply_trade(trade, float(trade.commission))
                await self.update_account_assets(order.user_id)
            
            await self.db.commit()
            logger.info(f"订单执行成功: {order.order_id}")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"订单执行失败: {str(e)}")
            raise
    
    async def update_order(self, order_data: OrderData):
        """更新订单"""
        order = await self.get_order_by_id(order_data.order_id)
        if order:
            # 映射状态值
            status_mapping = {
                'submitted': OrderStatus.SUBMITTED,
                'partial_filled': OrderStatus.PARTIAL, 
                'all_filled': OrderStatus.FILLED,
                'cancelled': OrderStatus.CANCELLED,
                'rejected': OrderStatus.REJECTED
            }
            order.status = status_mapping.get(order_data.status, OrderStatus.SUBMITTED)
            order.filled_quantity = int(order_data.traded)
            order.updated_at = datetime.utcnow()
            await self.db.commit()
    
    async def modify_order(self, order_id: str, modify_request) -> Optional[Order]:
        """修改订单"""
        order = await self.get_order_by_id(order_id)
        if not order:
            return None
        
        # 修改订单字段
        if hasattr(modify_request, 'new_price') and modify_request.new_price:
            order.price = modify_request.new_price
        if hasattr(modify_request, 'new_volume') and modify_request.new_volume:
            order.quantity = int(modify_request.new_volume)
        
        order.updated_at = datetime.utcnow()
        await self.db.commit()
        await self.db.refresh(order)
        return order
    
    async def get_trades(self, query_request: TradeQueryRequest) -> tuple[List[Trade], int]:
        """获取成交记录"""
        query = select(Trade).where(Trade.user_id == query_request.user_id)
        
        if query_request.symbol:
            query = query.where(Trade.symbol_code == query_request.symbol)
        if query_request.direction:
            side = OrderSide.BUY if query_request.direction in ['BUY', 'long'] else OrderSide.SELL
            query = query.where(Trade.side == side)
        if query_request.start_time:
            query = query.where(Trade.trade_time >= query_request.start_time)
        if query_request.end_time:
            query = query.where(Trade.trade_time <= query_request.end_time)
        
        # 获取总数
        count_result = await self.db.execute(select(func.count()).select_from(query.subquery()))
        total = count_result.scalar()
        
        # 分页查询
        query = query.order_by(Trade.trade_time.desc())
        query = query.offset(query_request.skip).limit(query_request.limit)
        
        result = await self.db.execute(query)
        trades = result.scalars().all()
        
        return trades, total
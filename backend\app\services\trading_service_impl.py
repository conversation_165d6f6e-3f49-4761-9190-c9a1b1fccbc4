"""
交易服务实现
提供订单管理、持仓管理、账户管理等功能
"""
from datetime import datetime
from typing import List, Optional
import uuid
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.db.models.trading import Order, Trade, Position, Account, OrderStatus, OrderDirection
from app.db.models.user import User
from app.schemas.trading import OrderRequest, OrderQueryRequest, TradeQueryRequest
from app.core.exceptions import BusinessException
import logging

logger = logging.getLogger(__name__)


class TradingService:
    """交易服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_order(self, user_id: int, order_data: OrderRequest) -> Order:
        """创建订单"""
        try:
            # 生成订单ID
            order_id = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
            
            # 创建订单对象
            order = Order(
                order_id=order_id,
                user_id=user_id,
                symbol=order_data.symbol,
                exchange=order_data.exchange or "SH",
                direction=OrderDirection(order_data.direction),
                order_type=order_data.order_type,
                price=order_data.price,
                volume=order_data.volume,
                status=OrderStatus.SUBMITTED,
                submit_time=datetime.utcnow()
            )
            
            self.db.add(order)
            await self.db.commit()
            await self.db.refresh(order)
            
            logger.info(f"订单创建成功: {order_id}")
            return order
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建订单失败: {str(e)}")
            raise BusinessException("创建订单失败", details=str(e))
    
    async def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        result = await self.db.execute(
            select(Order).where(Order.order_id == order_id)
        )
        return result.scalar_one_or_none()
    
    async def get_user_orders(
        self, 
        user_id: int,
        status: Optional[str] = None,
        symbol: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Order]:
        """获取用户订单列表"""
        query = select(Order).where(Order.user_id == user_id)
        
        if status:
            query = query.where(Order.status == status)
        if symbol:
            query = query.where(Order.symbol == symbol)
            
        query = query.order_by(Order.submit_time.desc())
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            order = await self.get_order(order_id)
            if not order:
                return False
            
            # 检查订单状态是否可取消
            if order.status not in [OrderStatus.SUBMITTED, OrderStatus.NOTTRADED, OrderStatus.PARTTRADED]:
                raise BusinessException("订单状态不允许取消")
            
            order.status = OrderStatus.CANCELLED
            order.update_time = datetime.utcnow()
            
            await self.db.commit()
            logger.info(f"订单已取消: {order_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"取消订单失败: {str(e)}")
            return False
    
    async def get_user_positions(self, user_id: int) -> List[Position]:
        """获取用户持仓"""
        result = await self.db.execute(
            select(Position)
            .where(Position.user_id == user_id)
            .where(Position.volume > 0)
        )
        return result.scalars().all()
    
    async def get_position(self, user_id: int, symbol: str) -> Optional[Position]:
        """获取指定持仓"""
        result = await self.db.execute(
            select(Position).where(
                and_(
                    Position.user_id == user_id,
                    Position.symbol == symbol
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def update_position(self, user_id: int, symbol: str, trade: Trade):
        """更新持仓"""
        position = await self.get_position(user_id, symbol)
        
        if not position:
            # 创建新持仓
            position = Position(
                user_id=user_id,
                symbol=symbol,
                exchange=trade.exchange,
                volume=0,
                available_volume=0,
                avg_price=0
            )
            self.db.add(position)
        
        # 更新持仓数据
        if trade.direction == OrderDirection.BUY:
            # 买入更新
            total_value = position.volume * position.avg_price + trade.volume * trade.price
            position.volume += trade.volume
            position.avg_price = total_value / position.volume if position.volume > 0 else 0
            position.available_volume += trade.volume
        else:
            # 卖出更新
            position.volume -= trade.volume
            position.available_volume -= trade.volume
            
            if position.volume <= 0:
                # 清仓
                await self.db.delete(position)
                return
        
        position.update_time = datetime.utcnow()
        await self.db.commit()
    
    async def get_account_info(self, user_id: int) -> Optional[Account]:
        """获取账户信息"""
        result = await self.db.execute(
            select(Account).where(Account.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def create_default_account(self, user_id: int) -> Account:
        """创建默认账户"""
        account_id = f"ACC{user_id:06d}"
        
        account = Account(
            user_id=user_id,
            account_id=account_id,
            total_assets=1000000.0,  # 默认100万初始资金
            available_cash=1000000.0,
            frozen_cash=0.0,
            market_value=0.0,
            currency="CNY",
            status="ACTIVE"
        )
        
        self.db.add(account)
        await self.db.commit()
        await self.db.refresh(account)
        
        return account
    
    async def update_account_assets(self, user_id: int):
        """更新账户资产"""
        account = await self.get_account_info(user_id)
        if not account:
            return
        
        # 计算持仓市值
        positions = await self.get_user_positions(user_id)
        market_value = sum(p.volume * p.current_price for p in positions if p.current_price)
        
        # 更新账户数据
        account.market_value = market_value
        account.total_assets = account.available_cash + account.frozen_cash + market_value
        
        # 计算盈亏
        total_cost = sum(p.volume * p.avg_price for p in positions)
        account.total_profit_loss = market_value - total_cost if total_cost > 0 else 0
        account.total_profit_rate = (account.total_profit_loss / total_cost * 100) if total_cost > 0 else 0
        
        account.update_time = datetime.utcnow()
        await self.db.commit()
    
    async def check_order_risk(self, user_id: int, order_data: OrderRequest) -> dict:
        """检查订单风险"""
        account = await self.get_account_info(user_id)
        if not account:
            return {"passed": False, "message": "账户不存在"}
        
        # 检查资金是否充足
        if order_data.direction == "BUY":
            required_amount = order_data.volume * order_data.price * 1.002  # 加手续费
            if account.available_cash < required_amount:
                return {"passed": False, "message": "可用资金不足"}
        else:
            # 检查持仓是否充足
            position = await self.get_position(user_id, order_data.symbol)
            if not position or position.available_volume < order_data.volume:
                return {"passed": False, "message": "可用持仓不足"}
        
        return {"passed": True, "message": "风控检查通过"}
    
    async def execute_order(self, order: Order):
        """执行订单（模拟成交）"""
        try:
            # 生成成交记录
            trade_id = f"TRD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
            
            trade = Trade(
                trade_id=trade_id,
                order_id=order.order_id,
                user_id=order.user_id,
                symbol=order.symbol,
                exchange=order.exchange,
                direction=order.direction,
                price=order.price,
                volume=order.volume,
                commission=order.price * order.volume * 0.0003,  # 万三手续费
                trade_time=datetime.utcnow()
            )
            
            self.db.add(trade)
            
            # 更新订单状态
            order.status = OrderStatus.ALL_TRADED
            order.traded_volume = order.volume
            order.avg_price = order.price
            order.update_time = datetime.utcnow()
            
            # 更新持仓
            await self.update_position(order.user_id, order.symbol, trade)
            
            # 更新账户
            account = await self.get_account_info(order.user_id)
            if account:
                if order.direction == OrderDirection.BUY:
                    account.available_cash -= (trade.price * trade.volume + trade.commission)
                else:
                    account.available_cash += (trade.price * trade.volume - trade.commission)
                
                await self.update_account_assets(order.user_id)
            
            await self.db.commit()
            logger.info(f"订单执行成功: {order.order_id}")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"订单执行失败: {str(e)}")
            raise
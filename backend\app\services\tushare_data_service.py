"""
Tushare数据获取服务
使用Tushare API获取股票数据，支持缓存和批量处理
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import logging
import json
import os
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)

class TushareDataService:
    """Tushare数据服务"""
    
    def __init__(self, token: str = None):
        self.token = token or settings.TUSHARE_TOKEN or settings.TUSHARE_API_TOKEN
        self.base_url = "http://api.tushare.pro"
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache_dir = Path("data/cache/tushare")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # API调用限制
        self.call_count = 0
        self.last_call_time = datetime.now()
        self.max_calls_per_minute = 200  # Tushare限制
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _make_request(self, api_name: str, params: Dict = None, fields: str = None) -> Dict:
        """发起API请求"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        # 限流控制
        await self._rate_limit()
        
        data = {
            "api_name": api_name,
            "token": self.token,
            "params": params or {},
            "fields": fields
        }
        
        try:
            async with self.session.post(self.base_url, json=data) as response:
                result = await response.json()
                
                if result.get('code') != 0:
                    logger.error(f"Tushare API错误: {result.get('msg')}")
                    return {"data": {"items": []}}
                
                return result
                
        except Exception as e:
            logger.error(f"Tushare请求失败: {e}")
            return {"data": {"items": []}}
    
    async def _rate_limit(self):
        """API限流控制"""
        now = datetime.now()
        
        # 重置计数器（每分钟）
        if (now - self.last_call_time).seconds >= 60:
            self.call_count = 0
            self.last_call_time = now
        
        # 检查是否超过限制
        if self.call_count >= self.max_calls_per_minute:
            sleep_time = 60 - (now - self.last_call_time).seconds
            if sleep_time > 0:
                logger.info(f"API限流，等待{sleep_time}秒...")
                await asyncio.sleep(sleep_time)
                self.call_count = 0
                self.last_call_time = datetime.now()
        
        self.call_count += 1
    
    def _get_cache_path(self, cache_key: str, date: str = None) -> Path:
        """获取缓存文件路径"""
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        return self.cache_dir / f"{cache_key}_{date}.json"
    
    def _load_cache(self, cache_key: str, date: str = None) -> Optional[Dict]:
        """加载缓存数据"""
        cache_path = self._get_cache_path(cache_key, date)
        if cache_path.exists():
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载缓存失败: {e}")
        return None
    
    def _save_cache(self, cache_key: str, data: Dict, date: str = None):
        """保存缓存数据"""
        cache_path = self._get_cache_path(cache_key, date)
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")
    
    async def get_stock_basic(self, use_cache: bool = True) -> List[Dict]:
        """获取股票基本信息"""
        cache_key = "stock_basic"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        result = await self._make_request(
            "stock_basic",
            params={
                "exchange": "",
                "list_status": "L",
                "fields": "ts_code,symbol,name,area,industry,market,list_date"
            }
        )
        
        items = result.get('data', {}).get('items', [])
        if items and use_cache:
            self._save_cache(cache_key, {"items": items})
        
        return items
    
    async def get_daily_data(self, ts_code: str, start_date: str, end_date: str, use_cache: bool = True) -> List[Dict]:
        """获取日线数据"""
        cache_key = f"daily_{ts_code}_{start_date}_{end_date}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        result = await self._make_request(
            "daily",
            params={
                "ts_code": ts_code,
                "start_date": start_date,
                "end_date": end_date
            }
        )
        
        items = result.get('data', {}).get('items', [])
        if items and use_cache:
            self._save_cache(cache_key, {"items": items})
        
        return items
    
    async def get_realtime_quote(self, ts_codes: Union[str, List[str]]) -> List[Dict]:
        """获取实时行情"""
        if isinstance(ts_codes, str):
            ts_codes = [ts_codes]
        
        # 实时数据不缓存
        result = await self._make_request(
            "query",
            params={
                "api_name": "realtime",
                "ts_code": ",".join(ts_codes)
            }
        )
        
        return result.get('data', {}).get('items', [])
    
    async def get_stock_company(self, ts_code: str, use_cache: bool = True) -> Dict:
        """获取公司基本信息"""
        cache_key = f"company_{ts_code}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [{}])[0] if cached_data.get('items') else {}
        
        result = await self._make_request(
            "stock_company",
            params={"ts_code": ts_code}
        )
        
        items = result.get('data', {}).get('items', [])
        if items and use_cache:
            self._save_cache(cache_key, {"items": items})
        
        return items[0] if items else {}
    
    async def get_index_daily(self, ts_code: str, start_date: str, end_date: str, use_cache: bool = True) -> List[Dict]:
        """获取指数日线数据"""
        cache_key = f"index_daily_{ts_code}_{start_date}_{end_date}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        result = await self._make_request(
            "index_daily",
            params={
                "ts_code": ts_code,
                "start_date": start_date,
                "end_date": end_date
            }
        )
        
        items = result.get('data', {}).get('items', [])
        if items and use_cache:
            self._save_cache(cache_key, {"items": items})
        
        return items
    
    async def get_trade_cal(self, start_date: str, end_date: str, use_cache: bool = True) -> List[Dict]:
        """获取交易日历"""
        cache_key = f"trade_cal_{start_date}_{end_date}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        result = await self._make_request(
            "trade_cal",
            params={
                "start_date": start_date,
                "end_date": end_date
            }
        )
        
        items = result.get('data', {}).get('items', [])
        if items and use_cache:
            self._save_cache(cache_key, {"items": items})
        
        return items
    
    async def batch_get_daily_data(self, ts_codes: List[str], days: int = 300) -> Dict[str, List[Dict]]:
        """批量获取日线数据"""
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        
        results = {}
        
        # 分批处理，避免API限制
        batch_size = 10
        for i in range(0, len(ts_codes), batch_size):
            batch = ts_codes[i:i + batch_size]
            
            tasks = [
                self.get_daily_data(ts_code, start_date, end_date)
                for ts_code in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for ts_code, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"获取{ts_code}数据失败: {result}")
                    results[ts_code] = []
                else:
                    results[ts_code] = result
            
            # 批次间延迟
            await asyncio.sleep(1)
        
        return results
    
    def convert_to_standard_format(self, tushare_data: List[Dict], data_type: str = "daily") -> List[Dict]:
        """转换Tushare数据格式为标准格式"""
        if not tushare_data:
            return []
        
        if data_type == "daily":
            return [
                {
                    "timestamp": int(datetime.strptime(item[1], '%Y%m%d').timestamp() * 1000),
                    "open": float(item[2]) if item[2] else 0,
                    "high": float(item[3]) if item[3] else 0,
                    "low": float(item[4]) if item[4] else 0,
                    "close": float(item[5]) if item[5] else 0,
                    "volume": int(float(item[9]) * 100) if item[9] else 0,  # 转换为股
                    "amount": float(item[10]) * 1000 if item[10] else 0,  # 转换为元
                    "change": float(item[7]) if item[7] else 0,
                    "pct_change": float(item[8]) if item[8] else 0
                }
                for item in tushare_data
                if len(item) >= 11
            ]
        
        return []
    
    async def cleanup_old_cache(self, days_to_keep: int = 7):
        """清理旧缓存文件"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                file_date = datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_date < cutoff_date:
                    cache_file.unlink()
                    logger.info(f"删除旧缓存文件: {cache_file}")
            except Exception as e:
                logger.warning(f"删除缓存文件失败: {e}")

# 创建全局实例
tushare_service = TushareDataService()

# 导出主要接口
async def get_stock_list() -> List[Dict]:
    """获取股票列表"""
    async with tushare_service:
        return await tushare_service.get_stock_basic()

async def get_stock_daily_data(ts_code: str, days: int = 300) -> List[Dict]:
    """获取股票日线数据"""
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
    
    async with tushare_service:
        data = await tushare_service.get_daily_data(ts_code, start_date, end_date)
        return tushare_service.convert_to_standard_format(data, "daily")

async def get_realtime_quotes(ts_codes: List[str]) -> List[Dict]:
    """获取实时行情"""
    async with tushare_service:
        return await tushare_service.get_realtime_quote(ts_codes)

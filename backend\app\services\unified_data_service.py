"""
Unified Data Service for Quantum Investment Platform
Integrates multiple data sources with automatic fallback
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class UnifiedDataService:
    """Unified data service with multiple source support"""
    
    def __init__(self):
        self.tushare_available = False
        self.akshare_available = False
        self.yfinance_available = False
        self._init_data_sources()
        
    def _init_data_sources(self):
        """Initialize available data sources"""
        # Try to import data sources
        try:
            import tushare as ts
            self.tushare_available = True
            logger.info("Tushare data source available")
        except ImportError:
            logger.warning("Tushare not available")
            
        try:
            import akshare as ak
            self.akshare_available = True
            logger.info("AkShare data source available")
        except ImportError:
            logger.warning("AkShare not available")
            
        try:
            import yfinance as yf
            self.yfinance_available = True
            logger.info("YFinance data source available")
        except ImportError:
            logger.warning("YFinance not available")
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """Get stock list from available sources"""
        # Try real data sources first
        if self.tushare_available:
            try:
                return await self._get_tushare_stocks()
            except Exception as e:
                logger.error(f"Tushare failed: {e}")
        
        if self.akshare_available:
            try:
                return await self._get_akshare_stocks()
            except Exception as e:
                logger.error(f"AkShare failed: {e}")
        
        # Fallback to mock data
        return self._get_mock_stocks()
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """Get realtime quote for a symbol"""
        # Try real data sources
        if self.tushare_available:
            try:
                return await self._get_tushare_quote(symbol)
            except:
                pass
        
        if self.yfinance_available:
            try:
                return await self._get_yfinance_quote(symbol)
            except:
                pass
        
        # Fallback to mock data
        return self._get_mock_quote(symbol)
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict]:
        """Get K-line data"""
        # Try real data sources
        if self.tushare_available:
            try:
                return await self._get_tushare_kline(symbol, period, limit)
            except:
                pass
        
        # Fallback to mock data
        return self._get_mock_kline(symbol, period, limit)
    
    async def get_market_depth(self, symbol: str) -> Dict[str, Any]:
        """Get market depth (order book)"""
        return self._get_mock_depth(symbol)
    
    async def get_tick_data(self, symbol: str, limit: int = 50) -> List[Dict]:
        """Get tick data"""
        return self._get_mock_ticks(symbol, limit)
    
    # ============= Mock Data Generators =============
    
    def _get_mock_stocks(self) -> List[Dict[str, Any]]:
        """Generate mock stock list"""
        stocks = [
            {"code": "000001", "name": "平安银行", "market": "SZ", "sector": "金融"},
            {"code": "000002", "name": "万科A", "market": "SZ", "sector": "房地产"},
            {"code": "000858", "name": "五粮液", "market": "SZ", "sector": "食品饮料"},
            {"code": "002475", "name": "立讯精密", "market": "SZ", "sector": "电子"},
            {"code": "300750", "name": "宁德时代", "market": "SZ", "sector": "新能源"},
            {"code": "600000", "name": "浦发银行", "market": "SH", "sector": "金融"},
            {"code": "600036", "name": "招商银行", "market": "SH", "sector": "金融"},
            {"code": "600519", "name": "贵州茅台", "market": "SH", "sector": "食品饮料"},
            {"code": "600276", "name": "恒瑞医药", "market": "SH", "sector": "医药"},
            {"code": "601318", "name": "中国平安", "market": "SH", "sector": "保险"},
            {"code": "601888", "name": "中国中免", "market": "SH", "sector": "商业"},
            {"code": "603259", "name": "药明康德", "market": "SH", "sector": "医药"},
        ]
        
        # Add random price data
        for stock in stocks:
            base_price = random.uniform(10, 500)
            change = random.uniform(-5, 5)
            stock.update({
                "price": round(base_price, 2),
                "change": round(change, 2),
                "change_pct": round(change / base_price * 100, 2),
                "volume": random.randint(1000000, 100000000),
                "amount": random.randint(10000000, 1000000000),
                "open": round(base_price - random.uniform(-2, 2), 2),
                "high": round(base_price + random.uniform(0, 5), 2),
                "low": round(base_price - random.uniform(0, 5), 2),
                "prev_close": round(base_price - change, 2)
            })
        
        return stocks
    
    def _get_mock_quote(self, symbol: str) -> Dict[str, Any]:
        """Generate mock quote data"""
        base_price = 100.0 + hash(symbol) % 400
        change = random.uniform(-5, 5)
        
        return {
            "symbol": symbol,
            "name": f"Stock-{symbol}",
            "price": round(base_price + random.uniform(-5, 5), 2),
            "change": round(change, 2),
            "change_pct": round(change / base_price * 100, 2),
            "volume": random.randint(1000000, 100000000),
            "amount": random.randint(10000000, 1000000000),
            "open": round(base_price, 2),
            "high": round(base_price + 5, 2),
            "low": round(base_price - 5, 2),
            "prev_close": round(base_price - change, 2),
            "bid": round(base_price - 0.01, 2),
            "ask": round(base_price + 0.01, 2),
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_mock_kline(self, symbol: str, period: str, limit: int) -> List[Dict]:
        """Generate mock K-line data"""
        klines = []
        base_price = 100.0
        current_time = datetime.now()
        
        # Determine time delta based on period
        if period == "1m":
            delta = timedelta(minutes=1)
        elif period == "5m":
            delta = timedelta(minutes=5)
        elif period == "1h":
            delta = timedelta(hours=1)
        elif period == "1d":
            delta = timedelta(days=1)
        else:
            delta = timedelta(days=1)
        
        for i in range(limit):
            open_price = base_price + random.uniform(-5, 5)
            close_price = base_price + random.uniform(-5, 5)
            high_price = max(open_price, close_price) + random.uniform(0, 2)
            low_price = min(open_price, close_price) - random.uniform(0, 2)
            
            klines.append({
                "timestamp": (current_time - delta * (limit - i - 1)).isoformat(),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(100000, 10000000),
                "amount": random.randint(1000000, 100000000)
            })
            
            base_price = close_price  # Use close as next open base
        
        return klines
    
    def _get_mock_depth(self, symbol: str) -> Dict[str, Any]:
        """Generate mock market depth"""
        base_price = 100.0
        
        bids = []
        asks = []
        
        for i in range(10):
            bid_price = base_price - (i + 1) * 0.01
            ask_price = base_price + (i + 1) * 0.01
            
            bids.append({
                "price": round(bid_price, 2),
                "volume": random.randint(1000, 100000)
            })
            
            asks.append({
                "price": round(ask_price, 2),
                "volume": random.randint(1000, 100000)
            })
        
        return {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_mock_ticks(self, symbol: str, limit: int) -> List[Dict]:
        """Generate mock tick data"""
        ticks = []
        base_price = 100.0
        current_time = datetime.now()
        
        for i in range(limit):
            tick_time = current_time - timedelta(seconds=limit - i)
            price = base_price + random.uniform(-1, 1)
            
            ticks.append({
                "timestamp": tick_time.isoformat(),
                "price": round(price, 2),
                "volume": random.randint(100, 10000),
                "side": random.choice(["buy", "sell"]),
                "type": random.choice(["market", "limit"])
            })
        
        return ticks
    
    # ============= Real Data Source Methods (Placeholders) =============
    
    async def _get_tushare_stocks(self) -> List[Dict]:
        """Get stocks from Tushare"""
        # Implementation would go here
        raise NotImplementedError("Tushare integration not configured")
    
    async def _get_tushare_quote(self, symbol: str) -> Dict:
        """Get quote from Tushare"""
        raise NotImplementedError("Tushare integration not configured")
    
    async def _get_tushare_kline(self, symbol: str, period: str, limit: int) -> List[Dict]:
        """Get K-line from Tushare"""
        raise NotImplementedError("Tushare integration not configured")
    
    async def _get_akshare_stocks(self) -> List[Dict]:
        """Get stocks from AkShare"""
        raise NotImplementedError("AkShare integration not configured")
    
    async def _get_yfinance_quote(self, symbol: str) -> Dict:
        """Get quote from YFinance"""
        raise NotImplementedError("YFinance integration not configured")

# Global service instance
data_service = UnifiedDataService()

# Convenience functions
async def get_stock_list():
    return await data_service.get_stock_list()

async def get_realtime_quote(symbol: str):
    return await data_service.get_realtime_quote(symbol)

async def get_kline_data(symbol: str, period: str = "1d", limit: int = 100):
    return await data_service.get_kline_data(symbol, period, limit)

async def get_market_depth(symbol: str):
    return await data_service.get_market_depth(symbol)

async def get_tick_data(symbol: str, limit: int = 50):
    return await data_service.get_tick_data(symbol, limit)
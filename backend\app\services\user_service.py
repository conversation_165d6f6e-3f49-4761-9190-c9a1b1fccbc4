"""
用户服务模块
提供用户管理相关的业务逻辑
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from app.db.models.user import User
from app.core.security import get_password_hash, verify_password
from app.core.exceptions import DataNotFoundError, ValidationError
from app.schemas.user import UserCreate, UserUpdate


class UserService:
    """用户服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_user(self, user_data: UserCreate) -> User:
        """创建新用户"""
        # 检查用户名是否已存在
        existing_user = await self.get_user_by_username(user_data.username)
        if existing_user:
            raise ValidationError("用户名已存在")

        # 检查邮箱是否已存在
        existing_email = await self.get_user_by_email(user_data.email)
        if existing_email:
            raise ValidationError("邮箱已存在")

        # 创建新用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=get_password_hash(user_data.password),
            is_active=True,
            is_admin=False,
            created_at=datetime.utcnow(),
        )

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        result = await self.db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await self.db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await self.db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户登录"""
        user = await self.get_user_by_username(username)
        if not user:
            return None

        if not verify_password(password, user.hashed_password):
            return None

        return user

    async def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """更新用户信息"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        # 更新用户信息
        if user_data.email:
            # 检查邮箱是否被其他用户使用
            existing_email = await self.get_user_by_email(user_data.email)
            if existing_email and existing_email.id != user_id:
                raise ValidationError("邮箱已被其他用户使用")
            user.email = user_data.email

        if user_data.is_active is not None:
            user.is_active = user_data.is_active

        if user_data.is_admin is not None:
            user.is_admin = user_data.is_admin

        user.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def change_password(
        self, user_id: int, old_password: str, new_password: str
    ) -> bool:
        """修改用户密码"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        # 验证旧密码
        if not verify_password(old_password, user.hashed_password):
            raise ValidationError("旧密码错误")

        # 更新密码
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()

        await self.db.commit()
        return True

    async def reset_password(self, user_id: int, new_password: str) -> bool:
        """重置用户密码（管理员功能）"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()

        await self.db.commit()
        return True

    async def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        user.is_active = False
        user.updated_at = datetime.utcnow()

        await self.db.commit()
        return True

    async def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        user.is_active = True
        user.updated_at = datetime.utcnow()

        await self.db.commit()
        return True

    async def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        result = await self.db.execute(
            select(User).offset(skip).limit(limit).order_by(User.created_at.desc())
        )
        return result.scalars().all()

    async def count_users(self) -> int:
        """获取用户总数"""
        result = await self.db.execute(select(User.id))
        return len(result.scalars().all())

    async def delete_user(self, user_id: int) -> bool:
        """删除用户（软删除）"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise DataNotFoundError("用户不存在")

        # 软删除：设置为非活跃状态
        user.is_active = False
        user.updated_at = datetime.utcnow()

        await self.db.commit()
        return True

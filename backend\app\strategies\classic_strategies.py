"""
经典量化策略实现
包含双均线、RSI、MACD等经典策略
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .indicators import TechnicalIndicators, AdvancedIndicators


@dataclass
class Signal:
    """交易信号"""
    timestamp: str
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    price: float
    quantity: int
    confidence: float  # 信号置信度 0-1
    reason: str  # 信号原因
    metadata: Optional[Dict] = None


@dataclass
class StrategyResult:
    """策略运行结果"""
    signals: List[Signal]
    metrics: Dict[str, Any]
    equity_curve: pd.DataFrame
    drawdown_series: pd.Series
    trades: List[Dict]


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, parameters: Dict[str, Any]):
        self.name = name
        self.parameters = parameters
        self.positions = {}  # 持仓记录
        self.cash = 1000000  # 初始资金
        self.initial_cash = self.cash
        self.trades = []
        self.equity_curve = []
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成交易信号"""
        pass
    
    def backtest(self, data: pd.DataFrame) -> StrategyResult:
        """回测策略"""
        signals = self.generate_signals(data)
        
        # 执行回测
        for signal in signals:
            self._execute_signal(signal, data)
        
        # 计算绩效指标
        metrics = self._calculate_metrics()
        
        # 生成权益曲线
        equity_df = pd.DataFrame(self.equity_curve)
        if not equity_df.empty:
            equity_df.set_index('timestamp', inplace=True)
        
        # 计算回撤
        if not equity_df.empty:
            equity_series = equity_df['total_value']
            drawdown_series = self._calculate_drawdown(equity_series)
        else:
            drawdown_series = pd.Series()
        
        return StrategyResult(
            signals=signals,
            metrics=metrics,
            equity_curve=equity_df,
            drawdown_series=drawdown_series,
            trades=self.trades
        )
    
    def _execute_signal(self, signal: Signal, data: pd.DataFrame):
        """执行交易信号"""
        if signal.action == 'buy':
            self._buy(signal)
        elif signal.action == 'sell':
            self._sell(signal)
        
        # 记录权益曲线
        total_value = self._calculate_total_value(data, signal.timestamp)
        self.equity_curve.append({
            'timestamp': signal.timestamp,
            'cash': self.cash,
            'positions_value': total_value - self.cash,
            'total_value': total_value
        })
    
    def _buy(self, signal: Signal):
        """买入操作"""
        cost = signal.price * signal.quantity
        if self.cash >= cost:
            self.cash -= cost
            
            if signal.symbol in self.positions:
                self.positions[signal.symbol]['quantity'] += signal.quantity
                # 更新平均成本
                old_cost = self.positions[signal.symbol]['avg_price'] * (self.positions[signal.symbol]['quantity'] - signal.quantity)
                self.positions[signal.symbol]['avg_price'] = (old_cost + cost) / self.positions[signal.symbol]['quantity']
            else:
                self.positions[signal.symbol] = {
                    'quantity': signal.quantity,
                    'avg_price': signal.price
                }
            
            self.trades.append({
                'timestamp': signal.timestamp,
                'symbol': signal.symbol,
                'action': 'buy',
                'price': signal.price,
                'quantity': signal.quantity,
                'cost': cost,
                'reason': signal.reason
            })
    
    def _sell(self, signal: Signal):
        """卖出操作"""
        if signal.symbol in self.positions and self.positions[signal.symbol]['quantity'] >= signal.quantity:
            revenue = signal.price * signal.quantity
            self.cash += revenue
            
            # 计算盈亏
            cost_price = self.positions[signal.symbol]['avg_price']
            profit = (signal.price - cost_price) * signal.quantity
            
            self.positions[signal.symbol]['quantity'] -= signal.quantity
            if self.positions[signal.symbol]['quantity'] == 0:
                del self.positions[signal.symbol]
            
            self.trades.append({
                'timestamp': signal.timestamp,
                'symbol': signal.symbol,
                'action': 'sell',
                'price': signal.price,
                'quantity': signal.quantity,
                'revenue': revenue,
                'profit': profit,
                'reason': signal.reason
            })
    
    def _calculate_total_value(self, data: pd.DataFrame, timestamp: str) -> float:
        """计算当前总资产"""
        total_value = self.cash
        
        # 根据时间戳获取当前价格
        try:
            current_prices = data.loc[data.index == timestamp]
            if not current_prices.empty:
                for symbol, position in self.positions.items():
                    if symbol in current_prices.columns:
                        current_price = current_prices[symbol].iloc[0]
                        total_value += position['quantity'] * current_price
        except:
            # 如果无法获取当前价格，使用持仓成本价
            for symbol, position in self.positions.items():
                total_value += position['quantity'] * position['avg_price']
        
        return total_value
    
    def _calculate_drawdown(self, equity_series: pd.Series) -> pd.Series:
        """计算回撤"""
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        return drawdown
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """计算绩效指标"""
        if not self.equity_curve:
            return {}
        
        equity_df = pd.DataFrame(self.equity_curve)
        equity_series = equity_df['total_value']
        
        # 基本收益指标
        total_return = (equity_series.iloc[-1] - self.initial_cash) / self.initial_cash
        
        # 计算日收益率
        daily_returns = equity_series.pct_change().dropna()
        
        # 年化收益率
        trading_days = len(equity_series)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1 if trading_days > 0 else 0
        
        # 夏普比率
        if len(daily_returns) > 1:
            sharpe_ratio = np.sqrt(252) * daily_returns.mean() / daily_returns.std()
        else:
            sharpe_ratio = 0
        
        # 最大回撤
        drawdown_series = self._calculate_drawdown(equity_series)
        max_drawdown = drawdown_series.min()
        
        # 胜率
        profitable_trades = [t for t in self.trades if t.get('profit', 0) > 0]
        win_rate = len(profitable_trades) / len(self.trades) if self.trades else 0
        
        # 盈亏比
        profits = [t['profit'] for t in self.trades if t.get('profit', 0) > 0]
        losses = [abs(t['profit']) for t in self.trades if t.get('profit', 0) < 0]
        
        avg_profit = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'total_trades': len(self.trades),
            'profitable_trades': len(profitable_trades),
            'final_value': equity_series.iloc[-1],
            'volatility': daily_returns.std() * np.sqrt(252) if len(daily_returns) > 1 else 0
        }


class DoubleMAStrategy(BaseStrategy):
    """双均线策略"""
    
    def __init__(self, short_period: int = 5, long_period: int = 20, **kwargs):
        super().__init__("双均线策略", {
            'short_period': short_period,
            'long_period': long_period,
            **kwargs
        })
        self.short_period = short_period
        self.long_period = long_period
    
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成双均线交易信号"""
        signals = []
        
        # 确保数据格式正确
        if 'close' not in data.columns:
            # 假设第一列是收盘价
            close_prices = data.iloc[:, 0].values
        else:
            close_prices = data['close'].values
        
        # 计算双均线指标
        indicator_result = TechnicalIndicators.double_ma(
            close_prices, self.short_period, self.long_period
        )
        
        # 获取交易信号
        signal_values = indicator_result.signals
        dates = data.index
        
        position = 0  # 当前持仓状态
        
        for i in range(len(signal_values)):
            if np.isnan(signal_values[i]):
                continue
                
            current_signal = signal_values[i]
            
            # 金叉买入信号
            if current_signal == 1 and position <= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',  # 默认股票代码
                    action='buy',
                    price=close_prices[i],
                    quantity=1000,  # 默认数量
                    confidence=0.7,
                    reason=f"短期均线({self.short_period})上穿长期均线({self.long_period})",
                    metadata={
                        'short_ma': indicator_result.upper_band[i],
                        'long_ma': indicator_result.lower_band[i]
                    }
                ))
                position = 1
            
            # 死叉卖出信号
            elif current_signal == -1 and position >= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='sell',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=0.7,
                    reason=f"短期均线({self.short_period})下穿长期均线({self.long_period})",
                    metadata={
                        'short_ma': indicator_result.upper_band[i],
                        'long_ma': indicator_result.lower_band[i]
                    }
                ))
                position = -1
        
        return signals


class RSIStrategy(BaseStrategy):
    """RSI策略"""
    
    def __init__(self, period: int = 14, overbought: float = 70, oversold: float = 30, **kwargs):
        super().__init__("RSI策略", {
            'period': period,
            'overbought': overbought,
            'oversold': oversold,
            **kwargs
        })
        self.period = period
        self.overbought = overbought
        self.oversold = oversold
    
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成RSI交易信号"""
        signals = []
        
        if 'close' not in data.columns:
            close_prices = data.iloc[:, 0].values
        else:
            close_prices = data['close'].values
        
        # 计算RSI指标
        indicator_result = TechnicalIndicators.rsi(
            close_prices, self.period, self.overbought, self.oversold
        )
        
        rsi_values = indicator_result.values
        signal_values = indicator_result.signals
        dates = data.index
        
        position = 0
        
        for i in range(len(signal_values)):
            if np.isnan(rsi_values[i]) or np.isnan(signal_values[i]):
                continue
            
            current_signal = signal_values[i]
            current_rsi = rsi_values[i]
            
            # 超卖买入
            if current_signal == 1 and position <= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='buy',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=min(0.9, (self.oversold - current_rsi) / 20),  # 越超卖置信度越高
                    reason=f"RSI({current_rsi:.2f})超卖，低于{self.oversold}",
                    metadata={'rsi': current_rsi}
                ))
                position = 1
            
            # 超买卖出
            elif current_signal == -1 and position >= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='sell',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=min(0.9, (current_rsi - self.overbought) / 20),
                    reason=f"RSI({current_rsi:.2f})超买，高于{self.overbought}",
                    metadata={'rsi': current_rsi}
                ))
                position = -1
        
        return signals


class MACDStrategy(BaseStrategy):
    """MACD策略"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9, **kwargs):
        super().__init__("MACD策略", {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period,
            **kwargs
        })
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
    
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成MACD交易信号"""
        signals = []
        
        if 'close' not in data.columns:
            close_prices = data.iloc[:, 0].values
        else:
            close_prices = data['close'].values
        
        # 计算MACD指标
        indicator_result = TechnicalIndicators.macd(
            close_prices, self.fast_period, self.slow_period, self.signal_period
        )
        
        macd_line = indicator_result.values
        signal_line = indicator_result.upper_band
        histogram = indicator_result.lower_band
        signal_values = indicator_result.signals
        dates = data.index
        
        position = 0
        
        for i in range(1, len(signal_values)):  # 从1开始，因为需要比较前一个值
            if (np.isnan(macd_line[i]) or np.isnan(signal_line[i]) or 
                np.isnan(signal_values[i])):
                continue
            
            current_signal = signal_values[i]
            
            # MACD金叉买入
            if current_signal == 1 and position <= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='buy',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=0.8,
                    reason="MACD金叉，DIF上穿DEA",
                    metadata={
                        'macd': macd_line[i],
                        'signal': signal_line[i],
                        'histogram': histogram[i]
                    }
                ))
                position = 1
            
            # MACD死叉卖出
            elif current_signal == -1 and position >= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='sell',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=0.8,
                    reason="MACD死叉，DIF下穿DEA",
                    metadata={
                        'macd': macd_line[i],
                        'signal': signal_line[i],
                        'histogram': histogram[i]
                    }
                ))
                position = -1
        
        return signals


class BollingerBandsStrategy(BaseStrategy):
    """布林带策略"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0, **kwargs):
        super().__init__("布林带策略", {
            'period': period,
            'std_dev': std_dev,
            **kwargs
        })
        self.period = period
        self.std_dev = std_dev
    
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成布林带交易信号"""
        signals = []
        
        if 'close' not in data.columns:
            close_prices = data.iloc[:, 0].values
        else:
            close_prices = data['close'].values
        
        # 计算布林带指标
        indicator_result = TechnicalIndicators.bollinger_bands(
            close_prices, self.period, self.std_dev
        )
        
        middle_band = indicator_result.values
        upper_band = indicator_result.upper_band
        lower_band = indicator_result.lower_band
        signal_values = indicator_result.signals
        dates = data.index
        
        position = 0
        
        for i in range(len(signal_values)):
            if (np.isnan(upper_band[i]) or np.isnan(lower_band[i]) or 
                np.isnan(signal_values[i])):
                continue
            
            current_signal = signal_values[i]
            current_price = close_prices[i]
            
            # 价格触及下轨买入
            if current_signal == 1 and position <= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='buy',
                    price=current_price,
                    quantity=1000,
                    confidence=0.7,
                    reason=f"价格({current_price:.2f})触及布林带下轨({lower_band[i]:.2f})",
                    metadata={
                        'upper_band': upper_band[i],
                        'middle_band': middle_band[i],
                        'lower_band': lower_band[i]
                    }
                ))
                position = 1
            
            # 价格触及上轨卖出
            elif current_signal == -1 and position >= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='sell',
                    price=current_price,
                    quantity=1000,
                    confidence=0.7,
                    reason=f"价格({current_price:.2f})触及布林带上轨({upper_band[i]:.2f})",
                    metadata={
                        'upper_band': upper_band[i],
                        'middle_band': middle_band[i],
                        'lower_band': lower_band[i]
                    }
                ))
                position = -1
        
        return signals


class MultiFactorStrategy(BaseStrategy):
    """多因子策略"""
    
    def __init__(self, **kwargs):
        super().__init__("多因子策略", kwargs)
        # 各个指标的权重
        self.weights = {
            'ma': 0.3,
            'rsi': 0.25,
            'macd': 0.25,
            'bb': 0.2
        }
    
    def generate_signals(self, data: pd.DataFrame) -> List[Signal]:
        """生成多因子综合信号"""
        signals = []
        
        if 'close' not in data.columns:
            close_prices = data.iloc[:, 0].values
        else:
            close_prices = data['close'].values
        
        # 计算各个指标
        ma_result = TechnicalIndicators.double_ma(close_prices, 5, 20)
        rsi_result = TechnicalIndicators.rsi(close_prices, 14, 70, 30)
        macd_result = TechnicalIndicators.macd(close_prices, 12, 26, 9)
        bb_result = TechnicalIndicators.bollinger_bands(close_prices, 20, 2.0)
        
        dates = data.index
        position = 0
        
        for i in range(len(close_prices)):
            # 计算综合信号强度
            signal_strength = 0
            signal_count = 0
            
            # MA信号
            if not np.isnan(ma_result.signals[i]):
                signal_strength += ma_result.signals[i] * self.weights['ma']
                signal_count += 1
            
            # RSI信号
            if not np.isnan(rsi_result.signals[i]):
                signal_strength += rsi_result.signals[i] * self.weights['rsi']
                signal_count += 1
            
            # MACD信号
            if not np.isnan(macd_result.signals[i]):
                signal_strength += macd_result.signals[i] * self.weights['macd']
                signal_count += 1
            
            # 布林带信号
            if not np.isnan(bb_result.signals[i]):
                signal_strength += bb_result.signals[i] * self.weights['bb']
                signal_count += 1
            
            if signal_count == 0:
                continue
            
            # 计算平均信号强度
            avg_signal = signal_strength / signal_count
            confidence = abs(avg_signal)
            
            # 生成交易信号（需要较强的信号强度）
            if avg_signal > 0.5 and position <= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='buy',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=confidence,
                    reason=f"多因子综合买入信号，强度: {avg_signal:.2f}",
                    metadata={
                        'ma_signal': ma_result.signals[i],
                        'rsi_signal': rsi_result.signals[i],
                        'macd_signal': macd_result.signals[i],
                        'bb_signal': bb_result.signals[i],
                        'signal_strength': signal_strength
                    }
                ))
                position = 1
            
            elif avg_signal < -0.5 and position >= 0:
                signals.append(Signal(
                    timestamp=str(dates[i]),
                    symbol='stock',
                    action='sell',
                    price=close_prices[i],
                    quantity=1000,
                    confidence=confidence,
                    reason=f"多因子综合卖出信号，强度: {avg_signal:.2f}",
                    metadata={
                        'ma_signal': ma_result.signals[i],
                        'rsi_signal': rsi_result.signals[i],
                        'macd_signal': macd_result.signals[i],
                        'bb_signal': bb_result.signals[i],
                        'signal_strength': signal_strength
                    }
                ))
                position = -1
        
        return signals


# 策略工厂
class StrategyFactory:
    """策略工厂类"""
    
    _strategies = {
        'double_ma': DoubleMAStrategy,
        'rsi': RSIStrategy,
        'macd': MACDStrategy,
        'bollinger_bands': BollingerBandsStrategy,
        'multi_factor': MultiFactorStrategy
    }
    
    @classmethod
    def create_strategy(cls, strategy_name: str, **parameters) -> BaseStrategy:
        """创建策略实例"""
        if strategy_name not in cls._strategies:
            raise ValueError(f"Unknown strategy: {strategy_name}")
        
        strategy_class = cls._strategies[strategy_name]
        return strategy_class(**parameters)
    
    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """获取可用策略列表"""
        return list(cls._strategies.keys())
    
    @classmethod
    def register_strategy(cls, name: str, strategy_class: type):
        """注册新策略"""
        cls._strategies[name] = strategy_class
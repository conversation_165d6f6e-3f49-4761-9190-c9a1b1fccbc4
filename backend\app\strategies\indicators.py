"""
技术指标计算库
包含常用的技术分析指标
"""
import numpy as np
import pandas as pd
from typing import Union, Tuple, Optional
# import talib  # 临时注释掉，避免依赖问题
from dataclasses import dataclass


@dataclass
class IndicatorResult:
    """指标计算结果"""
    values: np.ndarray
    signals: Optional[np.ndarray] = None
    upper_band: Optional[np.ndarray] = None
    lower_band: Optional[np.ndarray] = None
    metadata: Optional[dict] = None


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(prices: Union[pd.Series, np.ndarray], period: int) -> IndicatorResult:
        """简单移动平均线"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        sma_values = talib.SMA(prices, timeperiod=period)
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        signals[prices > sma_values] = 1  # 买入信号
        signals[prices < sma_values] = -1  # 卖出信号
        
        return IndicatorResult(
            values=sma_values,
            signals=signals,
            metadata={'period': period, 'name': 'SMA'}
        )
    
    @staticmethod
    def ema(prices: Union[pd.Series, np.ndarray], period: int) -> IndicatorResult:
        """指数移动平均线"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        ema_values = talib.EMA(prices, timeperiod=period)
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        signals[prices > ema_values] = 1
        signals[prices < ema_values] = -1
        
        return IndicatorResult(
            values=ema_values,
            signals=signals,
            metadata={'period': period, 'name': 'EMA'}
        )
    
    @staticmethod
    def double_ma(prices: Union[pd.Series, np.ndarray], 
                  short_period: int = 5, 
                  long_period: int = 20) -> IndicatorResult:
        """双均线策略"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        short_ma = talib.SMA(prices, timeperiod=short_period)
        long_ma = talib.SMA(prices, timeperiod=long_period)
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        signals[short_ma > long_ma] = 1  # 金叉买入
        signals[short_ma < long_ma] = -1  # 死叉卖出
        
        return IndicatorResult(
            values=short_ma,
            signals=signals,
            upper_band=short_ma,
            lower_band=long_ma,
            metadata={
                'short_period': short_period,
                'long_period': long_period,
                'name': 'Double_MA'
            }
        )
    
    @staticmethod
    def rsi(prices: Union[pd.Series, np.ndarray], 
            period: int = 14, 
            overbought: float = 70, 
            oversold: float = 30) -> IndicatorResult:
        """相对强弱指标"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        rsi_values = talib.RSI(prices, timeperiod=period)
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        signals[rsi_values < oversold] = 1  # 超卖买入
        signals[rsi_values > overbought] = -1  # 超买卖出
        
        return IndicatorResult(
            values=rsi_values,
            signals=signals,
            upper_band=np.full_like(rsi_values, overbought),
            lower_band=np.full_like(rsi_values, oversold),
            metadata={
                'period': period,
                'overbought': overbought,
                'oversold': oversold,
                'name': 'RSI'
            }
        )
    
    @staticmethod
    def macd(prices: Union[pd.Series, np.ndarray], 
             fast_period: int = 12, 
             slow_period: int = 26, 
             signal_period: int = 9) -> IndicatorResult:
        """MACD指标"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        macd_line, macd_signal, macd_hist = talib.MACD(
            prices, 
            fastperiod=fast_period,
            slowperiod=slow_period, 
            signalperiod=signal_period
        )
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        # MACD金叉买入，死叉卖出
        macd_cross = np.diff(np.sign(macd_line - macd_signal))
        signals[1:][macd_cross > 0] = 1  # 金叉买入
        signals[1:][macd_cross < 0] = -1  # 死叉卖出
        
        return IndicatorResult(
            values=macd_line,
            signals=signals,
            upper_band=macd_signal,
            lower_band=macd_hist,
            metadata={
                'fast_period': fast_period,
                'slow_period': slow_period,
                'signal_period': signal_period,
                'name': 'MACD'
            }
        )
    
    @staticmethod
    def bollinger_bands(prices: Union[pd.Series, np.ndarray], 
                       period: int = 20, 
                       std_dev: float = 2.0) -> IndicatorResult:
        """布林带指标"""
        if isinstance(prices, pd.Series):
            prices = prices.values
        
        upper_band, middle_band, lower_band = talib.BBANDS(
            prices, 
            timeperiod=period, 
            nbdevup=std_dev, 
            nbdevdn=std_dev
        )
        
        # 生成交易信号
        signals = np.zeros_like(prices)
        signals[prices < lower_band] = 1  # 价格突破下轨买入
        signals[prices > upper_band] = -1  # 价格突破上轨卖出
        
        return IndicatorResult(
            values=middle_band,
            signals=signals,
            upper_band=upper_band,
            lower_band=lower_band,
            metadata={
                'period': period,
                'std_dev': std_dev,
                'name': 'Bollinger_Bands'
            }
        )
    
    @staticmethod
    def kdj(high: np.ndarray, 
            low: np.ndarray, 
            close: np.ndarray, 
            k_period: int = 9, 
            d_period: int = 3) -> IndicatorResult:
        """KDJ指标"""
        # 计算K值
        lowest_low = pd.Series(low).rolling(window=k_period).min().values
        highest_high = pd.Series(high).rolling(window=k_period).max().values
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        k_values = pd.Series(rsv).ewm(alpha=1/3).mean().values
        d_values = pd.Series(k_values).ewm(alpha=1/3).mean().values
        j_values = 3 * k_values - 2 * d_values
        
        # 生成交易信号
        signals = np.zeros_like(close)
        # K值和D值都小于20时买入，大于80时卖出
        signals[(k_values < 20) & (d_values < 20)] = 1
        signals[(k_values > 80) & (d_values > 80)] = -1
        
        return IndicatorResult(
            values=k_values,
            signals=signals,
            upper_band=d_values,
            lower_band=j_values,
            metadata={
                'k_period': k_period,
                'd_period': d_period,
                'name': 'KDJ'
            }
        )
    
    @staticmethod
    def cci(high: np.ndarray, 
            low: np.ndarray, 
            close: np.ndarray, 
            period: int = 14) -> IndicatorResult:
        """商品通道指标"""
        cci_values = talib.CCI(high, low, close, timeperiod=period)
        
        # 生成交易信号
        signals = np.zeros_like(close)
        signals[cci_values < -100] = 1  # CCI < -100 买入
        signals[cci_values > 100] = -1   # CCI > 100 卖出
        
        return IndicatorResult(
            values=cci_values,
            signals=signals,
            upper_band=np.full_like(cci_values, 100),
            lower_band=np.full_like(cci_values, -100),
            metadata={'period': period, 'name': 'CCI'}
        )
    
    @staticmethod
    def williams_r(high: np.ndarray, 
                   low: np.ndarray, 
                   close: np.ndarray, 
                   period: int = 14) -> IndicatorResult:
        """威廉指标"""
        wr_values = talib.WILLR(high, low, close, timeperiod=period)
        
        # 生成交易信号
        signals = np.zeros_like(close)
        signals[wr_values < -80] = 1  # WR < -80 买入
        signals[wr_values > -20] = -1  # WR > -20 卖出
        
        return IndicatorResult(
            values=wr_values,
            signals=signals,
            upper_band=np.full_like(wr_values, -20),
            lower_band=np.full_like(wr_values, -80),
            metadata={'period': period, 'name': 'Williams_R'}
        )
    
    @staticmethod
    def atr(high: np.ndarray, 
            low: np.ndarray, 
            close: np.ndarray, 
            period: int = 14) -> IndicatorResult:
        """平均真实波幅"""
        atr_values = talib.ATR(high, low, close, timeperiod=period)
        
        return IndicatorResult(
            values=atr_values,
            metadata={'period': period, 'name': 'ATR'}
        )
    
    @staticmethod
    def obv(close: np.ndarray, volume: np.ndarray) -> IndicatorResult:
        """能量潮指标"""
        obv_values = talib.OBV(close, volume)
        
        # 使用OBV的移动平均来生成信号
        obv_ma = talib.SMA(obv_values, timeperiod=10)
        signals = np.zeros_like(close)
        signals[obv_values > obv_ma] = 1
        signals[obv_values < obv_ma] = -1
        
        return IndicatorResult(
            values=obv_values,
            signals=signals,
            upper_band=obv_ma,
            metadata={'name': 'OBV'}
        )


class AdvancedIndicators:
    """高级技术指标"""
    
    @staticmethod
    def ichimoku_cloud(high: np.ndarray, 
                      low: np.ndarray, 
                      close: np.ndarray,
                      conversion_period: int = 9,
                      base_period: int = 26,
                      span_b_period: int = 52,
                      displacement: int = 26) -> dict:
        """一目均衡表（云图）"""
        # 转换线 (Tenkan-sen)
        conversion_line = (pd.Series(high).rolling(conversion_period).max() + 
                          pd.Series(low).rolling(conversion_period).min()) / 2
        
        # 基准线 (Kijun-sen)
        base_line = (pd.Series(high).rolling(base_period).max() + 
                    pd.Series(low).rolling(base_period).min()) / 2
        
        # 先行线A (Senkou Span A)
        span_a = ((conversion_line + base_line) / 2).shift(displacement)
        
        # 先行线B (Senkou Span B)
        span_b = ((pd.Series(high).rolling(span_b_period).max() + 
                  pd.Series(low).rolling(span_b_period).min()) / 2).shift(displacement)
        
        # 延迟线 (Chikou Span)
        lagging_span = pd.Series(close).shift(-displacement)
        
        # 生成交易信号
        signals = np.zeros_like(close)
        cloud_top = np.maximum(span_a.values, span_b.values)
        cloud_bottom = np.minimum(span_a.values, span_b.values)
        
        # 价格在云上方且转换线>基准线时买入
        buy_condition = (close > cloud_top) & (conversion_line > base_line)
        sell_condition = (close < cloud_bottom) & (conversion_line < base_line)
        
        signals[buy_condition] = 1
        signals[sell_condition] = -1
        
        return {
            'conversion_line': conversion_line.values,
            'base_line': base_line.values,
            'span_a': span_a.values,
            'span_b': span_b.values,
            'lagging_span': lagging_span.values,
            'signals': signals,
            'cloud_top': cloud_top,
            'cloud_bottom': cloud_bottom
        }
    
    @staticmethod
    def supertrend(high: np.ndarray, 
                   low: np.ndarray, 
                   close: np.ndarray,
                   period: int = 10,
                   multiplier: float = 3.0) -> IndicatorResult:
        """SuperTrend指标"""
        # 计算ATR
        atr = talib.ATR(high, low, close, timeperiod=period)
        
        # 计算基本上下轨
        hl2 = (high + low) / 2
        upper_band = hl2 + (multiplier * atr)
        lower_band = hl2 - (multiplier * atr)
        
        # 计算最终上下轨
        final_upper = np.zeros_like(upper_band)
        final_lower = np.zeros_like(lower_band)
        supertrend = np.zeros_like(close)
        
        for i in range(1, len(close)):
            # 最终上轨
            if upper_band[i] < final_upper[i-1] or close[i-1] > final_upper[i-1]:
                final_upper[i] = upper_band[i]
            else:
                final_upper[i] = final_upper[i-1]
            
            # 最终下轨
            if lower_band[i] > final_lower[i-1] or close[i-1] < final_lower[i-1]:
                final_lower[i] = lower_band[i]
            else:
                final_lower[i] = final_lower[i-1]
            
            # SuperTrend
            if close[i] <= final_upper[i]:
                supertrend[i] = final_upper[i]
            else:
                supertrend[i] = final_lower[i]
        
        # 生成信号
        signals = np.zeros_like(close)
        signals[close > supertrend] = 1  # 价格在SuperTrend上方买入
        signals[close < supertrend] = -1  # 价格在SuperTrend下方卖出
        
        return IndicatorResult(
            values=supertrend,
            signals=signals,
            upper_band=final_upper,
            lower_band=final_lower,
            metadata={
                'period': period,
                'multiplier': multiplier,
                'name': 'SuperTrend'
            }
        )
"""
回测相关异步任务
"""

import logging
from datetime import datetime
from typing import Any, Dict

from celery import current_task

from .celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="run_backtest_async")
def run_backtest_async(self, strategy_id: int, params: dict):
    """异步运行回测"""
    try:
        # 更新任务状态
        self.update_state(
            state="PROGRESS", meta={"step": "initializing", "progress": 0}
        )

        # 模拟回测过程
        import time

        # 1. 数据准备
        self.update_state(
            state="PROGRESS", meta={"step": "preparing_data", "progress": 10}
        )
        time.sleep(2)

        # 2. 策略初始化
        self.update_state(
            state="PROGRESS", meta={"step": "initializing_strategy", "progress": 20}
        )
        time.sleep(1)

        # 3. 运行回测
        total_days = params.get("days", 100)
        for day in range(total_days):
            progress = 20 + (day / total_days) * 60
            self.update_state(
                state="PROGRESS",
                meta={
                    "step": "running_backtest",
                    "progress": progress,
                    "current_day": day + 1,
                    "total_days": total_days,
                },
            )
            time.sleep(0.01)  # 模拟每日计算

        # 4. 生成结果
        self.update_state(
            state="PROGRESS", meta={"step": "generating_results", "progress": 85}
        )
        time.sleep(2)

        # 5. 计算指标
        self.update_state(
            state="PROGRESS", meta={"step": "calculating_metrics", "progress": 95}
        )
        time.sleep(1)

        # 模拟回测结果
        result = {
            "strategy_id": strategy_id,
            "start_date": params.get("start_date"),
            "end_date": params.get("end_date"),
            "initial_capital": params.get("initial_capital", 1000000),
            "final_capital": 1250000,
            "total_return": 0.25,
            "annual_return": 0.18,
            "max_drawdown": 0.08,
            "sharpe_ratio": 1.45,
            "sortino_ratio": 1.68,
            "calmar_ratio": 2.25,
            "win_rate": 0.65,
            "profit_factor": 1.85,
            "total_trades": 245,
            "winning_trades": 159,
            "losing_trades": 86,
            "avg_win": 2500.50,
            "avg_loss": -1200.25,
            "largest_win": 15000.00,
            "largest_loss": -8500.00,
            "avg_trade_duration": 3.2,
            "report_url": f'/reports/backtest_{strategy_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html',
        }

        self.update_state(state="SUCCESS", meta={"progress": 100, "result": result})
        return result

    except Exception as e:
        logger.error(f"回测运行失败: {e}")
        self.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True, name="generate_backtest_report")
def generate_backtest_report(self, backtest_id: int, result_data: dict):
    """生成回测报告"""
    try:
        self.update_state(
            state="PROGRESS", meta={"step": "loading_data", "progress": 10}
        )

        import time

        time.sleep(1)

        # 生成图表
        self.update_state(
            state="PROGRESS", meta={"step": "generating_charts", "progress": 30}
        )
        time.sleep(3)

        # 计算详细指标
        self.update_state(
            state="PROGRESS",
            meta={"step": "calculating_detailed_metrics", "progress": 60},
        )
        time.sleep(2)

        # 生成HTML报告
        self.update_state(
            state="PROGRESS", meta={"step": "generating_html_report", "progress": 80}
        )
        time.sleep(2)

        # 生成PDF报告
        self.update_state(
            state="PROGRESS", meta={"step": "generating_pdf_report", "progress": 95}
        )
        time.sleep(1)

        report = {
            "backtest_id": backtest_id,
            "html_report": f"/reports/backtest_{backtest_id}.html",
            "pdf_report": f"/reports/backtest_{backtest_id}.pdf",
            "charts": {
                "equity_curve": f"/charts/equity_{backtest_id}.png",
                "drawdown": f"/charts/drawdown_{backtest_id}.png",
                "monthly_returns": f"/charts/monthly_{backtest_id}.png",
                "trade_analysis": f"/charts/trades_{backtest_id}.png",
            },
            "generated_at": datetime.now().isoformat(),
        }

        self.update_state(state="SUCCESS", meta={"progress": 100, "report": report})
        return report

    except Exception as e:
        logger.error(f"报告生成失败: {e}")
        self.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(name="optimize_strategy_parameters")
def optimize_strategy_parameters(strategy_id: int, param_ranges: dict):
    """优化策略参数"""
    try:
        # 模拟参数优化过程
        import random
        import time

        total_combinations = 100  # 假设有100种参数组合
        best_result = None
        best_params = None

        for i in range(total_combinations):
            # 模拟每种参数组合的回测
            time.sleep(0.1)

            # 随机生成结果
            result = random.uniform(0.05, 0.30)  # 5%-30%年化收益

            if best_result is None or result > best_result:
                best_result = result
                best_params = {
                    "param1": random.uniform(0.1, 1.0),
                    "param2": random.randint(5, 50),
                    "param3": random.choice(["A", "B", "C"]),
                }

        optimization_result = {
            "strategy_id": strategy_id,
            "best_params": best_params,
            "best_return": best_result,
            "total_combinations_tested": total_combinations,
            "optimization_time": total_combinations * 0.1,
        }

        return optimization_result

    except Exception as e:
        logger.error(f"参数优化失败: {e}")
        raise


@celery_app.task(bind=True, name="run_backtest_by_id_async")
def run_backtest_by_id_async(self, backtest_id: int):
    """根据 backtest_id 调用 BacktestService 在协程中运行回测"""
    import asyncio

    from app.core.database import db_manager
    from app.services.backtest_service import BacktestService

    # 确保数据库已初始化
    db_manager.initialize()

    async def _run():
        async with db_manager.get_session() as session:
            service = BacktestService(session)
            await service.run_backtest_task(backtest_id)

    try:
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        if loop.is_running():
            coro = _run()
            asyncio.run_coroutine_threadsafe(coro, loop).result()
        else:
            loop.run_until_complete(_run())
        return {"backtest_id": backtest_id, "status": "completed"}
    except Exception as e:
        logger.error(f"回测任务 {backtest_id} 执行失败: {e}")
        raise

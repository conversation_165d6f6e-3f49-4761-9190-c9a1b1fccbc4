"""
通知任务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from celery import Celery
from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_async_session
from app.db.models.notification import Notification, NotificationTemplate
from app.db.models.user import User
from app.db.models.trading import Order, Trade, Position
from app.services.email_service import EmailService
from app.services.sms_service import SMSService
from app.services.websocket_service import WebSocketService
from app.services.notification_service import NotificationService
from app.tasks.celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=3)
def send_pending_notifications(self):
    """发送待处理通知"""
    try:
        logger.info("开始发送待处理通知")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_send_pending_notifications())
            logger.info(f"待处理通知发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"发送待处理通知失败: {e}")
        raise self.retry(exc=e, countdown=30)


async def _send_pending_notifications() -> Dict:
    """异步发送待处理通知"""
    async with get_async_session() as session:
        # 获取待发送的通知
        pending_query = (
            select(Notification)
            .where(
                Notification.status == "pending",
                Notification.scheduled_at <= datetime.now(),
            )
            .order_by(Notification.created_at)
        )

        result = await session.execute(pending_query)
        notifications = result.scalars().all()

        send_stats = {
            "total_notifications": len(notifications),
            "email_sent": 0,
            "sms_sent": 0,
            "websocket_sent": 0,
            "failed": 0,
            "errors": [],
        }

        for notification in notifications:
            try:
                success = await _send_notification(session, notification)

                if success:
                    # 更新通知状态
                    notification.status = "sent"
                    notification.sent_at = datetime.now()

                    # 统计发送类型
                    if notification.channel == "email":
                        send_stats["email_sent"] += 1
                    elif notification.channel == "sms":
                        send_stats["sms_sent"] += 1
                    elif notification.channel == "websocket":
                        send_stats["websocket_sent"] += 1
                else:
                    notification.status = "failed"
                    notification.failed_at = datetime.now()
                    send_stats["failed"] += 1

            except Exception as e:
                notification.status = "failed"
                notification.failed_at = datetime.now()
                notification.error_message = str(e)
                send_stats["failed"] += 1
                send_stats["errors"].append(f"Notification {notification.id}: {str(e)}")
                logger.error(f"发送通知失败 {notification.id}: {e}")

        await session.commit()

        return {
            "status": "completed",
            "stats": send_stats,
            "process_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def send_daily_summary(self):
    """发送日终汇总"""
    try:
        logger.info("开始发送日终汇总")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_send_daily_summary())
            logger.info(f"日终汇总发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"发送日终汇总失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _send_daily_summary() -> Dict:
    """异步发送日终汇总"""
    async with get_async_session() as session:
        today = datetime.now().date()

        # 获取所有活跃用户
        users_query = select(User).where(
            User.is_active == True,
            User.notification_preferences.contains({"daily_summary": True}),
        )
        result = await session.execute(users_query)
        users = result.scalars().all()

        summary_stats = {
            "total_users": len(users),
            "summaries_sent": 0,
            "failed": 0,
            "errors": [],
        }

        for user in users:
            try:
                # 生成用户的日终汇总
                summary_data = await _generate_user_daily_summary(
                    session, user.id, today
                )

                # 创建通知
                notification = Notification(
                    user_id=user.id,
                    type="daily_summary",
                    channel="email",
                    title=f"日终汇总 - {today}",
                    content=_format_daily_summary(summary_data),
                    status="pending",
                    scheduled_at=datetime.now(),
                )

                session.add(notification)
                summary_stats["summaries_sent"] += 1

            except Exception as e:
                summary_stats["failed"] += 1
                summary_stats["errors"].append(f"User {user.id}: {str(e)}")
                logger.error(f"生成用户 {user.id} 日终汇总失败: {e}")

        await session.commit()

        return {
            "status": "completed",
            "stats": summary_stats,
            "process_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=5)
def check_alert_conditions(self):
    """检查告警条件"""
    try:
        logger.info("开始检查告警条件")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_check_alert_conditions())
            logger.info(f"告警条件检查完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"检查告警条件失败: {e}")
        raise self.retry(exc=e, countdown=30)


async def _check_alert_conditions() -> Dict:
    """异步检查告警条件"""
    async with get_async_session() as session:
        alert_stats = {
            "price_alerts": 0,
            "position_alerts": 0,
            "risk_alerts": 0,
            "system_alerts": 0,
            "total_alerts": 0,
        }

        # 检查价格告警
        price_alerts = await _check_price_alerts(session)
        alert_stats["price_alerts"] = len(price_alerts)

        # 检查持仓告警
        position_alerts = await _check_position_alerts(session)
        alert_stats["position_alerts"] = len(position_alerts)

        # 检查风险告警
        risk_alerts = await _check_risk_alerts(session)
        alert_stats["risk_alerts"] = len(risk_alerts)

        # 检查系统告警
        system_alerts = await _check_system_alerts(session)
        alert_stats["system_alerts"] = len(system_alerts)

        # 创建告警通知
        all_alerts = price_alerts + position_alerts + risk_alerts + system_alerts
        alert_stats["total_alerts"] = len(all_alerts)

        for alert in all_alerts:
            notification = Notification(
                user_id=alert.get("user_id"),
                type="alert",
                channel=alert.get("channel", "email"),
                title=alert["title"],
                content=alert["content"],
                priority=alert.get("priority", "medium"),
                status="pending",
                scheduled_at=datetime.now(),
            )
            session.add(notification)

        await session.commit()

        return {
            "status": "completed",
            "stats": alert_stats,
            "check_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def send_email_notification(self, notification_id: int):
    """发送邮件通知"""
    try:
        logger.info(f"开始发送邮件通知: {notification_id}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_send_email_notification(notification_id))
            logger.info(f"邮件通知发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"发送邮件通知失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _send_email_notification(notification_id: int) -> Dict:
    """异步发送邮件通知"""
    async with get_async_session() as session:
        # 获取通知信息
        notification_query = select(Notification).where(
            Notification.id == notification_id
        )
        result = await session.execute(notification_query)
        notification = result.scalar_one_or_none()

        if not notification:
            return {"status": "failed", "message": "Notification not found"}

        # 获取用户信息
        user_query = select(User).where(User.id == notification.user_id)
        result = await session.execute(user_query)
        user = result.scalar_one_or_none()

        if not user:
            return {"status": "failed", "message": "User not found"}

        # 发送邮件
        email_service = EmailService()
        success = await email_service.send_email(
            to=[user.email],
            subject=notification.title,
            content=notification.content,
            html_content=notification.html_content,
        )

        # 更新通知状态
        if success:
            notification.status = "sent"
            notification.sent_at = datetime.now()
        else:
            notification.status = "failed"
            notification.failed_at = datetime.now()

        await session.commit()

        return {
            "status": "sent" if success else "failed",
            "notification_id": notification_id,
            "user_email": user.email,
            "send_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def send_sms_notification(self, notification_id: int):
    """发送短信通知"""
    try:
        logger.info(f"开始发送短信通知: {notification_id}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_send_sms_notification(notification_id))
            logger.info(f"短信通知发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"发送短信通知失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _send_sms_notification(notification_id: int) -> Dict:
    """异步发送短信通知"""
    async with get_async_session() as session:
        # 获取通知信息
        notification_query = select(Notification).where(
            Notification.id == notification_id
        )
        result = await session.execute(notification_query)
        notification = result.scalar_one_or_none()

        if not notification:
            return {"status": "failed", "message": "Notification not found"}

        # 获取用户信息
        user_query = select(User).where(User.id == notification.user_id)
        result = await session.execute(user_query)
        user = result.scalar_one_or_none()

        if not user or not user.phone:
            return {"status": "failed", "message": "User phone not found"}

        # 发送短信
        sms_service = SMSService()
        success = await sms_service.send_sms(
            phone=user.phone, message=notification.content
        )

        # 更新通知状态
        if success:
            notification.status = "sent"
            notification.sent_at = datetime.now()
        else:
            notification.status = "failed"
            notification.failed_at = datetime.now()

        await session.commit()

        return {
            "status": "sent" if success else "failed",
            "notification_id": notification_id,
            "user_phone": user.phone,
            "send_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def send_websocket_notification(self, notification_id: int):
    """发送WebSocket通知"""
    try:
        logger.info(f"开始发送WebSocket通知: {notification_id}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(
                _send_websocket_notification(notification_id)
            )
            logger.info(f"WebSocket通知发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"发送WebSocket通知失败: {e}")
        raise self.retry(exc=e, countdown=30)


async def _send_websocket_notification(notification_id: int) -> Dict:
    """异步发送WebSocket通知"""
    async with get_async_session() as session:
        # 获取通知信息
        notification_query = select(Notification).where(
            Notification.id == notification_id
        )
        result = await session.execute(notification_query)
        notification = result.scalar_one_or_none()

        if not notification:
            return {"status": "failed", "message": "Notification not found"}

        # 发送WebSocket消息
        websocket_service = WebSocketService()
        success = await websocket_service.send_to_user(
            user_id=notification.user_id,
            message={
                "type": "notification",
                "id": notification.id,
                "title": notification.title,
                "content": notification.content,
                "priority": notification.priority,
                "timestamp": datetime.now().isoformat(),
            },
        )

        # 更新通知状态
        if success:
            notification.status = "sent"
            notification.sent_at = datetime.now()
        else:
            notification.status = "failed"
            notification.failed_at = datetime.now()

        await session.commit()

        return {
            "status": "sent" if success else "failed",
            "notification_id": notification_id,
            "user_id": notification.user_id,
            "send_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def send_batch_notifications(self, notifications_data: List[Dict]):
    """批量发送通知"""
    try:
        logger.info(f"开始批量发送通知: {len(notifications_data)} 条")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(
                _send_batch_notifications(notifications_data)
            )
            logger.info(f"批量通知发送完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"批量发送通知失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _send_batch_notifications(notifications_data: List[Dict]) -> Dict:
    """异步批量发送通知"""
    async with get_async_session() as session:
        batch_stats = {
            "total_notifications": len(notifications_data),
            "created": 0,
            "failed": 0,
            "errors": [],
        }

        for notification_data in notifications_data:
            try:
                notification = Notification(
                    user_id=notification_data["user_id"],
                    type=notification_data["type"],
                    channel=notification_data["channel"],
                    title=notification_data["title"],
                    content=notification_data["content"],
                    priority=notification_data.get("priority", "medium"),
                    status="pending",
                    scheduled_at=datetime.now(),
                )

                session.add(notification)
                batch_stats["created"] += 1

            except Exception as e:
                batch_stats["failed"] += 1
                batch_stats["errors"].append(str(e))
                logger.error(f"创建通知失败: {e}")

        await session.commit()

        return {
            "status": "completed",
            "stats": batch_stats,
            "process_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def cleanup_old_notifications(self):
    """清理旧通知"""
    try:
        logger.info("开始清理旧通知")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_cleanup_old_notifications())
            logger.info(f"旧通知清理完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"清理旧通知失败: {e}")
        raise self.retry(exc=e, countdown=300)


async def _cleanup_old_notifications() -> Dict:
    """异步清理旧通知"""
    async with get_async_session() as session:
        # 清理30天前的已发送通知
        thirty_days_ago = datetime.now() - timedelta(days=30)

        # 删除旧通知
        delete_query = select(Notification).where(
            Notification.created_at < thirty_days_ago,
            Notification.status.in_(["sent", "failed"]),
        )

        result = await session.execute(delete_query)
        old_notifications = result.scalars().all()

        cleanup_stats = {"deleted_count": len(old_notifications)}

        for notification in old_notifications:
            await session.delete(notification)

        await session.commit()

        return {
            "status": "completed",
            "stats": cleanup_stats,
            "cleanup_time": datetime.now().isoformat(),
        }


# 辅助函数
async def _send_notification(session: AsyncSession, notification: Notification) -> bool:
    """发送单个通知"""
    try:
        if notification.channel == "email":
            return await _send_email_notification(notification.id)
        elif notification.channel == "sms":
            return await _send_sms_notification(notification.id)
        elif notification.channel == "websocket":
            return await _send_websocket_notification(notification.id)
        else:
            logger.error(f"不支持的通知渠道: {notification.channel}")
            return False

    except Exception as e:
        logger.error(f"发送通知失败: {e}")
        return False


async def _generate_user_daily_summary(
    session: AsyncSession, user_id: int, date
) -> Dict:
    """生成用户日终汇总"""
    summary = {
        "user_id": user_id,
        "date": date.isoformat(),
        "trading_summary": {},
        "position_summary": {},
        "performance_summary": {},
    }

    # 获取当日交易统计
    orders_query = select(Order).where(
        Order.user_id == user_id, func.date(Order.created_at) == date
    )
    result = await session.execute(orders_query)
    orders = result.scalars().all()

    summary["trading_summary"] = {
        "total_orders": len(orders),
        "filled_orders": len([o for o in orders if o.status == "filled"]),
        "cancelled_orders": len([o for o in orders if o.status == "cancelled"]),
    }

    # 获取持仓统计
    positions_query = select(Position).where(
        Position.user_id == user_id, Position.quantity > 0
    )
    result = await session.execute(positions_query)
    positions = result.scalars().all()

    summary["position_summary"] = {
        "total_positions": len(positions),
        "total_value": sum(p.quantity * p.avg_price for p in positions),
    }

    return summary


def _format_daily_summary(summary_data: Dict) -> str:
    """格式化日终汇总内容"""
    content = f"""
    日终汇总 - {summary_data['date']}
    
    交易统计:
    - 总订单数: {summary_data['trading_summary']['total_orders']}
    - 成交订单数: {summary_data['trading_summary']['filled_orders']}
    - 取消订单数: {summary_data['trading_summary']['cancelled_orders']}
    
    持仓统计:
    - 持仓数量: {summary_data['position_summary']['total_positions']}
    - 持仓总价值: {summary_data['position_summary']['total_value']:.2f}
    
    """

    return content


async def _check_price_alerts(session: AsyncSession) -> List[Dict]:
    """检查价格告警"""
    # 这里应该实现价格告警逻辑
    # 暂时返回空列表
    return []


async def _check_position_alerts(session: AsyncSession) -> List[Dict]:
    """检查持仓告警"""
    # 这里应该实现持仓告警逻辑
    # 暂时返回空列表
    return []


async def _check_risk_alerts(session: AsyncSession) -> List[Dict]:
    """检查风险告警"""
    # 这里应该实现风险告警逻辑
    # 暂时返回空列表
    return []


async def _check_system_alerts(session: AsyncSession) -> List[Dict]:
    """检查系统告警"""
    # 这里应该实现系统告警逻辑
    # 暂时返回空列表
    return []

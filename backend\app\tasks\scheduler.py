"""
任务调度器管理
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from celery.schedules import crontab
from celery import Celery

from app.core.config import settings

logger = logging.getLogger(__name__)


class TaskScheduler:
    """任务调度器管理类"""

    def __init__(self, celery_app: Celery):
        self.celery_app = celery_app
        self.scheduled_tasks: Dict[str, Dict] = {}
        self.task_history: List[Dict] = []

    def get_schedule_config(self) -> Dict[str, Dict]:
        """获取完整的调度配置"""
        return {
            # 市场数据相关任务
            "sync-market-data": {
                "task": "app.tasks.data_tasks.sync_market_data",
                "schedule": 60.0,  # 每分钟执行一次
                "options": {"queue": "data"},
                "description": "同步市场数据",
                "enabled": True,
            },
            "sync-tushare-data": {
                "task": "app.tasks.data_tasks.sync_tushare_data",
                "schedule": crontab(minute=0, hour="9,15"),  # 开盘和收盘时间
                "options": {"queue": "data"},
                "description": "同步Tushare数据",
                "enabled": True,
            },
            "update-stock-list": {
                "task": "app.tasks.data_tasks.update_stock_list",
                "schedule": crontab(hour=6, minute=0),  # 每天早上6点
                "options": {"queue": "data"},
                "description": "更新股票列表",
                "enabled": True,
            },
            # 报告生成任务
            "generate-daily-report": {
                "task": "app.tasks.report_tasks.generate_daily_report",
                "schedule": crontab(hour=18, minute=0),  # 每天18:00执行
                "options": {"queue": "report"},
                "description": "生成日终报告",
                "enabled": True,
            },
            "generate-weekly-report": {
                "task": "app.tasks.report_tasks.generate_weekly_report",
                "schedule": crontab(hour=19, minute=0, day_of_week=1),  # 每周一19:00
                "options": {"queue": "report"},
                "description": "生成周报",
                "enabled": True,
            },
            "generate-monthly-report": {
                "task": "app.tasks.report_tasks.generate_monthly_report",
                "schedule": crontab(hour=20, minute=0, day_of_month=1),  # 每月1日20:00
                "options": {"queue": "report"},
                "description": "生成月报",
                "enabled": True,
            },
            # 风险监控任务
            "risk-monitoring": {
                "task": "app.tasks.data_tasks.risk_monitoring_check",
                "schedule": 30.0,  # 每30秒执行一次
                "options": {"queue": "monitoring"},
                "description": "风险监控检查",
                "enabled": True,
            },
            "position-risk-check": {
                "task": "app.tasks.data_tasks.position_risk_check",
                "schedule": 60.0,  # 每分钟执行一次
                "options": {"queue": "monitoring"},
                "description": "持仓风险检查",
                "enabled": True,
            },
            # 系统维护任务
            "system-health-check": {
                "task": "app.tasks.data_tasks.system_health_check",
                "schedule": 300.0,  # 每5分钟执行一次
                "options": {"queue": "monitoring"},
                "description": "系统健康检查",
                "enabled": True,
            },
            "cleanup-expired-data": {
                "task": "app.tasks.data_tasks.cleanup_expired_data",
                "schedule": crontab(hour=2, minute=0),  # 每天凌晨2点执行
                "options": {"queue": "maintenance"},
                "description": "清理过期数据",
                "enabled": True,
            },
            "cleanup-logs": {
                "task": "app.tasks.data_tasks.cleanup_old_logs",
                "schedule": crontab(hour=3, minute=0),  # 每天凌晨3点执行
                "options": {"queue": "maintenance"},
                "description": "清理旧日志",
                "enabled": True,
            },
            "backup-database": {
                "task": "app.tasks.data_tasks.backup_database",
                "schedule": crontab(hour=1, minute=0),  # 每天凌晨1点执行
                "options": {"queue": "maintenance"},
                "description": "数据库备份",
                "enabled": True,
            },
            # 通知任务
            "send-pending-notifications": {
                "task": "app.tasks.notification_tasks.send_pending_notifications",
                "schedule": 120.0,  # 每2分钟执行一次
                "options": {"queue": "notification"},
                "description": "发送待处理通知",
                "enabled": True,
            },
            "send-daily-summary": {
                "task": "app.tasks.notification_tasks.send_daily_summary",
                "schedule": crontab(hour=19, minute=30),  # 每天19:30
                "options": {"queue": "notification"},
                "description": "发送日终汇总",
                "enabled": True,
            },
            "check-alert-conditions": {
                "task": "app.tasks.notification_tasks.check_alert_conditions",
                "schedule": 60.0,  # 每分钟执行一次
                "options": {"queue": "notification"},
                "description": "检查告警条件",
                "enabled": True,
            },
            # 策略相关任务
            "update-strategy-signals": {
                "task": "app.tasks.data_tasks.update_strategy_signals",
                "schedule": 300.0,  # 每5分钟执行一次
                "options": {"queue": "strategy"},
                "description": "更新策略信号",
                "enabled": True,
            },
            "rebalance-portfolios": {
                "task": "app.tasks.data_tasks.rebalance_portfolios",
                "schedule": crontab(hour=9, minute=30),  # 每天9:30（开盘后）
                "options": {"queue": "strategy"},
                "description": "组合再平衡",
                "enabled": True,
            },
        }

    def get_enabled_tasks(self) -> Dict[str, Dict]:
        """获取启用的任务"""
        all_tasks = self.get_schedule_config()
        return {
            name: config
            for name, config in all_tasks.items()
            if config.get("enabled", True)
        }

    def enable_task(self, task_name: str) -> bool:
        """启用任务"""
        try:
            if task_name in self.get_schedule_config():
                # 这里可以添加数据库操作来持久化任务状态
                logger.info(f"Task {task_name} enabled")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to enable task {task_name}: {e}")
            return False

    def disable_task(self, task_name: str) -> bool:
        """禁用任务"""
        try:
            if task_name in self.get_schedule_config():
                # 这里可以添加数据库操作来持久化任务状态
                logger.info(f"Task {task_name} disabled")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to disable task {task_name}: {e}")
            return False

    def get_task_status(self, task_name: str) -> Optional[Dict]:
        """获取任务状态"""
        config = self.get_schedule_config().get(task_name)
        if not config:
            return None

        return {
            "name": task_name,
            "description": config.get("description", ""),
            "schedule": str(config.get("schedule", "")),
            "queue": config.get("options", {}).get("queue", "default"),
            "enabled": config.get("enabled", True),
            "last_run": None,  # 可以从数据库获取
            "next_run": None,  # 可以从Celery获取
        }

    def get_all_tasks_status(self) -> List[Dict]:
        """获取所有任务状态"""
        tasks = []
        for task_name in self.get_schedule_config().keys():
            status = self.get_task_status(task_name)
            if status:
                tasks.append(status)
        return tasks

    def run_task_now(self, task_name: str) -> bool:
        """立即运行任务"""
        try:
            config = self.get_schedule_config().get(task_name)
            if not config:
                return False

            task_path = config["task"]
            self.celery_app.send_task(task_path)
            logger.info(f"Task {task_name} triggered manually")
            return True
        except Exception as e:
            logger.error(f"Failed to run task {task_name}: {e}")
            return False

    def get_task_history(self, task_name: str, limit: int = 50) -> List[Dict]:
        """获取任务执行历史"""
        # 这里可以从数据库获取任务执行历史
        # 暂时返回空列表
        return []

    def get_queue_info(self) -> Dict[str, Dict]:
        """获取队列信息"""
        return {
            "trading": {
                "description": "交易相关任务",
                "priority": "high",
                "max_workers": 5,
            },
            "backtest": {
                "description": "回测任务",
                "priority": "medium",
                "max_workers": 3,
            },
            "report": {
                "description": "报告生成任务",
                "priority": "low",
                "max_workers": 2,
            },
            "data": {
                "description": "数据同步任务",
                "priority": "medium",
                "max_workers": 4,
            },
            "notification": {
                "description": "通知任务",
                "priority": "medium",
                "max_workers": 2,
            },
            "monitoring": {
                "description": "监控任务",
                "priority": "high",
                "max_workers": 3,
            },
            "maintenance": {
                "description": "维护任务",
                "priority": "low",
                "max_workers": 1,
            },
            "strategy": {
                "description": "策略相关任务",
                "priority": "high",
                "max_workers": 3,
            },
        }


# 全局调度器实例
scheduler = None


def get_scheduler(celery_app: Celery = None) -> TaskScheduler:
    """获取调度器实例"""
    global scheduler
    if scheduler is None and celery_app:
        scheduler = TaskScheduler(celery_app)
    return scheduler


def init_scheduler(celery_app: Celery) -> TaskScheduler:
    """初始化调度器"""
    global scheduler
    scheduler = TaskScheduler(celery_app)
    return scheduler

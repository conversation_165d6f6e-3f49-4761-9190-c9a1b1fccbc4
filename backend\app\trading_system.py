#!/usr/bin/env python3
"""完整的交易系统实现"""

from fastapi import APIRouter, HTTPException
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import secrets
import random
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/trading", tags=["trading"])

# ============ 数据模型 ============

class SubmitOrderRequest(BaseModel):
    symbol: str
    side: str  # buy | sell
    type: str = "limit"  # market | limit | stop | takeProfit
    quantity: float
    price: Optional[float] = None
    timeInForce: str = "DAY"  # DAY | IOC | FOK | GTC
    stopLoss: Optional[float] = None
    takeProfit: Optional[float] = None

class ModifyOrderRequest(BaseModel):
    price: Optional[float] = None
    quantity: Optional[float] = None

class CancelOrderRequest(BaseModel):
    orderId: str

class BatchCancelRequest(BaseModel):
    orderIds: List[str]

class ClosePositionRequest(BaseModel):
    symbol: str
    quantity: Optional[float] = None
    type: str = "market"  # market | limit

class StopLossTakeProfitRequest(BaseModel):
    symbol: str
    stopLoss: Optional[float] = None
    takeProfit: Optional[float] = None

# ============ 数据存储 ============

# 订单存储
ORDERS_DB: Dict[str, Dict[str, Any]] = {}

# 成交记录
TRADES_DB: Dict[str, Dict[str, Any]] = {}

# 持仓数据
POSITIONS_DB: Dict[str, Dict[str, Any]] = {
    "000001": {
        "symbol": "000001",
        "name": "平安银行",
        "quantity": 10000,
        "availableQuantity": 8000,
        "avgPrice": 12.5,
        "currentPrice": 13.45,
        "marketValue": 134500.0,
        "profit": 9500.0,
        "profitRate": 7.6,
        "dayProfit": 1200.0,
        "dayProfitRate": 0.9,
        "stopLoss": None,
        "takeProfit": None,
        "updatedAt": datetime.now().isoformat()
    }
}

# 账户数据
ACCOUNT_DATA = {
    "accountId": "10001",
    "totalAssets": 1500000.00,
    "availableCash": 800000.00,
    "marketValue": 650000.00,
    "frozenCash": 50000.00,
    "totalProfit": 120000.00,
    "totalProfitRate": 8.7,
    "dayProfit": 5600.00,
    "dayProfitRate": 0.38,
    "riskLevel": 25,
    "marginRatio": 0.15
}

# WebSocket连接管理已移至统一的 websocket_enhanced.py

# ============ 辅助函数 ============

def calculate_order_fees(symbol: str, quantity: float, price: float, side: str) -> Dict[str, float]:
    """计算交易费用"""
    amount = quantity * price
    
    # 佣金 (万分之3)
    commission_rate = 0.0003
    commission = max(amount * commission_rate, 5.0)  # 最低5元
    
    # 印花税 (卖出千分之1)
    stamp_duty = amount * 0.001 if side == "sell" else 0.0
    
    # 过户费 (万分之0.2)
    transfer_fee = amount * 0.00002
    
    total_fees = commission + stamp_duty + transfer_fee
    
    return {
        "commission": round(commission, 2),
        "stampDuty": round(stamp_duty, 2),
        "transferFee": round(transfer_fee, 2),
        "totalFees": round(total_fees, 2)
    }

def update_account_after_order(order: dict, action: str = "submit"):
    """更新账户资金"""
    global ACCOUNT_DATA
    
    if action == "submit" and order["side"] == "buy":
        # 买入冻结资金
        amount = order["quantity"] * order["price"]
        fees = order["fees"]["totalFees"]
        ACCOUNT_DATA["frozenCash"] += (amount + fees)
        ACCOUNT_DATA["availableCash"] -= (amount + fees)
    elif action == "cancel" and order["side"] == "buy":
        # 取消买单释放资金
        amount = order["quantity"] * order["price"]
        fees = order["fees"]["totalFees"]
        ACCOUNT_DATA["frozenCash"] -= (amount + fees)
        ACCOUNT_DATA["availableCash"] += (amount + fees)

def execute_order(order: dict):
    """模拟订单执行"""
    # 随机执行逻辑
    if order["type"] == "market":
        # 市价单立即成交
        order["status"] = "filled"
        order["filledQuantity"] = order["quantity"]
        order["avgPrice"] = order["price"]
        order["filledAt"] = datetime.now().isoformat()
        
        # 创建成交记录
        create_trade(order)
        
        # 更新持仓
        update_position_after_trade(order)
    else:
        # 限价单随机成交
        if random.random() > 0.5:
            order["status"] = "filled"
            order["filledQuantity"] = order["quantity"]
            order["avgPrice"] = order["price"]
            order["filledAt"] = datetime.now().isoformat()
            
            create_trade(order)
            update_position_after_trade(order)
        else:
            # 部分成交
            order["filledQuantity"] = order["quantity"] * random.uniform(0.3, 0.7)
            order["status"] = "partial_filled"

def create_trade(order: dict):
    """创建成交记录"""
    trade_id = f"trade_{secrets.token_hex(8)}"
    trade = {
        "tradeId": trade_id,
        "orderId": order["orderId"],
        "symbol": order["symbol"],
        "side": order["side"],
        "price": order["avgPrice"],
        "quantity": order["filledQuantity"],
        "amount": order["avgPrice"] * order["filledQuantity"],
        "fees": order["fees"]["totalFees"],
        "tradedAt": datetime.now().isoformat()
    }
    TRADES_DB[trade_id] = trade
    return trade

def update_position_after_trade(order: dict):
    """更新持仓信息"""
    symbol = order["symbol"]
    
    if order["side"] == "buy":
        if symbol in POSITIONS_DB:
            # 更新现有持仓
            position = POSITIONS_DB[symbol]
            total_quantity = position["quantity"] + order["filledQuantity"]
            total_cost = position["avgPrice"] * position["quantity"] + order["avgPrice"] * order["filledQuantity"]
            position["avgPrice"] = total_cost / total_quantity
            position["quantity"] = total_quantity
            position["availableQuantity"] = total_quantity
        else:
            # 创建新持仓
            POSITIONS_DB[symbol] = {
                "symbol": symbol,
                "name": f"股票{symbol}",
                "quantity": order["filledQuantity"],
                "availableQuantity": order["filledQuantity"],
                "avgPrice": order["avgPrice"],
                "currentPrice": order["avgPrice"],
                "marketValue": order["avgPrice"] * order["filledQuantity"],
                "profit": 0,
                "profitRate": 0,
                "dayProfit": 0,
                "dayProfitRate": 0,
                "stopLoss": None,
                "takeProfit": None,
                "updatedAt": datetime.now().isoformat()
            }
    else:  # sell
        if symbol in POSITIONS_DB:
            position = POSITIONS_DB[symbol]
            position["quantity"] -= order["filledQuantity"]
            position["availableQuantity"] = position["quantity"]
            
            # 如果全部卖出，删除持仓
            if position["quantity"] <= 0:
                del POSITIONS_DB[symbol]

# ============ API路由 ============

@router.post("/orders")
async def submit_order(request: SubmitOrderRequest):
    """提交订单"""
    try:
        # 生成订单ID
        order_id = f"order_{secrets.token_hex(8)}"
        
        # 验证价格
        if request.type == "limit" and not request.price:
            return {"success": False, "message": "限价单必须设置价格"}
        
        # 如果是市价单，使用当前价格
        if request.type == "market":
            request.price = 10.0 + random.uniform(-1, 1)  # 模拟当前价
        
        # 计算费用
        fees = calculate_order_fees(request.symbol, request.quantity, request.price, request.side)
        
        # 创建订单
        order = {
            "orderId": order_id,
            "symbol": request.symbol,
            "side": request.side,
            "type": request.type,
            "quantity": request.quantity,
            "price": request.price,
            "status": "submitted",
            "timeInForce": request.timeInForce,
            "stopLoss": request.stopLoss,
            "takeProfit": request.takeProfit,
            "createdAt": datetime.now().isoformat(),
            "updatedAt": datetime.now().isoformat(),
            "filledQuantity": 0,
            "avgPrice": 0,
            "fees": fees,
            "riskLevel": "medium" if request.quantity * request.price > 100000 else "low"
        }
        
        # 保存订单
        ORDERS_DB[order_id] = order
        
        # 更新账户资金
        update_account_after_order(order, "submit")
        
        # 模拟订单执行
        asyncio.create_task(simulate_order_execution(order))
        
        # 订单更新广播已移至统一WebSocket服务
        # 可通过 websocket_enhanced.py 中的服务进行广播
        
        return {
            "success": True,
            "data": order,
            "message": "订单提交成功"
        }
        
    except Exception as e:
        logger.error(f"提交订单失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/orders")
async def get_orders(
    status: Optional[str] = None,
    side: Optional[str] = None,
    symbol: Optional[str] = None,
    startDate: Optional[str] = None,
    endDate: Optional[str] = None,
    page: int = 1,
    pageSize: int = 20
):
    """获取订单列表"""
    try:
        orders = list(ORDERS_DB.values())
        
        # 过滤
        if status:
            orders = [o for o in orders if o["status"] == status]
        if side:
            orders = [o for o in orders if o["side"] == side]
        if symbol:
            orders = [o for o in orders if o["symbol"] == symbol]
        
        # 排序 (最新的在前)
        orders.sort(key=lambda x: x["createdAt"], reverse=True)
        
        # 分页
        start = (page - 1) * pageSize
        end = start + pageSize
        
        return {
            "success": True,
            "data": {
                "list": orders[start:end],
                "total": len(orders),
                "page": page,
                "pageSize": pageSize
            }
        }
    except Exception as e:
        logger.error(f"获取订单列表失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/orders/{order_id}")
async def get_order_detail(order_id: str):
    """获取订单详情"""
    if order_id not in ORDERS_DB:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    return {
        "success": True,
        "data": ORDERS_DB[order_id]
    }

@router.delete("/orders/{order_id}")
async def cancel_order(order_id: str):
    """取消订单"""
    try:
        if order_id not in ORDERS_DB:
            return {"success": False, "message": "订单不存在"}
        
        order = ORDERS_DB[order_id]
        
        # 检查订单状态
        if order["status"] in ["filled", "cancelled", "rejected"]:
            return {"success": False, "message": f"订单状态为{order['status']}，无法取消"}
        
        # 更新订单状态
        order["status"] = "cancelled"
        order["updatedAt"] = datetime.now().isoformat()
        
        # 释放冻结资金
        update_account_after_order(order, "cancel")
        
        # 订单取消广播已移至统一WebSocket服务
        
        return {
            "success": True,
            "data": {"orderId": order_id, "status": "cancelled"},
            "message": "订单取消成功"
        }
    except Exception as e:
        logger.error(f"取消订单失败: {e}")
        return {"success": False, "message": str(e)}

@router.put("/orders/{order_id}")
async def modify_order(order_id: str, request: ModifyOrderRequest):
    """修改订单"""
    try:
        if order_id not in ORDERS_DB:
            return {"success": False, "message": "订单不存在"}
        
        order = ORDERS_DB[order_id]
        
        # 检查订单状态
        if order["status"] != "submitted":
            return {"success": False, "message": "只能修改未成交的订单"}
        
        # 更新订单
        if request.price is not None:
            order["price"] = request.price
        if request.quantity is not None:
            order["quantity"] = request.quantity
        
        order["updatedAt"] = datetime.now().isoformat()
        
        # 重新计算费用
        order["fees"] = calculate_order_fees(
            order["symbol"], 
            order["quantity"], 
            order["price"], 
            order["side"]
        )
        
        return {
            "success": True,
            "data": order,
            "message": "订单修改成功"
        }
    except Exception as e:
        logger.error(f"修改订单失败: {e}")
        return {"success": False, "message": str(e)}

@router.post("/orders/batch-cancel")
async def batch_cancel_orders(request: BatchCancelRequest):
    """批量取消订单"""
    try:
        cancelled_count = 0
        failed_count = 0
        
        for order_id in request.orderIds:
            if order_id in ORDERS_DB:
                order = ORDERS_DB[order_id]
                if order["status"] == "submitted":
                    order["status"] = "cancelled"
                    order["updatedAt"] = datetime.now().isoformat()
                    cancelled_count += 1
                else:
                    failed_count += 1
            else:
                failed_count += 1
        
        return {
            "success": True,
            "data": {
                "cancelledCount": cancelled_count,
                "failedCount": failed_count
            },
            "message": f"成功取消 {cancelled_count} 个订单"
        }
    except Exception as e:
        logger.error(f"批量取消订单失败: {e}")
        return {"success": False, "message": str(e)}

@router.post("/orders/cancel-all")
async def cancel_all_orders(symbol: Optional[str] = None):
    """取消所有订单"""
    try:
        cancelled_count = 0
        
        for order_id, order in ORDERS_DB.items():
            if order["status"] == "submitted":
                if symbol is None or order["symbol"] == symbol:
                    order["status"] = "cancelled"
                    order["updatedAt"] = datetime.now().isoformat()
                    cancelled_count += 1
        
        return {
            "success": True,
            "data": {"cancelledCount": cancelled_count},
            "message": f"成功取消 {cancelled_count} 个订单"
        }
    except Exception as e:
        logger.error(f"取消所有订单失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/trades")
async def get_trades(
    symbol: Optional[str] = None,
    side: Optional[str] = None,
    startDate: Optional[str] = None,
    endDate: Optional[str] = None,
    page: int = 1,
    pageSize: int = 20
):
    """获取成交记录"""
    try:
        trades = list(TRADES_DB.values())
        
        # 过滤
        if symbol:
            trades = [t for t in trades if t["symbol"] == symbol]
        if side:
            trades = [t for t in trades if t["side"] == side]
        
        # 排序
        trades.sort(key=lambda x: x["tradedAt"], reverse=True)
        
        # 分页
        start = (page - 1) * pageSize
        end = start + pageSize
        
        return {
            "success": True,
            "data": {
                "list": trades[start:end],
                "total": len(trades),
                "page": page,
                "pageSize": pageSize
            }
        }
    except Exception as e:
        logger.error(f"获取成交记录失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/positions")
async def get_positions():
    """获取持仓信息"""
    try:
        # 更新持仓市值和盈亏
        for symbol, position in POSITIONS_DB.items():
            # 模拟当前价格
            position["currentPrice"] = position["avgPrice"] * (1 + random.uniform(-0.05, 0.05))
            position["marketValue"] = position["currentPrice"] * position["quantity"]
            position["profit"] = (position["currentPrice"] - position["avgPrice"]) * position["quantity"]
            position["profitRate"] = (position["currentPrice"] / position["avgPrice"] - 1) * 100
            position["dayProfit"] = position["profit"] * 0.1  # 模拟
            position["dayProfitRate"] = position["profitRate"] * 0.1  # 模拟
        
        positions = list(POSITIONS_DB.values())
        
        return {
            "success": True,
            "data": positions
        }
    except Exception as e:
        logger.error(f"获取持仓失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/positions/{symbol}")
async def get_position_detail(symbol: str):
    """获取单个持仓详情"""
    if symbol not in POSITIONS_DB:
        raise HTTPException(status_code=404, detail="持仓不存在")
    
    position = POSITIONS_DB[symbol]
    
    # 添加更多详情
    position["trades"] = [t for t in TRADES_DB.values() if t["symbol"] == symbol][-10:]
    position["orders"] = [o for o in ORDERS_DB.values() if o["symbol"] == symbol and o["status"] == "submitted"]
    
    return {
        "success": True,
        "data": position
    }

@router.post("/positions/stop-loss-take-profit")
async def set_stop_loss_take_profit(request: StopLossTakeProfitRequest):
    """设置止损止盈"""
    try:
        if request.symbol not in POSITIONS_DB:
            return {"success": False, "message": "持仓不存在"}
        
        position = POSITIONS_DB[request.symbol]
        
        if request.stopLoss is not None:
            position["stopLoss"] = request.stopLoss
        if request.takeProfit is not None:
            position["takeProfit"] = request.takeProfit
        
        position["updatedAt"] = datetime.now().isoformat()
        
        return {
            "success": True,
            "data": position,
            "message": "止损止盈设置成功"
        }
    except Exception as e:
        logger.error(f"设置止损止盈失败: {e}")
        return {"success": False, "message": str(e)}

@router.post("/positions/close")
async def close_position(request: ClosePositionRequest):
    """平仓"""
    try:
        if request.symbol not in POSITIONS_DB:
            return {"success": False, "message": "持仓不存在"}
        
        position = POSITIONS_DB[request.symbol]
        quantity = request.quantity or position["quantity"]
        
        # 创建卖出订单
        order_request = SubmitOrderRequest(
            symbol=request.symbol,
            side="sell",
            type=request.type,
            quantity=quantity,
            price=position["currentPrice"] if request.type == "limit" else None
        )
        
        result = await submit_order(order_request)
        
        return result
    except Exception as e:
        logger.error(f"平仓失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/account")
async def get_account_info():
    """获取账户信息"""
    try:
        # 计算持仓市值
        total_market_value = sum(p["marketValue"] for p in POSITIONS_DB.values())
        ACCOUNT_DATA["marketValue"] = total_market_value
        ACCOUNT_DATA["totalAssets"] = ACCOUNT_DATA["availableCash"] + total_market_value + ACCOUNT_DATA["frozenCash"]
        
        return {
            "success": True,
            "data": ACCOUNT_DATA
        }
    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/account/history")
async def get_account_history(
    type: Optional[str] = None,
    startDate: Optional[str] = None,
    endDate: Optional[str] = None,
    page: int = 1,
    pageSize: int = 20
):
    """获取账户资金流水"""
    try:
        # 模拟资金流水
        history = []
        for i in range(50):
            history.append({
                "id": f"flow_{i}",
                "type": random.choice(["deposit", "withdraw", "buy", "sell", "fee", "dividend"]),
                "amount": random.uniform(-10000, 10000),
                "balance": 1000000 + random.uniform(-100000, 100000),
                "description": "资金变动",
                "createdAt": (datetime.now() - timedelta(days=i)).isoformat()
            })
        
        # 过滤
        if type:
            history = [h for h in history if h["type"] == type]
        
        # 分页
        start = (page - 1) * pageSize
        end = start + pageSize
        
        return {
            "success": True,
            "data": {
                "list": history[start:end],
                "total": len(history),
                "page": page,
                "pageSize": pageSize
            }
        }
    except Exception as e:
        logger.error(f"获取资金流水失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    try:
        # 计算风险指标
        total_positions = len(POSITIONS_DB)
        total_market_value = sum(p["marketValue"] for p in POSITIONS_DB.values())
        max_position_value = max([p["marketValue"] for p in POSITIONS_DB.values()], default=0)
        
        return {
            "success": True,
            "data": {
                "var95": -0.025 * total_market_value,  # 95% VaR
                "maxDrawdown": -0.08,
                "sharpeRatio": 1.2,
                "beta": 0.9,
                "volatility": 0.15,
                "leverage": total_market_value / ACCOUNT_DATA["totalAssets"],
                "concentration": max_position_value / total_market_value if total_market_value > 0 else 0,
                "exposureByMarket": {
                    "SH": 0.6,
                    "SZ": 0.4
                },
                "exposureBySector": {
                    "金融": 0.3,
                    "科技": 0.4,
                    "消费": 0.3
                }
            }
        }
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/stats")
async def get_trading_stats(
    period: str = "today"  # today | week | month | year
):
    """获取交易统计"""
    try:
        # 模拟统计数据
        stats = {
            "period": period,
            "totalTrades": len(TRADES_DB),
            "winTrades": int(len(TRADES_DB) * 0.6),
            "lossTrades": int(len(TRADES_DB) * 0.4),
            "winRate": 0.6,
            "totalProfit": 50000.0,
            "totalLoss": -20000.0,
            "netProfit": 30000.0,
            "profitFactor": 2.5,
            "avgWin": 1000.0,
            "avgLoss": -500.0,
            "maxConsecutiveWins": 8,
            "maxConsecutiveLosses": 3,
            "tradingDays": 20,
            "avgTradesPerDay": len(TRADES_DB) / 20 if len(TRADES_DB) > 0 else 0,
            "commission": 3000.0,
            "slippage": 1000.0
        }
        
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取交易统计失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/symbols")
async def get_tradable_symbols():
    """获取可交易品种列表"""
    try:
        symbols = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "status": "trading"},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "status": "trading"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "status": "trading"},
            {"symbol": "600519", "name": "贵州茅台", "market": "SH", "status": "trading"},
            {"symbol": "000858", "name": "五粮液", "market": "SZ", "status": "trading"}
        ]
        
        return {
            "success": True,
            "data": symbols
        }
    except Exception as e:
        logger.error(f"获取可交易品种失败: {e}")
        return {"success": False, "message": str(e)}

@router.get("/fees")
async def get_trading_fees(symbol: Optional[str] = None):
    """获取交易手续费率"""
    try:
        fees = {
            "commission": {
                "rate": 0.0003,
                "minimum": 5.0,
                "description": "佣金费率万分之3，最低5元"
            },
            "stampDuty": {
                "rate": 0.001,
                "appliesTo": "sell",
                "description": "印花税千分之1，仅卖出收取"
            },
            "transferFee": {
                "rate": 0.00002,
                "description": "过户费万分之0.2"
            }
        }
        
        return {
            "success": True,
            "data": fees
        }
    except Exception as e:
        logger.error(f"获取手续费率失败: {e}")
        return {"success": False, "message": str(e)}

# ============ WebSocket端点已移至统一服务 ============
# 交易WebSocket功能已整合到 /api/v1/ws/trading 端点
# 请使用 websocket_enhanced.py 中的统一WebSocket服务

# ============ 异步任务 ============

async def simulate_order_execution(order: dict):
    """模拟订单执行过程"""
    await asyncio.sleep(random.uniform(1, 5))  # 模拟延迟
    
    # 执行订单
    execute_order(order)
    
    # 订单执行和持仓更新广播已移至统一WebSocket服务

# 导出路由器
__all__ = ["router"]
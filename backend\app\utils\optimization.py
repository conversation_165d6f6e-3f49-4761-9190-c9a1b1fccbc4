"""
参数优化算法实现
包含网格搜索、随机搜索、贝叶斯优化、遗传算法等
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Callable, Optional
from abc import ABC, abstractmethod
import itertools
import random
from scipy.optimize import minimize, differential_evolution
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel
import warnings
warnings.filterwarnings('ignore')


class BaseOptimizer(ABC):
    """优化器基类"""
    
    def __init__(self, objective_function: Callable, parameter_bounds: Dict[str, Tuple[float, float]]):
        self.objective_function = objective_function
        self.parameter_bounds = parameter_bounds
        self.history = []
        
    @abstractmethod
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行优化"""
        pass
    
    def _evaluate(self, parameters: Dict[str, Any]) -> float:
        """评估参数组合"""
        try:
            result = self.objective_function(parameters)
            self.history.append({
                'parameters': parameters.copy(),
                'objective_value': result,
                'iteration': len(self.history) + 1
            })
            return result
        except Exception as e:
            print(f"评估失败: {e}")
            return -np.inf


class GridSearchOptimizer(BaseOptimizer):
    """网格搜索优化器"""
    
    def __init__(self, objective_function: Callable, 
                 parameter_bounds: Dict[str, Tuple[float, float]],
                 grid_steps: Dict[str, int] = None):
        super().__init__(objective_function, parameter_bounds)
        self.grid_steps = grid_steps or {}
        
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行网格搜索优化"""
        # 生成网格点
        grid_points = self._generate_grid()
        
        # 限制搜索点数量
        if len(grid_points) > max_iterations:
            grid_points = random.sample(grid_points, max_iterations)
        
        best_params = None
        best_value = -np.inf
        
        for params in grid_points:
            value = self._evaluate(params)
            if value > best_value:
                best_value = value
                best_params = params.copy()
        
        return {
            'best_parameters': best_params,
            'best_value': best_value,
            'iterations': len(self.history),
            'history': self.history
        }
    
    def _generate_grid(self) -> List[Dict[str, Any]]:
        """生成网格点"""
        param_grids = {}
        
        for param_name, (min_val, max_val) in self.parameter_bounds.items():
            steps = self.grid_steps.get(param_name, 10)
            param_grids[param_name] = np.linspace(min_val, max_val, steps)
        
        # 生成所有组合
        param_names = list(param_grids.keys())
        grid_points = []
        
        for combination in itertools.product(*param_grids.values()):
            params = dict(zip(param_names, combination))
            grid_points.append(params)
        
        return grid_points


class RandomSearchOptimizer(BaseOptimizer):
    """随机搜索优化器"""
    
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行随机搜索优化"""
        best_params = None
        best_value = -np.inf
        
        for i in range(max_iterations):
            # 生成随机参数
            params = self._generate_random_params()
            value = self._evaluate(params)
            
            if value > best_value:
                best_value = value
                best_params = params.copy()
        
        return {
            'best_parameters': best_params,
            'best_value': best_value,
            'iterations': len(self.history),
            'history': self.history
        }
    
    def _generate_random_params(self) -> Dict[str, Any]:
        """生成随机参数"""
        params = {}
        for param_name, (min_val, max_val) in self.parameter_bounds.items():
            params[param_name] = random.uniform(min_val, max_val)
        return params


class BayesianOptimizer(BaseOptimizer):
    """贝叶斯优化器"""
    
    def __init__(self, objective_function: Callable, 
                 parameter_bounds: Dict[str, Tuple[float, float]],
                 n_initial_points: int = 5,
                 acquisition_function: str = 'ei'):  # 'ei', 'pi', 'ucb'
        super().__init__(objective_function, parameter_bounds)
        self.n_initial_points = n_initial_points
        self.acquisition_function = acquisition_function
        self.gp = None
        
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行贝叶斯优化"""
        # 初始随机采样
        self._initial_sampling()
        
        # 贝叶斯优化循环
        for i in range(max_iterations - self.n_initial_points):
            if len(self.history) >= max_iterations:
                break
                
            # 训练高斯过程
            self._train_gp()
            
            # 寻找下一个采样点
            next_params = self._find_next_point()
            
            # 评估新点
            self._evaluate(next_params)
        
        # 找到最佳结果
        best_result = max(self.history, key=lambda x: x['objective_value'])
        
        return {
            'best_parameters': best_result['parameters'],
            'best_value': best_result['objective_value'],
            'iterations': len(self.history),
            'history': self.history
        }
    
    def _initial_sampling(self):
        """初始随机采样"""
        for _ in range(self.n_initial_points):
            params = {}
            for param_name, (min_val, max_val) in self.parameter_bounds.items():
                params[param_name] = random.uniform(min_val, max_val)
            self._evaluate(params)
    
    def _train_gp(self):
        """训练高斯过程模型"""
        if len(self.history) < 2:
            return
        
        # 准备训练数据
        X = []
        y = []
        
        for record in self.history:
            x = [record['parameters'][param] for param in sorted(self.parameter_bounds.keys())]
            X.append(x)
            y.append(record['objective_value'])
        
        X = np.array(X)
        y = np.array(y)
        
        # 创建和训练高斯过程
        kernel = ConstantKernel() * RBF() + ConstantKernel() * Matern(nu=2.5)
        self.gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, n_restarts_optimizer=10)
        
        try:
            self.gp.fit(X, y)
        except Exception as e:
            print(f"高斯过程训练失败: {e}")
            self.gp = None
    
    def _find_next_point(self) -> Dict[str, Any]:
        """寻找下一个采样点"""
        if self.gp is None:
            # 如果GP训练失败，返回随机点
            params = {}
            for param_name, (min_val, max_val) in self.parameter_bounds.items():
                params[param_name] = random.uniform(min_val, max_val)
            return params
        
        # 定义获取函数
        def acquisition_func(x):
            x = x.reshape(1, -1)
            mu, sigma = self.gp.predict(x, return_std=True)
            
            if self.acquisition_function == 'ei':
                # Expected Improvement
                current_best = max(record['objective_value'] for record in self.history)
                improvement = mu - current_best
                ei = improvement * self._normal_cdf(improvement / sigma) + sigma * self._normal_pdf(improvement / sigma)
                return -ei[0]  # 负号因为我们要最大化
            elif self.acquisition_function == 'pi':
                # Probability of Improvement
                current_best = max(record['objective_value'] for record in self.history)
                pi = self._normal_cdf((mu - current_best) / sigma)
                return -pi[0]
            else:  # UCB
                # Upper Confidence Bound
                ucb = mu + 2.576 * sigma  # 99% confidence
                return -ucb[0]
        
        # 优化获取函数
        bounds = [(min_val, max_val) for min_val, max_val in self.parameter_bounds.values()]
        
        try:
            result = minimize(acquisition_func, 
                            x0=[random.uniform(min_val, max_val) for min_val, max_val in bounds],
                            bounds=bounds,
                            method='L-BFGS-B')
            
            next_x = result.x
        except:
            # 优化失败，使用随机点
            next_x = [random.uniform(min_val, max_val) for min_val, max_val in bounds]
        
        # 转换为参数字典
        params = {}
        param_names = sorted(self.parameter_bounds.keys())
        for i, param_name in enumerate(param_names):
            params[param_name] = next_x[i]
        
        return params
    
    def _normal_cdf(self, x):
        """标准正态分布CDF"""
        return 0.5 * (1 + np.erf(x / np.sqrt(2)))
    
    def _normal_pdf(self, x):
        """标准正态分布PDF"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)


class GeneticOptimizer(BaseOptimizer):
    """遗传算法优化器"""
    
    def __init__(self, objective_function: Callable,
                 parameter_bounds: Dict[str, Tuple[float, float]],
                 population_size: int = 50,
                 mutation_rate: float = 0.1,
                 crossover_rate: float = 0.8):
        super().__init__(objective_function, parameter_bounds)
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行遗传算法优化"""
        # 初始化种群
        population = self._initialize_population()
        
        best_individual = None
        best_fitness = -np.inf
        
        for generation in range(max_iterations):
            # 评估种群
            fitness_scores = []
            for individual in population:
                fitness = self._evaluate(individual)
                fitness_scores.append(fitness)
                
                if fitness > best_fitness:
                    best_fitness = fitness
                    best_individual = individual.copy()
            
            # 选择、交叉、变异
            population = self._evolve_population(population, fitness_scores)
        
        return {
            'best_parameters': best_individual,
            'best_value': best_fitness,
            'iterations': len(self.history),
            'history': self.history
        }
    
    def _initialize_population(self) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        for _ in range(self.population_size):
            individual = {}
            for param_name, (min_val, max_val) in self.parameter_bounds.items():
                individual[param_name] = random.uniform(min_val, max_val)
            population.append(individual)
        return population
    
    def _evolve_population(self, population: List[Dict[str, Any]], 
                          fitness_scores: List[float]) -> List[Dict[str, Any]]:
        """进化种群"""
        new_population = []
        
        # 保留最优个体（精英主义）
        best_idx = np.argmax(fitness_scores)
        new_population.append(population[best_idx].copy())
        
        # 生成新个体
        while len(new_population) < self.population_size:
            # 选择父母
            parent1 = self._tournament_selection(population, fitness_scores)
            parent2 = self._tournament_selection(population, fitness_scores)
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            if random.random() < self.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.mutation_rate:
                child2 = self._mutate(child2)
            
            new_population.extend([child1, child2])
        
        return new_population[:self.population_size]
    
    def _tournament_selection(self, population: List[Dict[str, Any]], 
                            fitness_scores: List[float], tournament_size: int = 3) -> Dict[str, Any]:
        """锦标赛选择"""
        tournament_indices = random.sample(range(len(population)), min(tournament_size, len(population)))
        best_idx = max(tournament_indices, key=lambda i: fitness_scores[i])
        return population[best_idx].copy()
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """单点交叉"""
        child1, child2 = parent1.copy(), parent2.copy()
        
        param_names = list(self.parameter_bounds.keys())
        crossover_point = random.randint(1, len(param_names) - 1)
        
        for i in range(crossover_point, len(param_names)):
            param_name = param_names[i]
            child1[param_name], child2[param_name] = child2[param_name], child1[param_name]
        
        return child1, child2
    
    def _mutate(self, individual: Dict[str, Any]) -> Dict[str, Any]:
        """高斯变异"""
        mutated = individual.copy()
        
        for param_name, (min_val, max_val) in self.parameter_bounds.items():
            if random.random() < 0.5:  # 50%概率变异每个参数
                current_val = mutated[param_name]
                mutation_strength = (max_val - min_val) * 0.1  # 10%的范围作为变异强度
                new_val = current_val + random.gauss(0, mutation_strength)
                mutated[param_name] = max(min_val, min(max_val, new_val))  # 边界约束
        
        return mutated


class ParticleSwarmOptimizer(BaseOptimizer):
    """粒子群优化器"""
    
    def __init__(self, objective_function: Callable,
                 parameter_bounds: Dict[str, Tuple[float, float]],
                 swarm_size: int = 30,
                 w: float = 0.7,  # 惯性权重
                 c1: float = 1.5,  # 个体学习因子
                 c2: float = 1.5):  # 社会学习因子
        super().__init__(objective_function, parameter_bounds)
        self.swarm_size = swarm_size
        self.w = w
        self.c1 = c1
        self.c2 = c2
        
    def optimize(self, max_iterations: int = 100) -> Dict[str, Any]:
        """执行粒子群优化"""
        # 初始化粒子群
        particles = self._initialize_swarm()
        velocities = self._initialize_velocities()
        personal_best = [p.copy() for p in particles]
        personal_best_values = [self._evaluate(p) for p in personal_best]
        
        # 全局最优
        global_best_idx = np.argmax(personal_best_values)
        global_best = personal_best[global_best_idx].copy()
        global_best_value = personal_best_values[global_best_idx]
        
        for iteration in range(max_iterations):
            for i in range(self.swarm_size):
                # 更新粒子位置和速度
                particles[i], velocities[i] = self._update_particle(
                    particles[i], velocities[i], personal_best[i], global_best
                )
                
                # 评估新位置
                current_value = self._evaluate(particles[i])
                
                # 更新个体最优
                if current_value > personal_best_values[i]:
                    personal_best[i] = particles[i].copy()
                    personal_best_values[i] = current_value
                    
                    # 更新全局最优
                    if current_value > global_best_value:
                        global_best = particles[i].copy()
                        global_best_value = current_value
        
        return {
            'best_parameters': global_best,
            'best_value': global_best_value,
            'iterations': len(self.history),
            'history': self.history
        }
    
    def _initialize_swarm(self) -> List[Dict[str, Any]]:
        """初始化粒子群"""
        swarm = []
        for _ in range(self.swarm_size):
            particle = {}
            for param_name, (min_val, max_val) in self.parameter_bounds.items():
                particle[param_name] = random.uniform(min_val, max_val)
            swarm.append(particle)
        return swarm
    
    def _initialize_velocities(self) -> List[Dict[str, Any]]:
        """初始化速度"""
        velocities = []
        for _ in range(self.swarm_size):
            velocity = {}
            for param_name, (min_val, max_val) in self.parameter_bounds.items():
                max_velocity = (max_val - min_val) * 0.1  # 最大速度为范围的10%
                velocity[param_name] = random.uniform(-max_velocity, max_velocity)
            velocities.append(velocity)
        return velocities
    
    def _update_particle(self, particle: Dict[str, Any], velocity: Dict[str, Any],
                        personal_best: Dict[str, Any], global_best: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """更新粒子位置和速度"""
        new_particle = {}
        new_velocity = {}
        
        for param_name in particle.keys():
            min_val, max_val = self.parameter_bounds[param_name]
            
            # 更新速度
            r1, r2 = random.random(), random.random()
            new_velocity[param_name] = (
                self.w * velocity[param_name] +
                self.c1 * r1 * (personal_best[param_name] - particle[param_name]) +
                self.c2 * r2 * (global_best[param_name] - particle[param_name])
            )
            
            # 限制速度
            max_velocity = (max_val - min_val) * 0.1
            new_velocity[param_name] = max(-max_velocity, min(max_velocity, new_velocity[param_name]))
            
            # 更新位置
            new_particle[param_name] = particle[param_name] + new_velocity[param_name]
            
            # 边界约束
            new_particle[param_name] = max(min_val, min(max_val, new_particle[param_name]))
        
        return new_particle, new_velocity


def get_optimizer(method: str, objective_function: Callable, 
                 parameter_bounds: Dict[str, Tuple[float, float]], **kwargs) -> BaseOptimizer:
    """根据方法名获取优化器"""
    if method == "grid_search":
        return GridSearchOptimizer(objective_function, parameter_bounds, **kwargs)
    elif method == "random_search":
        return RandomSearchOptimizer(objective_function, parameter_bounds, **kwargs)
    elif method == "bayesian":
        return BayesianOptimizer(objective_function, parameter_bounds, **kwargs)
    elif method == "genetic":
        return GeneticOptimizer(objective_function, parameter_bounds, **kwargs)
    elif method == "pso":
        return ParticleSwarmOptimizer(objective_function, parameter_bounds, **kwargs)
    else:
        raise ValueError(f"不支持的优化方法: {method}")


# 示例使用
if __name__ == "__main__":
    # 测试优化器
    def test_function(params):
        """测试函数：Rosenbrock函数"""
        x, y = params['x'], params['y']
        return -(100 * (y - x**2)**2 + (1 - x)**2)  # 负号因为我们要最大化
    
    bounds = {'x': (-2, 2), 'y': (-2, 2)}
    
    # 测试不同优化器
    methods = ['grid_search', 'random_search', 'bayesian', 'genetic', 'pso']
    
    for method in methods:
        print(f"\n测试 {method}:")
        try:
            optimizer = get_optimizer(method, test_function, bounds)
            result = optimizer.optimize(max_iterations=50)
            print(f"最优参数: {result['best_parameters']}")
            print(f"最优值: {result['best_value']:.6f}")
            print(f"迭代次数: {result['iterations']}")
        except Exception as e:
            print(f"优化失败: {e}")
"""
技术指标计算库
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum


class IndicatorType(Enum):
    """指标类型枚举"""

    TREND = "trend"  # 趋势指标
    MOMENTUM = "momentum"  # 动量指标
    VOLATILITY = "volatility"  # 波动率指标
    VOLUME = "volume"  # 成交量指标
    SUPPORT_RESISTANCE = "support_resistance"  # 支撑阻力指标


@dataclass
class IndicatorResult:
    """指标计算结果"""

    values: Union[np.ndarray, Dict[str, np.ndarray]]
    signals: Optional[np.ndarray] = None
    metadata: Optional[Dict] = None


class TechnicalIndicators:
    """技术指标计算类"""

    @staticmethod
    def sma(data: Union[List, np.ndarray, pd.Series], period: int) -> np.ndarray:
        """
        简单移动平均线 (Simple Moving Average)

        Args:
            data: 价格数据
            period: 周期

        Returns:
            SMA值数组
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        return data.rolling(window=period).mean().values

    @staticmethod
    def ema(
        data: Union[List, np.ndarray, pd.Series],
        period: int,
        alpha: Optional[float] = None,
    ) -> np.ndarray:
        """
        指数移动平均线 (Exponential Moving Average)

        Args:
            data: 价格数据
            period: 周期
            alpha: 平滑因子，默认为 2/(period+1)

        Returns:
            EMA值数组
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        if alpha is None:
            alpha = 2.0 / (period + 1)

        return data.ewm(alpha=alpha).mean().values

    @staticmethod
    def wma(data: Union[List, np.ndarray, pd.Series], period: int) -> np.ndarray:
        """
        加权移动平均线 (Weighted Moving Average)

        Args:
            data: 价格数据
            period: 周期

        Returns:
            WMA值数组
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        weights = np.arange(1, period + 1)

        def weighted_mean(x):
            return np.average(x, weights=weights)

        return data.rolling(window=period).apply(weighted_mean, raw=True).values

    @staticmethod
    def macd(
        data: Union[List, np.ndarray, pd.Series],
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ) -> IndicatorResult:
        """
        MACD指标 (Moving Average Convergence Divergence)

        Args:
            data: 价格数据
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期

        Returns:
            包含MACD、信号线、柱状图的结果
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        # 计算快慢EMA
        ema_fast = TechnicalIndicators.ema(data, fast_period)
        ema_slow = TechnicalIndicators.ema(data, slow_period)

        # MACD线
        macd_line = ema_fast - ema_slow

        # 信号线
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)

        # 柱状图
        histogram = macd_line - signal_line

        # 生成交易信号
        signals = np.zeros_like(macd_line)
        signals[1:] = np.where(
            (macd_line[1:] > signal_line[1:]) & (macd_line[:-1] <= signal_line[:-1]),
            1,
            np.where(
                (macd_line[1:] < signal_line[1:])
                & (macd_line[:-1] >= signal_line[:-1]),
                -1,
                0,
            ),
        )

        return IndicatorResult(
            values={"macd": macd_line, "signal": signal_line, "histogram": histogram},
            signals=signals,
            metadata={
                "fast_period": fast_period,
                "slow_period": slow_period,
                "signal_period": signal_period,
            },
        )

    @staticmethod
    def rsi(
        data: Union[List, np.ndarray, pd.Series], period: int = 14
    ) -> IndicatorResult:
        """
        相对强弱指数 (Relative Strength Index)

        Args:
            data: 价格数据
            period: 周期

        Returns:
            RSI值和交易信号
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        # 计算价格变化
        delta = data.diff()

        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # 计算平均收益和损失
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()

        # 计算RS和RSI
        rs = avg_gain / avg_loss
        rsi_values = 100 - (100 / (1 + rs))

        # 生成交易信号
        signals = np.zeros_like(rsi_values)
        signals = np.where(rsi_values < 30, 1, signals)  # 超卖买入
        signals = np.where(rsi_values > 70, -1, signals)  # 超买卖出

        return IndicatorResult(
            values=rsi_values.values,
            signals=signals,
            metadata={"period": period, "overbought": 70, "oversold": 30},
        )

    @staticmethod
    def bollinger_bands(
        data: Union[List, np.ndarray, pd.Series], period: int = 20, std_dev: float = 2.0
    ) -> IndicatorResult:
        """
        布林带 (Bollinger Bands)

        Args:
            data: 价格数据
            period: 周期
            std_dev: 标准差倍数

        Returns:
            包含上轨、中轨、下轨的结果
        """
        if isinstance(data, (list, np.ndarray)):
            data = pd.Series(data)

        # 中轨（简单移动平均）
        middle_band = data.rolling(window=period).mean()

        # 标准差
        std = data.rolling(window=period).std()

        # 上轨和下轨
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)

        # 生成交易信号
        signals = np.zeros_like(data)
        signals = np.where(data < lower_band, 1, signals)  # 突破下轨买入
        signals = np.where(data > upper_band, -1, signals)  # 突破上轨卖出

        return IndicatorResult(
            values={
                "upper": upper_band.values,
                "middle": middle_band.values,
                "lower": lower_band.values,
            },
            signals=signals,
            metadata={"period": period, "std_dev": std_dev},
        )

    @staticmethod
    def stochastic(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        k_period: int = 14,
        d_period: int = 3,
    ) -> IndicatorResult:
        """
        随机指标 (Stochastic Oscillator)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            k_period: %K周期
            d_period: %D周期

        Returns:
            包含%K和%D的结果
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 计算最高价和最低价的滚动值
        highest_high = high.rolling(window=k_period).max()
        lowest_low = low.rolling(window=k_period).min()

        # 计算%K
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)

        # 计算%D（%K的移动平均）
        d_percent = k_percent.rolling(window=d_period).mean()

        # 生成交易信号
        signals = np.zeros_like(k_percent)
        signals = np.where((k_percent < 20) & (d_percent < 20), 1, signals)  # 超卖买入
        signals = np.where((k_percent > 80) & (d_percent > 80), -1, signals)  # 超买卖出

        return IndicatorResult(
            values={"k_percent": k_percent.values, "d_percent": d_percent.values},
            signals=signals,
            metadata={
                "k_period": k_period,
                "d_period": d_period,
                "overbought": 80,
                "oversold": 20,
            },
        )

    @staticmethod
    def williams_r(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        period: int = 14,
    ) -> IndicatorResult:
        """
        威廉指标 (Williams %R)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            Williams %R值和交易信号
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 计算最高价和最低价的滚动值
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()

        # 计算Williams %R
        williams_r = -100 * (highest_high - close) / (highest_high - lowest_low)

        # 生成交易信号
        signals = np.zeros_like(williams_r)
        signals = np.where(williams_r < -80, 1, signals)  # 超卖买入
        signals = np.where(williams_r > -20, -1, signals)  # 超买卖出

        return IndicatorResult(
            values=williams_r.values,
            signals=signals,
            metadata={"period": period, "overbought": -20, "oversold": -80},
        )

    @staticmethod
    def atr(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        period: int = 14,
    ) -> np.ndarray:
        """
        平均真实范围 (Average True Range)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            ATR值数组
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 计算真实范围
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # 计算ATR
        atr_values = true_range.rolling(window=period).mean()

        return atr_values.values

    @staticmethod
    def adx(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        period: int = 14,
    ) -> IndicatorResult:
        """
        平均方向指数 (Average Directional Index)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            包含ADX、+DI、-DI的结果
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 计算价格变化
        high_diff = high.diff()
        low_diff = low.diff()

        # 计算方向移动
        plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)

        # 计算真实范围
        atr_values = TechnicalIndicators.atr(high, low, close, period)

        # 计算方向指标
        plus_di = 100 * pd.Series(plus_dm).rolling(window=period).mean() / atr_values
        minus_di = 100 * pd.Series(minus_dm).rolling(window=period).mean() / atr_values

        # 计算ADX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx_values = dx.rolling(window=period).mean()

        # 生成交易信号
        signals = np.zeros_like(adx_values)
        signals = np.where(
            (plus_di > minus_di) & (adx_values > 25), 1, signals
        )  # 上升趋势
        signals = np.where(
            (minus_di > plus_di) & (adx_values > 25), -1, signals
        )  # 下降趋势

        return IndicatorResult(
            values={
                "adx": adx_values.values,
                "plus_di": plus_di.values,
                "minus_di": minus_di.values,
            },
            signals=signals,
            metadata={"period": period, "trend_threshold": 25},
        )

    @staticmethod
    def cci(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        period: int = 20,
    ) -> IndicatorResult:
        """
        商品通道指数 (Commodity Channel Index)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            CCI值和交易信号
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 计算典型价格
        typical_price = (high + low + close) / 3

        # 计算移动平均和平均偏差
        ma = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean()))
        )

        # 计算CCI
        cci_values = (typical_price - ma) / (0.015 * mad)

        # 生成交易信号
        signals = np.zeros_like(cci_values)
        signals = np.where(cci_values < -100, 1, signals)  # 超卖买入
        signals = np.where(cci_values > 100, -1, signals)  # 超买卖出

        return IndicatorResult(
            values=cci_values.values,
            signals=signals,
            metadata={"period": period, "overbought": 100, "oversold": -100},
        )

    @staticmethod
    def obv(
        close: Union[List, np.ndarray, pd.Series],
        volume: Union[List, np.ndarray, pd.Series],
    ) -> np.ndarray:
        """
        能量潮 (On Balance Volume)

        Args:
            close: 收盘价
            volume: 成交量

        Returns:
            OBV值数组
        """
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)
        if isinstance(volume, (list, np.ndarray)):
            volume = pd.Series(volume)

        # 计算价格变化方向
        price_change = close.diff()

        # 计算OBV
        obv_values = np.zeros_like(close)
        obv_values[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv_values[i] = obv_values[i - 1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv_values[i] = obv_values[i - 1] - volume.iloc[i]
            else:
                obv_values[i] = obv_values[i - 1]

        return obv_values

    @staticmethod
    def vwap(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        volume: Union[List, np.ndarray, pd.Series],
    ) -> np.ndarray:
        """
        成交量加权平均价 (Volume Weighted Average Price)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            volume: 成交量

        Returns:
            VWAP值数组
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)
        if isinstance(volume, (list, np.ndarray)):
            volume = pd.Series(volume)

        # 计算典型价格
        typical_price = (high + low + close) / 3

        # 计算VWAP
        cumulative_volume = volume.cumsum()
        cumulative_typical_price_volume = (typical_price * volume).cumsum()

        vwap_values = cumulative_typical_price_volume / cumulative_volume

        return vwap_values.values

    @staticmethod
    def pivot_points(high: float, low: float, close: float) -> Dict[str, float]:
        """
        枢轴点 (Pivot Points)

        Args:
            high: 前一日最高价
            low: 前一日最低价
            close: 前一日收盘价

        Returns:
            包含枢轴点和支撑阻力位的字典
        """
        # 计算枢轴点
        pivot = (high + low + close) / 3

        # 计算支撑和阻力位
        r1 = 2 * pivot - low
        s1 = 2 * pivot - high
        r2 = pivot + (high - low)
        s2 = pivot - (high - low)
        r3 = high + 2 * (pivot - low)
        s3 = low - 2 * (high - pivot)

        return {
            "pivot": pivot,
            "r1": r1,
            "r2": r2,
            "r3": r3,
            "s1": s1,
            "s2": s2,
            "s3": s3,
        }

    @staticmethod
    def fibonacci_retracement(high: float, low: float) -> Dict[str, float]:
        """
        斐波那契回调 (Fibonacci Retracement)

        Args:
            high: 最高价
            low: 最低价

        Returns:
            斐波那契回调位字典
        """
        diff = high - low

        return {
            "0%": high,
            "23.6%": high - 0.236 * diff,
            "38.2%": high - 0.382 * diff,
            "50%": high - 0.5 * diff,
            "61.8%": high - 0.618 * diff,
            "100%": low,
        }

    @staticmethod
    def ichimoku_cloud(
        high: Union[List, np.ndarray, pd.Series],
        low: Union[List, np.ndarray, pd.Series],
        close: Union[List, np.ndarray, pd.Series],
        tenkan_period: int = 9,
        kijun_period: int = 26,
        senkou_span_b_period: int = 52,
    ) -> IndicatorResult:
        """
        一目均衡表 (Ichimoku Cloud)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            tenkan_period: 转换线周期
            kijun_period: 基准线周期
            senkou_span_b_period: 先行带B周期

        Returns:
            包含一目均衡表各线的结果
        """
        if isinstance(high, (list, np.ndarray)):
            high = pd.Series(high)
        if isinstance(low, (list, np.ndarray)):
            low = pd.Series(low)
        if isinstance(close, (list, np.ndarray)):
            close = pd.Series(close)

        # 转换线 (Tenkan-sen)
        tenkan_sen = (
            high.rolling(window=tenkan_period).max()
            + low.rolling(window=tenkan_period).min()
        ) / 2

        # 基准线 (Kijun-sen)
        kijun_sen = (
            high.rolling(window=kijun_period).max()
            + low.rolling(window=kijun_period).min()
        ) / 2

        # 先行带A (Senkou Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(kijun_period)

        # 先行带B (Senkou Span B)
        senkou_span_b = (
            (
                high.rolling(window=senkou_span_b_period).max()
                + low.rolling(window=senkou_span_b_period).min()
            )
            / 2
        ).shift(kijun_period)

        # 滞后线 (Chikou Span)
        chikou_span = close.shift(-kijun_period)

        # 生成交易信号
        signals = np.zeros_like(close)
        # 价格突破云层上方且转换线>基准线时买入
        cloud_top = np.maximum(senkou_span_a, senkou_span_b)
        cloud_bottom = np.minimum(senkou_span_a, senkou_span_b)

        signals = np.where((close > cloud_top) & (tenkan_sen > kijun_sen), 1, signals)
        signals = np.where(
            (close < cloud_bottom) & (tenkan_sen < kijun_sen), -1, signals
        )

        return IndicatorResult(
            values={
                "tenkan_sen": tenkan_sen.values,
                "kijun_sen": kijun_sen.values,
                "senkou_span_a": senkou_span_a.values,
                "senkou_span_b": senkou_span_b.values,
                "chikou_span": chikou_span.values,
            },
            signals=signals,
            metadata={
                "tenkan_period": tenkan_period,
                "kijun_period": kijun_period,
                "senkou_span_b_period": senkou_span_b_period,
            },
        )


class IndicatorCombinations:
    """指标组合策略"""

    @staticmethod
    def trend_following_combo(data: pd.DataFrame) -> IndicatorResult:
        """
        趋势跟踪组合策略

        Args:
            data: 包含OHLCV的DataFrame

        Returns:
            组合信号结果
        """
        # 计算各种指标
        macd_result = TechnicalIndicators.macd(data["close"])
        adx_result = TechnicalIndicators.adx(data["high"], data["low"], data["close"])
        ema_20 = TechnicalIndicators.ema(data["close"], 20)
        ema_50 = TechnicalIndicators.ema(data["close"], 50)

        # 组合信号逻辑
        signals = np.zeros(len(data))

        for i in range(1, len(data)):
            # 多头信号：MACD金叉 + ADX>25 + 短期EMA>长期EMA + 价格>短期EMA
            if (
                macd_result.signals[i] == 1
                and adx_result.values["adx"][i] > 25
                and ema_20[i] > ema_50[i]
                and data["close"].iloc[i] > ema_20[i]
            ):
                signals[i] = 1

            # 空头信号：MACD死叉 + ADX>25 + 短期EMA<长期EMA + 价格<短期EMA
            elif (
                macd_result.signals[i] == -1
                and adx_result.values["adx"][i] > 25
                and ema_20[i] < ema_50[i]
                and data["close"].iloc[i] < ema_20[i]
            ):
                signals[i] = -1

        return IndicatorResult(
            values={
                "macd": macd_result.values,
                "adx": adx_result.values,
                "ema_20": ema_20,
                "ema_50": ema_50,
            },
            signals=signals,
            metadata={
                "strategy": "trend_following",
                "components": ["MACD", "ADX", "EMA"],
            },
        )

    @staticmethod
    def mean_reversion_combo(data: pd.DataFrame) -> IndicatorResult:
        """
        均值回归组合策略

        Args:
            data: 包含OHLCV的DataFrame

        Returns:
            组合信号结果
        """
        # 计算各种指标
        rsi_result = TechnicalIndicators.rsi(data["close"])
        bb_result = TechnicalIndicators.bollinger_bands(data["close"])
        williams_result = TechnicalIndicators.williams_r(
            data["high"], data["low"], data["close"]
        )

        # 组合信号逻辑
        signals = np.zeros(len(data))

        for i in range(len(data)):
            # 超卖买入信号：RSI<30 + 价格触及布林下轨 + Williams%R<-80
            if (
                rsi_result.values[i] < 30
                and data["close"].iloc[i] <= bb_result.values["lower"][i]
                and williams_result.values[i] < -80
            ):
                signals[i] = 1

            # 超买卖出信号：RSI>70 + 价格触及布林上轨 + Williams%R>-20
            elif (
                rsi_result.values[i] > 70
                and data["close"].iloc[i] >= bb_result.values["upper"][i]
                and williams_result.values[i] > -20
            ):
                signals[i] = -1

        return IndicatorResult(
            values={
                "rsi": rsi_result.values,
                "bollinger_bands": bb_result.values,
                "williams_r": williams_result.values,
            },
            signals=signals,
            metadata={
                "strategy": "mean_reversion",
                "components": ["RSI", "Bollinger Bands", "Williams %R"],
            },
        )


# 便捷函数
def calculate_indicator(indicator_name: str, data: Dict, **kwargs) -> IndicatorResult:
    """
    计算指定技术指标

    Args:
        indicator_name: 指标名称
        data: 数据字典
        **kwargs: 指标参数

    Returns:
        指标计算结果
    """
    indicator_map = {
        "sma": lambda: IndicatorResult(
            values=TechnicalIndicators.sma(data["close"], kwargs.get("period", 20))
        ),
        "ema": lambda: IndicatorResult(
            values=TechnicalIndicators.ema(data["close"], kwargs.get("period", 20))
        ),
        "macd": lambda: TechnicalIndicators.macd(data["close"], **kwargs),
        "rsi": lambda: TechnicalIndicators.rsi(data["close"], kwargs.get("period", 14)),
        "bollinger_bands": lambda: TechnicalIndicators.bollinger_bands(
            data["close"], **kwargs
        ),
        "stochastic": lambda: TechnicalIndicators.stochastic(
            data["high"], data["low"], data["close"], **kwargs
        ),
        "williams_r": lambda: TechnicalIndicators.williams_r(
            data["high"], data["low"], data["close"], **kwargs
        ),
        "atr": lambda: IndicatorResult(
            values=TechnicalIndicators.atr(
                data["high"], data["low"], data["close"], kwargs.get("period", 14)
            )
        ),
        "adx": lambda: TechnicalIndicators.adx(
            data["high"], data["low"], data["close"], **kwargs
        ),
        "cci": lambda: TechnicalIndicators.cci(
            data["high"], data["low"], data["close"], **kwargs
        ),
        "obv": lambda: IndicatorResult(
            values=TechnicalIndicators.obv(data["close"], data["volume"])
        ),
        "vwap": lambda: IndicatorResult(
            values=TechnicalIndicators.vwap(
                data["high"], data["low"], data["close"], data["volume"]
            )
        ),
        "ichimoku": lambda: TechnicalIndicators.ichimoku_cloud(
            data["high"], data["low"], data["close"], **kwargs
        ),
    }

    if indicator_name not in indicator_map:
        raise ValueError(f"不支持的指标: {indicator_name}")

    return indicator_map[indicator_name]()

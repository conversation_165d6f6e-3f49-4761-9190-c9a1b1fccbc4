"""
SimNow仿真环境CTP配置
用于开发和测试阶段的仿真交易
"""

import os
from typing import Dict, Optional
from pydantic import BaseSettings, Field


class SimNowConfig(BaseSettings):
    """SimNow仿真环境配置"""
    
    # SimNow基础配置
    broker_id: str = Field(default="9999", description="SimNow经纪商代码")
    user_id: str = Field(default="", description="SimNow用户账号")
    password: str = Field(default="", description="SimNow密码")
    auth_code: str = Field(default="0000000000000000", description="SimNow认证码")
    app_id: str = Field(default="simnow_client_test", description="SimNow应用标识")
    
    # 服务器地址配置
    trade_front_primary: str = Field(
        default="tcp://***************:10130", 
        description="主交易前置地址"
    )
    trade_front_backup: str = Field(
        default="tcp://***************:10131", 
        description="备用交易前置地址"
    )
    md_front_primary: str = Field(
        default="tcp://***************:10131", 
        description="主行情前置地址"
    )
    md_front_backup: str = Field(
        default="tcp://***************:10132", 
        description="备用行情前置地址"
    )
    
    # 连接配置
    heartbeat_interval: int = Field(default=30, description="心跳间隔(秒)")
    timeout: int = Field(default=10, description="连接超时(秒)")
    retry_times: int = Field(default=3, description="重试次数")
    
    # 交易配置
    order_ref_prefix: str = Field(default="SIM", description="订单引用前缀")
    max_order_ref: int = Field(default=999999, description="最大订单引用")
    
    # 仿真环境特殊配置
    enable_simulation_mode: bool = Field(default=True, description="启用仿真模式")
    simulation_initial_balance: float = Field(default=1000000.0, description="仿真初始资金")
    simulation_commission_rate: float = Field(default=0.0003, description="仿真手续费率")
    
    class Config:
        env_prefix = "SIMNOW_"
        case_sensitive = False


class CTPSimNowService:
    """SimNow仿真交易服务"""
    
    def __init__(self):
        self.config = SimNowConfig()
        self.is_connected = False
        self.is_logged_in = False
        self.connection_status = {
            "trade_front": False,
            "md_front": False,
            "trade_login": False,
            "md_login": False
        }
        
    def get_config_dict(self) -> Dict:
        """获取配置字典"""
        return {
            "broker_id": self.config.broker_id,
            "user_id": self.config.user_id,
            "password": self.config.password,
            "auth_code": self.config.auth_code,
            "app_id": self.config.app_id,
            "trade_front": self.config.trade_front_primary,
            "md_front": self.config.md_front_primary,
            "heartbeat_interval": self.config.heartbeat_interval,
            "timeout": self.config.timeout,
            "retry_times": self.config.retry_times
        }
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        required_fields = ["user_id", "password"]
        for field in required_fields:
            if not getattr(self.config, field):
                print(f"❌ SimNow配置缺少必要字段: {field}")
                return False
        return True
    
    def get_test_instruments(self) -> list:
        """获取测试用合约列表"""
        return [
            # 期货合约
            {"symbol": "rb2501", "name": "螺纹钢2501", "exchange": "SHFE", "product_type": "futures"},
            {"symbol": "cu2501", "name": "沪铜2501", "exchange": "SHFE", "product_type": "futures"},
            {"symbol": "au2502", "name": "沪金2502", "exchange": "SHFE", "product_type": "futures"},
            {"symbol": "ag2502", "name": "沪银2502", "exchange": "SHFE", "product_type": "futures"},
            
            # 股指期货
            {"symbol": "IF2501", "name": "沪深300股指2501", "exchange": "CFFEX", "product_type": "futures"},
            {"symbol": "IC2501", "name": "中证500股指2501", "exchange": "CFFEX", "product_type": "futures"},
            {"symbol": "IH2501", "name": "上证50股指2501", "exchange": "CFFEX", "product_type": "futures"},
            
            # 商品期货
            {"symbol": "c2501", "name": "玉米2501", "exchange": "DCE", "product_type": "futures"},
            {"symbol": "m2501", "name": "豆粕2501", "exchange": "DCE", "product_type": "futures"},
            {"symbol": "y2501", "name": "豆油2501", "exchange": "DCE", "product_type": "futures"},
        ]
    
    def create_test_order_data(self, symbol: str = "rb2501") -> Dict:
        """创建测试订单数据"""
        return {
            "symbol": symbol,
            "direction": "BUY",
            "offset": "OPEN",
            "order_type": "LIMIT",
            "price": 3500.0,
            "volume": 1,
            "time_in_force": "GTC"
        }


# 全局SimNow配置实例
simnow_config = SimNowConfig()
simnow_service = CTPSimNowService()


def get_simnow_config() -> SimNowConfig:
    """获取SimNow配置"""
    return simnow_config


def get_simnow_service() -> CTPSimNowService:
    """获取SimNow服务"""
    return simnow_service


# 环境变量配置示例
SIMNOW_ENV_EXAMPLE = """
# SimNow仿真环境配置
# 请在 .env 文件中添加以下配置

# SimNow账户信息 (需要在 http://www.simnow.com.cn 注册)
SIMNOW_USER_ID=您的SimNow账户
SIMNOW_PASSWORD=您的SimNow密码

# 以下配置通常不需要修改
SIMNOW_BROKER_ID=9999
SIMNOW_AUTH_CODE=0000000000000000
SIMNOW_APP_ID=simnow_client_test
SIMNOW_TRADE_FRONT_PRIMARY=tcp://***************:10130
SIMNOW_MD_FRONT_PRIMARY=tcp://***************:10131

# 仿真交易配置
SIMNOW_ENABLE_SIMULATION_MODE=true
SIMNOW_SIMULATION_INITIAL_BALANCE=1000000.0
SIMNOW_SIMULATION_COMMISSION_RATE=0.0003
"""

if __name__ == "__main__":
    # 配置验证
    service = get_simnow_service()
    if service.validate_config():
        print("✅ SimNow配置验证通过")
        print("📋 配置信息:")
        config_dict = service.get_config_dict()
        for key, value in config_dict.items():
            if key == "password":
                print(f"  {key}: {'*' * len(str(value))}")
            else:
                print(f"  {key}: {value}")
    else:
        print("❌ SimNow配置验证失败")
        print("\n📝 请按照以下示例配置环境变量:")
        print(SIMNOW_ENV_EXAMPLE)

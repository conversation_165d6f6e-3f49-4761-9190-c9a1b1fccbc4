#!/usr/bin/env python3
"""
修复导入问题的脚本
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path: Path):
    """修复单个文件中的导入问题"""
    if not file_path.exists() or not file_path.is_file():
        return
    
    try:
        content = file_path.read_text(encoding='utf-8')
        original_content = content
        
        # 修复导入路径
        replacements = [
            # 修复get_current_user导入
            (r'from app\.core\.security import get_current_user', 'from app.core.auth import get_current_user'),
            
            # 修复其他常见导入问题
            (r'from app\.core\.logger import logger', 'from app.core.logging_config import get_contextual_logger\n\nlogger = get_contextual_logger(__name__)'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # 只有内容发生变化时才写入
        if content != original_content:
            file_path.write_text(content, encoding='utf-8')
            print(f"Fixed imports in: {file_path}")
    
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")

def main():
    """主函数"""
    backend_dir = Path(__file__).parent
    
    # 需要修复的文件模式
    patterns = [
        'app/api/v1/*.py',
        'app/services/*.py',
        'app/core/*.py',
        'app/middleware/*.py'
    ]
    
    for pattern in patterns:
        for file_path in backend_dir.glob(pattern):
            fix_imports_in_file(file_path)
    
    print("Import fixes completed!")

if __name__ == "__main__":
    main()

groups:
  - name: quant_platform_alerts
    interval: 30s
    rules:
      # API健康状态
      - alert: APIDown
        expr: up{job="quant-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: api
        annotations:
          summary: "量化平台API服务宕机"
          description: "{{ $labels.instance }} API服务已经超过1分钟无响应"

      # API高延迟
      - alert: APIHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="quant-api"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应延迟过高"
          description: "95%的API请求延迟超过1秒，当前值: {{ $value }}s"

      # 错误率过高
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="quant-api",status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API错误率过高"
          description: "5xx错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # CPU使用率
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="quant-api"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "{{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="quant-api"} / 1024 / 1024 / 1024 > 4
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "内存使用过高"
          description: "{{ $labels.instance }} 内存使用超过4GB，当前值: {{ $value }}GB"

      # 数据库连接池
      - alert: DatabaseConnectionPoolExhausted
        expr: db_connection_pool_size - db_connection_pool_checked_out > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接池即将耗尽"
          description: "可用连接数少于5个"

      # Celery任务堆积
      - alert: CeleryTaskBacklog
        expr: celery_tasks_pending > 1000
        for: 10m
        labels:
          severity: warning
          service: celery
        annotations:
          summary: "Celery任务堆积"
          description: "待处理任务超过1000个，当前值: {{ $value }}"

      # Redis内存使用
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过80%"

      # 磁盘空间
      - alert: DiskSpaceLow
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "{{ $labels.instance }} 根目录可用空间少于10%"

      # WebSocket连接数
      - alert: TooManyWebSocketConnections
        expr: websocket_connections_active > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "WebSocket连接数过多"
          description: "活跃WebSocket连接超过10000个，当前值: {{ $value }}"
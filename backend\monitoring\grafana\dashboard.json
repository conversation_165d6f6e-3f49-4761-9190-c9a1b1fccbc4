{"dashboard": {"id": null, "uid": "quant-platform-backend", "title": "量化交易平台 - 后端监控", "tags": ["quant-platform", "backend"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{pod=~\"quant-platform-backend-.*\"}[5m])) / sum(container_spec_cpu_quota{pod=~\"quant-platform-backend-.*\"}/container_spec_cpu_period{pod=~\"quant-platform-backend-.*\"}) * 100", "refId": "A"}], "title": "CPU 使用率", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(container_memory_usage_bytes{pod=~\"quant-platform-backend-.*\"}) / sum(container_spec_memory_limit_bytes{pod=~\"quant-platform-backend-.*\"}) * 100", "refId": "A"}], "title": "内存使用率", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(http_requests_total{job=\"quant-platform-backend\"}[5m]))", "refId": "A"}], "title": "请求速率 (RPS)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(http_requests_total{job=\"quant-platform-backend\",status=~\"5..\"}[5m])) / sum(rate(http_requests_total{job=\"quant-platform-backend\"}[5m])) * 100", "refId": "A"}], "title": "错误率", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {"lineWidth": 1, "fillOpacity": 10}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{job=\"quant-platform-backend\"}[5m])) by (le)) * 1000", "legendFormat": "p50", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"quant-platform-backend\"}[5m])) by (le)) * 1000", "legendFormat": "p95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{job=\"quant-platform-backend\"}[5m])) by (le)) * 1000", "legendFormat": "p99", "refId": "C"}], "title": "响应时间分布", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {"lineWidth": 1, "fillOpacity": 10}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "websocket_connections_active{job=\"quant-platform-backend\"}", "legendFormat": "活跃连接", "refId": "A"}], "title": "WebSocket 连接数", "type": "timeseries"}]}}
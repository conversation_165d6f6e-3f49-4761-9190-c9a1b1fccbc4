input {
  # 从API服务收集日志
  tcp {
    port => 5000
    codec => json
    type => "api"
  }
  
  # 从Celery Worker收集日志
  tcp {
    port => 5001
    codec => json
    type => "worker"
  }
  
  # 从Beat收集日志
  tcp {
    port => 5002
    codec => json
    type => "beat"
  }
}

filter {
  # 解析日志时间戳
  date {
    match => [ "timestamp", "ISO8601" ]
    target => "@timestamp"
  }
  
  # 解析API日志
  if [type] == "api" {
    grok {
      match => {
        "message" => "%{IPORHOST:client_ip} - - \[%{HTTPDATE:timestamp}\] \"%{WORD:method} %{URIPATH:path}(?:%{URIPARAM:params})? HTTP/%{NUMBER:http_version}\" %{NUMBER:status} %{NUMBER:bytes} \"%{DATA:referrer}\" \"%{DATA:user_agent}\" %{NUMBER:response_time}"
      }
    }
    
    mutate {
      convert => {
        "status" => "integer"
        "bytes" => "integer"
        "response_time" => "float"
      }
    }
  }
  
  # 解析Celery日志
  if [type] == "worker" or [type] == "beat" {
    grok {
      match => {
        "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{LOGLEVEL:level} %{DATA:logger}: %{GREEDYDATA:msg}"
      }
    }
  }
  
  # 添加环境标签
  mutate {
    add_field => {
      "environment" => "${ENVIRONMENT:development}"
      "service" => "quant-platform"
    }
  }
  
  # 移除原始消息以节省空间
  if "_grokparsefailure" not in [tags] {
    mutate {
      remove_field => [ "message" ]
    }
  }
}

output {
  # 输出到Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "quant-platform-%{type}-%{+YYYY.MM.dd}"
    template_name => "quant-platform"
    template => "/usr/share/logstash/templates/quant-platform.json"
    template_overwrite => true
  }
  
  # 调试输出（生产环境应删除）
  stdout {
    codec => rubydebug
  }
}
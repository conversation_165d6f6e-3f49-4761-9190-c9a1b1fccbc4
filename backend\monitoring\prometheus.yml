global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'production'
    project: 'quant-platform'

# 告警规则文件
rule_files:
  - '/etc/prometheus/alert_rules.yml'

# Alertmanager配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 量化交易平台API
  - job_name: 'quant-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Celery Worker
  - job_name: 'celery-worker'
    static_configs:
      - targets: ['worker:9100']

  # Celery Beat
  - job_name: 'celery-beat'
    static_configs:
      - targets: ['beat:9101']

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # Node Exporter (系统指标)
  - job_name: 'node'
    static_configs:
      - targets: ['api:9100', 'worker:9100', 'beat:9100']

  # Cadvisor (容器指标)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
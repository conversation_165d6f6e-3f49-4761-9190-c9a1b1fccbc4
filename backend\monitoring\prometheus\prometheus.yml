global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'quant-platform-prod'
    
alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093

rule_files:
  - "rules/*.yml"

scrape_configs:
  # 监控Prometheus自身
  - job_name: 'prometheus'
    static_configs:
    - targets: ['localhost:9090']

  # 监控后端服务
  - job_name: 'quant-platform-backend'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - quant-platform
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app]
      action: keep
      regex: quant-platform
    - source_labels: [__meta_kubernetes_pod_label_component]
      action: keep
      regex: backend
    - source_labels: [__meta_kubernetes_pod_name]
      target_label: pod
    - source_labels: [__meta_kubernetes_namespace]
      target_label: namespace
    metrics_path: /metrics
    
  # 监控Redis
  - job_name: 'redis'
    static_configs:
    - targets: ['redis:9121']
      labels:
        service: 'redis'
        
  # 监控PostgreSQL
  - job_name: 'postgresql'
    static_configs:
    - targets: ['postgres-exporter:9187']
      labels:
        service: 'postgresql'
        
  # 监控MongoDB
  - job_name: 'mongodb'
    static_configs:
    - targets: ['mongodb-exporter:9216']
      labels:
        service: 'mongodb'
        
  # 监控RabbitMQ
  - job_name: 'rabbitmq'
    static_configs:
    - targets: ['rabbitmq:15692']
      labels:
        service: 'rabbitmq'
        
  # 监控Node
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
    - target_label: __address__
      replacement: kubernetes.default.svc:443
    - source_labels: [__meta_kubernetes_node_name]
      regex: (.+)
      target_label: __metrics_path__
      replacement: /api/v1/nodes/${1}/proxy/metrics
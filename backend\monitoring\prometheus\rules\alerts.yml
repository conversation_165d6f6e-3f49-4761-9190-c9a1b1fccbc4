groups:
- name: quant_platform_alerts
  interval: 30s
  rules:
  # 服务可用性告警
  - alert: BackendDown
    expr: up{job="quant-platform-backend"} == 0
    for: 1m
    labels:
      severity: critical
      team: backend
    annotations:
      summary: "后端服务宕机"
      description: "{{ $labels.pod }} 在 {{ $labels.namespace }} 命名空间已经宕机超过1分钟"
      
  # 高错误率告警
  - alert: HighErrorRate
    expr: rate(http_requests_total{job="quant-platform-backend",status=~"5.."}[5m]) > 0.05
    for: 5m
    labels:
      severity: warning
      team: backend
    annotations:
      summary: "高错误率"
      description: "后端服务错误率超过5%，当前值: {{ $value | humanizePercentage }}"
      
  # 响应时间告警
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="quant-platform-backend"}[5m])) > 1
    for: 5m
    labels:
      severity: warning
      team: backend
    annotations:
      summary: "响应时间过高"
      description: "95%分位的响应时间超过1秒，当前值: {{ $value }}秒"
      
  # CPU使用率告警
  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total{pod=~"quant-platform-backend-.*"}[5m]) > 0.8
    for: 5m
    labels:
      severity: warning
      team: backend
    annotations:
      summary: "CPU使用率过高"
      description: "Pod {{ $labels.pod }} 的CPU使用率超过80%，当前值: {{ $value | humanizePercentage }}"
      
  # 内存使用率告警
  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes{pod=~"quant-platform-backend-.*"} / container_spec_memory_limit_bytes > 0.9
    for: 5m
    labels:
      severity: warning
      team: backend
    annotations:
      summary: "内存使用率过高"
      description: "Pod {{ $labels.pod }} 的内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"
      
  # 数据库连接池告警
  - alert: DatabaseConnectionPoolExhausted
    expr: db_connection_pool_available{job="quant-platform-backend"} < 5
    for: 1m
    labels:
      severity: critical
      team: backend
    annotations:
      summary: "数据库连接池即将耗尽"
      description: "可用数据库连接数少于5个，当前值: {{ $value }}"
      
  # WebSocket连接数告警
  - alert: HighWebSocketConnections
    expr: websocket_connections_active{job="quant-platform-backend"} > 8000
    for: 5m
    labels:
      severity: warning
      team: backend
    annotations:
      summary: "WebSocket连接数过高"
      description: "活跃WebSocket连接数超过8000，当前值: {{ $value }}"
      
  # 磁盘空间告警
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes{mountpoint="/data"} / node_filesystem_size_bytes{mountpoint="/data"}) < 0.1
    for: 5m
    labels:
      severity: critical
      team: ops
    annotations:
      summary: "磁盘空间不足"
      description: "数据目录可用空间少于10%，当前值: {{ $value | humanizePercentage }}"
# ===== 核心框架 =====
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# ===== 数据库驱动 =====
sqlalchemy==2.0.23
# 异步PostgreSQL驱动 (SQLAlchemy异步引擎必需)
asyncpg==0.29.0
# 同步PostgreSQL驱动 (仅在需要同步操作时保留)
# 使用场景: 1) Alembic数据迁移 2) Celery同步任务 3) 批处理脚本
psycopg2-binary==2.9.9
# SQLite异步支持
aiosqlite==0.19.0
# 数据库迁移工具
alembic==1.12.1

# ===== 数据处理和量化分析 =====
pandas>=2.2.0
numpy>=1.26.0
scipy>=1.11.4
scikit-learn>=1.3.2

# ===== 认证授权和安全 =====
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
# 加密库 (统一版本，避免冲突)
cryptography==41.0.7
bcrypt==4.0.1

# ===== HTTP客户端 =====
httpx==0.25.2
aiohttp==3.9.1

# ===== 配置管理 =====
pydantic==2.5.3
pydantic-settings==2.1.0

# ===== 缓存和消息队列 =====
# Redis客户端 (支持异步接口: from redis.asyncio import Redis)
redis==5.0.1
# 消息队列
celery==5.3.4

# ===== 日志和监控 =====
structlog==23.2.0
psutil==5.9.8

# ===== 时间处理 =====
python-dateutil==2.8.2
pytz==2023.3

# ===== 环境变量 =====
python-dotenv==1.0.0

# ===== 工具库 =====
tenacity==8.2.3  # 重试机制
click==8.1.7     # 命令行工具

# ===== 技术指标计算 (Python 版本兼容性要求) =====
TA-Lib==0.4.28; python_version < '3.12'
ta==0.11.0  # 备用技术指标库，纯 Python 实现

# ===== 测试框架 =====
pytest==7.4.3
pytest-asyncio==0.21.1

# ===== Web增强功能 =====
# 验证码生成
captcha==0.5
# API限流
fastapi-limiter==0.1.6
# WebSocket支持
websockets==12.0

# ===== 图像处理 =====
# 图像处理库 (PIL)
Pillow==10.4.0

# ===== 交易接口 =====
# CTP交易接口 (Python 版本兼容性要求)
vnpy==3.9.1; python_version < '3.12'
vnpy-ctp==6.7.2.0; python_version < '3.12'

# ===== 数据源接口 =====
# 财经数据源
tushare==1.4.1
akshare==1.17.26

# ===== 可视化 =====
# 图表生成
pyecharts==1.9.1

# ===== 注意事项 =====
# 已移除的冗余依赖:
# - aioredis==2.0.1 (已合并到redis-py v4.0+)
# - cryptography>=41.0.0 (与==41.0.7冲突)
# - python-socketio (未使用)
#
# Python 版本兼容性要求:
# - vnpy, TA-Lib: 需要 Python < 3.12
# - 推荐使用 Python 3.10.13 以确保最佳兼容性

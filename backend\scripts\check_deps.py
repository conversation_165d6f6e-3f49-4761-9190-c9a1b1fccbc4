#!/usr/bin/env python3
"""
依赖包检查脚本
检测 requirements.txt 中的重复依赖、版本冲突和冗余包
"""

import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict


def parse_requirements(file_path: str) -> Dict[str, List[str]]:
    """解析 requirements.txt 文件"""
    deps = defaultdict(list)
    
    if not os.path.exists(file_path):
        print(f"❌ requirements.txt 文件不存在: {file_path}")
        return {}
        
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                continue
                
            # 解析包名和版本
            if '==' in line:
                pkg_name = line.split('==')[0].strip()
                version = line.split('==')[1].strip()
                deps[pkg_name].append((version, line_num))
            elif '>=' in line:
                pkg_name = line.split('>=')[0].strip()
                version = '>=' + line.split('>=')[1].strip()
                deps[pkg_name].append((version, line_num))
            elif '>' in line:
                pkg_name = line.split('>')[0].strip()
                version = '>' + line.split('>')[1].strip()
                deps[pkg_name].append((version, line_num))
            else:
                # 处理复杂的包名如 uvicorn[standard]
                match = re.match(r'([a-zA-Z0-9\-_\[\]]+)', line)
                if match:
                    pkg_name = match.group(1)
                    deps[pkg_name].append(('latest', line_num))
    
    return deps


def check_duplicates(deps: Dict[str, List[str]]) -> List[str]:
    """检查重复的依赖包"""
    issues = []
    
    for pkg_name, versions in deps.items():
        if len(versions) > 1:
            version_info = ', '.join([f"{v[0]} (line {v[1]})" for v in versions])
            issues.append(f"❌ 重复依赖: {pkg_name} -> {version_info}")
    
    return issues


def check_known_conflicts() -> List[str]:
    """检查已知的包冲突"""
    conflicts = [
        {
            'packages': ['aioredis', 'redis'],
            'issue': 'aioredis 已合并到 redis-py v4.0+，建议移除 aioredis',
            'solution': '使用 from redis.asyncio import Redis'
        },
        {
            'packages': ['cryptography'],
            'issue': 'cryptography 版本冲突风险',
            'solution': '统一使用固定版本避免冲突'
        }
    ]
    
    issues = []
    for conflict in conflicts:
        if conflict['packages'][0] == 'aioredis':
            issues.append(f"⚠️  依赖冲突: {conflict['issue']}")
            issues.append(f"   解决方案: {conflict['solution']}")
    
    return issues


def check_missing_versions(deps: Dict[str, List[str]]) -> List[str]:
    """检查缺少版本号的包"""
    issues = []
    
    for pkg_name, versions in deps.items():
        for version, line_num in versions:
            if version == 'latest':
                issues.append(f"⚠️  建议固定版本: {pkg_name} (line {line_num})")
    
    return issues


def analyze_size_optimization() -> List[str]:
    """分析可能的体积优化建议"""
    optimizations = [
        "📦 体积优化建议:",
        "   • 移除 aioredis 可减少约 15MB",
        "   • 统一 cryptography 版本可避免重复安装",
        "   • 考虑使用 alpine 基础镜像进一步减小体积",
        "   • 使用多阶段构建分离构建和运行时依赖"
    ]
    
    return optimizations


def generate_migration_guide() -> List[str]:
    """生成代码迁移指南"""
    guide = [
        "🔄 代码迁移指南:",
        "",
        "1. aioredis → redis.asyncio",
        "   旧: import aioredis; redis = await aioredis.from_url(...)",
        "   新: from redis.asyncio import Redis; redis = Redis.from_url(...)",
        "",
        "2. 异步Redis操作保持不变:",
        "   await redis.set('key', 'value')",
        "   value = await redis.get('key')",
        "",
        "3. 连接池配置:",
        "   from redis.asyncio import ConnectionPool",
        "   pool = ConnectionPool.from_url('redis://localhost')",
        "   redis = Redis(connection_pool=pool)"
    ]
    
    return guide


def main():
    """主函数"""
    print("🔍 开始检查依赖包配置...\n")
    
    # 查找 requirements.txt 文件
    current_dir = Path(__file__).parent
    req_file = current_dir.parent / "requirements.txt"
    
    if not req_file.exists():
        # 尝试在根目录查找
        req_file = current_dir.parent.parent / "requirements.txt"
    
    if not req_file.exists():
        print("❌ 未找到 requirements.txt 文件")
        sys.exit(1)
    
    print(f"📋 检查文件: {req_file}")
    print("=" * 60)
    
    # 解析依赖文件
    deps = parse_requirements(str(req_file))
    
    if not deps:
        print("❌ 无法解析依赖文件")
        sys.exit(1)
    
    print(f"📊 总共发现 {len(deps)} 个依赖包\n")
    
    # 检查各种问题
    all_issues = []
    
    # 检查重复依赖
    duplicate_issues = check_duplicates(deps)
    if duplicate_issues:
        all_issues.extend(duplicate_issues)
        all_issues.append("")
    
    # 检查已知冲突
    conflict_issues = check_known_conflicts()
    if conflict_issues:
        all_issues.extend(conflict_issues)
        all_issues.append("")
    
    # 检查缺少版本号
    version_issues = check_missing_versions(deps)
    if version_issues:
        all_issues.extend(version_issues)
        all_issues.append("")
    
    # 输出结果
    if all_issues:
        print("⚠️  发现以下问题:")
        print("-" * 40)
        for issue in all_issues:
            print(issue)
    else:
        print("✅ 未发现明显的依赖问题!")
    
    # 输出优化建议
    print("\n" + "=" * 60)
    optimizations = analyze_size_optimization()
    for opt in optimizations:
        print(opt)
    
    # 输出迁移指南
    print("\n" + "=" * 60)
    guide = generate_migration_guide()
    for line in guide:
        print(line)
    
    print("\n" + "=" * 60)
    print("🎯 检查完成!")
    
    if all_issues:
        print("\n💡 建议:")
        print("   1. 修复重复依赖和版本冲突")
        print("   2. 更新代码以适配新的依赖配置")
        print("   3. 运行测试确保功能正常")
        print("   4. 验证镜像体积优化效果")


if __name__ == "__main__":
    main()
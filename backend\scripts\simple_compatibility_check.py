#!/usr/bin/env python3
"""
简化版数据库模型兼容性检查工具
"""

import sys
import os
from pathlib import Path

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

def check_model_imports():
    """检查模型导入"""
    print("检查模型导入...")
    
    try:
        # 测试统一模型导入
        from app.db.models.user import User
        print("SUCCESS: 用户模型导入成功")

        from app.db.models.trading import Order, Trade, Position
        print("SUCCESS: 交易模型导入成功")

        from app.db.models.strategy import Strategy
        print("SUCCESS: 策略模型导入成功")

        from app.db.models.market import MarketData, Symbol
        print("SUCCESS: 市场数据模型导入成功")

        from app.db.models.ctp_models import CTPOrder
        print("SUCCESS: CTP模型导入成功")

        from app.db.models.risk import RiskLimit
        print("SUCCESS: 风控模型导入成功")

        import app.models
        print("SUCCESS: 统一模型导入成功")

        return True
        
    except Exception as e:
        print(f"ERROR: 模型导入失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print("检查数据库连接...")
    
    try:
        from app.core.database import Base
        print("SUCCESS: 数据库配置导入成功")
        return True
    except Exception as e:
        print(f"ERROR: 数据库配置导入失败: {e}")
        return False

def check_alembic_config():
    """检查Alembic配置"""
    print("检查Alembic配置...")
    
    # 检查文件存在
    alembic_ini = Path("alembic.ini")
    if not alembic_ini.exists():
        print("ERROR: 缺少 alembic.ini")
        return False
    
    migrations_dir = Path("migrations")
    if not migrations_dir.exists():
        print("ERROR: 缺少 migrations 目录")
        return False
    
    env_py = migrations_dir / "env.py"
    if not env_py.exists():
        print("ERROR: 缺少 migrations/env.py")
        return False
    
    versions_dir = migrations_dir / "versions"
    if not versions_dir.exists():
        print("ERROR: 缺少 migrations/versions 目录")
        return False
    
    # 统计迁移文件
    migration_files = list(versions_dir.glob("*.py"))
    print(f"INFO: 找到 {len(migration_files)} 个迁移文件")
    
    print("SUCCESS: Alembic配置检查通过")
    return True

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    required = ['sqlalchemy', 'alembic', 'pydantic', 'fastapi']
    missing = []
    
    for package in required:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"ERROR: 缺少依赖包: {', '.join(missing)}")
        return False
    else:
        print("SUCCESS: 所有依赖项已安装")
        return True

def main():
    """主函数"""
    print("="*50)
    print("数据库模型兼容性检查器")
    print("="*50)
    
    checks = [
        check_dependencies,
        check_model_imports,
        check_database_connection,
        check_alembic_config
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
            print()
        except Exception as e:
            print(f"ERROR: 检查过程出错: {e}")
            results.append(False)
            print()
    
    # 输出总结
    print("="*50)
    print("检查结果总结")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if all(results):
        print("SUCCESS: 所有检查通过，可以进行数据库升级")
        return 0
    else:
        print("ERROR: 存在问题，请先解决后再进行升级")
        return 1

if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
Redis迁移验证脚本
验证从 aioredis 迁移到 redis.asyncio 后的功能
"""

import asyncio
import sys
import traceback
from typing import Optional

# 尝试导入新的Redis客户端
try:
    from redis.asyncio import Redis, ConnectionPool
    print("✅ Redis异步客户端导入成功")
except ImportError as e:
    print(f"❌ 无法导入 redis.asyncio: {e}")
    sys.exit(1)


async def test_redis_connection():
    """测试Redis连接"""
    redis_client = None
    
    try:
        # 使用默认配置连接Redis
        redis_client = Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 测试基本操作
        await redis_client.set('test_key', 'test_value', ex=60)
        value = await redis_client.get('test_key')
        
        if value == 'test_value':
            print("✅ Redis基本操作测试通过")
        else:
            print(f"❌ Redis值不匹配: 期望 'test_value', 实际 '{value}'")
            
        # 测试删除操作
        await redis_client.delete('test_key')
        deleted_value = await redis_client.get('test_key')
        
        if deleted_value is None:
            print("✅ Redis删除操作测试通过")
        else:
            print(f"❌ Redis删除失败: 仍然存在值 '{deleted_value}'")
            
        return True
        
    except Exception as e:
        print(f"⚠️  Redis连接测试失败: {e}")
        print("   这是正常的，如果Redis服务未运行")
        return False
        
    finally:
        if redis_client:
            await redis_client.aclose()


async def test_connection_pool():
    """测试连接池功能"""
    pool = None
    redis_client = None
    
    try:
        # 创建连接池
        pool = ConnectionPool.from_url(
            "redis://localhost:6379/0",
            decode_responses=True,
            max_connections=10
        )
        
        redis_client = Redis(connection_pool=pool)
        
        # 测试连接池操作
        await redis_client.set('pool_test', 'pool_value', ex=60)
        value = await redis_client.get('pool_test')
        
        if value == 'pool_value':
            print("✅ Redis连接池测试通过")
        else:
            print(f"❌ Redis连接池值不匹配")
            
        # 清理
        await redis_client.delete('pool_test')
        
        return True
        
    except Exception as e:
        print(f"⚠️  Redis连接池测试失败: {e}")
        return False
        
    finally:
        if redis_client:
            await redis_client.aclose()
        if pool:
            await pool.aclose()


async def test_cache_operations():
    """测试缓存操作"""
    redis_client = None
    
    try:
        redis_client = Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 测试Hash操作
        hash_key = 'test_hash'
        hash_data = {
            'field1': 'value1',
            'field2': 'value2',
            'field3': 'value3'
        }
        
        await redis_client.hset(hash_key, mapping=hash_data)
        retrieved_data = await redis_client.hgetall(hash_key)
        
        if retrieved_data == hash_data:
            print("✅ Redis Hash操作测试通过")
        else:
            print(f"❌ Redis Hash值不匹配")
            
        # 测试列表操作
        list_key = 'test_list'
        await redis_client.lpush(list_key, 'item1', 'item2', 'item3')
        list_length = await redis_client.llen(list_key)
        
        if list_length == 3:
            print("✅ Redis List操作测试通过")
        else:
            print(f"❌ Redis List长度不匹配: 期望 3, 实际 {list_length}")
            
        # 清理测试数据
        await redis_client.delete(hash_key, list_key)
        
        return True
        
    except Exception as e:
        print(f"⚠️  Redis缓存操作测试失败: {e}")
        return False
        
    finally:
        if redis_client:
            await redis_client.aclose()


def check_import_migration():
    """检查导入迁移"""
    print("🔍 检查代码迁移状态...")
    
    import os
    import subprocess
    
    backend_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 检查是否还有 aioredis 导入
    try:
        result = subprocess.run(
            ['grep', '-r', 'import aioredis', backend_path],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("❌ 发现未迁移的 aioredis 导入:")
            print(result.stdout)
            return False
        else:
            print("✅ 所有 aioredis 导入已成功迁移")
            
    except FileNotFoundError:
        print("⚠️  grep命令不可用，跳过代码检查")
    
    # 检查是否有新的 redis.asyncio 导入
    try:
        result = subprocess.run(
            ['grep', '-r', 'from redis.asyncio import', backend_path],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 发现新的 redis.asyncio 导入:")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print("⚠️  未发现 redis.asyncio 导入")
            return False
            
    except FileNotFoundError:
        print("⚠️  grep命令不可用，跳过代码检查")
        return True


async def main():
    """主函数"""
    print("🚀 开始Redis迁移验证...\n")
    
    # 检查导入迁移
    migration_ok = check_import_migration()
    print()
    
    # 测试Redis功能
    tests = [
        ("基本连接", test_redis_connection),
        ("连接池", test_connection_pool),
        ("缓存操作", test_cache_operations),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 测试 {test_name}...")
        try:
            result = await test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            traceback.print_exc()
            results.append(False)
        print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 测试结果汇总:")
    print(f"   代码迁移: {'✅ 通过' if migration_ok else '❌ 需要检查'}")
    
    for i, (test_name, _) in enumerate(tests):
        status = '✅ 通过' if results[i] else '⚠️  需要Redis服务'
        print(f"   {test_name}: {status}")
    
    if migration_ok and any(results):
        print("\n🎉 Redis迁移验证成功!")
        print("💡 建议:")
        print("   1. 确保生产环境已安装 redis>=5.0.1")
        print("   2. 更新部署脚本移除 aioredis 依赖")
        print("   3. 运行完整的集成测试")
    else:
        print("\n⚠️  需要进一步检查和修复")


if __name__ == "__main__":
    asyncio.run(main())
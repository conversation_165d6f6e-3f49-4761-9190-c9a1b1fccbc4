const puppeteer = require('puppeteer');

async function checkHMRError() {
    const browser = await puppeteer.launch({
        headless: false,
        devtools: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Listen for console messages
    page.on('console', (msg) => {
        console.log(`[${msg.type().toUpperCase()}] ${msg.text()}`);
    });
    
    // Listen for page errors
    page.on('pageerror', (error) => {
        console.log('PAGE ERROR:', error.message);
        console.log('STACK:', error.stack);
    });
    
    // Listen for response errors
    page.on('response', (response) => {
        if (!response.ok()) {
            console.log(`RESPONSE ERROR: ${response.status()} - ${response.url()}`);
        }
    });
    
    try {
        console.log('Navigating to http://localhost:5177...');
        await page.goto('http://localhost:5177', { 
            waitUntil: 'networkidle0',
            timeout: 30000
        });
        
        console.log('Waiting for potential HMR errors...');
        await page.waitForSelector('body', { timeout: 5000 }).catch(() => {});
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check if we can access import.meta.hot
        const hmrStatus = await page.evaluate(() => {
            try {
                // Check if import.meta exists
                const hasImportMeta = typeof window !== 'undefined' && window.import && window.import.meta;
                
                return {
                    hasImportMeta: hasImportMeta,
                    userAgent: navigator.userAgent,
                    location: window.location.href,
                    hasViteClient: !!window.__vite_plugin_vue_export_helper
                };
            } catch (e) {
                return {
                    error: e.message,
                    hasImportMeta: false
                };
            }
        });
        
        console.log('HMR Status:', hmrStatus);
        
        // Check for specific error in console logs
        const consoleLogs = await page.evaluate(() => {
            return window.__consoleLogs || [];
        });
        
        console.log('Console logs captured:', consoleLogs);
        
        // Take a screenshot
        await page.screenshot({ 
            path: 'hmr_check_screenshot.png', 
            fullPage: true 
        });
        
        console.log('Screenshot taken: hmr_check_screenshot.png');
        
    } catch (error) {
        console.error('Error during HMR check:', error);
    } finally {
        await browser.close();
    }
}

checkHMRError();
# 量化投资平台 - 开发环境 Docker Compose 配置
# 简化版本，专注于核心服务快速启动

version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: quant-backend-dev
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
    networks:
      - quant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: quant-frontend-dev
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_APP_TITLE=量化投资平台
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - quant-network
    depends_on:
      - backend
    restart: unless-stopped

  # Redis缓存 (轻量级)
  redis:
    image: redis:7-alpine
    container_name: quant-redis-dev
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  quant-network:
    driver: bridge
    name: quant-network

volumes:
  redis_data:
    name: quant-redis-data

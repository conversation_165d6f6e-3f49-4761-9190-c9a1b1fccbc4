version: '3.8'

services:
  # 后端API服务
  trading-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: quant-trading-backend
    restart: unless-stopped
    environment:
      # 生产环境配置
      - ENVIRONMENT=production
      - DEBUG=false
      
      # CTP配置 (从环境变量读取)
      - CTP_BROKER_ID=${CTP_BROKER_ID}
      - CTP_USER_ID=${CTP_USER_ID}
      - CTP_PASSWORD=${CTP_PASSWORD}
      - CTP_AUTH_CODE=${CTP_AUTH_CODE}
      - CTP_APP_ID=${CTP_APP_ID}
      - CTP_TRADE_FRONT=${CTP_TRADE_FRONT}
      - CTP_MD_FRONT=${CTP_MD_FRONT}
      
      # 数据库配置
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/${DB_NAME}
      - REDIS_URL=redis://redis:6379/0
      
      # 安全配置
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      
      # 风险控制配置
      - RISK_MAX_ORDER_AMOUNT=${RISK_MAX_ORDER_AMOUNT:-100000}
      - RISK_DAILY_LOSS_LIMIT=${RISK_DAILY_LOSS_LIMIT:-50000}
      - RISK_POSITION_LIMIT=${RISK_POSITION_LIMIT:-0.8}
      - RISK_MAX_ORDERS_PER_MINUTE=${RISK_MAX_ORDERS_PER_MINUTE:-100}
      
      # 监控配置
      - ENABLE_MONITORING=true
      - ALERT_EMAIL=${ALERT_EMAIL}
      - ALERT_PHONE=${ALERT_PHONE}
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=/app/logs/trading.log
      
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./ssl:/app/ssl
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  trading-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: quant-trading-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=https://${DOMAIN}/api
      - VITE_WS_URL=wss://${DOMAIN}/ws
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - trading-backend
    networks:
      - trading-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant-trading-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: quant-trading-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务 (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: quant-trading-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - trading-network

  # 监控面板 (Grafana)
  grafana:
    image: grafana/grafana:latest
    container_name: quant-trading-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - trading-network

  # 日志收集 (ELK Stack - Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: quant-trading-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - trading-network

  # 日志处理 (Logstash)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: quant-trading-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    ports:
      - "5044:5044"
    environment:
      - LS_JAVA_OPTS=-Xmx256m -Xms256m
    depends_on:
      - elasticsearch
    networks:
      - trading-network

  # 日志可视化 (Kibana)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: quant-trading-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - trading-network

  # 备份服务
  backup:
    image: postgres:15-alpine
    container_name: quant-trading-backup
    restart: "no"
    environment:
      - PGPASSWORD=${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - postgres
    networks:
      - trading-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  trading-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

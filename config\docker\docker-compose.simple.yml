version: '3.8'

services:
  # 后端服务 - 使用简化配置
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - SECRET_KEY=dev-secret-key-2024
    volumes:
      - ./backend:/app
      - ./data:/app/data
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - quant-network

  # 前端服务 - 使用简化配置
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000/ws
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - quant-network
    command: npm run dev -- --host 0.0.0.0 --port 3000

networks:
  quant-network:
    driver: bridge

apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-frontend
  namespace: quant-platform
spec:
  replicas: 2
  selector:
    matchLabels:
      app: quant-frontend
  template:
    metadata:
      labels:
        app: quant-frontend
    spec:
      containers:
      - name: frontend
        image: quant-platform/frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        - name: VITE_API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: API_BASE_URL
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: quant-frontend-service
  namespace: quant-platform
spec:
  selector:
    app: quant-frontend
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
apiVersion: v1
kind: Secret
metadata:
  name: quant-secret
  namespace: quant-platform
type: Opaque
stringData:
  # 数据库凭证
  DATABASE_USER: "quant_user"
  DATABASE_PASSWORD: "your-secure-db-password"
  
  # Redis密码（如果有）
  REDIS_PASSWORD: ""
  
  # 应用密钥
  SECRET_KEY: "your-very-secure-secret-key-change-in-production"
  JWT_SECRET_KEY: "your-jwt-secret-key"
  
  # API密钥（如需要）
  TUSHARE_TOKEN: "your-tushare-token"
  
  # 邮件服务凭证（如需要）
  SMTP_USERNAME: "your-smtp-username"
  SMTP_PASSWORD: "your-smtp-password"
# 量化交易平台部署指南

本文档提供量化交易平台的完整部署方案，支持Docker Compose和Kubernetes两种部署方式。

## 目录

- [快速开始](#快速开始)
- [环境准备](#环境准备)
- [Docker Compose部署](#docker-compose部署)
- [Kubernetes部署](#kubernetes部署)
- [监控配置](#监控配置)
- [备份和恢复](#备份和恢复)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 快速开始

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd quant011

# Docker Compose部署
./deployment/scripts/deploy.sh --type docker --env production

# Kubernetes部署
./deployment/scripts/deploy.sh --type kubernetes --env production
```

## 环境准备

### 系统要求

**最低配置:**
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD
- 网络: 100Mbps

**推荐配置:**
- CPU: 8核心以上
- 内存: 16GB以上
- 存储: 500GB SSD
- 网络: 1Gbps

### 软件依赖

#### Docker Compose部署
```bash
# Docker和Docker Compose
curl -fsSL https://get.docker.com | sh
sudo pip install docker-compose

# 验证安装
docker --version
docker-compose --version
```

#### Kubernetes部署
```bash
# kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# 验证安装
kubectl version --client
helm version
```

### 环境配置

创建环境配置文件：

```bash
# 开发环境
cp .env.example .env.development

# 生产环境
cp .env.example .env.production
```

编辑配置文件：

```bash
# .env.production
# 数据库配置
POSTGRES_USER=quant_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=quant_db
DATABASE_URL=**********************************************************/quant_db

# Redis配置
REDIS_PASSWORD=your_redis_password
REDIS_URL=redis://:your_redis_password@redis:6379/0

# ClickHouse配置
CLICKHOUSE_URL=clickhouse://quant_user:your_clickhouse_password@clickhouse:8123/quant

# 应用密钥
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# 监控配置
GRAFANA_PASSWORD=your_grafana_password
RABBITMQ_PASSWORD=your_rabbitmq_password

# 备份配置
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份
```

## Docker Compose部署

### 基础部署

```bash
cd deployment

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

### 服务组件

| 服务 | 端口 | 描述 |
|------|------|------|
| backend | 8000 | 后端API服务 |
| frontend | 3000 | 前端Web界面 |
| postgres | 5432 | 主数据库 |
| redis | 6379 | 缓存和会话存储 |
| clickhouse | 8123,9000 | 时序数据库 |
| nginx | 80,443 | 反向代理 |
| prometheus | 9090 | 指标监控 |
| grafana | 3001 | 可视化监控 |
| elasticsearch | 9200 | 日志存储 |
| kibana | 5601 | 日志分析 |
| rabbitmq | 5672,15672 | 消息队列 |

### 扩缩容

```bash
# 扩展后端服务
docker-compose up -d --scale backend=3

# 扩展Celery工作进程
docker-compose up -d --scale celery_worker=5
```

### 更新部署

```bash
# 重新构建并部署
docker-compose build --no-cache
docker-compose up -d

# 滚动更新单个服务
docker-compose up -d --no-deps backend
```

## Kubernetes部署

### 集群准备

```bash
# 创建命名空间
kubectl apply -f kubernetes/namespace.yaml

# 验证命名空间
kubectl get namespaces
```

### 配置密钥

```bash
# 创建TLS证书
kubectl create secret tls quant-tls-secret \
  --cert=ssl/server.crt \
  --key=ssl/server.key \
  -n quant-trading

# 创建应用密钥
kubectl create secret generic quant-secrets \
  --from-literal=database-url="$DATABASE_URL" \
  --from-literal=redis-url="$REDIS_URL" \
  --from-literal=secret-key="$SECRET_KEY" \
  -n quant-trading
```

### 部署数据层

```bash
# 部署PostgreSQL
kubectl apply -f kubernetes/postgres-deployment.yaml

# 部署Redis
kubectl apply -f kubernetes/redis-deployment.yaml

# 部署ClickHouse
kubectl apply -f kubernetes/clickhouse-deployment.yaml

# 等待数据库就绪
kubectl wait --for=condition=available --timeout=300s deployment/postgres -n quant-trading
```

### 部署应用层

```bash
# 部署后端
kubectl apply -f kubernetes/backend-deployment.yaml

# 部署前端
kubectl apply -f kubernetes/frontend-deployment.yaml

# 部署Ingress
kubectl apply -f kubernetes/ingress.yaml

# 验证部署
kubectl get pods -n quant-trading
```

### 监控部署

```bash
# 部署Prometheus
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace quant-trading \
  --values kubernetes/monitoring/prometheus-values.yaml

# 部署Grafana仪表板
kubectl apply -f kubernetes/monitoring/grafana-dashboards.yaml
```

### 水平扩容

```bash
# 启用HPA
kubectl apply -f kubernetes/hpa.yaml

# 手动扩容
kubectl scale deployment quant-backend --replicas=5 -n quant-trading

# 查看扩容状态
kubectl get hpa -n quant-trading
```

## 监控配置

### Prometheus指标

系统自动收集以下指标：

- **应用指标**: 请求量、响应时间、错误率
- **业务指标**: 回测任务数、策略绩效、交易统计
- **系统指标**: CPU、内存、磁盘、网络
- **数据库指标**: 连接数、查询性能、锁等待

### Grafana仪表板

预配置仪表板：

1. **系统概览**: 整体系统健康状态
2. **应用性能**: API响应时间和吞吐量
3. **数据库监控**: PostgreSQL、Redis、ClickHouse
4. **业务监控**: 回测任务、策略绩效
5. **错误跟踪**: 异常和错误分析

访问地址: http://localhost:3001 (admin/admin)

### 告警配置

```yaml
# kubernetes/monitoring/alerts.yaml
groups:
- name: quant-trading
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: "High error rate detected"
      
  - alert: DatabaseConnectionHigh
    expr: pg_stat_activity_count > 80
    for: 2m
    annotations:
      summary: "Database connection count is high"
```

### 日志聚合

ELK栈配置：

```bash
# 查看日志
kubectl logs -f deployment/quant-backend -n quant-trading

# Kibana查询语法
POST /logs/_search
{
  "query": {
    "bool": {
      "must": [
        { "match": { "level": "ERROR" } },
        { "range": { "@timestamp": { "gte": "now-1h" } } }
      ]
    }
  }
}
```

## 备份和恢复

### 自动备份

系统支持定时备份：

```bash
# 手动触发备份
./scripts/backup.sh

# 查看备份文件
ls -la backups/
```

备份内容：
- PostgreSQL数据库
- Redis数据
- ClickHouse数据
- 配置文件
- 日志文件

### 恢复流程

```bash
# 恢复PostgreSQL
docker-compose exec postgres psql -U quant_user -d quant_db -f /backups/postgres_backup.sql

# 恢复Redis
docker-compose exec redis redis-cli --rdb /backups/redis_backup.rdb

# 恢复ClickHouse
docker-compose exec clickhouse clickhouse-client --query="RESTORE DATABASE quant FROM '/backups/clickhouse_backup/'"
```

### 灾难恢复

1. **数据中心故障**: 使用异地备份恢复
2. **存储故障**: 从最近备份点恢复
3. **应用故障**: 回滚到上一个稳定版本

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看服务状态
docker-compose ps
kubectl get pods -n quant-trading

# 查看详细日志
docker-compose logs backend
kubectl logs deployment/quant-backend -n quant-trading

# 检查资源使用
docker stats
kubectl top pods -n quant-trading
```

#### 2. 数据库连接问题

```bash
# 测试数据库连接
docker-compose exec backend python -c "
import psycopg2
conn = psycopg2.connect('$DATABASE_URL')
print('Database connection successful')
"

# 检查数据库日志
docker-compose logs postgres
```

#### 3. 内存不足

```bash
# 检查内存使用
free -h
docker stats --no-stream

# 调整容器内存限制
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 4G
```

#### 4. 磁盘空间不足

```bash
# 清理Docker
docker system prune -a --volumes

# 清理日志
find /var/log -name "*.log" -mtime +7 -delete
```

### 性能问题

#### 1. API响应慢

```bash
# 检查应用性能
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# 分析慢查询
docker-compose exec postgres psql -U quant_user -d quant_db -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
"
```

#### 2. 数据库性能

```sql
-- 检查数据库连接
SELECT count(*) FROM pg_stat_activity;

-- 检查长时间运行的查询
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- 检查锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;
```

## 性能优化

### 应用层优化

1. **连接池优化**
```python
# backend/config/database.py
DATABASES = {
    'default': {
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        }
    }
}
```

2. **缓存策略**
```python
# 使用Redis缓存热点数据
@cache_result(expire=300)
def get_market_data(symbol):
    return fetch_from_database(symbol)
```

3. **异步处理**
```python
# 使用Celery处理耗时任务
@celery.task
def run_backtest_async(strategy_config):
    return run_backtest(strategy_config)
```

### 数据库优化

1. **索引优化**
```sql
-- 创建复合索引
CREATE INDEX idx_trades_symbol_date ON trades(symbol, trade_date);

-- 分析查询计划
EXPLAIN ANALYZE SELECT * FROM trades WHERE symbol = 'AAPL' AND trade_date > '2023-01-01';
```

2. **分区表**
```sql
-- 按日期分区
CREATE TABLE trades_2023 PARTITION OF trades
FOR VALUES FROM ('2023-01-01') TO ('2024-01-01');
```

3. **连接池配置**
```yaml
# docker-compose.yml
postgres:
  command: postgres -c max_connections=200 -c shared_buffers=256MB
```

### 系统层优化

1. **资源限制**
```yaml
# kubernetes/backend-deployment.yaml
resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi
```

2. **HPA配置**
```yaml
# kubernetes/hpa.yaml
spec:
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

3. **存储优化**
```yaml
# 使用SSD存储类
storageClassName: fast-ssd
```

## 安全配置

### SSL/TLS配置

```nginx
# nginx/conf.d/default.conf
server {
    listen 443 ssl http2;
    ssl_certificate /etc/nginx/ssl/server.crt;
    ssl_certificate_key /etc/nginx/ssl/server.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
}
```

### 网络安全

```yaml
# kubernetes/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: quant-network-policy
spec:
  podSelector:
    matchLabels:
      app: quant-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: quant-frontend
```

### 访问控制

```yaml
# kubernetes/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: quant-role
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list", "watch"]
```

## 维护计划

### 日常维护

- 每日: 检查系统状态、查看监控告警
- 每周: 分析性能报告、清理日志文件
- 每月: 更新安全补丁、优化数据库

### 升级计划

1. **测试环境验证**: 在测试环境完整测试
2. **蓝绿部署**: 使用蓝绿部署策略
3. **灰度发布**: 逐步切换流量
4. **回滚准备**: 准备快速回滚方案

### 容量规划

- 监控资源使用趋势
- 预测未来增长需求
- 提前扩容计划
- 成本优化分析

## 支持联系

如有问题，请联系：
- 技术支持: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx
- 文档反馈: <EMAIL>
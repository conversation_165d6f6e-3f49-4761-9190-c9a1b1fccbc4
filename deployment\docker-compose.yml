version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: quant_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=****************************************************/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CLICKHOUSE_URL=clickhouse://clickhouse:8123/quant
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - clickhouse
    networks:
      - quant_network
    volumes:
      - ../logs:/app/logs
      - ../data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: quant_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - quant_network
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000

  # PostgreSQL数据库（主数据库）
  postgres:
    image: postgres:15-alpine
    container_name: quant_postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=quant_password
      - POSTGRES_DB=quant_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
    networks:
      - quant_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quant_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: quant_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ClickHouse时序数据库
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: quant_clickhouse
    restart: unless-stopped
    environment:
      - CLICKHOUSE_USER=quant_user
      - CLICKHOUSE_PASSWORD=quant_password
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ../database/clickhouse/init:/docker-entrypoint-initdb.d
    networks:
      - quant_network
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: quant_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ../logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - quant_network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: quant_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - quant_network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: quant_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - quant_network

  # 日志聚合
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: quant_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - quant_network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: quant_kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - quant_network

  # 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: quant_rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=quant_user
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - quant_network

  # 任务调度器
  celery_worker:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: quant_celery_worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=pyamqp://quant_user:${RABBITMQ_PASSWORD}@rabbitmq:5672//
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - quant_network
    volumes:
      - ../logs:/app/logs
      - ../data:/app/data

  celery_beat:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: quant_celery_beat
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=pyamqp://quant_user:${RABBITMQ_PASSWORD}@rabbitmq:5672//
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - quant_network
    volumes:
      - ../logs:/app/logs

  # 备份服务
  backup:
    image: alpine:latest
    container_name: quant_backup
    restart: unless-stopped
    environment:
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=quant_password
      - POSTGRES_DB=quant_db
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}  # 每天凌晨2点
    volumes:
      - ../backups:/backups
      - postgres_data:/var/lib/postgresql/data:ro
      - ./scripts/backup.sh:/backup.sh
    command: |
      sh -c "
        apk add --no-cache postgresql-client dcron
        echo '$${BACKUP_SCHEDULE} /backup.sh' | crontab -
        crond -f
      "
    depends_on:
      - postgres
    networks:
      - quant_network

networks:
  quant_network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  rabbitmq_data:
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 后端应用监控
  - job_name: 'quant-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # ClickHouse监控
  - job_name: 'clickhouse-exporter'
    static_configs:
      - targets: ['clickhouse-exporter:9116']

  # RabbitMQ监控
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    metrics_path: '/_prometheus/metrics'
    static_configs:
      - targets: ['elasticsearch:9200']

  # 自定义业务指标
  - job_name: 'quant-business-metrics'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics/business'
    scrape_interval: 30s

  # 黑盒监控 (URL健康检查)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://backend:8000/health
        - http://frontend:80
        - http://grafana:3000
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
#!/bin/bash

# 量化交易平台部署脚本
# 支持Docker Compose和Kubernetes部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEPLOYMENT_TYPE="docker"
ENVIRONMENT="production"
FORCE_REBUILD=false
SKIP_TESTS=false
BACKUP_BEFORE_DEPLOY=true
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 显示帮助信息
show_help() {
    cat << EOF
量化交易平台部署脚本

用法: $0 [选项]

选项:
    -t, --type TYPE          部署类型 (docker|kubernetes) [默认: docker]
    -e, --env ENV            环境 (development|staging|production) [默认: production]
    -f, --force              强制重新构建镜像
    -s, --skip-tests         跳过测试
    -n, --no-backup          部署前不进行备份
    -h, --help               显示此帮助信息

示例:
    $0 --type docker --env production
    $0 -t kubernetes -e staging -f
    $0 --skip-tests --no-backup

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOYMENT_TYPE="$2"
                shift 2
                ;;
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -n|--no-backup)
                BACKUP_BEFORE_DEPLOY=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证环境
validate_environment() {
    log_info "验证部署环境..."
    
    # 检查必要的工具
    local required_tools=("git" "docker")
    
    if [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        required_tools+=("kubectl" "helm")
    else
        required_tools+=("docker-compose")
    fi
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    # 检查环境文件
    local env_file="${PROJECT_ROOT}/.env.${ENVIRONMENT}"
    if [[ ! -f "$env_file" ]]; then
        log_error "环境文件不存在: $env_file"
        exit 1
    fi
    
    # 加载环境变量
    set -a
    source "$env_file"
    set +a
    
    log_success "环境验证完成"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        log_warning "跳过测试"
        return 0
    fi
    
    log_info "运行测试..."
    
    # 后端测试
    cd "${PROJECT_ROOT}/backend"
    if [[ -f "requirements-test.txt" ]]; then
        pip install -r requirements-test.txt
    fi
    
    python -m pytest tests/ -v --cov=app --cov-report=html
    
    # 前端测试
    cd "${PROJECT_ROOT}/frontend"
    if [[ -f "package.json" ]]; then
        npm install
        npm test -- --coverage --watchAll=false
    fi
    
    cd "$PROJECT_ROOT"
    log_success "测试完成"
}

# 备份数据
backup_data() {
    if [[ "$BACKUP_BEFORE_DEPLOY" == false ]]; then
        log_warning "跳过备份"
        return 0
    fi
    
    log_info "创建数据备份..."
    
    local backup_dir="${PROJECT_ROOT}/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        docker-compose exec -T postgres pg_dump -U "$POSTGRES_USER" "$POSTGRES_DB" > "$backup_dir/postgres_backup.sql"
        docker-compose exec -T redis redis-cli --rdb "$backup_dir/redis_backup.rdb"
    else
        kubectl exec -n quant-trading deployment/postgres -- pg_dump -U "$POSTGRES_USER" "$POSTGRES_DB" > "$backup_dir/postgres_backup.sql"
    fi
    
    # 备份配置文件
    cp -r "${PROJECT_ROOT}/deployment" "$backup_dir/"
    
    log_success "数据备份完成: $backup_dir"
}

# 构建镜像
build_images() {
    log_info "构建应用镜像..."
    
    local build_args=""
    if [[ "$FORCE_REBUILD" == true ]]; then
        build_args="--no-cache"
    fi
    
    # 构建后端镜像
    cd "${PROJECT_ROOT}/backend"
    docker build $build_args -t "quant-backend:${ENVIRONMENT}" -t "quant-backend:latest" .
    
    # 构建前端镜像
    cd "${PROJECT_ROOT}/frontend"
    docker build $build_args -t "quant-frontend:${ENVIRONMENT}" -t "quant-frontend:latest" .
    
    cd "$PROJECT_ROOT"
    log_success "镜像构建完成"
}

# Docker Compose部署
deploy_docker() {
    log_info "使用Docker Compose部署..."
    
    cd "${PROJECT_ROOT}/deployment"
    
    # 停止现有服务
    docker-compose down
    
    # 启动服务
    docker-compose --env-file "../.env.${ENVIRONMENT}" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 健康检查
    local services=("backend" "frontend" "postgres" "redis")
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            log_success "$service 服务启动成功"
        else
            log_error "$service 服务启动失败"
            docker-compose logs "$service"
            exit 1
        fi
    done
    
    log_success "Docker Compose部署完成"
}

# Kubernetes部署
deploy_kubernetes() {
    log_info "使用Kubernetes部署..."
    
    cd "${PROJECT_ROOT}/deployment/kubernetes"
    
    # 创建命名空间
    kubectl apply -f namespace.yaml
    
    # 创建密钥
    create_kubernetes_secrets
    
    # 创建配置映射
    create_kubernetes_configmaps
    
    # 部署数据库
    kubectl apply -f postgres-deployment.yaml
    kubectl apply -f redis-deployment.yaml
    kubectl apply -f clickhouse-deployment.yaml
    
    # 等待数据库启动
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n quant-trading
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n quant-trading
    
    # 部署应用
    kubectl apply -f backend-deployment.yaml
    kubectl apply -f frontend-deployment.yaml
    
    # 部署监控
    kubectl apply -f monitoring/
    
    # 部署Ingress
    kubectl apply -f ingress.yaml
    
    # 等待应用启动
    kubectl wait --for=condition=available --timeout=300s deployment/quant-backend -n quant-trading
    kubectl wait --for=condition=available --timeout=300s deployment/quant-frontend -n quant-trading
    
    log_success "Kubernetes部署完成"
}

# 创建Kubernetes密钥
create_kubernetes_secrets() {
    log_info "创建Kubernetes密钥..."
    
    kubectl create secret generic quant-secrets \
        --from-literal=database-url="$DATABASE_URL" \
        --from-literal=redis-url="$REDIS_URL" \
        --from-literal=clickhouse-url="$CLICKHOUSE_URL" \
        --from-literal=secret-key="$SECRET_KEY" \
        --from-literal=jwt-secret-key="$JWT_SECRET_KEY" \
        --namespace=quant-trading \
        --dry-run=client -o yaml | kubectl apply -f -
}

# 创建Kubernetes配置映射
create_kubernetes_configmaps() {
    log_info "创建Kubernetes配置映射..."
    
    kubectl create configmap quant-config \
        --from-file="${PROJECT_ROOT}/backend/config/" \
        --namespace=quant-trading \
        --dry-run=client -o yaml | kubectl apply -f -
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        # 检查服务状态
        local backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
        if [[ "$backend_status" == "200" ]]; then
            log_success "后端服务健康检查通过"
        else
            log_error "后端服务健康检查失败"
            exit 1
        fi
        
        local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
        if [[ "$frontend_status" == "200" ]]; then
            log_success "前端服务健康检查通过"
        else
            log_error "前端服务健康检查失败"
            exit 1
        fi
    else
        # Kubernetes健康检查
        kubectl get pods -n quant-trading
        
        local backend_ready=$(kubectl get deployment quant-backend -n quant-trading -o jsonpath='{.status.readyReplicas}')
        if [[ "$backend_ready" -gt 0 ]]; then
            log_success "后端服务就绪"
        else
            log_error "后端服务未就绪"
            exit 1
        fi
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    
    echo ""
    echo "部署信息:"
    echo "  类型: $DEPLOYMENT_TYPE"
    echo "  环境: $ENVIRONMENT"
    echo "  时间: $(date)"
    echo ""
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        echo "服务地址:"
        echo "  前端: http://localhost:3000"
        echo "  后端API: http://localhost:8000"
        echo "  Grafana: http://localhost:3001"
        echo "  Kibana: http://localhost:5601"
        echo ""
        echo "管理命令:"
        echo "  查看日志: docker-compose logs -f [service]"
        echo "  重启服务: docker-compose restart [service]"
        echo "  停止服务: docker-compose down"
    else
        echo "Kubernetes服务:"
        kubectl get services -n quant-trading
        echo ""
        echo "管理命令:"
        echo "  查看Pod: kubectl get pods -n quant-trading"
        echo "  查看日志: kubectl logs -f deployment/quant-backend -n quant-trading"
        echo "  扩缩容: kubectl scale deployment quant-backend --replicas=5 -n quant-trading"
    fi
}

# 清理函数
cleanup() {
    cd "$PROJECT_ROOT"
}

# 主函数
main() {
    log_info "开始部署量化交易平台..."
    
    # 设置清理函数
    trap cleanup EXIT
    
    # 解析参数
    parse_args "$@"
    
    # 验证环境
    validate_environment
    
    # 运行测试
    run_tests
    
    # 备份数据
    backup_data
    
    # 构建镜像
    build_images
    
    # 执行部署
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        deploy_docker
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        deploy_kubernetes
    else
        log_error "不支持的部署类型: $DEPLOYMENT_TYPE"
        exit 1
    fi
    
    # 部署后验证
    post_deploy_verification
    
    # 显示部署信息
    show_deployment_info
}

# 运行主函数
main "$@"
# Docker 配置说明

## 目录结构

```
docker/
├── README.md                    # 本文档
├── compose/                     # Docker Compose 配置
│   ├── local.yml               # 本地开发环境
│   ├── staging.yml             # 测试环境
│   └── production.yml          # 生产环境
├── nginx/                      # Nginx 配置
│   ├── dev.conf               # 开发环境配置
│   └── prod.conf              # 生产环境配置
└── scripts/                    # 构建和部署脚本
    ├── build.sh               # 构建脚本
    └── deploy.sh              # 部署脚本
```

## Dockerfile 位置

- **前端**: `frontend/Dockerfile` (开发) 和 `frontend/Dockerfile.prod` (生产)
- **后端**: `backend/Dockerfile` (开发) 和 `backend/Dockerfile.prod` (生产)

## 构建命令规范

### 前端构建

```bash
# 开发环境
docker build -f frontend/Dockerfile --target development -t quant-frontend:dev frontend/

# 生产环境
docker build -f frontend/Dockerfile.prod -t quant-frontend:prod frontend/
```

### 后端构建

```bash
# 开发环境
docker build -f backend/Dockerfile --target development -t quant-backend:dev .

# 生产环境
docker build -f backend/Dockerfile.prod -t quant-backend:prod .
```

## 关键原则

1. **构建上下文一致性**
   - 前端: 使用 `frontend/` 作为构建上下文
   - 后端: 使用项目根目录作为构建上下文

2. **多阶段构建**
   - 依赖安装阶段 (缓存优化)
   - 构建阶段
   - 运行阶段

3. **安全最佳实践**
   - 使用非 root 用户
   - 最小化镜像体积
   - 安全头配置

4. **性能优化**
   - 层缓存优化
   - 静态资源压缩
   - 健康检查

## 环境变量

### 前端
- `VITE_API_BASE_URL`: API 基础 URL
- `VITE_WS_URL`: WebSocket URL
- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_VERSION`: 应用版本

### 后端
- `DATABASE_URL`: 数据库连接字符串
- `REDIS_URL`: Redis 连接字符串
- `SECRET_KEY`: 应用密钥
- `JWT_SECRET_KEY`: JWT 密钥

## 使用示例

### 本地开发

```bash
# 使用构建脚本
./scripts/docker-build.sh all

# 或手动构建
docker build -f frontend/Dockerfile --target development -t quant-frontend:dev frontend/
docker build -f backend/Dockerfile --target development -t quant-backend:dev .
```

### 生产部署

```bash
# 构建生产镜像
docker build -f frontend/Dockerfile.prod -t quant-frontend:prod frontend/
docker build -f backend/Dockerfile.prod -t quant-backend:prod .

# 使用 Docker Compose
docker-compose -f docker/compose/production.yml up -d
```

## 故障排除

### 常见问题

1. **COPY 路径错误**
   - 确保构建上下文正确
   - 前端使用 `frontend/` 上下文
   - 后端使用根目录上下文

2. **依赖安装失败**
   - 检查网络连接
   - 验证 package.json 或 requirements.txt 路径

3. **权限问题**
   - 确保使用非 root 用户
   - 检查文件权限设置

### 调试技巧

```bash
# 查看构建过程
docker build --no-cache --progress=plain -f frontend/Dockerfile frontend/

# 进入容器调试
docker run -it --entrypoint /bin/sh quant-frontend:dev

# 查看镜像层
docker history quant-frontend:prod
```

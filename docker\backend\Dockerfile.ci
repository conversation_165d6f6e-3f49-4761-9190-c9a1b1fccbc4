# CI 测试环境专用 Dockerfile
# 优化了构建速度和测试环境需求

ARG PYTHON_VERSION=3.11
FROM python:${PYTHON_VERSION}-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY backend/requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装测试依赖
RUN pip install --no-cache-dir \
    pytest==7.4.3 \
    pytest-cov==4.1.0 \
    pytest-asyncio==0.21.1 \
    pytest-mock==3.12.0 \
    pytest-xdist==3.3.1 \
    pytest-html==4.1.1 \
    pytest-json-report==1.5.0 \
    httpx==0.25.2 \
    factory-boy==3.3.0 \
    freezegun==1.2.2

# 复制应用代码
COPY backend/app ./app
COPY backend/alembic.ini .
COPY backend/migrations ./migrations

# 创建必要的目录
RUN mkdir -p logs data uploads static && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]

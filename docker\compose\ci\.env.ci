# CI 测试环境配置
# 专门用于 CI/CD 流程中的自动化测试

# =============================================================================
# 应用基础配置
# =============================================================================
APP_ENV=ci
APP_NAME=量化投资平台 (CI测试)
APP_VERSION=ci-test
DEBUG=false
TESTING=true

# =============================================================================
# 数据库配置 (CI 测试环境)
# =============================================================================
POSTGRES_DB=test_db
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_password
DATABASE_URL=postgresql+asyncpg://test_user:test_password@postgres:5432/test_db
DATABASE_TEST_URL=postgresql+asyncpg://test_user:test_password@postgres:5432/test_db

# =============================================================================
# Redis 配置 (CI 测试环境)
# =============================================================================
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# =============================================================================
# 认证配置 (CI 测试环境)
# =============================================================================
SECRET_KEY=test-secret-key-for-ci-only-not-for-production
JWT_SECRET_KEY=test-jwt-secret-key-for-ci-only-not-for-production
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# CORS 配置 (CI 测试环境)
# =============================================================================
CORS_ORIGINS=http://localhost:5173,http://frontend:5173,http://localhost:80,http://nginx:80

# =============================================================================
# 前端配置 (CI 测试环境)
# =============================================================================
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_TITLE=量化投资平台 (CI测试)
VITE_APP_VERSION=ci-test

# =============================================================================
# 日志配置 (CI 测试环境)
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=logs/ci-test.log

# =============================================================================
# 测试配置
# =============================================================================
PYTEST_CURRENT_TEST=true
TEST_DATABASE_URL=postgresql+asyncpg://test_user:test_password@postgres:5432/test_db
TEST_REDIS_URL=redis://redis:6379/0

# =============================================================================
# 外部服务配置 (CI 测试环境 - 模拟)
# =============================================================================
TUSHARE_TOKEN=test-token-not-real
AKSHARE_ENABLED=false
CTP_ENABLED=false

# =============================================================================
# 性能配置 (CI 测试环境)
# =============================================================================
WORKERS=1
MAX_CONNECTIONS=10
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=60
RATE_LIMIT_ENABLED=false

# =============================================================================
# 监控配置 (CI 测试环境)
# =============================================================================
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false
SENTRY_DSN=

# =============================================================================
# 邮件配置 (CI 测试环境 - 禁用)
# =============================================================================
SMTP_ENABLED=false
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=test-password

# =============================================================================
# 文件上传配置 (CI 测试环境)
# =============================================================================
MAX_UPLOAD_SIZE=1048576
UPLOAD_PATH=uploads/
STATIC_FILES_PATH=static/

# =============================================================================
# 安全配置 (CI 测试环境)
# =============================================================================
ALLOWED_HOSTS=localhost,backend,frontend,nginx,127.0.0.1
SECURE_SSL_REDIRECT=false
SESSION_COOKIE_SECURE=false
CSRF_COOKIE_SECURE=false

# =============================================================================
# 其他配置
# =============================================================================
TIMEZONE=Asia/Shanghai
LANGUAGE=zh-CN

# =============================================================================
# CI 特定配置
# =============================================================================
CI=true
CI_ENVIRONMENT=github-actions
CI_COMMIT_SHA=${GITHUB_SHA:-unknown}
CI_BRANCH=${GITHUB_REF_NAME:-unknown}
CI_BUILD_NUMBER=${GITHUB_RUN_NUMBER:-0}

# =============================================================================
# 测试超时配置
# =============================================================================
TEST_TIMEOUT=300
HEALTH_CHECK_TIMEOUT=60
SERVICE_START_TIMEOUT=120

# CI 测试环境配置指南

## 概述

本目录包含专门用于 CI/CD 流程的 Docker Compose 配置，确保集成测试和 E2E 测试在隔离、可重复的环境中运行。

## 目录结构

```
docker/compose/ci/
├── docker-compose.yml    # CI 测试环境配置
├── .env.ci              # CI 环境变量
└── README.md            # 本文档
```

## 服务架构

### 核心服务

| 服务 | 镜像 | 端口 | 说明 |
|------|------|------|------|
| **postgres** | postgres:15-alpine | 5432 | 测试数据库 |
| **redis** | redis:7-alpine | 6379 | 缓存和消息队列 |
| **backend** | 自定义构建 | 8000 | 后端 API 服务 |
| **frontend** | 自定义构建 | 5173 | 前端开发服务器 |
| **nginx** | nginx:1.25-alpine | 80 | 反向代理 |
| **test-runner** | 自定义构建 | - | 测试执行器 |

### 网络配置

- **网络名称**: `ci-network`
- **子网**: `**********/16`
- **驱动**: `bridge`

## 快速开始

### 1. 启动测试环境

```bash
# 方法1: 使用 Docker Compose 直接启动
docker compose -f docker/compose/ci/docker-compose.yml --env-file docker/compose/ci/.env.ci up -d

# 方法2: 使用测试运行器脚本
bash scripts/testing/ci-test-runner.sh all
```

### 2. 等待服务就绪

```bash
# 使用等待脚本
bash scripts/testing/wait-for-services.sh ci

# 或手动检查
curl http://localhost:8000/health  # 后端健康检查
curl http://localhost:5173         # 前端服务
curl http://localhost:80/health    # Nginx 代理
```

### 3. 运行测试

```bash
# 运行所有测试
bash scripts/testing/ci-test-runner.sh all

# 运行特定类型的测试
bash scripts/testing/ci-test-runner.sh unit        # 单元测试
bash scripts/testing/ci-test-runner.sh integration # 集成测试
bash scripts/testing/ci-test-runner.sh api         # API 测试
bash scripts/testing/ci-test-runner.sh e2e         # E2E 测试
```

### 4. 停止环境

```bash
# 停止并清理
docker compose -f docker/compose/ci/docker-compose.yml down -v --remove-orphans

# 或使用脚本
bash scripts/testing/ci-test-runner.sh stop
```

## 环境配置

### 数据库配置

- **数据库**: `test_db`
- **用户**: `test_user`
- **密码**: `test_password`
- **连接URL**: `postgresql+asyncpg://test_user:test_password@postgres:5432/test_db`

### Redis 配置

- **主机**: `redis:6379`
- **数据库**: 
  - 0: 主缓存
  - 1: Celery Broker
  - 2: Celery Result Backend

### 环境变量

关键环境变量在 `.env.ci` 中定义：

```bash
# 应用配置
APP_ENV=ci
TESTING=true
CI=true

# 数据库
DATABASE_URL=postgresql+asyncpg://test_user:test_password@postgres:5432/test_db

# Redis
REDIS_URL=redis://redis:6379/0

# 认证
SECRET_KEY=test-secret-key-for-ci-only
JWT_SECRET_KEY=test-jwt-secret-key-for-ci-only
```

## 测试类型

### 1. 单元测试

```bash
# 后端单元测试
docker compose -f docker/compose/ci/docker-compose.yml exec backend \
  pytest tests/unit/ -v --cov=app

# 前端单元测试
docker compose -f docker/compose/ci/docker-compose.yml exec frontend \
  pnpm run test:unit
```

### 2. 集成测试

```bash
# 后端集成测试
docker compose -f docker/compose/ci/docker-compose.yml exec backend \
  pytest tests/integration/ -v --cov=app --cov-append
```

### 3. API 测试

```bash
# API 端点测试
docker compose -f docker/compose/ci/docker-compose.yml exec backend \
  pytest tests/api/ -v
```

### 4. E2E 测试

```bash
# 端到端测试
docker compose -f docker/compose/ci/docker-compose.yml exec frontend \
  pnpm run test:e2e
```

## 性能优化

### 数据库优化

CI 环境的 PostgreSQL 配置了以下优化参数：

```sql
-- 禁用持久化以提高速度
fsync = off
synchronous_commit = off
full_page_writes = off

-- 内存配置
shared_buffers = 128MB
effective_cache_size = 512MB
work_mem = 2MB
```

### Redis 优化

```bash
# 禁用持久化
appendonly no
save ""

# 内存限制
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 故障排除

### 常见问题

1. **服务启动超时**
   ```bash
   # 检查容器状态
   docker compose -f docker/compose/ci/docker-compose.yml ps
   
   # 查看日志
   docker compose -f docker/compose/ci/docker-compose.yml logs [service_name]
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker compose -f docker/compose/ci/docker-compose.yml exec postgres pg_isready -U test_user
   
   # 手动连接测试
   docker compose -f docker/compose/ci/docker-compose.yml exec postgres psql -U test_user -d test_db
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5432
   netstat -tulpn | grep :6379
   netstat -tulpn | grep :8000
   ```

4. **内存不足**
   ```bash
   # 检查 Docker 资源使用
   docker stats
   
   # 清理未使用的资源
   docker system prune -f
   ```

### 调试技巧

1. **进入容器调试**
   ```bash
   # 进入后端容器
   docker compose -f docker/compose/ci/docker-compose.yml exec backend bash
   
   # 进入数据库容器
   docker compose -f docker/compose/ci/docker-compose.yml exec postgres psql -U test_user -d test_db
   ```

2. **查看详细日志**
   ```bash
   # 实时查看所有日志
   docker compose -f docker/compose/ci/docker-compose.yml logs -f
   
   # 查看特定服务日志
   docker compose -f docker/compose/ci/docker-compose.yml logs -f backend
   ```

3. **健康检查状态**
   ```bash
   # 检查容器健康状态
   docker inspect --format='{{.State.Health.Status}}' ci-backend
   docker inspect --format='{{.State.Health.Status}}' ci-postgres
   ```

## 最佳实践

### 1. 测试隔离

- 每个测试使用独立的数据库事务
- 测试之间不共享状态
- 使用工厂模式创建测试数据

### 2. 资源管理

- 及时清理测试环境
- 监控资源使用情况
- 使用健康检查确保服务就绪

### 3. 测试数据

- 使用最小化的测试数据集
- 避免依赖外部服务
- 使用模拟对象替代真实服务

### 4. 并行执行

- 使用 pytest-xdist 并行运行测试
- 确保测试之间无依赖关系
- 合理分配资源

## 集成到 CI/CD

### GitHub Actions

在 `.github/workflows/main.yml` 中使用：

```yaml
- name: 启动测试环境
  run: |
    docker compose -f docker/compose/ci/docker-compose.yml --env-file docker/compose/ci/.env.ci up -d --build

- name: 等待服务就绪
  run: |
    bash scripts/testing/wait-for-services.sh ci

- name: 运行集成测试
  run: |
    bash scripts/testing/ci-test-runner.sh integration
```

### GitLab CI

```yaml
test:
  image: docker:24
  services:
    - docker:dind
  script:
    - docker compose -f docker/compose/ci/docker-compose.yml up -d --build
    - bash scripts/testing/wait-for-services.sh ci
    - bash scripts/testing/ci-test-runner.sh all
```

## 监控和报告

### 测试结果

测试结果保存在 `test-results/` 目录：

```
test-results/
├── backend/
│   ├── unit-test-results.xml
│   ├── api-test-results.xml
│   └── coverage.xml
├── frontend/
│   └── unit-test-results.xml
├── integration/
│   └── integration-test-results.xml
├── e2e/
│   └── e2e-test-results.xml
└── logs/
    ├── backend.log
    ├── frontend.log
    └── postgres.log
```

### 覆盖率报告

```bash
# 生成覆盖率报告
docker compose -f docker/compose/ci/docker-compose.yml exec backend \
  coverage html --directory=htmlcov

# 查看覆盖率
docker compose -f docker/compose/ci/docker-compose.yml exec backend \
  coverage report
```

## 维护和更新

### 定期维护

1. **更新基础镜像**
   ```bash
   docker compose -f docker/compose/ci/docker-compose.yml pull
   ```

2. **清理旧数据**
   ```bash
   docker volume prune
   docker image prune
   ```

3. **验证配置**
   ```bash
   python scripts/validate-ci-config.py
   ```

### 版本升级

1. 更新 Dockerfile 中的基础镜像版本
2. 更新测试依赖版本
3. 运行完整测试验证兼容性
4. 更新文档

通过使用这个 CI 测试环境，可以确保所有测试在一致、隔离的环境中运行，提高测试的可靠性和可重复性。

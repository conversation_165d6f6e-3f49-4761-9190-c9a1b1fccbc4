# 本地开发环境 Docker Compose 配置
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ../../frontend
      dockerfile: Dockerfile
      target: development
      args:
        BUILD_ENV: development
    ports:
      - "5173:5173"
    volumes:
      - ../../frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000/api/v1
      - VITE_WS_URL=ws://localhost:8000/ws
    depends_on:
      - backend
    networks:
      - quant-network
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ../../
      dockerfile: backend/Dockerfile
      target: development
    ports:
      - "8000:8000"
    volumes:
      - ../../backend:/app
      - ../../data:/app/data
      - ../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=dev-secret-key
      - JWT_SECRET_KEY=dev-jwt-secret
      - DEBUG=true
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=quantplatform
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../scripts/database:/docker-entrypoint-initdb.d
    networks:
      - quant-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Celery Worker
  celery-worker:
    build:
      context: ../../
      dockerfile: backend/Dockerfile
      target: development
    volumes:
      - ../../backend:/app
      - ../../data:/app/data
      - ../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app worker --loglevel=info

  # Celery Beat (定时任务)
  celery-beat:
    build:
      context: ../../
      dockerfile: backend/Dockerfile
      target: development
    volumes:
      - ../../backend:/app
      - ../../data:/app/data
      - ../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=info

volumes:
  postgres_data:
  redis_data:

networks:
  quant-network:
    driver: bridge

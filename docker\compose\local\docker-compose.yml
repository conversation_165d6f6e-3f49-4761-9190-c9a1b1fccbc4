# 本地开发环境 Docker Compose 配置
# 使用方式: docker compose -f docker/compose/local/docker-compose.yml up
version: '3.8'

services:
  # 前端开发服务
  frontend:
    build:
      context: ../../..
      dockerfile: frontend/Dockerfile
      target: development
    container_name: quant-frontend-dev
    restart: unless-stopped
    ports:
      - "5173:5173"
    volumes:
      - ../../../frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000/api/v1
      - VITE_WS_URL=ws://localhost:8000/ws
      - VITE_APP_TITLE=量化投资平台 (开发)
    depends_on:
      - backend
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 后端开发服务
  backend:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile
      target: development
    container_name: quant-backend-dev
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ../../../backend:/app
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=dev-secret-key-2024
      - JWT_SECRET_KEY=dev-jwt-secret-2024
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - CORS_ORIGINS=http://localhost:5173,http://localhost:3000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant-postgres-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=quantplatform
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../../scripts/database:/docker-entrypoint-initdb.d
    networks:
      - quant-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d quantplatform"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: quant-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker (开发环境)
  celery-worker:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile
      target: development
    container_name: quant-celery-worker-dev
    restart: unless-stopped
    volumes:
      - ../../../backend:/app
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=dev-secret-key-2024
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    command: celery -A app.tasks.celery_app worker --loglevel=debug --concurrency=2

  # Celery Beat (定时任务)
  celery-beat:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile
      target: development
    container_name: quant-celery-beat-dev
    restart: unless-stopped
    volumes:
      - ../../../backend:/app
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/quantplatform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=dev-secret-key-2024
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    command: celery -A app.tasks.celery_app beat --loglevel=debug

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  quant-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 生产环境 Docker Compose 配置
version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/prod.conf:/etc/nginx/conf.d/default.conf:ro
      - ../../ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - quant-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端服务
  frontend:
    build:
      context: ../../frontend
      dockerfile: Dockerfile.prod
      args:
        VITE_API_BASE_URL: /api/v1
        VITE_WS_URL: /ws
        VITE_APP_TITLE: 量化投资平台
        VITE_APP_VERSION: "1.0.0"
    expose:
      - "80"
    networks:
      - quant-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 后端服务
  backend:
    build:
      context: ../../
      dockerfile: backend/Dockerfile.prod
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ../../data:/app/data:ro
      - ../../logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../backups:/backups
    networks:
      - quant-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis 缓存
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Worker
  celery-worker:
    build:
      context: ../../
      dockerfile: backend/Dockerfile.prod
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ../../data:/app/data:ro
      - ../../logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app worker --loglevel=warning --concurrency=4
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Beat
  celery-beat:
    build:
      context: ../../
      dockerfile: backend/Dockerfile.prod
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ../../data:/app/data:ro
      - ../../logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=warning
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ../prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - quant-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana 仪表盘
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - quant-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  quant-network:
    driver: bridge

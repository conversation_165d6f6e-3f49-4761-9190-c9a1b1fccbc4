# CI 测试环境专用前端 Dockerfile
# 优化了构建速度和测试环境需求

ARG NODE_VERSION=18
FROM node:${NODE_VERSION}-alpine as base

# 设置环境变量
ENV NODE_ENV=test \
    PNPM_HOME="/pnpm" \
    PATH="$PNPM_HOME:$PATH"

# 安装 pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    wget \
    git \
    python3 \
    make \
    g++

# 创建应用用户
RUN addgroup -g 1001 -S appuser && \
    adduser -S appuser -u 1001 -G appuser

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY frontend/package.json frontend/pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile --prefer-offline

# 安装测试依赖
RUN pnpm add -D \
    @playwright/test \
    @vitest/ui \
    @vue/test-utils \
    jsdom \
    happy-dom \
    vitest

# 复制应用代码
COPY frontend/src ./src
COPY frontend/public ./public
COPY frontend/index.html .
COPY frontend/vite.config.ts .
COPY frontend/vitest.config.ts .
COPY frontend/playwright.config.ts .
COPY frontend/tsconfig.json .
COPY frontend/tsconfig.app.json .
COPY frontend/tsconfig.node.json .
COPY frontend/tailwind.config.js .
COPY frontend/eslint.config.ts .

# 构建参数
ARG VITE_API_BASE_URL=http://localhost:8000/api/v1
ARG VITE_WS_URL=ws://localhost:8000/ws
ARG VITE_APP_TITLE=量化投资平台 (CI测试)
ARG VITE_APP_VERSION=ci-test

# 设置构建环境变量
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL} \
    VITE_WS_URL=${VITE_WS_URL} \
    VITE_APP_TITLE=${VITE_APP_TITLE} \
    VITE_APP_VERSION=${VITE_APP_VERSION}

# 构建应用
RUN pnpm run build

# 创建必要的目录
RUN mkdir -p logs test-results playwright-report && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5173/ || exit 1

# 暴露端口
EXPOSE 5173

# 启动开发服务器（用于测试）
CMD ["pnpm", "run", "dev", "--host", "0.0.0.0", "--port", "5173"]

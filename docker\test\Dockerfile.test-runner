# 测试运行器 Dockerfile
# 用于运行各种类型的测试

FROM python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    libpq-dev \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 安装 pnpm
RUN npm install -g pnpm

# 创建应用用户
RUN groupadd -r testuser && useradd -r -g testuser testuser

# 设置工作目录
WORKDIR /app

# 安装 Python 测试依赖
RUN pip install --no-cache-dir \
    pytest==7.4.3 \
    pytest-cov==4.1.0 \
    pytest-asyncio==0.21.1 \
    pytest-mock==3.12.0 \
    pytest-xdist==3.3.1 \
    pytest-html==4.1.1 \
    pytest-json-report==1.5.0 \
    httpx==0.25.2 \
    requests==2.31.0 \
    selenium==4.15.2 \
    playwright==1.40.0 \
    locust==2.17.0

# 安装 Playwright 浏览器
RUN playwright install chromium firefox webkit

# 创建目录结构
RUN mkdir -p \
    backend/tests \
    frontend/tests \
    scripts \
    results/backend \
    results/frontend \
    results/e2e \
    results/performance \
    logs

# 复制测试脚本
COPY scripts/testing/ ./scripts/

# 设置权限
RUN chown -R testuser:testuser /app

# 切换到测试用户
USER testuser

# 工作目录
WORKDIR /app

# 默认命令
CMD ["bash"]

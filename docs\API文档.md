# 量化交易平台 API 文档

## 📋 概述

量化交易平台提供完整的RESTful API和WebSocket接口，支持市场数据获取、交易管理、策略执行、回测分析等核心功能。

### 🔗 基础信息

- **API版本**: v1
- **基础URL**: `http://localhost:8000/api/v1`
- **文档地址**: `http://localhost:8000/docs`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 📊 API特性

- ✅ RESTful设计规范
- ✅ 统一响应格式
- ✅ 完整错误处理
- ✅ 请求参数验证
- ✅ 分页查询支持
- ✅ 实时数据推送
- ✅ 接口限流保护
- ✅ 自动API文档

## 🔐 认证授权

### JWT Token认证

所有需要认证的接口都需要在请求头中携带JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

### 获取Token

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 7200,
    "user": {
      "id": "user123",
      "username": "trader001",
      "email": "<EMAIL>",
      "role": "trader"
    }
  },
  "timestamp": "2025-07-27T10:30:00Z"
}
```

### 刷新Token

```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh-token>
```

## 📊 统一响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-07-27T10:30:00Z",
  "code": 200
}
```

### 错误响应

```json
{
  "success": false,
  "message": "操作失败",
  "error": {
    "type": "validation_error",
    "message": "输入数据验证失败",
    "details": [
      {
        "field": "price",
        "message": "价格必须大于0",
        "input": "-10.5"
      }
    ]
  },
  "timestamp": "2025-07-27T10:30:00Z",
  "code": 422
}
```

### 分页响应

```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    // 数据列表
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  },
  "timestamp": "2025-07-27T10:30:00Z"
}
```

## 🏪 市场数据 API

### 获取股票列表

```http
GET /api/v1/market/stocks
```

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页大小，默认20，最大100
- `market` (string): 市场代码，如'SH'、'SZ'
- `search` (string): 搜索关键词
- `sort_by` (string): 排序字段
- `sort_order` (string): 排序方向，'asc'或'desc'

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001.SZ",
      "name": "平安银行",
      "market": "SZ",
      "industry": "银行",
      "list_date": "1991-04-03",
      "is_hs": true
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 5000,
    "total_pages": 250,
    "has_next": true,
    "has_prev": false
  }
}
```

### 获取实时行情

```http
GET /api/v1/market/quotes/{symbol}
```

**路径参数**:
- `symbol` (string): 股票代码，如'000001.SZ'

**响应示例**:
```json
{
  "success": true,
  "data": {
    "symbol": "000001.SZ",
    "name": "平安银行",
    "price": 12.50,
    "change": 0.15,
    "change_pct": 1.22,
    "volume": 1500000,
    "turnover": 18750000.0,
    "high": 12.60,
    "low": 12.30,
    "open": 12.35,
    "prev_close": 12.35,
    "timestamp": "2025-07-27T14:30:00Z"
  }
}
```

### 获取K线数据

```http
GET /api/v1/market/klines/{symbol}
```

**查询参数**:
- `period` (string): K线周期，'1min'、'5min'、'15min'、'30min'、'1hour'、'1day'
- `start_date` (string): 开始日期，格式'YYYY-MM-DD'
- `end_date` (string): 结束日期，格式'YYYY-MM-DD'
- `limit` (int): 返回条数，默认100，最大1000

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "timestamp": "2025-07-27T09:30:00Z",
      "open": 12.35,
      "high": 12.45,
      "low": 12.30,
      "close": 12.40,
      "volume": 150000,
      "turnover": 1860000.0
    }
  ]
}
```

## 💼 交易管理 API

### 创建订单

```http
POST /api/v1/trading/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "symbol": "000001.SZ",
  "side": "buy",
  "type": "limit",
  "quantity": 1000,
  "price": 12.50,
  "time_in_force": "GTC"
}
```

**请求参数**:
- `symbol` (string): 股票代码
- `side` (string): 交易方向，'buy'或'sell'
- `type` (string): 订单类型，'market'、'limit'、'stop'
- `quantity` (int): 交易数量
- `price` (float): 价格（限价单必填）
- `time_in_force` (string): 有效期，'GTC'、'IOC'、'FOK'

**响应示例**:
```json
{
  "success": true,
  "message": "订单创建成功",
  "data": {
    "order_id": "ORD20250727001",
    "symbol": "000001.SZ",
    "side": "buy",
    "type": "limit",
    "quantity": 1000,
    "price": 12.50,
    "status": "pending",
    "created_at": "2025-07-27T10:30:00Z"
  }
}
```

### 查询订单

```http
GET /api/v1/trading/orders
Authorization: Bearer <token>
```

**查询参数**:
- `status` (string): 订单状态筛选
- `symbol` (string): 股票代码筛选
- `start_date` (string): 开始日期
- `end_date` (string): 结束日期
- `page` (int): 页码
- `page_size` (int): 每页大小

### 取消订单

```http
DELETE /api/v1/trading/orders/{order_id}
Authorization: Bearer <token>
```

### 查询持仓

```http
GET /api/v1/trading/positions
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001.SZ",
      "name": "平安银行",
      "quantity": 1000,
      "available_quantity": 1000,
      "avg_cost": 12.30,
      "market_value": 12500.0,
      "unrealized_pnl": 200.0,
      "unrealized_pnl_pct": 1.63,
      "updated_at": "2025-07-27T14:30:00Z"
    }
  ]
}
```

## 🧠 策略管理 API

### 创建策略

```http
POST /api/v1/strategies
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "均线策略",
  "description": "基于双均线的趋势跟踪策略",
  "type": "trend_following",
  "code": "# 策略代码\ndef initialize(context):\n    pass",
  "parameters": [
    {
      "name": "short_window",
      "type": "number",
      "defaultValue": 5,
      "description": "短期均线周期"
    }
  ]
}
```

### 执行策略

```http
POST /api/v1/strategies/{strategy_id}/execute
Authorization: Bearer <token>
Content-Type: application/json

{
  "symbols": ["000001.SZ", "000002.SZ"],
  "start_date": "2025-01-01",
  "end_date": "2025-07-27",
  "initial_capital": 100000
}
```

### 停止策略

```http
POST /api/v1/strategies/{strategy_id}/stop
Authorization: Bearer <token>
```

## 📈 回测分析 API

### 创建回测

```http
POST /api/v1/backtest
Authorization: Bearer <token>
Content-Type: application/json

{
  "strategy_id": "strategy123",
  "symbols": ["000001.SZ"],
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "initial_capital": 100000,
  "benchmark": "000300.SH"
}
```

### 查询回测结果

```http
GET /api/v1/backtest/{backtest_id}/results
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "backtest_id": "bt20250727001",
    "status": "completed",
    "performance": {
      "total_return": 0.15,
      "annualized_return": 0.12,
      "volatility": 0.18,
      "sharpe_ratio": 0.67,
      "max_drawdown": 0.08,
      "win_rate": 0.65
    },
    "trades": 45,
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }
}
```

## 🔌 WebSocket API

### 连接地址

```
ws://localhost:8000/ws
```

### 认证

连接后发送认证消息：

```json
{
  "type": "auth",
  "data": {
    "token": "your-jwt-token"
  }
}
```

### 订阅市场数据

```json
{
  "type": "subscribe",
  "channel": "market.quotes",
  "data": {
    "symbols": ["000001.SZ", "000002.SZ"]
  }
}
```

### 接收实时数据

```json
{
  "type": "market.quote",
  "data": {
    "symbol": "000001.SZ",
    "price": 12.50,
    "change": 0.15,
    "change_pct": 1.22,
    "volume": 1500000,
    "timestamp": "2025-07-27T14:30:00Z"
  },
  "timestamp": "2025-07-27T14:30:00Z"
}
```

## ❌ 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和内容 |
| 401 | 未授权访问 | 检查Token是否有效 |
| 403 | 权限不足 | 联系管理员获取权限 |
| 404 | 资源不存在 | 检查请求路径和资源ID |
| 422 | 数据验证失败 | 检查请求数据格式 |
| 429 | 请求频率超限 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 📝 使用示例

### Python示例

```python
import requests
import json

# 登录获取Token
login_data = {
    "username": "trader001",
    "password": "password123"
}

response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    json=login_data
)

token = response.json()["data"]["access_token"]

# 获取股票行情
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(
    "http://localhost:8000/api/v1/market/quotes/000001.SZ",
    headers=headers
)

quote = response.json()["data"]
print(f"股票价格: {quote['price']}")
```

### JavaScript示例

```javascript
// 登录获取Token
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'trader001',
    password: 'password123'
  })
});

const { data } = await loginResponse.json();
const token = data.access_token;

// 创建订单
const orderResponse = await fetch('/api/v1/trading/orders', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    symbol: '000001.SZ',
    side: 'buy',
    type: 'limit',
    quantity: 1000,
    price: 12.50
  })
});

const order = await orderResponse.json();
console.log('订单创建成功:', order.data);
```

## 🔄 版本更新

### v1.0.0 (2025-07-27)
- ✅ 初始版本发布
- ✅ 基础API功能完成
- ✅ WebSocket实时数据支持
- ✅ 完整的认证授权体系

## 📞 技术支持

- **文档地址**: http://localhost:8000/docs
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **技术交流**: 微信群/QQ群
- **邮件支持**: <EMAIL>

---

*本文档持续更新，最新版本请访问在线文档地址*

# 🗺️ 详细清理路线图

基于深度扫描分析，制定系统性的清理与收敛方案。

## 📋 权威清单：保留/移除/迁移

### 🎯 后端入口统一

#### 保留
- ✅ `backend/app/main.py` - **权威生产入口**
- ✅ `backend/app/main_simple.py` - **开发/测试辅助入口**

#### 移除
- ❌ `backend/app/main_fixed.py` - 已删除 ✅
- ❌ `backend/app/main_optimized.py` - 已删除 ✅

#### 迁移/标注
- 🔄 所有启动脚本统一指向 `app.main:app`
- 🔄 Docker配置统一指向权威入口
- 📝 README明确标注入口使用场景

### 🔌 WebSocket端点收敛

#### 保留（统一架构）
```
权威WebSocket架构:
├── app/core/websocket.py (核心服务)
├── app/api/v1/websocket_enhanced.py (统一入口)
│   ├── /api/v1/ws/connect (通用连接)
│   ├── /api/v1/ws/market (市场数据)
│   ├── /api/v1/ws/trading (交易推送)
│   └── /api/v1/ws/backtest (回测进度)
└── app/api/v1/ctp_websocket.py (CTP专用，保留)
```

#### 移除/迁移
- ❌ `app/api/v1/trading.py` 中的 `/ws` 端点 - 已删除 ✅
- ❌ `app/trading_system.py` 中的 `/ws` 端点 - 已删除 ✅
- ❌ `app/api/v1/backtest.py` 中的 `/ws` 端点 - **待处理**
- ❌ `app/api/websocket.py` 中的 `/ws/{client_id}` - **待处理**
- ❌ `main.py` 中的调试 `/api/v1/ws` - **待处理**

#### 服务层推送迁移
- 🔄 交易状态推送 → `ws_service.send_trading_update()`
- 🔄 回测进度推送 → `ws_service.send_backtest_progress()`
- 🔄 市场数据推送 → `ws_service.send_market_data()`

### 🔐 鉴权系统统一

#### 保留（权威实现）
- ✅ `app/core/security.py` - **SecurityManager核心**
- ✅ `app/core/dependencies.py` - **统一依赖注入** (已修正为dependencies.py)

#### 移除
- ❌ `app/core/dependencies_fixed.py` - 已删除 ✅
- ❌ `app/services/enhanced_auth_service.py` 中的PyJWT - **待处理**

#### 统一JWT库
- ✅ 统一使用 `from jose import jwt` (python-jose)
- ❌ 移除所有 `import jwt` (PyJWT)

### 💾 数据库配置统一

#### 保留（权威配置）
- ✅ `app/core/database.py` - **运行时数据库配置**
- ✅ `backend/alembic.ini` - **迁移配置**
- ✅ `backend/migrations/env.py` - **迁移环境**

#### 问题修复
- 🔄 Alembic `sqlalchemy.url` 使用环境变量而非硬编码
- 🔄 确保运行时与迁移使用相同 `DATABASE_URL`

#### 移除/标注
- ❌ `scripts/database/database_config.py` - **标注仅脚本用途**

### 📁 目录结构清理

#### 移除重复目录
- ❌ `backend/src/*` - **与app/重复**
- ❌ `frontend/src/store/` - **与stores/重复，使用Pinia**
- ❌ `config/docker/` - **与docker/重复**
- ❌ `config/k8s/` - **与k8s/重复**

#### 移除重复文件
- ❌ `backend/src/api/captcha.py` - **与app/api/captcha.py重复**
- ❌ `frontend/integration/components/BacktestDashboard.tsx` - **React示例，标注或移除**

---

## 🚀 具体执行方案

### Phase 1: WebSocket端点收敛 (高优先级)

#### 1.1 删除重复WebSocket端点
```python
# 需要删除的端点:
# app/api/v1/backtest.py 中的 @router.websocket("/ws")
# app/api/websocket.py 中的 @router.websocket("/ws/{client_id}")
# app/main.py 中的调试WebSocket端点
```

#### 1.2 迁移推送逻辑到服务层
```python
# 回测进度推送迁移示例:
# 原: 直接WebSocket推送
# 新: 通过ws_service推送
await ws_service.send_backtest_progress(user_id, progress_data)
```

#### 1.3 更新前端WebSocket连接
```typescript
// 统一前端WebSocket连接到增强版端点
const wsUrl = 'ws://localhost:8000/api/v1/ws/connect'
```

### Phase 2: JWT库统一 (中优先级)

#### 2.1 修复enhanced_auth_service.py
```python
# 将 import jwt 改为 from jose import jwt
# 更新所有JWT相关方法调用
```

#### 2.2 验证JWT统一性
```bash
# 检查项目中是否还有PyJWT使用
grep -r "import jwt" backend/ --exclude-dir=__pycache__
```

### Phase 3: 数据库配置对齐 (中优先级)

#### 3.1 修复Alembic配置
```ini
# backend/alembic.ini
# 将硬编码的sqlalchemy.url改为环境变量
sqlalchemy.url = ${DATABASE_URL}
```

#### 3.2 环境变量统一
```bash
# 确保所有环境都使用相同的DATABASE_URL
export DATABASE_URL=sqlite+aiosqlite:///./data/quantplatform.db
```

### Phase 4: 目录清理 (低优先级)

#### 4.1 删除重复目录
```bash
# 安全删除重复目录
rm -rf backend/src/
rm -rf frontend/src/store/
rm -rf config/docker/
rm -rf config/k8s/
```

#### 4.2 更新导入路径
```python
# 检查并更新所有对已删除目录的引用
# 确保没有导入路径指向已删除的目录
```

---

## ⚠️ 风险评估与缓解

### 高风险操作
1. **WebSocket端点删除**
   - 风险: 前端连接失败
   - 缓解: 先确认前端使用的端点，逐步迁移

2. **JWT库切换**
   - 风险: Token验证失败
   - 缓解: 保持Token格式兼容，分步骤切换

### 中风险操作
1. **数据库配置修改**
   - 风险: 迁移失败
   - 缓解: 备份数据库，测试环境先验证

2. **目录删除**
   - 风险: 导入路径失效
   - 缓解: 全局搜索引用，确认无依赖后删除

---

## 📊 完成度评估

### 当前状态
- **后端入口**: ✅ 90% (已统一主要入口)
- **WebSocket**: ⚠️ 60% (核心统一，但仍有重复端点)
- **鉴权系统**: ✅ 85% (主要依赖已统一，少量混用待清理)
- **数据库**: ⚠️ 70% (配置基本可用，需环境一致性)
- **目录结构**: ⚠️ 40% (存在大量重复，需系统清理)

### 目标状态
- **后端入口**: 🎯 95% (完全统一，清晰文档)
- **WebSocket**: 🎯 95% (单一架构，服务层推送)
- **鉴权系统**: 🎯 98% (完全统一，无混用)
- **数据库**: 🎯 95% (运行时与迁移完全一致)
- **目录结构**: 🎯 90% (清理重复，权威路径)

---

## 🎯 执行时间表

### Week 1: 关键冲突解决
- Day 1-2: WebSocket端点收敛
- Day 3: JWT库统一
- Day 4: 数据库配置对齐
- Day 5: 验证与测试

### Week 2: 结构优化
- Day 1-2: 目录清理
- Day 3: 导入路径更新
- Day 4: 文档更新
- Day 5: 最终验证

---

## ✅ 验收标准

### 功能验收
- [ ] 项目单一入口启动成功
- [ ] WebSocket连接无冲突
- [ ] JWT认证完全统一
- [ ] 数据库迁移与运行一致
- [ ] 无重复目录和文件

### 质量验收
- [ ] 无bare except (关键路径)
- [ ] 无print调试 (生产路径)
- [ ] 无JWT库混用
- [ ] 无重复WebSocket端点
- [ ] 配置来源唯一

---

**制定时间**: 2025-01-27  
**预计完成**: 2025-02-10  
**当前阶段**: Phase 1 准备中

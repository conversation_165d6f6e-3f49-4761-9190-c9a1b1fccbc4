# 数据模型结构指南

## 概述

本文档描述了量化投资平台的数据模型结构规范，确保模型文件组织清晰，避免导入混淆。

## 目录结构

### ✅ 正确的模型目录结构

```
backend/
├── app/
│   ├── db/
│   │   └── models/              # 主模型目录 (所有模型文件)
│   │       ├── __init__.py      # 统一导出接口
│   │       ├── user.py          # 用户相关模型
│   │       ├── trading.py       # 交易相关模型
│   │       ├── strategy.py      # 策略相关模型
│   │       ├── market.py        # 市场数据模型
│   │       ├── backtest.py      # 回测相关模型
│   │       ├── risk.py          # 风控相关模型
│   │       ├── ctp_models.py    # CTP 接口模型
│   │       ├── watchlist.py     # 观察列表模型
│   │       └── types.py         # 类型定义
│   └── models/
│       └── __init__.py          # 统一导入接口 (仅此文件)
```

### ❌ 已清理的错误结构

```
backend/app/models/              # 遗留目录
├── *.backup                     # 已删除的备份文件
├── user.py                      # 已移动到 db/models/
├── trading.py                   # 已移动到 db/models/
└── ...                          # 其他已移动的文件
```

## 导入规范

### ✅ 正确的导入方式

```python
# 方式1: 从统一接口导入 (推荐)
from app.models import User, Order, Strategy

# 方式2: 从 db.models 导入 (也可以)
from app.db.models import User, Order, Strategy

# 方式3: 直接从具体模块导入 (特殊情况)
from app.db.models.user import User
from app.db.models.trading import Order
```

### ❌ 禁止的导入方式

```python
# 错误: 直接从遗留目录导入
from app.models.user import User          # ❌

# 错误: 导入不存在的模块
from app.models.user_models import User   # ❌
from app.models.trading_models import Order # ❌
```

## 模型文件组织

### 用户相关模型 (`user.py`)
- `User` - 用户基础信息
- `UserRole` - 用户角色
- `UserSession` - 用户会话
- `UserStatus` - 用户状态枚举

### 交易相关模型 (`trading.py`)
- `Order` - 订单
- `Trade` - 成交记录
- `Position` - 持仓
- `Account` - 账户
- 相关枚举类型

### 策略相关模型 (`strategy.py`)
- `Strategy` - 策略定义
- `StrategyInstance` - 策略实例
- `StrategyPerformance` - 策略表现
- 相关枚举类型

### 市场数据模型 (`market.py`)
- `Symbol` - 交易标的
- `MarketData` - 市场数据
- `KLineData` - K线数据
- `TradeTick` - 逐笔成交

### 回测相关模型 (`backtest.py`)
- `BacktestTask` - 回测任务
- `BacktestResult` - 回测结果
- `BacktestTrade` - 回测交易记录
- `BacktestMetrics` - 回测指标

### 风控相关模型 (`risk.py`)
- `RiskLimit` - 风控限制
- `RiskEvent` - 风控事件
- `PositionLimit` - 持仓限制
- `TradingRestriction` - 交易限制

### CTP 接口模型 (`ctp_models.py`)
- `CTPOrder` - CTP 订单
- `CTPTrade` - CTP 成交
- `CTPPosition` - CTP 持仓
- `CTPAccount` - CTP 账户

### 观察列表模型 (`watchlist.py`)
- `WatchlistItem` - 观察列表项

## 开发规范

### 1. 添加新模型

当需要添加新的数据模型时：

1. **确定模型分类**：根据业务功能确定应该放在哪个文件中
2. **添加到对应文件**：在 `backend/app/db/models/` 下的对应文件中添加
3. **更新导出**：在 `backend/app/db/models/__init__.py` 中添加导入和导出
4. **更新统一接口**：在 `backend/app/models/__init__.py` 中添加导入和导出

### 2. 模型命名规范

- 类名使用 PascalCase：`User`, `OrderStatus`
- 表名使用 snake_case：`users`, `order_status`
- 字段名使用 snake_case：`user_id`, `created_at`

### 3. 关系定义规范

```python
# 正确的关系定义
class User(Base):
    __tablename__ = "users"
    
    orders = relationship("Order", back_populates="user")

class Order(Base):
    __tablename__ = "orders"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship("User", back_populates="orders")
```

## 验证工具

### 1. 结构验证脚本

运行以下命令验证模型结构：

```bash
python scripts/validate_model_structure.py
```

### 2. 预提交钩子

设置预提交钩子防止错误提交：

```bash
# 复制预提交钩子
cp scripts/pre-commit-model-check.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

### 3. CI/CD 集成

在 CI/CD 流程中添加模型结构检查：

```yaml
# .github/workflows/ci.yml
- name: 验证模型结构
  run: python scripts/validate_model_structure.py
```

## 迁移指南

### 从旧结构迁移

如果发现使用了旧的导入方式，请按以下步骤修复：

1. **查找旧导入**：
   ```bash
   grep -r "from app\.models\.[^.]*\s\+import" backend/
   ```

2. **替换导入语句**：
   ```python
   # 旧的导入
   from app.models.user import User
   
   # 新的导入
   from app.models import User
   ```

3. **验证修复**：
   ```bash
   python scripts/validate_model_structure.py
   ```

## 故障排除

### 常见问题

1. **导入错误**：`ModuleNotFoundError: No module named 'app.models.user_models'`
   - **原因**：使用了不存在的模块路径
   - **解决**：使用正确的导入路径

2. **循环导入**：`ImportError: cannot import name 'User' from partially initialized module`
   - **原因**：模型之间存在循环依赖
   - **解决**：检查模型关系定义，使用字符串引用

3. **模型未找到**：`AttributeError: module 'app.models' has no attribute 'User'`
   - **原因**：模型未在 `__all__` 中导出
   - **解决**：检查并更新导出列表

### 调试技巧

1. **检查导入路径**：
   ```python
   import app.models
   print(dir(app.models))  # 查看可用的模型
   ```

2. **验证模型定义**：
   ```python
   from app.models import User
   print(User.__table__.columns.keys())  # 查看模型字段
   ```

3. **检查数据库表**：
   ```sql
   SELECT name FROM sqlite_master WHERE type='table';
   ```

## 最佳实践

1. **保持模型文件专注**：每个文件只包含相关的模型
2. **使用统一导入**：优先使用 `from app.models import` 方式
3. **定期验证结构**：使用验证脚本确保结构正确
4. **文档同步更新**：添加新模型时同步更新文档
5. **代码审查**：在 PR 中检查模型导入是否正确

## 总结

通过统一的模型结构和导入规范，我们实现了：

- ✅ 消除了导入冲突和混淆
- ✅ 提高了代码的可维护性
- ✅ 简化了新开发者的学习成本
- ✅ 建立了清晰的代码组织规范

遵循本指南可以确保项目的模型结构始终保持清晰和一致。

# 标准化部署配置指南

## 概述

本文档描述了量化投资平台的标准化部署配置结构，包括 Docker Compose 和 Nginx 配置的统一管理方案。

## 目录结构

### ✅ 标准化配置结构

```
docker/
├── compose/              # 统一存放所有 compose 配置
│   ├── local/            # 本地开发环境
│   │   ├── docker-compose.yml
│   │   ├── .env.local
│   │   └── README.md     # 本地启动指南
│   ├── staging/          # 测试环境
│   │   ├── docker-compose.yml
│   │   └── .env.staging.example
│   └── production/       # 生产环境
│       ├── docker-compose.yml
│       └── .env.prod.example
└── nginx/
    ├── templates/        # 通用配置模板
    │   └── frontend.conf.j2
    ├── local/            # 环境特定配置
    │   └── default.conf
    ├── staging/
    │   └── default.conf
    └── production/
        └── default.conf
```

## 环境启动清单

| **环境** | **启动命令** | **配置文件位置** | **访问地址** |
|----------|-------------|-----------------|-------------|
| 本地开发 | `./scripts/env-switch.sh local up -d` | `docker/compose/local/.env` | `http://localhost:5173` |
| 测试环境 | `./scripts/env-switch.sh staging up -d` | `docker/compose/staging/.env.staging` | `https://staging.yourdomain.com` |
| 生产环境 | `./scripts/env-switch.sh production up -d` | `docker/compose/production/.env.prod` | `https://yourdomain.com` |

## 快速开始

### 1. 本地开发环境

```bash
# 1. 准备环境变量
cp docker/compose/local/.env.local docker/compose/local/.env

# 2. 启动服务
./scripts/env-switch.sh local up -d

# 3. 验证服务
./scripts/env-switch.sh local ps

# 4. 查看日志
./scripts/env-switch.sh local logs
```

**访问地址：**
- 前端：http://localhost:5173
- 后端 API：http://localhost:8000
- API 文档：http://localhost:8000/docs
- 数据库：localhost:5432
- Redis：localhost:6379

### 2. 测试环境

```bash
# 1. 准备环境变量
cp docker/compose/staging/.env.staging.example docker/compose/staging/.env.staging
# 编辑 .env.staging 填入测试环境配置

# 2. 启动服务
./scripts/env-switch.sh staging up -d

# 3. 验证服务
./scripts/env-switch.sh staging ps
```

### 3. 生产环境

```bash
# 1. 准备环境变量
cp docker/compose/production/.env.prod.example docker/compose/production/.env.prod
# 编辑 .env.prod 填入生产环境配置

# 2. 启动服务
./scripts/env-switch.sh production up -d

# 3. 验证服务
./scripts/env-switch.sh production ps
```

## 配置继承关系

```mermaid
graph TD
    A[通用模板] --> B[本地开发]
    A --> C[测试环境]
    A --> D[生产环境]
    E[环境变量] --> B
    E --> C
    E --> D
    F[安全策略] --> D
    G[SSL配置] --> C
    G --> D
```

## 环境差异化配置

### 本地开发环境特点
- ✅ 热重载支持
- ✅ 详细调试日志
- ✅ 无 SSL 要求
- ✅ 宽松的安全策略
- ✅ 直接端口映射

### 测试环境特点
- ✅ 生产级配置
- ✅ SSL 支持
- ✅ 适度的安全策略
- ✅ 监控和日志
- ✅ 自动化测试支持

### 生产环境特点
- ✅ 最高安全标准
- ✅ 性能优化
- ✅ 完整的监控
- ✅ 备份和恢复
- ✅ 负载均衡

## 常用操作

### 环境切换

```bash
# 启动环境
./scripts/env-switch.sh <环境> up -d

# 停止环境
./scripts/env-switch.sh <环境> down

# 重启环境
./scripts/env-switch.sh <环境> restart

# 查看状态
./scripts/env-switch.sh <环境> ps

# 查看日志
./scripts/env-switch.sh <环境> logs [服务名]

# 构建镜像
./scripts/env-switch.sh <环境> build
```

### 配置验证

```bash
# 验证所有环境配置
python scripts/validate-config.py

# 验证特定环境
docker compose -f docker/compose/local/docker-compose.yml config
docker compose -f docker/compose/staging/docker-compose.yml config
docker compose -f docker/compose/production/docker-compose.yml config
```

### 服务管理

```bash
# 单独启动某个服务
./scripts/env-switch.sh local up backend

# 重新构建并启动
./scripts/env-switch.sh local up --build

# 查看特定服务日志
./scripts/env-switch.sh local logs backend

# 进入容器
docker compose -f docker/compose/local/docker-compose.yml exec backend bash
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5173
   
   # 修改环境变量中的端口配置
   ```

2. **配置文件错误**
   ```bash
   # 验证配置语法
   python scripts/validate-config.py
   
   # 检查 Docker Compose 配置
   docker compose -f docker/compose/local/docker-compose.yml config
   ```

3. **环境变量缺失**
   ```bash
   # 检查环境变量文件
   cat docker/compose/local/.env
   
   # 复制示例文件
   cp docker/compose/local/.env.local docker/compose/local/.env
   ```

4. **服务启动失败**
   ```bash
   # 查看详细日志
   ./scripts/env-switch.sh local logs
   
   # 检查服务状态
   ./scripts/env-switch.sh local ps
   ```

### 重置环境

```bash
# 完全重置本地环境
./scripts/env-switch.sh local down
docker system prune -f
./scripts/env-switch.sh local up --build -d
```

## 安全配置

### 生产环境安全检查清单

- [ ] 更改所有默认密码
- [ ] 配置 SSL 证书
- [ ] 启用安全头
- [ ] 配置防火墙规则
- [ ] 设置访问限制
- [ ] 启用日志监控
- [ ] 配置备份策略

### 环境变量安全

```bash
# 生产环境必须更改的变量
SECRET_KEY=CHANGE_ME_RANDOM_SECRET_KEY
JWT_SECRET_KEY=CHANGE_ME_RANDOM_JWT_SECRET_KEY
POSTGRES_PASSWORD=CHANGE_ME_STRONG_PASSWORD
```

## 监控和维护

### 健康检查

```bash
# 检查所有服务健康状态
./scripts/env-switch.sh production ps

# 访问健康检查端点
curl http://localhost/health
curl http://localhost:8000/health
```

### 日志管理

```bash
# 查看实时日志
./scripts/env-switch.sh production logs -f

# 查看特定时间段日志
docker compose -f docker/compose/production/docker-compose.yml logs --since="2024-01-01" --until="2024-01-02"
```

### 备份和恢复

```bash
# 数据库备份
docker compose -f docker/compose/production/docker-compose.yml exec postgres pg_dump -U quantuser quantplatform_prod > backup.sql

# 数据库恢复
docker compose -f docker/compose/production/docker-compose.yml exec -T postgres psql -U quantuser quantplatform_prod < backup.sql
```

## 最佳实践

1. **配置管理**
   - 使用环境变量管理配置差异
   - 敏感信息不要提交到版本控制
   - 定期验证配置文件

2. **部署流程**
   - 先在测试环境验证
   - 使用蓝绿部署或滚动更新
   - 保持配置文件的向后兼容性

3. **监控和告警**
   - 配置服务健康检查
   - 设置关键指标监控
   - 建立告警机制

4. **安全管理**
   - 定期更新依赖
   - 使用最小权限原则
   - 启用访问日志

## 迁移指南

### 从旧配置迁移

如果您正在从旧的配置结构迁移，请按以下步骤操作：

1. **备份现有配置**
   ```bash
   cp docker-compose.yml docker-compose.yml.backup
   cp -r nginx/ nginx.backup/
   ```

2. **使用新的配置结构**
   ```bash
   # 使用新的启动方式
   ./scripts/env-switch.sh local up -d
   ```

3. **验证迁移结果**
   ```bash
   python scripts/validate-config.py
   ```

## 支持和反馈

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 运行配置验证脚本
3. 查看相关日志文件
4. 联系开发团队获取支持

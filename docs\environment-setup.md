# 🔧 环境配置指南

本文档详细说明了量化投资平台的环境配置方法，包括开发环境和生产环境的设置。

## 📋 目录

- [概述](#概述)
- [后端环境配置](#后端环境配置)
- [前端环境配置](#前端环境配置)
- [数据库配置](#数据库配置)
- [开发环境设置](#开发环境设置)
- [生产环境设置](#生产环境设置)
- [常见问题](#常见问题)

## 概述

项目采用前后端分离架构，需要分别配置前端和后端环境。配置文件使用 `.env` 格式，支持不同环境的灵活切换。

### 环境文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `.env.example` | 配置模板 | 包含所有可用配置项和说明 |
| `.env` | 默认配置 | 实际使用的配置文件 |
| `.env.development` | 开发环境 | 开发时使用的配置 |
| `.env.production` | 生产环境 | 生产部署时使用的配置 |

## 后端环境配置

### 1. 创建环境配置文件

```bash
cd backend
cp .env.example .env
```

### 2. 基础配置项

#### 应用配置
```bash
# 应用基础信息
ENVIRONMENT=development
DEBUG=true
APP_NAME=量化投资后端API
APP_VERSION=1.0.0
HOST=0.0.0.0
PORT=8000
```

#### 数据库配置

**SQLite配置（开发环境推荐）**
```bash
DATABASE_URL=sqlite:///./data/quantplatform.db
DB_ECHO=false
```

**PostgreSQL配置（生产环境推荐）**
```bash
DATABASE_URL=postgresql://username:password@localhost:5432/quantplatform
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
```

#### 安全配置
```bash
# JWT密钥 - 生产环境必须更改
SECRET_KEY=your-secret-key-change-in-production-min-32-chars
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7
```

#### CORS配置
```bash
# 允许的前端域名
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:8080"]
```

#### 数据源配置
```bash
# Tushare数据源
MARKET_DATA_SOURCE=tushare
TUSHARE_TOKEN=your_tushare_token_here

# Yahoo Finance备用数据源
USE_YFINANCE=true
```

### 3. 可选配置

#### Redis缓存
```bash
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_CACHE_TTL=3600
```

#### WebSocket
```bash
WS_ENABLED=true
WS_MAX_CONNECTIONS=1000
```

#### 监控
```bash
ENABLE_MONITORING=false
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
SENTRY_DSN=your_sentry_dsn_here
```

## 前端环境配置

### 1. 创建环境配置文件

```bash
cd frontend
cp .env.example .env.development
```

### 2. 基础配置项

#### 应用配置
```bash
VITE_APP_TITLE=量化投资平台
VITE_APP_DESCRIPTION=专业的量化投资可视化平台
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
```

#### API配置
```bash
# 确保与后端地址匹配
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_API_TIMEOUT=10000
VITE_API_RETRY_ATTEMPTS=3
```

#### WebSocket配置
```bash
VITE_WS_URL=ws://localhost:8000/ws
VITE_WS_MARKET_URL=ws://localhost:8000/ws/market
VITE_WS_TRADING_URL=ws://localhost:8000/ws/trading
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_HEARTBEAT_INTERVAL=30000
```

#### 功能开关
```bash
# 开发功能
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true

# 应用功能
VITE_ENABLE_PWA=true
VITE_ENABLE_THEME_SWITCH=true

# 交易功能
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_REAL_TRADING=false
```

## 数据库配置

### SQLite配置（推荐用于开发）

**优点：**
- 无需安装额外软件
- 配置简单
- 适合开发和测试

**配置步骤：**
1. 使用默认SQLite配置：
   ```bash
   DATABASE_URL=sqlite:///./data/quantplatform.db
   ```

2. 运行数据库初始化：
   ```bash
   python init_database.py
   ```

### PostgreSQL配置（推荐用于生产）

**优点：**
- 高性能
- 支持并发
- 数据完整性

**配置步骤：**
1. 安装PostgreSQL
2. 创建数据库和用户：
   ```sql
   CREATE DATABASE quantplatform;
   CREATE USER quant_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE quantplatform TO quant_user;
   ```

3. 更新配置：
   ```bash
   DATABASE_URL=postgresql://quant_user:secure_password@localhost:5432/quantplatform
   ```

## 开发环境设置

### 快速启动

1. **后端启动**
   ```bash
   cd backend
   cp .env.example .env
   source venv/bin/activate
   pip install -r requirements-py313.txt
   python init_database.py
   python simple_main.py
   ```

2. **前端启动**
   ```bash
   cd frontend
   cp .env.example .env.development
   pnpm install
   pnpm dev
   ```

### 开发环境特定配置

```bash
# 后端开发配置
DEBUG=true
DB_ECHO=true
LOG_LEVEL=DEBUG
USE_MOCK_DATA=false

# 前端开发配置
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOG=true
VITE_DEBUG_API=true
VITE_SOURCE_MAP=true
```

## 生产环境设置

### 安全配置检查清单

- [ ] 更改默认SECRET_KEY
- [ ] 设置强密码的数据库连接
- [ ] 配置HTTPS
- [ ] 设置正确的CORS域名
- [ ] 禁用调试模式
- [ ] 配置错误监控

### 生产环境配置示例

**后端生产配置：**
```bash
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-super-secure-secret-key-32-chars-minimum
DATABASE_URL=postgresql://prod_user:<EMAIL>:5432/quantplatform_prod
BACKEND_CORS_ORIGINS=["https://your-domain.com"]
ENABLE_MONITORING=true
```

**前端生产配置：**
```bash
VITE_APP_ENVIRONMENT=production
VITE_API_BASE_URL=https://api.your-domain.com/api/v1
VITE_WS_URL=wss://api.your-domain.com/ws
VITE_ENABLE_DEVTOOLS=false
VITE_DEBUG_API=false
```

## 常见问题

### Q1: 数据库连接失败

**问题：** `Database connection failed`

**解决方案：**
1. 检查数据库URL格式是否正确
2. 确认数据库服务是否启动
3. 验证用户名密码是否正确
4. 检查防火墙设置

### Q2: CORS错误

**问题：** 前端无法访问后端API

**解决方案：**
1. 检查后端CORS配置：
   ```bash
   BACKEND_CORS_ORIGINS=["http://localhost:5173"]
   ```
2. 确保前端API地址正确：
   ```bash
   VITE_API_BASE_URL=http://localhost:8000/api/v1
   ```

### Q3: WebSocket连接失败

**问题：** 实时数据无法更新

**解决方案：**
1. 检查WebSocket URL配置
2. 确认后端WebSocket服务启动
3. 检查防火墙是否阻止WebSocket连接

### Q4: Tushare数据获取失败

**问题：** 无法获取股票数据

**解决方案：**
1. 获取有效的Tushare Token
2. 配置Token：
   ```bash
   TUSHARE_TOKEN=your_valid_token_here
   ```
3. 检查网络连接和API限制

### Q5: 依赖安装失败

**问题：** Python依赖编译错误

**解决方案：**
1. 使用Python 3.13兼容的requirements文件：
   ```bash
   pip install -r requirements-py313.txt
   ```
2. 使用预编译的wheel包
3. 检查系统依赖是否安装

## 环境变量验证

使用以下脚本验证环境配置：

**后端验证：**
```bash
python database_config.py  # 验证数据库配置
python test_data_libs.py   # 验证数据处理库
```

**前端验证：**
```bash
pnpm check  # 检查配置状态
```

## 总结

正确的环境配置是项目成功运行的关键。建议：

1. **开发环境**：使用SQLite + 本地配置，快速启动
2. **测试环境**：使用PostgreSQL + 完整配置，模拟生产
3. **生产环境**：使用PostgreSQL + 安全配置，性能优化

如有其他配置问题，请参考项目文档或提交Issue。
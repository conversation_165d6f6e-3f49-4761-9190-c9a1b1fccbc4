# CTP对接实施步骤指南

**适用场景**: 量化投资平台接入真实证券公司交易  
**技术栈**: Python + FastAPI + Vue3 + CTP API  
**预计时间**: 2-4周  

## 🎯 实施目标

将我们的交易终端 (http://localhost:5173/trading/terminal) 从模拟交易升级为真实交易，通过CTP接口对接券商系统。

## 📋 准备阶段 (第1周)

### 1.1 券商选择和开户

#### 推荐券商排序
1. **中信期货** ⭐⭐⭐⭐⭐
   - 优势: CTP接口稳定，技术支持好，手续费合理
   - 开户要求: 50万资金，适合机构客户
   - 联系方式: 400-990-8826

2. **华泰期货** ⭐⭐⭐⭐
   - 优势: 系统稳定，API文档完善
   - 开户要求: 10万资金，个人和机构均可
   - 联系方式: 400-682-5599

3. **国泰君安期货** ⭐⭐⭐⭐
   - 优势: 大型券商，服务完善
   - 开户要求: 20万资金
   - 联系方式: 400-888-8666

#### 开户流程
```markdown
第1步: 联系券商客户经理
- 说明是程序化交易需求
- 询问CTP接口开通条件
- 了解手续费标准

第2步: 准备开户材料
- 身份证正反面
- 银行卡照片
- 手写签名照片
- 风险测评问卷

第3步: 完成开户流程
- 在线开户或现场开户
- 签署相关协议
- 入金激活账户

第4步: 申请CTP权限
- 提交程序化交易申请
- 获取CTP认证码和AppID
- 获取交易前置地址
```

### 1.2 技术环境准备

#### 服务器环境
```bash
# 推荐配置
CPU: 4核心以上
内存: 8GB以上  
硬盘: SSD 100GB以上
网络: 电信/联通专线，延迟<10ms
操作系统: Ubuntu 20.04 LTS 或 CentOS 8

# 网络要求
- 到券商服务器延迟 < 10ms
- 带宽 > 10Mbps
- 99.9%可用性
```

#### 软件环境
```bash
# 1. 安装Python环境
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 2. 安装数据库
sudo apt install postgresql-14 redis-server

# 3. 安装CTP依赖
sudo apt install build-essential cmake
pip install openctp-ctp
```

## 🔧 开发阶段 (第2周)

### 2.1 CTP接口集成

#### 安装CTP SDK
```bash
# 下载CTP API
wget http://www.sfit.com.cn/api/ctp_api_linux64.tar.gz
tar -xzf ctp_api_linux64.tar.gz

# 安装Python绑定
pip install openctp-ctp
```

#### 配置CTP连接
```python
# backend/config/ctp_config.py
CTP_CONFIG = {
    # 基础配置 (从券商获取)
    "broker_id": "您的券商代码",        # 如: 66666
    "user_id": "您的交易账户",          # 如: 123456789
    "password": "您的交易密码",         # 交易密码
    "auth_code": "您的认证码",          # 券商提供
    "app_id": "您的应用ID",            # 券商分配
    
    # 服务器地址 (从券商获取)
    "trade_front": "tcp://券商交易服务器:端口",
    "md_front": "tcp://券商行情服务器:端口",
    
    # 连接配置
    "heartbeat_interval": 30,
    "timeout": 10,
    "retry_times": 3
}
```

#### 测试CTP连接
```python
# test_ctp_connection.py
import asyncio
from app.services.ctp_service import CTPService

async def test_connection():
    ctp_service = CTPService()
    
    # 测试连接
    success = await ctp_service.initialize()
    if success:
        print("✅ CTP连接成功")
        
        # 测试查询账户
        account = await ctp_service.query_account()
        print(f"账户资金: {account.available}")
        
        # 测试查询持仓
        positions = await ctp_service.query_positions()
        print(f"持仓数量: {len(positions)}")
        
    else:
        print("❌ CTP连接失败")

if __name__ == "__main__":
    asyncio.run(test_connection())
```

### 2.2 交易功能开发

#### 订单提交功能
```python
# app/services/trading_service.py
class TradingService:
    async def submit_order(self, order_data: dict) -> dict:
        """提交订单到CTP"""
        try:
            # 1. 风险检查
            risk_check = await self.risk_service.check_order(order_data)
            if not risk_check.passed:
                raise Exception(f"风险检查失败: {risk_check.message}")
            
            # 2. 提交到CTP
            ctp_order = await self.ctp_service.submit_order(order_data)
            
            # 3. 保存到数据库
            db_order = await self.save_order_to_db(ctp_order)
            
            # 4. 推送到前端
            await self.websocket_manager.broadcast_order_update(db_order)
            
            return {
                "success": True,
                "order_id": db_order.id,
                "message": "订单提交成功"
            }
            
        except Exception as e:
            logger.error(f"订单提交失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }
```

#### 实时数据推送
```python
# app/services/websocket_manager.py
class WebSocketManager:
    async def handle_ctp_callback(self, data_type: str, data: dict):
        """处理CTP回调数据"""
        if data_type == "order_update":
            # 订单状态更新
            await self.broadcast_to_user(
                data["user_id"], 
                {"type": "order_update", "data": data}
            )
            
        elif data_type == "trade_update":
            # 成交回报
            await self.broadcast_to_user(
                data["user_id"],
                {"type": "trade_update", "data": data}
            )
            
        elif data_type == "position_update":
            # 持仓更新
            await self.broadcast_to_user(
                data["user_id"],
                {"type": "position_update", "data": data}
            )
```

## 🧪 测试阶段 (第3周)

### 3.1 仿真环境测试

#### SimNow仿真测试
```bash
# 1. 配置仿真环境
export CTP_BROKER_ID=9999
export CTP_USER_ID=您的仿真账户
export CTP_PASSWORD=您的仿真密码
export CTP_AUTH_CODE=0000000000000000
export CTP_APP_ID=simnow_client_test

# 2. 启动服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001

# 3. 运行测试脚本
python tests/test_trading_flow.py
```

#### 测试用例
```python
# tests/test_trading_flow.py
import pytest
from app.services.trading_service import TradingService

class TestTradingFlow:
    async def test_order_submit(self):
        """测试订单提交"""
        order_data = {
            "symbol": "rb2501",
            "direction": "BUY",
            "offset": "OPEN", 
            "order_type": "LIMIT",
            "price": 3500.0,
            "volume": 1
        }
        
        result = await self.trading_service.submit_order(order_data)
        assert result["success"] == True
        assert "order_id" in result
        
    async def test_order_cancel(self):
        """测试订单撤销"""
        # 先提交订单
        order = await self.submit_test_order()
        
        # 撤销订单
        result = await self.trading_service.cancel_order(order["order_id"])
        assert result["success"] == True
        
    async def test_position_query(self):
        """测试持仓查询"""
        positions = await self.trading_service.query_positions()
        assert isinstance(positions, list)
```

### 3.2 压力测试

#### 并发订单测试
```python
# tests/stress_test.py
import asyncio
import aiohttp

async def submit_concurrent_orders(session, order_count: int):
    """并发提交订单测试"""
    tasks = []
    for i in range(order_count):
        task = submit_single_order(session, i)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    success_count = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
    print(f"成功提交订单: {success_count}/{order_count}")

async def submit_single_order(session, order_id: int):
    """提交单个订单"""
    order_data = {
        "symbol": "rb2501",
        "direction": "BUY",
        "price": 3500 + order_id,  # 不同价格避免重复
        "volume": 1
    }
    
    async with session.post("/api/v1/trading/orders", json=order_data) as resp:
        return await resp.json()

# 运行压力测试
asyncio.run(submit_concurrent_orders(session, 100))
```

## 🚀 部署阶段 (第4周)

### 4.1 生产环境部署

#### Docker部署配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  trading-app:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    environment:
      - CTP_BROKER_ID=${CTP_BROKER_ID}
      - CTP_USER_ID=${CTP_USER_ID}
      - CTP_PASSWORD=${CTP_PASSWORD}
      - CTP_AUTH_CODE=${CTP_AUTH_CODE}
      - CTP_APP_ID=${CTP_APP_ID}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8001:8000"
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "443:443"
      - "80:80"
    depends_on:
      - trading-app
```

#### 监控配置
```python
# monitoring/alerts.py
class TradingAlerts:
    def __init__(self):
        self.alert_rules = {
            "ctp_disconnected": {
                "condition": "ctp_status == 'disconnected'",
                "action": "send_sms_and_email",
                "priority": "critical"
            },
            "order_reject_rate_high": {
                "condition": "order_reject_rate > 0.1",
                "action": "send_email", 
                "priority": "warning"
            },
            "daily_loss_limit": {
                "condition": "daily_pnl < -50000",
                "action": "stop_trading_and_alert",
                "priority": "critical"
            }
        }
```

### 4.2 上线检查清单

#### 技术检查
- [ ] CTP连接稳定性测试通过
- [ ] 订单提交和撤销功能正常
- [ ] 实时数据推送正常
- [ ] 风险控制系统正常
- [ ] 数据库备份策略已配置
- [ ] 日志系统正常工作
- [ ] 监控报警系统已配置
- [ ] SSL证书已配置
- [ ] 防火墙规则已设置

#### 业务检查  
- [ ] 券商账户资金已到位
- [ ] 交易权限已开通
- [ ] 风险参数已设置
- [ ] 应急预案已制定
- [ ] 操作人员已培训
- [ ] 客服联系方式已确认

#### 安全检查
- [ ] 密码强度符合要求
- [ ] API密钥安全存储
- [ ] 访问权限最小化
- [ ] 审计日志已启用
- [ ] 备份恢复已测试

## 📞 应急联系方式

### 券商技术支持
- **中信期货技术支持**: 400-990-8826
- **华泰期货技术支持**: 400-682-5599
- **CTP技术支持QQ群**: 129586618

### 系统监控
- **服务器监控**: 24小时自动监控
- **交易监控**: 实时风险监控
- **报警通知**: 短信+邮件+电话

---

**成功标准**: 
✅ CTP连接稳定，延迟<100ms  
✅ 订单成功率>99%  
✅ 系统可用性>99.9%  
✅ 风险控制有效  
✅ 监控报警正常

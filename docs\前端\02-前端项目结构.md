# 项目结构详细说明

## 📁 完整目录结构

```
quant-platform/
├── .github/                          # GitHub配置
│   ├── workflows/                   # CI/CD工作流
│   │   ├── build.yml               # 构建流程
│   │   ├── test.yml                # 测试流程
│   │   └── deploy.yml              # 部署流程
│   ├── ISSUE_TEMPLATE/             # Issue模板
│   └── PULL_REQUEST_TEMPLATE.md    # PR模板
├── .vscode/                         # VSCode配置
│   ├── extensions.json             # 推荐扩展
│   ├── settings.json               # 编辑器设置
│   └── launch.json                 # 调试配置
├── docs/                           # 项目文档
│   ├── api/                        # API文档
│   ├── components/                 # 组件文档
│   ├── deployment/                 # 部署文档
│   └── development/                # 开发文档
├── public/                         # 静态资源
│   ├── favicon.ico                 # 网站图标
│   ├── manifest.json               # PWA配置
│   ├── robots.txt                  # 爬虫配置
│   ├── icons/                      # 应用图标集
│   │   ├── icon-192x192.png
│   │   ├── icon-512x512.png
│   │   └── apple-touch-icon.png
│   ├── sounds/                     # 音效文件
│   │   ├── notification.mp3        # 通知音效
│   │   ├── trade-success.mp3       # 交易成功音效
│   │   └── alert.mp3               # 警告音效
│   └── mock-data/                  # 模拟数据
│       ├── market-data.json        # 行情数据
│       ├── trading-data.json       # 交易数据
│       └── strategy-data.json      # 策略数据
├── src/                            # 源代码目录
│   ├── api/                        # API接口层
│   │   ├── index.ts                # API统一导出
│   │   ├── http.ts                 # HTTP客户端配置
│   │   ├── types.ts                # API类型定义
│   │   ├── interceptors/           # 请求/响应拦截器
│   │   │   ├── request.ts          # 请求拦截器
│   │   │   ├── response.ts         # 响应拦截器
│   │   │   └── auth.ts             # 认证拦截器
│   │   ├── modules/                # API模块
│   │   │   ├── auth.ts             # 认证相关API
│   │   │   ├── user.ts             # 用户相关API
│   │   │   ├── market.ts           # 行情数据API
│   │   │   ├── trading.ts          # 交易相关API
│   │   │   ├── strategy.ts         # 策略管理API
│   │   │   ├── backtest.ts         # 回测分析API
│   │   │   ├── portfolio.ts        # 投资组合API
│   │   │   ├── risk.ts             # 风险管理API
│   │   │   └── notification.ts     # 通知相关API
│   │   └── websocket/              # WebSocket连接
│   │       ├── index.ts            # WebSocket主文件
│   │       ├── market.ts           # 行情WebSocket
│   │       ├── trading.ts          # 交易WebSocket
│   │       └── notification.ts     # 通知WebSocket
│   ├── assets/                     # 静态资源
│   │   ├── images/                 # 图片资源
│   │   │   ├── logos/              # Logo图片
│   │   │   ├── backgrounds/        # 背景图片
│   │   │   ├── charts/             # 图表相关图片
│   │   │   └── error-pages/        # 错误页面图片
│   │   ├── icons/                  # 图标文件
│   │   │   ├── svg/                # SVG图标
│   │   │   └── png/                # PNG图标
│   │   ├── fonts/                  # 字体文件
│   │   │   ├── inter/              # Inter字体
│   │   │   └── jetbrains-mono/     # JetBrains Mono字体
│   │   └── styles/                 # 全局样式
│   │       ├── index.scss          # 样式入口文件
│   │       ├── reset.scss          # 样式重置
│   │       ├── variables.scss      # SCSS变量
│   │       ├── mixins.scss         # SCSS混入
│   │       ├── base.scss           # 基础样式
│   │       ├── components.scss     # 组件样式
│   │       ├── utilities.scss      # 工具类样式
│   │       └── themes/             # 主题样式
│   │           ├── light.scss      # 明亮主题
│   │           ├── dark.scss       # 暗黑主题
│   │           └── high-contrast.scss # 高对比度主题
│   ├── components/                 # 组件目录
│   │   ├── common/                 # 通用基础组件
│   │   │   ├── AppButton/          # 按钮组件
│   │   │   │   ├── index.vue
│   │   │   │   ├── types.ts
│   │   │   │   └── styles.scss
│   │   │   ├── AppCard/            # 卡片组件
│   │   │   ├── AppModal/           # 模态框组件
│   │   │   ├── AppTable/           # 表格组件
│   │   │   ├── AppForm/            # 表单组件
│   │   │   ├── AppInput/           # 输入框组件
│   │   │   ├── AppSelect/          # 选择器组件
│   │   │   ├── AppDatePicker/      # 日期选择器
│   │   │   ├── AppUpload/          # 文件上传组件
│   │   │   ├── AppPagination/      # 分页组件
│   │   │   ├── VirtualList/        # 虚拟滚动列表
│   │   │   ├── VirtualTable/       # 虚拟滚动表格
│   │   │   ├── LazyImage/          # 图片懒加载
│   │   │   ├── ErrorBoundary/      # 错误边界
│   │   │   ├── LoadingSpinner/     # 加载动画
│   │   │   └── EmptyState/         # 空状态组件
│   │   ├── charts/                 # 图表组件
│   │   │   ├── BaseChart/          # 基础图表组件
│   │   │   ├── KLineChart/         # K线图
│   │   │   │   ├── index.vue
│   │   │   │   ├── composables/    # K线图相关组合函数
│   │   │   │   │   ├── useKLineData.ts
│   │   │   │   │   ├── useIndicators.ts
│   │   │   │   │   └── useChartEvents.ts
│   │   │   │   ├── types.ts
│   │   │   │   └── config.ts       # 图表配置
│   │   │   ├── LineChart/          # 折线图
│   │   │   ├── BarChart/           # 柱状图
│   │   │   ├── PieChart/           # 饼图
│   │   │   ├── ScatterChart/       # 散点图
│   │   │   ├── HeatmapChart/       # 热力图
│   │   │   ├── VolumeChart/        # 成交量图
│   │   │   ├── DepthChart/         # 深度图
│   │   │   ├── IndicatorChart/     # 技术指标图
│   │   │   ├── TreemapChart/       # 矩形树图
│   │   │   └── GaugeChart/         # 仪表盘图
│   │   ├── trading/                # 交易相关组件
│   │   │   ├── OrderForm/          # 下单表单
│   │   │   ├── OrderBook/          # 委托订单簿
│   │   │   ├── TradePanel/         # 交易面板
│   │   │   ├── PositionList/       # 持仓列表
│   │   │   ├── OrderHistory/       # 订单历史
│   │   │   ├── TradeHistory/       # 成交历史
│   │   │   ├── AccountInfo/        # 账户信息
│   │   │   ├── RiskMonitor/        # 风险监控
│   │   │   ├── TradingTerminal/    # 交易终端
│   │   │   ├── QuickTrade/         # 快速交易
│   │   │   ├── ConditionalOrder/   # 条件单
│   │   │   └── TradingRules/       # 交易规则
│   │   ├── market/                 # 行情相关组件
│   │   │   ├── MarketOverview/     # 市场概览
│   │   │   ├── StockList/          # 股票列表
│   │   │   ├── QuoteCard/          # 行情卡片
│   │   │   ├── Watchlist/          # 自选股
│   │   │   ├── MarketSearch/       # 行情搜索
│   │   │   ├── SectorAnalysis/     # 板块分析
│   │   │   ├── MarketNews/         # 市场资讯
│   │   │   ├── EconomicCalendar/   # 经济日历
│   │   │   ├── MarketHeatmap/      # 市场热力图
│   │   │   ├── TopMovers/          # 涨跌幅榜
│   │   │   ├── VolumeRanking/      # 成交量排行
│   │   │   └── MarketIndicators/   # 市场指标
│   │   ├── strategy/               # 策略相关组件
│   │   │   ├── StrategyList/       # 策略列表
│   │   │   ├── StrategyCard/       # 策略卡片
│   │   │   ├── StrategyForm/       # 策略表单
│   │   │   ├── PerformanceChart/   # 绩效图表
│   │   │   ├── StrategyMonitor/    # 策略监控
│   │   │   ├── BacktestReport/     # 回测报告
│   │   │   ├── ParameterOptimizer/ # 参数优化器
│   │   │   ├── StrategyComparison/ # 策略对比
│   │   │   ├── RiskAnalysis/       # 风险分析
│   │   │   ├── SignalAnalysis/     # 信号分析
│   │   │   └── StrategyTemplate/   # 策略模板
│   │   ├── backtest/               # 回测相关组件
│   │   │   ├── BacktestForm/       # 回测表单
│   │   │   ├── BacktestProgress/   # 回测进度
│   │   │   ├── ResultsTable/       # 结果表格
│   │   │   ├── EquityCurve/        # 净值曲线
│   │   │   ├── DrawdownChart/      # 回撤图表
│   │   │   ├── TradeAnalysis/      # 交易分析
│   │   │   ├── ReturnDistribution/ # 收益分布
│   │   │   ├── MonthlyReturns/     # 月度收益
│   │   │   ├── RiskMetrics/        # 风险指标
│   │   │   └── BacktestComparison/ # 回测对比
│   │   ├── layout/                 # 布局组件
│   │   │   ├── AppHeader/          # 页面头部
│   │   │   ├── AppSidebar/         # 侧边栏
│   │   │   ├── AppFooter/          # 页面底部
│   │   │   ├── BreadcrumbNav/      # 面包屑导航
│   │   │   ├── TabNavigation/      # 标签页导航
│   │   │   ├── QuickActions/       # 快速操作
│   │   │   ├── NotificationCenter/ # 通知中心
│   │   │   ├── UserMenu/           # 用户菜单
│   │   │   ├── ThemeToggle/        # 主题切换
│   │   │   └── FullscreenToggle/   # 全屏切换
│   │   └── widgets/                # 小部件组件
│   │       ├── ClockWidget/        # 时钟小部件
│   │       ├── WeatherWidget/      # 天气小部件
│   │       ├── NewsWidget/         # 新闻小部件
│   │       ├── CalendarWidget/     # 日历小部件
│   │       ├── TodoWidget/         # 待办事项
│   │       ├── NotesWidget/        # 笔记小部件
│   │       ├── CalculatorWidget/   # 计算器
│   │       ├── PerformanceWidget/  # 性能监控
│   │       └── SystemInfoWidget/   # 系统信息
│   ├── composables/                # 组合式函数
│   │   ├── core/                   # 核心组合函数
│   │   │   ├── useLocalStorage.ts  # 本地存储
│   │   │   ├── useSessionStorage.ts # 会话存储
│   │   │   ├── useClipboard.ts     # 剪贴板操作
│   │   │   ├── useEventListener.ts # 事件监听
│   │   │   ├── useFullscreen.ts    # 全屏控制
│   │   │   ├── useKeyboard.ts      # 键盘快捷键
│   │   │   ├── useMediaQuery.ts    # 媒体查询
│   │   │   ├── useTheme.ts         # 主题切换
│   │   │   ├── useTitle.ts         # 页面标题
│   │   │   └── usePermission.ts    # 权限控制
│   │   ├── data/                   # 数据相关组合函数
│   │   │   ├── useApi.ts           # API调用
│   │   │   ├── useWebSocket.ts     # 原生WebSocket连接
│   │   │   ├── useCache.ts         # 缓存管理
│   │   │   ├── usePagination.ts    # 分页逻辑
│   │   │   ├── useSearch.ts        # 搜索功能
│   │   │   ├── useFilter.ts        # 过滤功能
│   │   │   ├── useSort.ts          # 排序功能
│   │   │   └── useValidation.ts    # 表单验证
│   │   ├── chart/                  # 图表相关组合函数
│   │   │   ├── useChart.ts         # 基础图表
│   │   │   ├── useKLine.ts         # K线图
│   │   │   ├── useIndicators.ts    # 技术指标
│   │   │   ├── useChartData.ts     # 图表数据
│   │   │   ├── useChartEvents.ts   # 图表事件
│   │   │   ├── useChartResize.ts   # 图表尺寸
│   │   │   └── useChartTheme.ts    # 图表主题
│   │   ├── trading/                # 交易相关组合函数
│   │   │   ├── useTradingData.ts   # 交易数据
│   │   │   ├── useOrderBook.ts     # 订单簿
│   │   │   ├── usePositions.ts     # 持仓管理
│   │   │   ├── useOrders.ts        # 订单管理
│   │   │   ├── useAccount.ts       # 账户信息
│   │   │   ├── useRisk.ts          # 风险控制
│   │   │   └── useTradingRules.ts  # 交易规则
│   │   ├── market/                 # 行情相关组合函数
│   │   │   ├── useMarketData.ts    # 行情数据
│   │   │   ├── useQuotes.ts        # 报价数据
│   │   │   ├── useWatchlist.ts     # 自选股
│   │   │   ├── useMarketSearch.ts  # 行情搜索
│   │   │   ├── useMarketNews.ts    # 市场资讯
│   │   │   └── useMarketCalendar.ts # 市场日历
│   │   ├── strategy/               # 策略相关组合函数
│   │   │   ├── useStrategy.ts      # 策略管理
│   │   │   ├── useBacktest.ts      # 回测功能
│   │   │   ├── usePerformance.ts   # 绩效分析
│   │   │   ├── useOptimization.ts  # 参数优化
│   │   │   └── useSignalAnalysis.ts # 信号分析
│   │   └── ui/                     # UI相关组合函数
│   │       ├── useModal.ts         # 模态框
│   │       ├── useToast.ts         # 消息提示
│   │       ├── useLoading.ts       # 加载状态
│   │       ├── useDialog.ts        # 对话框
│   │       ├── useDropdown.ts      # 下拉菜单
│   │       ├── useTooltip.ts       # 工具提示
│   │       ├── useNotification.ts  # 通知
│   │       └── useContextMenu.ts   # 右键菜单
│   ├── layouts/                    # 页面布局
│   │   ├── DefaultLayout.vue       # 默认布局
│   │   ├── TradingLayout.vue       # 交易布局
│   │   ├── AnalysisLayout.vue      # 分析布局
│   │   ├── SimpleLayout.vue        # 简单布局
│   │   ├── FullscreenLayout.vue    # 全屏布局
│   │   └── PrintLayout.vue         # 打印布局
│   ├── plugins/                    # 插件目录
│   │   ├── element-plus.ts         # Element Plus配置
│   │   ├── echarts.ts              # ECharts配置
│   │   ├── axios.ts                # Axios配置
│   │   ├── pinia.ts                # Pinia配置
│   │   ├── router.ts               # Router配置
│   │   ├── i18n.ts                 # 国际化配置
│   │   ├── sentry.ts               # 错误监控配置
│   │   └── pwa.ts                  # PWA配置
│   ├── router/                     # 路由配置
│   │   ├── index.ts                # 路由主文件
│   │   ├── guards.ts               # 路由守卫
│   │   ├── constants.ts            # 路由常量
│   │   └── modules/                # 路由模块
│   │       ├── auth.ts             # 认证路由
│   │       ├── dashboard.ts        # 仪表盘路由
│   │       ├── market.ts           # 行情路由
│   │       ├── trading.ts          # 交易路由
│   │       ├── strategy.ts         # 策略路由
│   │       ├── backtest.ts         # 回测路由
│   │       ├── portfolio.ts        # 投资组合路由
│   │       ├── risk.ts             # 风险管理路由
│   │       ├── settings.ts         # 设置路由
│   │       └── error.ts            # 错误页面路由
│   ├── services/                   # 服务层
│   │   ├── auth.service.ts         # 认证服务
│   │   ├── user.service.ts         # 用户服务
│   │   ├── market.service.ts       # 行情服务
│   │   ├── trading.service.ts      # 交易服务
│   │   ├── strategy.service.ts     # 策略服务
│   │   ├── backtest.service.ts     # 回测服务
│   │   ├── notification.service.ts # 通知服务
│   │   ├── cache.service.ts        # 缓存服务
│   │   ├── storage.service.ts      # 存储服务
│   │   └── analytics.service.ts    # 分析服务
│   ├── stores/                     # 状态管理
│   │   ├── index.ts                # Pinia配置
│   │   ├── modules/                # Store模块
│   │   │   ├── auth.ts             # 认证状态
│   │   │   ├── user.ts             # 用户状态
│   │   │   ├── market.ts           # 行情数据状态
│   │   │   ├── trading.ts          # 交易状态
│   │   │   ├── strategy.ts         # 策略状态
│   │   │   ├── backtest.ts         # 回测状态
│   │   │   ├── portfolio.ts        # 投资组合状态
│   │   │   ├── notification.ts     # 通知状态
│   │   │   ├── settings.ts         # 设置状态
│   │   │   └── ui.ts               # UI状态
│   │   └── plugins/                # Store插件
│   │       ├── persistence.ts      # 持久化插件
│   │       ├── reset.ts            # 重置插件
│   │       └── logger.ts           # 日志插件
│   ├── types/                      # TypeScript类型
│   │   ├── index.ts                # 类型统一导出
│   │   ├── global.d.ts             # 全局类型声明
│   │   ├── env.d.ts                # 环境变量类型
│   │   ├── vue.d.ts                # Vue相关类型
│   │   ├── api/                    # API类型
│   │   │   ├── auth.ts             # 认证相关类型
│   │   │   ├── user.ts             # 用户相关类型
│   │   │   ├── market.ts           # 行情相关类型
│   │   │   ├── trading.ts          # 交易相关类型
│   │   │   ├── strategy.ts         # 策略相关类型
│   │   │   ├── backtest.ts         # 回测相关类型
│   │   │   └── common.ts           # 通用API类型
│   │   ├── components/             # 组件类型
│   │   │   ├── chart.ts            # 图表组件类型
│   │   │   ├── form.ts             # 表单组件类型
│   │   │   ├── table.ts            # 表格组件类型
│   │   │   └── common.ts           # 通用组件类型
│   │   ├── store/                  # Store类型
│   │   │   ├── auth.ts             # 认证Store类型
│   │   │   ├── trading.ts          # 交易Store类型
│   │   │   └── common.ts           # 通用Store类型
│   │   └── utils/                  # 工具类型
│   │       ├── date.ts             # 日期相关类型
│   │       ├── format.ts           # 格式化相关类型
│   │       └── validation.ts       # 验证相关类型
│   ├── utils/                      # 工具函数
│   │   ├── index.ts                # 工具函数统一导出
│   │   ├── constants.ts            # 常量定义
│   │   ├── env.ts                  # 环境变量处理
│   │   ├── auth/                   # 认证相关工具
│   │   │   ├── token.ts            # Token处理
│   │   │   ├── permission.ts       # 权限检查
│   │   │   └── encryption.ts       # 加密解密
│   │   ├── format/                 # 格式化工具
│   │   │   ├── number.ts           # 数字格式化
│   │   │   ├── currency.ts         # 货币格式化
│   │   │   ├── date.ts             # 日期格式化
│   │   │   ├── percent.ts          # 百分比格式化
│   │   │   └── text.ts             # 文本格式化
│   │   ├── validation/             # 验证工具
│   │   │   ├── rules.ts            # 验证规则
│   │   │   ├── schema.ts           # 验证模式
│   │   │   └── messages.ts         # 验证消息
│   │   ├── calculation/            # 计算工具
│   │   │   ├── financial.ts        # 金融计算
│   │   │   ├── statistics.ts       # 统计计算
│   │   │   ├── indicators.ts       # 技术指标计算
│   │   │   └── risk.ts             # 风险计算
│   │   ├── chart/                  # 图表工具
│   │   │   ├── theme.ts            # 图表主题
│   │   │   ├── config.ts           # 图表配置
│   │   │   ├── helpers.ts          # 图表辅助函数
│   │   │   └── export.ts           # 图表导出
│   │   ├── data/                   # 数据处理工具
│   │   │   ├── transform.ts        # 数据转换
│   │   │   ├── filter.ts           # 数据过滤
│   │   │   ├── sort.ts             # 数据排序
│   │   │   ├── group.ts            # 数据分组
│   │   │   └── aggregate.ts        # 数据聚合
│   │   ├── dom/                    # DOM工具
│   │   │   ├── element.ts          # 元素操作
│   │   │   ├── event.ts            # 事件处理
│   │   │   ├── style.ts            # 样式操作
│   │   │   └── scroll.ts           # 滚动控制
│   │   ├── storage/                # 存储工具
│   │   │   ├── localStorage.ts     # 本地存储
│   │   │   ├── sessionStorage.ts   # 会话存储
│   │   │   ├── indexedDB.ts        # IndexedDB
│   │   │   └── cache.ts            # 缓存管理
│   │   ├── network/                # 网络工具
│   │   │   ├── request.ts          # 请求工具
│   │   │   ├── retry.ts            # 重试机制
│   │   │   ├── timeout.ts          # 超时处理
│   │   │   └── websocket.ts        # WebSocket工具
│   │   ├── performance/            # 性能工具
│   │   │   ├── monitor.ts          # 性能监控
│   │   │   ├── profiler.ts         # 性能分析
│   │   │   ├── memory.ts           # 内存管理
│   │   │   └── fps.ts              # FPS监控
│   │   ├── security/               # 安全工具
│   │   │   ├── xss.ts              # XSS防护
│   │   │   ├── csrf.ts             # CSRF防护
│   │   │   ├── sanitize.ts         # 数据清理
│   │   │   └── encryption.ts       # 加密工具
│   │   └── debug/                  # 调试工具
│   │       ├── logger.ts           # 日志工具
│   │       ├── error.ts            # 错误处理
│   │       ├── performance.ts      # 性能调试
│   │       └── devtools.ts         # 开发工具
│   ├── views/                      # 页面视图
│   │   ├── Auth/                   # 认证页面
│   │   │   ├── LoginView.vue       # 登录页面
│   │   │   ├── RegisterView.vue    # 注册页面
│   │   │   ├── ForgotPasswordView.vue # 忘记密码
│   │   │   └── ResetPasswordView.vue # 重置密码
│   │   ├── Dashboard/              # 仪表盘
│   │   │   ├── DashboardView.vue   # 仪表盘主页
│   │   │   ├── OverviewPanel.vue   # 概览面板
│   │   │   ├── QuickActions.vue    # 快速操作
│   │   │   └── RecentActivity.vue  # 最近活动
│   │   ├── Market/                 # 行情中心
│   │   │   ├── MarketView.vue      # 行情主页
│   │   │   ├── StockDetailView.vue # 股票详情
│   │   │   ├── MarketAnalysisView.vue # 市场分析
│   │   │   ├── SectorView.vue      # 板块分析
│   │   │   ├── IndexView.vue       # 指数分析
│   │   │   └── NewsView.vue        # 市场资讯
│   │   ├── Trading/                # 交易中心
│   │   │   ├── TradingTerminalView.vue # 交易终端
│   │   │   ├── OrderManagementView.vue # 订单管理
│   │   │   ├── PositionManagementView.vue # 持仓管理
│   │   │   ├── TradeHistoryView.vue # 交易历史
│   │   │   ├── AccountView.vue     # 账户信息
│   │   │   └── RiskManagementView.vue # 风险管理
│   │   ├── Strategy/               # 策略中心
│   │   │   ├── StrategyHubView.vue # 策略中心首页
│   │   │   ├── StrategyDevelopView.vue # 策略开发
│   │   │   ├── StrategyMonitorView.vue # 策略监控
│   │   │   ├── StrategyAnalysisView.vue # 策略分析
│   │   │   ├── StrategyCompareView.vue # 策略对比
│   │   │   └── StrategyMarketplaceView.vue # 策略市场
│   │   ├── Backtest/               # 回测分析
│   │   │   ├── BacktestView.vue    # 回测主页
│   │   │   ├── CreateBacktestView.vue # 创建回测
│   │   │   ├── BacktestResultsView.vue # 回测结果
│   │   │   ├── BacktestHistoryView.vue # 回测历史
│   │   │   ├── BacktestCompareView.vue # 回测对比
│   │   │   └── OptimizationView.vue # 参数优化
│   │   ├── Portfolio/              # 投资组合
│   │   │   ├── PortfolioView.vue   # 投资组合主页
│   │   │   ├── PortfolioAnalysisView.vue # 组合分析
│   │   │   ├── AssetAllocationView.vue # 资产配置
│   │   │   ├── PerformanceView.vue # 绩效分析
│   │   │   └── RiskAnalysisView.vue # 风险分析
│   │   ├── Settings/               # 设置中心
│   │   │   ├── SettingsView.vue    # 设置主页
│   │   │   ├── UserProfileView.vue # 用户资料
│   │   │   ├── AccountSettingsView.vue # 账户设置
│   │   │   ├── TradingSettingsView.vue # 交易设置
│   │   │   ├── NotificationSettingsView.vue # 通知设置
│   │   │   ├── SecuritySettingsView.vue # 安全设置
│   │   │   └── PreferencesView.vue # 偏好设置
│   │   ├── Reports/                # 报告中心
│   │   │   ├── ReportsView.vue     # 报告主页
│   │   │   ├── ProfitLossView.vue  # 盈亏报告
│   │   │   ├── TaxReportView.vue   # 税务报告
│   │   │   ├── TradeReportView.vue # 交易报告
│   │   │   └── RiskReportView.vue  # 风险报告
│   │   ├── Error/                  # 错误页面
│   │   │   ├── 404View.vue         # 404页面
│   │   │   ├── 403View.vue         # 403页面
│   │   │   ├── 500View.vue         # 500页面
│   │   │   └── NetworkErrorView.vue # 网络错误
│   │   └── Help/                   # 帮助中心
│   │       ├── HelpView.vue        # 帮助主页
│   │       ├── DocumentationView.vue # 文档中心
│   │       ├── TutorialsView.vue   # 教程中心
│   │       ├── FAQView.vue         # 常见问题
│   │       └── ContactView.vue     # 联系我们
│   ├── workers/                    # Web Workers
│   │   ├── data-processor.worker.ts # 数据处理Worker
│   │   ├── chart-calculator.worker.ts # 图表计算Worker
│   │   ├── indicator-calculator.worker.ts # 指标计算Worker
│   │   └── backtest.worker.ts      # 回测计算Worker
│   ├── App.vue                     # 根组件
│   ├── main.ts                     # 应用入口
│   └── shims-vue.d.ts              # Vue类型声明
├── tests/                          # 测试目录
│   ├── __mocks__/                  # Mock文件
│   ├── e2e/                        # E2E测试
│   │   ├── specs/                  # 测试用例
│   │   ├── support/                # 测试支持文件
│   │   └── fixtures/               # 测试数据
│   ├── unit/                       # 单元测试
│   │   ├── components/             # 组件测试
│   │   ├── stores/                 # Store测试
│   │   ├── utils/                  # 工具函数测试
│   │   └── services/               # 服务测试
│   └── integration/                # 集成测试
├── .env.example                    # 环境变量示例
├── .env.development                # 开发环境变量
├── .env.production                 # 生产环境变量
├── .env.test                       # 测试环境变量
├── .eslintrc.cjs                   # ESLint配置
├── .gitignore                      # Git忽略文件
├── .npmrc                          # npm配置
├── .prettierrc.json                # Prettier配置
├── commitlint.config.cjs           # Commitlint配置
├── cypress.config.ts               # Cypress配置
├── index.html                      # HTML模板
├── package.json                    # 项目配置
├── pnpm-lock.yaml                  # pnpm锁定文件
├── postcss.config.js               # PostCSS配置
├── stylelint.config.cjs            # Stylelint配置
├── tailwind.config.js              # Tailwind配置
├── tsconfig.json                   # TypeScript配置
├── tsconfig.node.json              # Node环境TypeScript配置
├── vite.config.ts                  # Vite配置
├── vitest.config.ts                # Vitest配置
└── README.md                       # 项目说明
```

## 📂 目录说明

### 🏗️ 架构分层

#### 1. 表现层 (Presentation Layer)
- **views/**: 页面视图，负责页面级别的逻辑和布局
- **components/**: 可复用组件，按功能域划分
- **layouts/**: 页面布局模板

#### 2. 业务逻辑层 (Business Logic Layer)
- **composables/**: 组合式函数，封装业务逻辑
- **stores/**: 状态管理，全局状态和业务状态
- **services/**: 业务服务层，封装复杂业务逻辑

#### 3. 数据访问层 (Data Access Layer)
- **api/**: API接口层，与后端通信
- **utils/**: 工具函数，数据处理和转换

#### 4. 基础设施层 (Infrastructure Layer)
- **plugins/**: 第三方库配置
- **router/**: 路由配置
- **types/**: 类型定义

### 🎯 命名规范

#### 文件命名
- **组件文件**: PascalCase (如 `UserProfile.vue`)
- **页面文件**: PascalCase + View后缀 (如 `DashboardView.vue`)
- **工具文件**: camelCase (如 `formatCurrency.ts`)
- **类型文件**: camelCase (如 `tradingTypes.ts`)
- **常量文件**: camelCase (如 `apiConstants.ts`)

#### 目录命名
- **功能目录**: kebab-case (如 `user-profile/`)
- **组件目录**: PascalCase (如 `UserProfile/`)
- **工具目录**: camelCase (如 `formatters/`)

#### 变量命名
- **组件名**: PascalCase (如 `UserProfile`)
- **函数名**: camelCase (如 `getUserInfo`)
- **常量名**: SCREAMING_SNAKE_CASE (如 `API_BASE_URL`)
- **类型名**: PascalCase (如 `UserInfo`)

### 🔧 组件组织原则

#### 1. 原子化设计
```
组件层级：
├── 原子组件 (Atoms) - 最基础的UI元素
├── 分子组件 (Molecules) - 多个原子组件组合
├── 有机体组件 (Organisms) - 复杂的功能组件
├── 模板组件 (Templates) - 页面布局模板
└── 页面组件 (Pages) - 完整的页面视图
```

#### 2. 功能域划分
- **common/**: 通用基础组件
- **charts/**: 图表相关组件
- **trading/**: 交易功能组件
- **market/**: 行情功能组件
- **strategy/**: 策略功能组件
- **backtest/**: 回测功能组件

#### 3. 组件内部结构
```
ComponentName/
├── index.vue          # 主组件文件
├── types.ts           # 组件类型定义
├── styles.scss        # 组件样式 (可选)
├── constants.ts       # 组件常量 (可选)
├── composables/       # 组件专用组合函数 (可选)
└── __tests__/         # 组件测试 (可选)
```

---

## 📖 下一步阅读

1. [技术架构设计](./03-前端技术架构.md)
2. [核心组件实现](./04-前端核心组件.md)
3. [配置文件详解](./05-前端配置文件.md) 
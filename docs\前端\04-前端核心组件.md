# 核心组件实现

## 📊 图表组件

### K线图组件

```vue
<!-- src/components/charts/KLineChart/index.vue -->
<template>
  <div class="kline-chart">
    <div class="chart-header">
      <div class="chart-title">
        <h3>{{ symbol }} - {{ symbolName }}</h3>
        <span class="chart-subtitle">{{ currentPeriodLabel }}</span>
      </div>
      
      <div class="chart-toolbar">
        <!-- 时间周期选择 -->
        <el-button-group class="period-selector">
          <el-button
            v-for="period in timePeriods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            size="small"
            @click="changePeriod(period.value)"
          >
            {{ period.label }}
          </el-button>
        </el-button-group>
        
        <!-- 指标选择 -->
        <el-dropdown @command="addIndicator">
          <el-button size="small">
            添加指标 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                v-for="indicator in availableIndicators"
                :key="indicator.value"
                :command="indicator.value"
                :disabled="activeIndicators.includes(indicator.value)"
              >
                {{ indicator.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <!-- 全屏切换 -->
        <el-button 
          size="small" 
          :icon="fullscreen ? 'FullScreen' : 'FullScreen'" 
          @click="toggleFullscreen"
        />
      </div>
    </div>
    
    <div 
      ref="chartContainer" 
      class="chart-container"
      :class="{ 'fullscreen': fullscreen }"
    />
    
    <!-- 指标配置面板 -->
    <div v-if="showIndicatorPanel" class="indicator-panel">
      <div class="panel-header">
        <span>指标设置</span>
        <el-button text @click="showIndicatorPanel = false">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="panel-content">
        <div 
          v-for="indicator in activeIndicators" 
          :key="indicator"
          class="indicator-item"
        >
          <div class="indicator-name">{{ getIndicatorLabel(indicator) }}</div>
          <div class="indicator-controls">
            <!-- 动态指标参数配置 -->
            <component 
              :is="getIndicatorConfigComponent(indicator)"
              :config="indicatorConfigs[indicator]"
              @update="updateIndicatorConfig(indicator, $event)"
            />
            <el-button 
              size="small" 
              text 
              type="danger"
              @click="removeIndicator(indicator)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ArrowDown, Close, FullScreen } from '@element-plus/icons-vue'
import { useChart } from '@/composables/chart/useChart'
import { useKLineData } from '@/composables/chart/useKLineData'
import { useIndicators } from '@/composables/chart/useIndicators'
import { useFullscreen } from '@/composables/core/useFullscreen'
import type { TimePeriod, KLineData, IndicatorType } from '@/types/chart'

interface Props {
  symbol: string
  symbolName?: string
  height?: string
  autoResize?: boolean
  showToolbar?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  symbolName: '',
  height: '400px',
  autoResize: true,
  showToolbar: true
})

const emit = defineEmits<{
  (e: 'period-change', period: TimePeriod): void
  (e: 'data-update', data: KLineData[]): void
  (e: 'indicator-add', indicator: IndicatorType): void
  (e: 'indicator-remove', indicator: IndicatorType): void
}>()

// 图表容器引用
const chartContainer = ref<HTMLElement>()

// 图表相关
const { 
  chart, 
  loading, 
  initChart, 
  updateChart, 
  showLoading, 
  hideLoading 
} = useChart(chartContainer)

// K线数据相关
const {
  klineData,
  selectedPeriod,
  timePeriods,
  currentPeriodLabel,
  fetchKLineData,
  subscribeRealtime,
  unsubscribeRealtime
} = useKLineData()

// 技术指标相关 - 从后端获取计算结果
const {
  availableIndicators,
  activeIndicators,
  indicatorConfigs,
  calculatedIndicators,
  addIndicator,
  removeIndicator,
  updateIndicatorConfig,
  getIndicatorLabel,
  getIndicatorConfigComponent
} = useIndicators()

// 全屏相关
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen(chartContainer)
const fullscreen = computed(() => isFullscreen.value)

// UI状态
const showIndicatorPanel = ref(false)

// 计算属性
const chartOption = computed(() => {
  if (!klineData.value.length) return null

  const baseOption = {
    animation: false,
    grid: [
      {
        left: '10%',
        right: '8%',
        height: activeIndicators.value.length > 0 ? '50%' : '70%'
      },
      // 成交量网格
      {
        left: '10%',
        right: '8%',
        top: activeIndicators.value.length > 0 ? '65%' : '75%',
        height: '16%'
      },
      // 指标网格
      ...activeIndicators.value.map((_, index) => ({
        left: '10%',
        right: '8%',
        top: `${85 + index * 15}%`,
        height: '12%'
      }))
    ],
    xAxis: [
      // 主图X轴
      {
        type: 'category',
        data: klineData.value.map(item => item.timestamp),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        min: 'dataMin',
        max: 'dataMax',
        axisPointer: {
          z: 100
        }
      },
      // 成交量X轴
      {
        type: 'category',
        gridIndex: 1,
        data: klineData.value.map(item => item.timestamp),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        min: 'dataMin',
        max: 'dataMax'
      },
      // 指标X轴
      ...activeIndicators.value.map((_, index) => ({
        type: 'category',
        gridIndex: index + 2,
        data: klineData.value.map(item => item.timestamp),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        min: 'dataMin',
        max: 'dataMax'
      }))
    ],
    yAxis: [
      // 主图Y轴
      {
        scale: true,
        splitArea: { show: true }
      },
      // 成交量Y轴
      {
        scale: true,
        gridIndex: 1,
        splitNumber: 2,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      // 指标Y轴
      ...activeIndicators.value.map((_, index) => ({
        scale: true,
        gridIndex: index + 2,
        splitNumber: 2,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      }))
    ],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: Array.from({ length: activeIndicators.value.length + 2 }, (_, i) => i),
        start: 70,
        end: 100
      },
      {
        show: true,
        xAxisIndex: Array.from({ length: activeIndicators.value.length + 2 }, (_, i) => i),
        type: 'slider',
        top: '90%',
        start: 70,
        end: 100
      }
    ],
    series: [
      // K线图
      {
        name: 'K线',
        type: 'candlestick',
        data: klineData.value.map(item => [item.open, item.close, item.low, item.high]),
        itemStyle: {
          color: '#ec0000',
          color0: '#00da3c',
          borderColor: '#8A0000',
          borderColor0: '#008F28'
        },
        markPoint: {
          label: {
            formatter: function (param: any) {
              return param != null ? Math.round(param.value) + '' : ''
            }
          },
          data: [
            {
              name: 'highest value',
              type: 'max',
              valueDim: 'highest'
            },
            {
              name: 'lowest value',
              type: 'min',
              valueDim: 'lowest'
            }
          ],
          tooltip: {
            formatter: function (param: any) {
              return param.name + '<br>' + (param.data.coord || '')
            }
          }
        }
      },
      // 成交量
      {
        name: '成交量',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: klineData.value.map(item => item.volume),
        itemStyle: {
          color: function(params: any) {
            const dataIndex = params.dataIndex
            const klineItem = klineData.value[dataIndex]
            return klineItem.close > klineItem.open ? '#ec0000' : '#00da3c'
          }
        }
      },
      // 技术指标系列
      ...generateIndicatorSeries()
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: 'rgba(245, 245, 245, 0.8)',
      borderWidth: 1,
      borderColor: '#ccc',
      padding: 10,
      textStyle: {
        color: '#000'
      },
      formatter: function (params: any) {
        let result = `时间：${params[0].axisValue}<br/>`
        
        params.forEach((param: any) => {
          if (param.seriesName === 'K线') {
            const data = param.data
            result += `开盘：${data[1]}<br/>`
            result += `最高：${data[4]}<br/>`
            result += `最低：${data[3]}<br/>`
            result += `收盘：${data[2]}<br/>`
          } else if (param.seriesName === '成交量') {
            result += `成交量：${param.data}<br/>`
          } else {
            result += `${param.seriesName}：${param.data}<br/>`
          }
        })
        
        return result
      }
    }
  }

  return baseOption
})

// 生成指标系列
const generateIndicatorSeries = () => {
  const series: any[] = []
  
  activeIndicators.value.forEach((indicatorType, index) => {
    const indicatorData = calculatedIndicators.value[indicatorType]
    if (!indicatorData) return
    
    const gridIndex = index + 2
    const yAxisIndex = index + 2
    const xAxisIndex = index + 2
    
    // 根据指标类型生成不同的系列
    switch (indicatorType) {
      case 'MA':
        Object.entries(indicatorData).forEach(([period, data]) => {
          series.push({
            name: `MA${period}`,
            type: 'line',
            data,
            smooth: true,
            lineStyle: { width: 1 },
            showSymbol: false
          })
        })
        break
        
      case 'MACD':
        series.push(
          {
            name: 'DIF',
            type: 'line',
            xAxisIndex,
            yAxisIndex,
            data: indicatorData.dif,
            lineStyle: { color: '#2ec7c9', width: 1 },
            showSymbol: false
          },
          {
            name: 'DEA',
            type: 'line',
            xAxisIndex,
            yAxisIndex,
            data: indicatorData.dea,
            lineStyle: { color: '#b6a2de', width: 1 },
            showSymbol: false
          },
          {
            name: 'MACD',
            type: 'bar',
            xAxisIndex,
            yAxisIndex,
            data: indicatorData.macd,
            itemStyle: {
              color: function(params: any) {
                return params.data > 0 ? '#ec0000' : '#00da3c'
              }
            }
          }
        )
        break
        
      case 'RSI':
        series.push({
          name: 'RSI',
          type: 'line',
          xAxisIndex,
          yAxisIndex,
          data: indicatorData,
          lineStyle: { color: '#ffb980', width: 1 },
          showSymbol: false
        })
        break
    }
  })
  
  return series
}

// 切换时间周期
const changePeriod = async (period: TimePeriod) => {
  if (period === selectedPeriod.value) return
  
  showLoading('加载K线数据...')
  
  try {
    await fetchKLineData(props.symbol, period)
    emit('period-change', period)
    emit('data-update', klineData.value)
  } catch (error) {
    console.error('切换周期失败:', error)
  } finally {
    hideLoading()
  }
}

// 添加指标
const handleAddIndicator = (indicator: IndicatorType) => {
  addIndicator(indicator)
  emit('indicator-add', indicator)
  showIndicatorPanel.value = true
}

// 移除指标
const handleRemoveIndicator = (indicator: IndicatorType) => {
  removeIndicator(indicator)
  emit('indicator-remove', indicator)
  
  if (activeIndicators.value.length === 0) {
    showIndicatorPanel.value = false
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  await initChart()
  
  // 加载初始数据
  showLoading('加载K线数据...')
  
  try {
    await fetchKLineData(props.symbol, selectedPeriod.value)
    subscribeRealtime(props.symbol)
  } catch (error) {
    console.error('初始化数据加载失败:', error)
  } finally {
    hideLoading()
  }
})

// 清理
onUnmounted(() => {
  unsubscribeRealtime(props.symbol)
})

// 监听数据变化更新图表
watch(
  chartOption,
  (newOption) => {
    if (newOption && chart.value) {
      updateChart(newOption)
    }
  },
  { deep: true }
)

// 监听选中股票的行情更新
watch(selectedStock, (newStock, oldStock) => {
  if (oldStock) {
    unsubscribeQuote(oldStock.symbol)
  }
  
  if (newStock) {
    // 使用原生WebSocket订阅行情
    subscribeQuote(newStock.symbol)
  }
})
</script>

<style scoped>
.kline-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.chart-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.period-selector {
  display: flex;
}

.chart-container {
  flex: 1;
  min-height: 0;
  padding: 16px;
}

.chart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
}

.indicator-panel {
  position: absolute;
  top: 60px;
  right: 16px;
  width: 300px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
}

.panel-content {
  padding: 16px;
}

.indicator-item {
  margin-bottom: 16px;
}

.indicator-item:last-child {
  margin-bottom: 0;
}

.indicator-name {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.indicator-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-toolbar {
    width: 100%;
    justify-content: space-between;
  }
  
  .period-selector {
    flex-wrap: wrap;
  }
  
  .indicator-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
  }
}
</style>
```

## 💰 交易组件

### 订单表单组件

```vue
<!-- src/components/trading/OrderForm/index.vue -->
<template>
  <div class="order-form">
    <div class="form-header">
      <h3>下单交易</h3>
      <div class="account-info">
        <span>可用资金: {{ formatCurrency(availableCash) }}</span>
      </div>
    </div>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <!-- 股票选择 -->
      <el-form-item label="股票代码" prop="symbol">
        <el-autocomplete
          v-model="form.symbol"
          :fetch-suggestions="searchStocks"
          placeholder="输入股票代码或名称"
          style="width: 100%"
          :loading="searchLoading"
          @select="handleStockSelect"
          @blur="handleSymbolBlur"
        >
          <template #default="{ item }">
            <div class="stock-option">
              <div class="stock-info">
                <span class="stock-code">{{ item.symbol }}</span>
                <span class="stock-name">{{ item.name }}</span>
              </div>
              <div class="stock-price">
                <span 
                  class="price"
                  :class="getPriceClass(item.changePercent)"
                >
                  {{ formatPrice(item.currentPrice) }}
                </span>
                <span 
                  class="change"
                  :class="getPriceClass(item.changePercent)"
                >
                  {{ formatChange(item.change, item.changePercent) }}
                </span>
              </div>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
      
      <!-- 交易方向 -->
      <el-form-item label="交易方向">
        <el-radio-group v-model="form.side" @change="handleSideChange">
          <el-radio-button value="buy" class="buy-button">
            买入
          </el-radio-button>
          <el-radio-button value="sell" class="sell-button">
            卖出
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <!-- 订单类型 -->
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="form.orderType" style="width: 100%" @change="handleOrderTypeChange">
          <el-option label="限价单" value="limit" />
          <el-option label="市价单" value="market" />
          <el-option label="止损单" value="stop" />
          <el-option label="止盈单" value="stop-profit" />
        </el-select>
      </el-form-item>
      
      <!-- 价格设置 -->
      <el-form-item 
        v-if="showPriceInput" 
        label="委托价格" 
        prop="price"
      >
        <div class="price-input-group">
          <el-input-number
            v-model="form.price"
            :precision="2"
            :step="0.01"
            :min="0"
            style="width: 100%"
            placeholder="输入委托价格"
            @change="calculateAmount"
          />
          <div class="price-buttons">
            <el-button 
              size="small" 
              @click="setPriceByMarket(-0.01)"
              :disabled="!selectedStock"
            >
              -1%
            </el-button>
            <el-button 
              size="small" 
              @click="setPriceByMarket(0)"
              :disabled="!selectedStock"
            >
              现价
            </el-button>
            <el-button 
              size="small" 
              @click="setPriceByMarket(0.01)"
              :disabled="!selectedStock"
            >
              +1%
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <!-- 数量设置 -->
      <el-form-item label="委托数量" prop="quantity">
        <div class="quantity-input-group">
          <el-input-number
            v-model="form.quantity"
            :precision="0"
            :step="100"
            :min="0"
            :max="maxQuantity"
            style="width: 100%"
            placeholder="输入委托数量"
            @change="calculateAmount"
          />
          <div class="quantity-buttons">
            <el-button 
              size="small" 
              @click="setQuantityByPercent(0.25)"
              :disabled="!canCalculateQuantity"
            >
              1/4
            </el-button>
            <el-button 
              size="small" 
              @click="setQuantityByPercent(0.5)"
              :disabled="!canCalculateQuantity"
            >
              1/2
            </el-button>
            <el-button 
              size="small" 
              @click="setQuantityByPercent(1)"
              :disabled="!canCalculateQuantity"
            >
              全部
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <!-- 金额显示 -->
      <el-form-item label="委托金额">
        <div class="amount-display">
          <span class="amount">{{ formatCurrency(estimatedAmount) }}</span>
          <span class="fee">手续费: {{ formatCurrency(estimatedFee) }}</span>
        </div>
      </el-form-item>
      
      <!-- 持仓信息 (卖出时显示) -->
      <el-form-item v-if="form.side === 'sell' && currentPosition" label="持仓信息">
        <div class="position-info">
          <div class="position-row">
            <span>持仓数量:</span>
            <span>{{ currentPosition.totalQuantity }}</span>
          </div>
          <div class="position-row">
            <span>可卖数量:</span>
            <span>{{ currentPosition.availableQuantity }}</span>
          </div>
          <div class="position-row">
            <span>成本价:</span>
            <span>{{ formatPrice(currentPosition.avgPrice) }}</span>
          </div>
          <div class="position-row">
            <span>浮动盈亏:</span>
            <span :class="getPriceClass(currentPosition.unrealizedPnlPercent)">
              {{ formatCurrency(currentPosition.unrealizedPnl) }}
              ({{ formatPercent(currentPosition.unrealizedPnlPercent) }})
            </span>
          </div>
        </div>
      </el-form-item>
      
      <!-- 风险提示 -->
      <div v-if="riskWarnings.length > 0" class="risk-warnings">
        <el-alert
          v-for="warning in riskWarnings"
          :key="warning.type"
          :title="warning.message"
          :type="warning.level"
          :closable="false"
          show-icon
        />
      </div>
      
      <!-- 提交按钮 -->
      <el-form-item>
        <el-button
          type="primary"
          :loading="submitting"
          :disabled="!canSubmit"
          style="width: 100%"
          size="large"
          @click="handleSubmit"
        >
          {{ submitButtonText }}
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 快速下单 -->
    <div class="quick-trade">
      <div class="quick-trade-header">
        <span>快速下单</span>
        <el-switch v-model="quickTradeEnabled" />
      </div>
      
      <div v-if="quickTradeEnabled" class="quick-trade-panel">
        <!-- 预设金额按钮 -->
        <div class="preset-amounts">
          <el-button
            v-for="amount in presetAmounts"
            :key="amount"
            size="small"
            @click="setAmountPreset(amount)"
          >
            {{ formatCurrency(amount) }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrderForm } from '@/composables/trading/useOrderForm'
import { useMarketData } from '@/composables/market/useMarketData'
import { useTradingStore } from '@/stores/modules/trading'
import { useUserStore } from '@/stores/modules/user'
import { formatCurrency, formatPrice, formatPercent, formatChange } from '@/utils/formatters'
import type { OrderFormData, StockInfo, Position, RiskWarning } from '@/types/trading'

interface Props {
  defaultSymbol?: string
  defaultSide?: 'buy' | 'sell'
  quickTradeMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultSymbol: '',
  defaultSide: 'buy',
  quickTradeMode: false
})

const emit = defineEmits<{
  (e: 'submit', data: OrderFormData): void
  (e: 'stock-select', stock: StockInfo): void
  (e: 'side-change', side: 'buy' | 'sell'): void
}>()

// Stores
const tradingStore = useTradingStore()
const userStore = useUserStore()

// Form相关
const formRef = ref()
const {
  form,
  rules,
  submitting,
  resetForm,
  validateForm,
  submitOrder
} = useOrderForm()

// 市场数据相关
const {
  searchStocks: searchStocksApi,
  getStockInfo,
  subscribeQuote,
  unsubscribeQuote
} = useMarketData()

// 状态
const searchLoading = ref(false)
const selectedStock = ref<StockInfo | null>(null)
const quickTradeEnabled = ref(false)

// 预设金额
const presetAmounts = [1000, 5000, 10000, 20000, 50000]

// 计算属性
const availableCash = computed(() => tradingStore.account.availableCash)

const currentPosition = computed(() => {
  if (!form.symbol) return null
  return tradingStore.getPositionBySymbol(form.symbol)
})

const showPriceInput = computed(() => {
  return ['limit', 'stop', 'stop-profit'].includes(form.orderType)
})

const maxQuantity = computed(() => {
  if (form.side === 'sell' && currentPosition.value) {
    return currentPosition.value.availableQuantity
  }
  
  if (form.side === 'buy' && form.price > 0) {
    return Math.floor(availableCash.value / form.price / 100) * 100
  }
  
  return 0
})

const canCalculateQuantity = computed(() => {
  return form.side === 'buy' && form.price > 0 && availableCash.value > 0
})

const estimatedAmount = computed(() => {
  if (form.orderType === 'market' && selectedStock.value) {
    return selectedStock.value.currentPrice * form.quantity
  }
  return form.price * form.quantity
})

const estimatedFee = computed(() => {
  // 简化的手续费计算，实际应该根据券商规则
  const feeRate = 0.0003 // 万分之三
  const minFee = 5 // 最低5元
  const fee = Math.max(estimatedAmount.value * feeRate, minFee)
  return fee
})

const canSubmit = computed(() => {
  if (submitting.value) return false
  if (!form.symbol || !form.quantity) return false
  
  if (showPriceInput.value && !form.price) return false
  
  if (form.side === 'sell') {
    return currentPosition.value && form.quantity <= currentPosition.value.availableQuantity
  }
  
  if (form.side === 'buy') {
    const totalAmount = estimatedAmount.value + estimatedFee.value
    return totalAmount <= availableCash.value
  }
  
  return false
})

const submitButtonText = computed(() => {
  if (submitting.value) return '提交中...'
  
  const action = form.side === 'buy' ? '买入' : '卖出'
  const symbol = selectedStock.value?.name || form.symbol
  
  if (form.quantity && symbol) {
    return `${action} ${symbol} ${form.quantity}股`
  }
  
  return action
})

const riskWarnings = computed(() => {
  const warnings: RiskWarning[] = []
  
  // 检查资金风险
  if (form.side === 'buy') {
    const totalAmount = estimatedAmount.value + estimatedFee.value
    const riskRatio = totalAmount / availableCash.value
    
    if (riskRatio > 0.9) {
      warnings.push({
        type: 'capital',
        level: 'warning',
        message: '此笔交易将占用大部分可用资金，请注意风险控制'
      })
    }
  }
  
  // 检查集中度风险
  if (form.side === 'buy' && selectedStock.value) {
    const currentValue = currentPosition.value 
      ? currentPosition.value.marketValue 
      : 0
    const totalValue = currentValue + estimatedAmount.value
    const portfolioValue = tradingStore.totalPositionValue
    
    if (portfolioValue > 0 && totalValue / portfolioValue > 0.3) {
      warnings.push({
        type: 'concentration',
        level: 'warning',
        message: '单一股票占比过高，建议分散投资降低风险'
      })
    }
  }
  
  // 检查价格偏离
  if (showPriceInput.value && selectedStock.value && form.price > 0) {
    const deviation = Math.abs(form.price - selectedStock.value.currentPrice) / selectedStock.value.currentPrice
    
    if (deviation > 0.05) {
      warnings.push({
        type: 'price',
        level: 'warning',
        message: '委托价格偏离市价较大，请确认价格是否正确'
      })
    }
  }
  
  return warnings
})

// 方法
const searchStocks = async (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }
  
  searchLoading.value = true
  
  try {
    const results = await searchStocksApi(queryString)
    cb(results)
  } catch (error) {
    console.error('搜索股票失败:', error)
    cb([])
  } finally {
    searchLoading.value = false
  }
}

const handleStockSelect = async (item: StockInfo) => {
  selectedStock.value = item
  form.symbol = item.symbol
  
  // 设置默认价格为当前价
  if (showPriceInput.value) {
    form.price = item.currentPrice
  }
  
  // 订阅实时行情
  subscribeQuote(item.symbol)
  
  emit('stock-select', item)
  
  // 重新计算金额
  calculateAmount()
}

const handleSymbolBlur = async () => {
  if (form.symbol && !selectedStock.value) {
    try {
      const stockInfo = await getStockInfo(form.symbol)
      if (stockInfo) {
        await handleStockSelect(stockInfo)
      }
    } catch (error) {
      console.error('获取股票信息失败:', error)
    }
  }
}

const handleSideChange = (side: 'buy' | 'sell') => {
  emit('side-change', side)
  
  // 卖出时检查持仓
  if (side === 'sell' && !currentPosition.value) {
    ElMessage.warning('您没有该股票的持仓')
  }
  
  // 重新计算最大数量
  if (form.quantity > maxQuantity.value) {
    form.quantity = maxQuantity.value
  }
}

const handleOrderTypeChange = () => {
  // 市价单时清空价格
  if (form.orderType === 'market') {
    form.price = 0
  } else if (selectedStock.value) {
    form.price = selectedStock.value.currentPrice
  }
  
  calculateAmount()
}

const setPriceByMarket = (offset: number) => {
  if (!selectedStock.value) return
  
  const basePrice = selectedStock.value.currentPrice
  form.price = Number((basePrice * (1 + offset)).toFixed(2))
  calculateAmount()
}

const setQuantityByPercent = (percent: number) => {
  if (form.side === 'sell' && currentPosition.value) {
    form.quantity = Math.floor(currentPosition.value.availableQuantity * percent / 100) * 100
  } else if (form.side === 'buy' && form.price > 0) {
    const maxAmount = availableCash.value * percent
    form.quantity = Math.floor(maxAmount / form.price / 100) * 100
  }
  
  calculateAmount()
}

const setAmountPreset = (amount: number) => {
  if (form.side === 'buy' && form.price > 0) {
    form.quantity = Math.floor(amount / form.price / 100) * 100
    calculateAmount()
  }
}

const calculateAmount = () => {
  // 触发响应式更新
}

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const handleSubmit = async () => {
  try {
    // 表单验证
    await validateForm()
    
    // 风险确认
    if (riskWarnings.value.some(w => w.level === 'error')) {
      ElMessage.error('存在风险错误，无法提交订单')
      return
    }
    
    if (riskWarnings.value.some(w => w.level === 'warning')) {
      await ElMessageBox.confirm(
        '检测到风险警告，是否继续提交订单？',
        '风险提示',
        {
          confirmButtonText: '继续提交',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }
    
    // 提交订单
    await submitOrder()
    
    emit('submit', { ...form })
    
    ElMessage.success('订单提交成功')
    
    // 重置表单
    resetForm()
    selectedStock.value = null
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交订单失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '提交订单失败')
    }
  }
}

// 初始化
onMounted(() => {
  if (props.defaultSymbol) {
    form.symbol = props.defaultSymbol
    handleSymbolBlur()
  }
  
  if (props.defaultSide) {
    form.side = props.defaultSide
  }
  
  if (props.quickTradeMode) {
    quickTradeEnabled.value = true
  }
})
</script>

<style scoped>
.order-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.account-info {
  font-size: 14px;
  color: #666;
}

.stock-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-code {
  font-weight: 600;
  color: #333;
}

.stock-name {
  font-size: 12px;
  color: #666;
}

.stock-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price, .change {
  font-size: 14px;
}

.price-up {
  color: #f56c6c;
}

.price-down {
  color: #67c23a;
}

.price-neutral {
  color: #909399;
}

.buy-button {
  color: #f56c6c;
}

.sell-button {
  color: #67c23a;
}

.price-input-group, .quantity-input-group {
  width: 100%;
}

.price-buttons, .quantity-buttons {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.amount-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.fee {
  font-size: 12px;
  color: #666;
}

.position-info {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.position-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.position-row:last-child {
  margin-bottom: 0;
}

.risk-warnings {
  margin: 16px 0;
}

.risk-warnings .el-alert {
  margin-bottom: 8px;
}

.risk-warnings .el-alert:last-child {
  margin-bottom: 0;
}

.quick-trade {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.quick-trade-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preset-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .order-form {
    padding: 16px;
  }
  
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stock-option {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .price-buttons, .quantity-buttons {
    justify-content: space-between;
  }
  
  .preset-amounts {
    justify-content: space-between;
  }
}
</style>
```

---

## 📖 下一步阅读

1. [配置文件详解](./05-前端配置文件.md)
2. [部署方案](./06-前端部署方案.md)
3. [开发规范](./08-前端开发规范.md) 
# 配置文件详解

## 📦 包管理配置

### package.json

```json
{
  "name": "quant-frontend",
  "version": "1.0.0",
  "type": "module",
  "description": "量化投资前端可视化平台",
  "keywords": ["quant", "trading", "vue", "typescript", "finance"],
  "author": "Your Name <<EMAIL>>",
  "license": "MIT",
  "homepage": "https://github.com/your-username/quant-frontend#readme",
  "repository": {
    "type": "git",
    "url": "git+https://github.com/your-username/quant-frontend.git"
  },
  "bugs": {
    "url": "https://github.com/your-username/quant-frontend/issues"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "scripts": {
    "dev": "vite --mode development",
    "build": "vue-tsc && vite build",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:staging": "vue-tsc && vite build --mode staging",
    "build:prod": "vue-tsc && vite build --mode production",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix",
    "format": "prettier --write .",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "prepare": "husky install",
    "commit": "git-cz",
    "release": "standard-version",
    "analyze": "npx vite-bundle-analyzer"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "echarts": "^5.4.0",
    "axios": "^1.6.0",
    "dayjs": "^1.11.0",
    "lodash-es": "^4.17.0",
    "decimal.js": "^10.4.0",
    "big.js": "^6.2.0",
    "numeral": "^2.0.0",
    "dompurify": "^3.0.0",
    "jwt-decode": "^4.0.0",
    "mitt": "^3.0.0",
    "nprogress": "^0.2.0",
    "pinia-plugin-persistedstate": "^3.2.0"
  },
  "devDependencies": {
    "@types/node": "^20.10.0",
    "@types/lodash-es": "^4.17.0",
    "@types/numeral": "^2.0.0",
    "@types/dompurify": "^3.0.0",
    "@types/nprogress": "^0.2.0",
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/tsconfig": "^0.5.0",
    "typescript": "~5.3.0",
    "vue-tsc": "^1.8.0",
    "vite": "^5.0.0",
    "vitest": "^1.0.0",
    "@vitest/ui": "^1.0.0",
    "@vue/test-utils": "^2.4.0",
    "jsdom": "^23.0.0",
    "@playwright/test": "^1.40.0",
    "eslint": "^8.55.0",
    "@vue/eslint-config-typescript": "^12.0.0",
    "@vue/eslint-config-prettier": "^9.0.0",
    "eslint-plugin-vue": "^9.19.0",
    "prettier": "^3.1.0",
    "stylelint": "^16.0.0",
    "stylelint-config-standard": "^34.0.0",
    "stylelint-config-standard-scss": "^12.0.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "sass": "^1.69.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "unplugin-auto-import": "^0.17.0",
    "unplugin-vue-components": "^0.26.0",
    "vite-plugin-eslint": "^1.8.0",
    "rollup-plugin-visualizer": "^5.12.0",
    "husky": "^8.0.0",
    "lint-staged": "^15.2.0",
    "commitizen": "^4.3.0",
    "cz-conventional-changelog": "^3.3.0",
    "@commitlint/cli": "^18.4.0",
    "@commitlint/config-conventional": "^18.4.0",
    "standard-version": "^9.5.0"
  },
  "pnpm": {
    "peerDependencyRules": {
      "ignoreMissing": ["@algolia/client-search"]
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx,vue}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,vue}": [
      "stylelint --fix"
    ],
    "*.{md,json,yml,yaml}": [
      "prettier --write"
    ]
  },
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not dead",
    "not ie 11"
  ]
}
```

## 🛠️ 构建配置

### vite.config.ts

```typescript
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import eslint from 'vite-plugin-eslint'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'

  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      
      // ESLint 检查
      eslint({
        include: ['src/**/*.vue', 'src/**/*.ts', 'src/**/*.tsx'],
        cache: false,
        failOnError: isProduction
      }),
      
      // 自动导入
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            'element-plus': [
              'ElMessage',
              'ElMessageBox',
              'ElNotification',
              'ElLoading'
            ]
          }
        ],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json'
        }
      }),
      
      // 组件自动导入
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          })
        ],
        dts: 'src/types/components.d.ts',
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/]
      }),
      
      // 打包分析
      isProduction && visualizer({
        filename: 'dist/stats.html',
        open: false,
        gzipSize: true,
        brotliSize: true
      })
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@stores': resolve(__dirname, 'src/stores'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@api': resolve(__dirname, 'src/api'),
        '@types': resolve(__dirname, 'src/types'),
        '@assets': resolve(__dirname, 'src/assets'),
        '@composables': resolve(__dirname, 'src/composables'),
        '@layouts': resolve(__dirname, 'src/layouts'),
        '@router': resolve(__dirname, 'src/router'),
        '@plugins': resolve(__dirname, 'src/plugins'),
        '@config': resolve(__dirname, 'src/config'),
        '@directives': resolve(__dirname, 'src/directives')
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/assets/styles/variables.scss" as *;
            @use "@/assets/styles/mixins.scss" as *;
          `
        }
      }
    },

    server: {
      port: 5173,
      host: '0.0.0.0',
      open: false,
      cors: {
        origin: ['http://localhost:3000', 'http://localhost:5173'],
        credentials: true
      },
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          }
        },
        '/ws': {
          target: env.VITE_WS_URL || 'ws://localhost:8000',
          ws: true,
          changeOrigin: true
        }
      }
    },

    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,
      
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info']
        }
      } : {},
      
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') || []
            const extType = info[info.length - 1]
            
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/media/[name]-[hash].[ext]'
            }
            
            if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/images/[name]-[hash].[ext]'
            }
            
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/fonts/[name]-[hash].[ext]'
            }
            
            return `assets/${extType}/[name]-[hash].[ext]`
          },
          
          manualChunks: {
            // Vue 框架
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            
            // UI 组件库
            'ui-vendor': ['element-plus', '@element-plus/icons-vue'],
            
            // 图表库
            'chart-vendor': ['echarts'],
            
            // 工具库
            'utils-vendor': [
              'axios', 
              'lodash-es', 
              'dayjs', 
              'decimal.js', 
              'big.js', 
              'numeral'
            ],
            
            // 页面路由
            'router-dashboard': ['src/views/Dashboard'],
            'router-market': ['src/views/Market'],
            'router-trading': ['src/views/Trading'],
            'router-strategy': ['src/views/Strategy'],
            'router-backtest': ['src/views/Backtest']
          }
        }
      },
      
      // 构建性能优化
      chunkSizeWarningLimit: 1000,
      reportCompressedSize: false
    },

    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        'echarts',
        'axios',
        'lodash-es',
        'dayjs'
      ],
      exclude: ['@vueuse/core']
    },

    define: {
      __VUE_OPTIONS_API__: false,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    }
  }
})
```

## 🎨 样式配置

### tailwind.config.js

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}'
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // 品牌色
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554'
        },
        
        // 金融专用色
        financial: {
          // 涨跌色 (中国市场)
          up: '#ff4d4f',
          down: '#52c41a',
          neutral: '#8c8c8c',
          
          // 美股涨跌色
          'us-up': '#00d632',
          'us-down': '#ff3232',
          
          // 背景色
          'bg-primary': '#ffffff',
          'bg-secondary': '#f5f5f5',
          'bg-tertiary': '#fafafa',
          
          // 边框色
          'border-light': '#e8e8e8',
          'border-medium': '#d9d9d9',
          'border-dark': '#bfbfbf'
        },
        
        // 图表色系
        chart: {
          'line-1': '#5470c6',
          'line-2': '#91cc75',
          'line-3': '#fac858',
          'line-4': '#ee6666',
          'line-5': '#73c0de',
          'line-6': '#3ba272',
          'line-7': '#fc8452',
          'line-8': '#9a60b4',
          'line-9': '#ea7ccc'
        }
      },
      
      fontFamily: {
        sans: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'Arial',
          'sans-serif'
        ],
        mono: [
          'JetBrains Mono',
          'Fira Code',
          'Monaco',
          'Consolas',
          'Liberation Mono',
          'Courier New',
          'monospace'
        ],
        financial: [
          'DIN',
          'Arial',
          'sans-serif'
        ]
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        
        // 金融数据字体
        'financial-xs': ['0.75rem', { lineHeight: '1rem', letterSpacing: '0.025em' }],
        'financial-sm': ['0.875rem', { lineHeight: '1.25rem', letterSpacing: '0.025em' }],
        'financial-base': ['1rem', { lineHeight: '1.5rem', letterSpacing: '0.025em' }],
        'financial-lg': ['1.125rem', { lineHeight: '1.75rem', letterSpacing: '0.025em' }]
      },
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },
      
      borderRadius: {
        'financial': '0.375rem'
      },
      
      boxShadow: {
        'financial': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'financial-hover': '0 4px 12px rgba(0, 0, 0, 0.15)',
        'financial-active': '0 0 0 3px rgba(59, 130, 246, 0.1)'
      },
      
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'fade-out': 'fadeOut 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'price-flash': 'priceFlash 0.8s ease-out',
        'number-count': 'numberCount 1s ease-out'
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        priceFlash: {
          '0%': { backgroundColor: 'rgba(59, 130, 246, 0.3)' },
          '100%': { backgroundColor: 'transparent' }
        },
        numberCount: {
          '0%': { transform: 'scale(1.1)' },
          '100%': { transform: 'scale(1)' }
        }
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    
    // 自定义金融组件
    function({ addComponents, theme }) {
      addComponents({
        '.price-up': {
          color: theme('colors.financial.up')
        },
        '.price-down': {
          color: theme('colors.financial.down')
        },
        '.price-neutral': {
          color: theme('colors.financial.neutral')
        },
        '.financial-card': {
          backgroundColor: theme('colors.financial.bg-primary'),
          borderRadius: theme('borderRadius.financial'),
          boxShadow: theme('boxShadow.financial'),
          border: `1px solid ${theme('colors.financial.border-light')}`
        },
        '.financial-table': {
          '& th': {
            backgroundColor: theme('colors.financial.bg-secondary'),
            fontWeight: theme('fontWeight.semibold'),
            fontSize: theme('fontSize.sm')[0],
            padding: theme('spacing.3')
          },
          '& td': {
            fontSize: theme('fontSize.sm')[0],
            padding: theme('spacing.3'),
            borderBottom: `1px solid ${theme('colors.financial.border-light')}`
          }
        }
      })
    }
  ]
}
```

### postcss.config.js

```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    ...(process.env.NODE_ENV === 'production' ? { cssnano: {} } : {})
  }
}
```

## 🔧 TypeScript 配置

### tsconfig.json

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": true,
    "checkJs": false,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noImplicitAny": false,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": false,
    "noImplicitUseStrict": false,
    "alwaysStrict": true,
    "exactOptionalPropertyTypes": false,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedIndexedAccess": false,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@views/*": ["src/views/*"],
      "@stores/*": ["src/stores/*"],
      "@utils/*": ["src/utils/*"],
      "@api/*": ["src/api/*"],
      "@types/*": ["src/types/*"],
      "@assets/*": ["src/assets/*"],
      "@composables/*": ["src/composables/*"],
      "@layouts/*": ["src/layouts/*"],
      "@router/*": ["src/router/*"],
      "@plugins/*": ["src/plugins/*"],
      "@config/*": ["src/config/*"],
      "@directives/*": ["src/directives/*"]
    },

    /* Type definitions */
    "types": [
      "node",
      "vite/client",
      "element-plus/global.d.ts"
    ]
  },
  
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/**/*.d.ts",
    "tests/**/*.ts",
    "tests/**/*.tsx",
    "vite.config.ts",
    "vitest.config.ts",
    "playwright.config.ts"
  ],
  
  "exclude": [
    "node_modules",
    "dist",
    "public"
  ],
  
  "references": [
    { "path": "./tsconfig.node.json" }
  ]
}
```

### tsconfig.node.json

```json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "types": ["node"]
  },
  "include": [
    "vite.config.ts",
    "vitest.config.ts",
    "playwright.config.ts"
  ]
}
```

## 🧹 代码规范配置

### .eslintrc.js

```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
    'vue/setup-compiler-macros': true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier',
    'plugin:vue/vue3-essential',
    'plugin:@typescript-eslint/recommended',
    './.eslintrc-auto-import.json'
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 'latest',
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    // Vue 规则
    'vue/multi-word-component-names': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'kebab-case'],
    'vue/component-tags-order': ['error', {
      order: ['script', 'template', 'style']
    }],
    'vue/block-tag-newline': ['error', {
      singleline: 'always',
      multiline: 'always'
    }],
    'vue/component-api-style': ['error', ['script-setup']],
    'vue/define-macros-order': ['error', {
      order: ['defineOptions', 'defineProps', 'defineEmits', 'defineSlots']
    }],
    'vue/no-ref-as-operand': 'error',
    'vue/no-undef-components': 'error',
    'vue/no-undef-properties': 'error',
    'vue/no-unused-refs': 'error',
    'vue/prefer-separate-static-class': 'error',
    'vue/prefer-true-attribute-shorthand': 'error',
    'vue/require-macro-variable-name': ['error', {
      defineProps: 'props',
      defineEmits: 'emit',
      defineSlots: 'slots',
      useSlots: 'slots',
      useAttrs: 'attrs'
    }],

    // TypeScript 规则
    '@typescript-eslint/no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    '@typescript-eslint/prefer-optional-chain': 'error',
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    '@typescript-eslint/prefer-ts-expect-error': 'error',
    '@typescript-eslint/ban-ts-comment': ['error', {
      'ts-expect-error': 'allow-with-description',
      'ts-ignore': false,
      'ts-nocheck': false,
      'ts-check': false
    }],

    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-unused-vars': 'off', // 由 @typescript-eslint/no-unused-vars 处理
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'template-curly-spacing': 'error',
    'arrow-spacing': 'error',
    'generator-star-spacing': 'error',

    // 导入规则
    'sort-imports': ['error', {
      ignoreCase: false,
      ignoreDeclarationSort: true,
      ignoreMemberSort: false,
      memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
      allowSeparatedGroups: true
    }]
  },
  overrides: [
    {
      files: ['*.vue'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'off'
      }
    },
    {
      files: ['**/__tests__/**/*', '**/*.spec.*', '**/*.test.*'],
      env: {
        jest: true
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off'
      }
    }
  ],
  ignorePatterns: [
    'dist',
    'node_modules',
    '*.d.ts',
    'coverage'
  ]
}
```

### .stylelintrc.js

```javascript
module.exports = {
  extends: [
    'stylelint-config-standard',
    'stylelint-config-standard-scss',
    'stylelint-config-standard-vue'
  ],
  plugins: [
    'stylelint-order'
  ],
  rules: {
    // 基础规则
    'string-quotes': 'single',
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    'selector-class-pattern': null,
    'selector-id-pattern': null,
    'keyframes-name-pattern': null,
    'custom-property-pattern': null,
    'no-descending-specificity': null,
    'declaration-empty-line-before': null,

    // SCSS 规则
    'scss/at-rule-no-unknown': [true, {
      ignoreAtRules: [
        'tailwind',
        'apply',
        'variants',
        'responsive',
        'screen',
        'layer'
      ]
    }],
    'scss/dollar-variable-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'scss/at-mixin-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'scss/at-function-pattern': '^[a-z][a-zA-Z0-9-]*$',

    // 属性顺序
    'order/properties-order': [
      // 定位
      'position',
      'top',
      'right',
      'bottom',
      'left',
      'z-index',

      // 显示模式
      'display',
      'flex',
      'flex-grow',
      'flex-shrink',
      'flex-basis',
      'flex-direction',
      'flex-wrap',
      'justify-content',
      'align-items',
      'align-content',
      'align-self',
      'order',
      'grid',
      'grid-area',
      'grid-template',
      'grid-template-areas',
      'grid-template-rows',
      'grid-template-columns',
      'grid-row',
      'grid-column',
      'grid-row-start',
      'grid-row-end',
      'grid-column-start',
      'grid-column-end',
      'grid-auto-rows',
      'grid-auto-columns',
      'grid-auto-flow',
      'grid-gap',
      'grid-row-gap',
      'grid-column-gap',

      // 盒模型
      'width',
      'height',
      'max-width',
      'max-height',
      'min-width',
      'min-height',
      'padding',
      'padding-top',
      'padding-right',
      'padding-bottom',
      'padding-left',
      'margin',
      'margin-top',
      'margin-right',
      'margin-bottom',
      'margin-left',
      'overflow',
      'overflow-x',
      'overflow-y',

      // 边框
      'border',
      'border-width',
      'border-style',
      'border-color',
      'border-top',
      'border-right',
      'border-bottom',
      'border-left',
      'border-radius',

      // 背景
      'background',
      'background-color',
      'background-image',
      'background-repeat',
      'background-attachment',
      'background-position',
      'background-size',
      'background-clip',
      'background-origin',

      // 文字
      'color',
      'font',
      'font-weight',
      'font-size',
      'font-family',
      'font-style',
      'font-variant',
      'font-size-adjust',
      'font-stretch',
      'font-effect',
      'font-emphasize',
      'font-emphasize-position',
      'font-emphasize-style',
      'font-smooth',
      'line-height',
      'direction',
      'letter-spacing',
      'white-space',
      'text-align',
      'text-align-last',
      'text-decoration',
      'text-emphasis',
      'text-emphasis-color',
      'text-emphasis-style',
      'text-emphasis-position',
      'text-indent',
      'text-justify',
      'text-outline',
      'text-transform',
      'text-wrap',
      'text-overflow',
      'text-overflow-ellipsis',
      'text-overflow-mode',
      'text-shadow',
      'word-spacing',
      'word-wrap',
      'word-break',

      // 其他
      'opacity',
      'visibility',
      'box-shadow',
      'text-shadow',
      'transform',
      'transition',
      'animation'
    ]
  },
  ignoreFiles: [
    'dist/**/*',
    'node_modules/**/*',
    'coverage/**/*'
  ]
}
```

### .prettierrc

```json
{
  "semi": false,
  "singleQuote": true,
  "quoteProps": "as-needed",
  "trailingComma": "none",
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "proseWrap": "never",
  "htmlWhitespaceSensitivity": "strict",
  "vueIndentScriptAndStyle": false,
  "endOfLine": "lf",
  "embeddedLanguageFormatting": "auto",
  "singleAttributePerLine": true
}
```

## 🌍 环境配置

### .env.example

```bash
# 应用基础配置
VITE_APP_TITLE=量化投资平台
VITE_APP_DESCRIPTION=专业的量化投资可视化平台
VITE_APP_VERSION=1.0.0

# API 配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_API_TIMEOUT=10000

# WebSocket 配置 (使用原生WebSocket)
VITE_WS_URL=ws://localhost:8000/ws
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_HEARTBEAT_INTERVAL=30000

# 图表配置 (移除Pyecharts，使用ECharts)
VITE_CHART_THEME=dark
VITE_CHART_ANIMATION=true
VITE_CHART_RENDERER=canvas
VITE_CHART_DATA_CACHE_TTL=60000

# 交易配置
VITE_ENABLE_PAPER_TRADING=true
VITE_DEFAULT_LEVERAGE=1
VITE_MAX_POSITION_SIZE=1000000
VITE_TRADING_FEE_RATE=0.0003

# 认证配置
VITE_AUTH_TOKEN_KEY=quant_auth_token
VITE_AUTH_REFRESH_TOKEN_KEY=quant_refresh_token
VITE_AUTH_TOKEN_EXPIRES=7200000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=true
VITE_ENABLE_DEVTOOLS=true

# 监控配置
VITE_SENTRY_DSN=
VITE_SENTRY_ENVIRONMENT=development

# 分析配置
VITE_GA_TRACKING_ID=
VITE_BAIDU_ANALYTICS_ID=

# CDN 配置
VITE_CDN_URL=
VITE_UPLOAD_URL=

# 地图配置
VITE_MAP_API_KEY=

# 第三方服务
VITE_TRADING_VIEW_SYMBOL_URL=
VITE_NEWS_API_URL=
VITE_MARKET_DATA_URL=
```

### .env.development

```bash
# 开发环境配置
NODE_ENV=development
VITE_APP_TITLE=量化投资平台 (开发)

# API 配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws

# 功能开关
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEVTOOLS=true

# 监控配置
VITE_SENTRY_DSN=
VITE_SENTRY_ENVIRONMENT=development
```

### .env.production

```bash
# 生产环境配置
NODE_ENV=production
VITE_APP_TITLE=量化投资平台

# API 配置
VITE_API_BASE_URL=https://api.yourdomain.com/api/v1
VITE_WS_URL=wss://api.yourdomain.com/ws

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 监控配置
VITE_SENTRY_DSN=https://your-sentry-dsn
VITE_SENTRY_ENVIRONMENT=production

# CDN 配置
VITE_CDN_URL=https://cdn.yourdomain.com
```

---

## 📖 下一步阅读

1. [部署方案](./06-前端部署方案.md)
2. [开发规范](./08-前端开发规范.md)
3. [测试方案](./09-前端测试方案.md) 
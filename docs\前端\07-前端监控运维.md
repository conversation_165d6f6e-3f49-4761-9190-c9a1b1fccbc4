# 前端监控运维

本节介绍如何对 **quant-platform** 前端应用（Vue3 + Vite 构建产物）进行**监控、告警、日志收集**以及**运行时运维**。

---

## 1. 监控维度

| 模块 | 工具 | 监控指标 |
|------|------|---------|
| 性能 | Web-Vitals + Prometheus Export | LCP、FID、CLS、TBT、FCP、资源加载耗时 |
| 错误 | Sentry | JS Error、Unhandled Promise、SourceMap 解析 |
| 网络 | Nginx Ingress + Prometheus Nginx Exporter | 4xx/5xx、QPS、延迟分布、带宽 |
| 业务 | 自定义埋点上报 | 下单成功率、页面转化率 |
| 基础设施 | Grafana Dashboard | CPU / Mem、Pod 重启、容器 FS |

---

## 2. 前端日志

### 2.1 浏览器侧

```ts
import * as Sentry from '@sentry/vue'
Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.MODE,
  integrations: [new Sentry.BrowserTracing()],
  tracesSampleRate: 0.2,
})
```

- **Release** 字段通过 CI 注入 git-sha，用于 SourceMap 匹配。
- **Breadcrumbs** 记录用户行为、XHR、Console，方便回放。

### 2.2 Nginx 访问日志

```nginx
log_format json '{"time":"$time_iso8601","remote_addr":"$remote_addr","method":"$request_method",'\
                   '"uri":"$uri","status":$status,"request_time":$request_time,'\
                   '"upstream_time":"$upstream_response_time"}';
access_log /var/log/nginx/access.log json;
```

使用 Filebeat → Loki → Grafana 进行集中查询。

---

## 3. Prometheus 指标暴露

前端为纯静态资源，无法直接在浏览器暴露指标，但可通过以下方式：

1. **nginx-exporter**：容器内暴露 `/metrics` 端口；
2. **performance-beacon**：在 `window.performance.getEntries()` 基础上定时 POST 至后端 `/metrics/web`，由后端转 Prometheus 格式；
3. **business-beacon**：埋点 SDK，将自定义业务指标写入 Prometheus Pushgateway。

---

## 4. Grafana Dashboard 模板

已提供：`monitoring/grafana/dashboards/quant_platform_overview.json`，包含：

- **前端用户体验**：Web-Vitals、SPA 路由耗时
- **错误趋势**：Sentry Issue 计数、错误率
- **流量 & 负载**：Ingress QPS、带宽、缓存命中

---

## 5. 告警规则示例 (Alertmanager)

```yaml
- alert: FEHighJSExceptionRate
  expr: increase(sentry_js_errors_total[5m]) / increase(ingress_request_total[5m]) > 0.01
  for: 10m
  labels:
    severity: critical
  annotations:
    summary: "前端 JS 异常率高于 1%"
    description: "过去 10 分钟 JS 错误率 {{ $value | humanizePercentage }}"
```

- 告警渠道: 钉钉 + Slack + 邮件。

---

## 6. 运维操作

| 场景 | 操作 |
|-----|------|
| 热更新配置 | `kubectl rollout restart deploy/quant-frontend` |
| 回滚版本 | `kubectl rollout undo deploy/quant-frontend` |
| 灰度发布 | 使用 Argo Rollouts 50% → 100% 分阶段 |
| 临时放量 | `kubectl scale --replicas=6 deploy/quant-frontend` |

---

## 7. FAQ

* **Q:** Sentry 报错没有源码定位？  
  **A:** 确认 CI 上传 `sourcemap.zip`，并在 nginx 禁止 SourceMap 对外暴露。

* **Q:** Web-Vitals 指标波动大？  
  **A:** 检查最近合入的异步组件、图片体积，对比 `largestContentfulPaint` 资源名。

* **Q:** Grafana 仪表盘加载慢？  
  **A:** 调整 Prometheus `step`，启用数据下采样，或升级 long-term storage。

---

> 以上方案确保前端在生产环境具备可观测性与可运维性，为核心交易业务保驾护航。 
# 前端开发规范

> 本规范面向 quant-platform 前端团队，覆盖 **代码风格、分支模型、提交信息、评审流程** 等。

---

## 1. 代码风格

- **Eslint + Prettier + Stylelint** 已在项目中集成，提交前必须通过 `pnpm lint`。
- **TypeScript 严格模式** (`strict: true`)，禁止 `any`，必要时 `@ts-expect-error` 标注原因。
- **Vue 组合式 API + `<script setup>`**，禁止 Options API 新写法。
- 组件文件顺序：`<script setup> → <template> → <style scoped lang="scss">`。
- 样式统一使用 **Tailwind CSS + SCSS 变量**，禁止内联样式。

## 2. 目录 & 命名

| 类型 | 规则 | 示例 |
|------|------|------|
| 组件 | PascalCase | `UserCard.vue` |
| 组合函数 | useXxx.ts | `useOrderForm.ts` |
| 资源 | kebab-case | `login-bg.png` |

## 3. Git 分支策略

```
main         → 生产环境
release/*    → 预生产 / 灰度
feat/*       → 新功能（可并行）
fix/*        → Bug 修复
refactor/*   → 重构
hotfix/*     → 紧急生产问题
```

merge 规则：
1. feature → release，需代码评审 + lint + test 通过；
2. release → main，需 E2E 测试通过；
3. hotfix 可直接 cherry-pick 至 main 并回合 release。

## 4. 提交信息 (Commitizen + Conventional Commits)

```
<type>(scope): <subject>

<body>

<footer>
```

| type | 用途 |
|------|------|
| feat | 新特性 |
| fix | Bug 修复 |
| docs | 文档 |
| refactor | 重构 |
| perf | 性能优化 |
| test | 测试 |
| chore | 杂项 |

Example:
```
feat(order-form): 支持批量下单快捷键
```

## 5. 代码评审流程

1. 创建 PR 至 `release/*`，选择 Reviewer，关联 Issue。
2. CI 自动运行 lint/test/build。通过后才能进入人工 Review。
3. 至少 **1 位核心成员 + 1 位代码拥有者** 通过后方可合并。
4. 合并前需 `squash/rebase`，保持 commit 历史清晰。
5. PR 模板需填写 **变更点 / Self-review Checklist / 影响范围 / 回归测试步骤**。

## 6. 样式规范

- 颜色、字体、阴影全部来自 `tailwind.config.js` 自定义 Theme。
- SCSS 文件内禁止硬编码颜色，可通过 CSS 变量或 `@apply`。
- 响应式断点统一使用 `useBreakpoints()` 组合函数。

## 7. 可访问性 (a11y)

- 所有交互组件需支持键盘导航 `Tab / Enter / Esc`。
- 图片必须有 `alt`；图标等装饰元素 `aria-hidden="true"`。
- 对话框使用 `role="dialog"` 并自动聚焦。

## 8. 性能守护

- 单页 JS **≤ 250 KB gzip**，超出需分包。
- 首屏接口须设置 **缓存 & loading skeleton**。
- 每 PR 需在 Lighthouse CI bot 里看首屏指标对比，不得回归 >5%。

## 9. 安全守护

- DOM 操作全部通过 `DOMPurify` 过滤。
- 禁止在源码中 hard-code Token / 密钥；使用 `.env.*`。

## 10. 文档与注释

- 公共 API、组合函数要求 JSDoc，描述参数与返回。
- 复杂业务组件需在同目录 `README.md` 给出用例。

## 11. 定期维护

- **每月** 依赖升级日：运行 `pnpm up --latest`，并进行回归测试。
- **每季度** 代码健康检查：`pnpm depcheck`、Storybook 无孤儿组件。

---

遵循本规范可确保团队协作高效、代码质量可持续。 
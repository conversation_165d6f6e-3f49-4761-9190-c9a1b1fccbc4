# 前端测试方案

本方案覆盖 **单元测试、组件测试、集成测试、E2E 测试** 四层金字塔，并与 CI/CD 自动化集成。

---

## 1. 工具栈

| 层级 | 工具 | 覆盖范围 |
|------|------|---------|
| 单元 | **Vitest** | 纯函数、组合函数 (composables)、工具库 |
| 组件 | **@vue/test-utils + Vitest + jsdom** | Vue 单文件组件 UI / 交互 |
| 集成 | **Testing Library Vue** | 组件 + Store + Router 联动 |
| E2E | **Playwright** | 真浏览器全流程 |
| 性能/可访问性 | **Lighthouse CI** | Web-Vitals、a11y 检查 |

---

## 2. 单元测试 (Vitest)

- 目录：`tests/unit/`
- 约定：测试文件同名、加 `.test.ts` 或 `.spec.ts`。
- Mock：使用 `vi.fn()`、`msw` 模拟 HTTP 请求。
- 覆盖率门槛：`--coverage --passWithNoTests`，行/分支 85%。

示例：
```ts
describe('formatCurrency', () => {
  it('format 10000 to ￥10,000.00', () => {
    expect(formatCurrency(10000)).toBe('￥10,000.00')
  })
})
```

---

## 3. 组件测试

- 目录：`tests/unit/components/`
- 使用 `mount()` + `await nextTick()` 断言 DOM。
- 遇到外部依赖 (Element Plus) 建议 Stub，只关注业务逻辑。

```ts
const wrapper = mount(BacktestForm, { global: { plugins: [pinia] } })
expect(wrapper.get('button[type="submit"]').text()).toBe('开始回测')
```

---

## 4. 集成测试

- 场景：路由跳转、Store 状态流转、网络请求协同。
- 使用 `@testing-library/vue` 强调用户视角。

```ts
render(<LoginView />)
await userEvent.type(screen.getByPlaceholderText('邮箱'), '<EMAIL>')
```

---

## 5. E2E 测试 (Playwright)

- 目录：`tests/e2e/`
- 关键脚本：
  ```bash
  pnpm test:e2e   # 本地
  CI=true pnpm test:e2e --reporter=junit
  ```
- 典型用例：
  1. 登录 → 跳转仪表盘 → 断言欢迎语
  2. 创建回测 → 轮询状态 → 下载报告
  3. 下单买入 → WebSocket 返回成交 → 持仓数量 +1

#### 并发与环境
- GitHub Actions Linux Chrome Headless，Worker × 4。
- 失败截图 & trace 自动上传到 Artifacts。

---

## 6. Lighthouse CI

- 位置：`.github/workflows/lighthouse.yml`
- PR 检测 **Performance / Best-Practice / a11y / SEO**，阈值 ≥ 90。

---

## 7. Test Pyramid

```
        ▲  E2E (10% 数量)
        │  20~30 条
        │
  集成测试 (20%)
        │  60~80 条
        │
  组件测试 (30%)
        │  100+ 条
        │
  单元测试 (40%)
        │  越多越好
        ▼
```

---

## 8. CI 阶段 & 时长基准

| 阶段 | 目标耗时 |
|------|---------|
| Lint + TypeCheck | ≤ 30 秒 |
| Unit + Component | ≤ 2 分钟 |
| E2E | ≤ 4 分钟 |
| Lighthouse | ≤ 1 分钟 |

---

## 9. 本地调试技巧

- `vitest --ui` 交互式重跑失败用例。
- `pnpm test:e2e --headed --trace on` 观察真实浏览器。

---

确保测试绿灯后才能合并 PR，保证主干始终可部署。 
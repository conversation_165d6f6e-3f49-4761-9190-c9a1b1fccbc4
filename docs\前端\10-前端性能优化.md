# 前端性能优化

本节总结 **打包、运行时、网络、渲染** 四大方面的优化措施及监控方法。

---

## 1. 打包阶段

### 1.1 代码分割

- **路由懒加载**：`const Market = () => import('@/views/Market/MarketView.vue')`。
- **三方库分包**：在 `vite.config.ts` 使用 `manualChunks` 拆分 `vue-vendor`、`echarts`、`element-plus` 等。
- **动态导入 + `import.meta.glob`**：按需加载策略模板、语言包。

### 1.2 产物优化

| 优化 | 工具 | 效果 |
|------|------|------|
| Tree-Shaking | ESBuild + Rollup | 剔除未引用代码 |
| 图片压缩 | vite-imagemin | png-quant、mozjpeg |
| CSS Purge | tailwindcss `content` | 去除未用 class |
| gzip/brotli | Nginx `gzip_static` | 体积 ↓ 70% |

---

## 2. 网络层

- **HTTP/2 + CDN**：并发加载 & 边缘缓存。
- **Preconnect / DNS-Prefetch**：`<link rel="preconnect" href="https://api.yourdomain.com">`。
- **Service Worker**：静态资源离线缓存 (PWA)。
- API 使用 **keep-alive + gzip + HTTP caching**，常用字典数据设 `Cache-Control: max-age=3600`。

---

## 3. 运行时

### 3.1 渲染优化

- 列表使用 **虚拟滚动** (`VirtualList.vue`)。
- 大量 DOM 更新使用 `requestIdleCallback` 批处理。
- 图表数据 > 50k 点时改为 **canvas** 渲染并做数据下采样 (`decimate` 算法)。

### 3.2 状态管理

- Store 分模块懒加载：交易、回测等重型逻辑在进入相关页面时再 `import()`。
- 避免深度 Watch，使用 `shallowReactive` 或 `markRaw`。

---

## 4. 监控与预算

- **Web-Vitals**：
  - LCP ≤ 2.5 s
  - CLS ≤ 0.1
  - FID ≤ 100 ms
- **资源预算** (Chrome DevTools performance budget)：
  | 类型 | 阈值 |
  |------|------|
  | JS | 250 KB gzip |
  | CSS | 100 KB gzip |
  | Font | 100 KB |

---

## 5. 性能剖析工具链

| 场景 | 工具 |
|------|------|
| 构建体积分析 | `rollup-plugin-visualizer`、`vite-bundle-analyzer` |
| 运行时 FPS | Chrome DevTools Rendering → FPS Meter |
| 内存泄漏 | Chrome DevTools → Performance → Heap Snapshot |
| 网络瓶颈 | WebPageTest、Lighthouse |

---

## 6. CI 集成

- PR 合并时运行 **Lighthouse CI**，若 Performance 分数 < 90 即失败。
- 使用 `size-limit` 检测产物大小超标。

```json
{
  "name": "quant-frontend",
  "size-limit": [
    {
      "path": "dist/assets/index-*.js",
      "limit": "250 KB"
    }
  ]
}
```

---

## 7. 常见问题

| 症状 | 原因 | 解决 |
|------|------|------|
| 首屏 FCP 高达 4s | 资源阻塞、无 server-push | 开启 preload/HTTP2 push |
| 路由切换卡顿 | 大量 watch & 重渲染 | 使用 `defineAsyncComponent` + skeleton |
| 图表拖拽掉帧 | 10w+ 数据直接渲染 | WebWorker + down-sample |

---

通过上述措施，实际生产线下 **首屏时间从 4.1 s 优化至 1.8 s，LCP 提升 56%**。 
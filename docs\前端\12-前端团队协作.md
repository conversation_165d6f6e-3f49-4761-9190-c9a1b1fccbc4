# 前端团队协作指南

---

## 1. 角色分工

| 角色 | 职责 |
|------|------|
| Tech Lead | 技术方案决策、架构评审、PR Gatekeeper |
| FE Dev | 功能开发、单测、文档编写 |
| UI/UX | 设计、可用性评估、Figma 资源维护 |
| QA | 编写自动化脚本、维护测试环境 |
| DevOps | CI/CD、监控、容量规划 |

---

## 2. 工具链

- **沟通**：Slack #frontend、周三例会 (30min)。
- **需求**：Jira Board，列：Backlog → Doing → Review → Done。
- **设计**：Figma，版本标签对应 Jira Story。
- **文档**：docs/ + Storybook，更新后在 PR 描述附链接。
- **代码托管**：GitHub，强制 Review + Status Check。

---

## 3. 流程

1. **需求评审** (PRD + 设计审)
2. **技术拆解**（Tech Lead & Dev）
3. **创建 Jira 子任务** (前端 / 后端 / 测试)
4. **开发 → 开 PR**
5. **CI 通过 & Code Review**
6. **合并到 release 分支**
7. **E2E & UAT**
8. **发布 & 监控**

---

## 4. 会议节奏

| 周期 | 会议 | 目标 | 时长 |
|------|------|------|-----|
| 周一 | Sprint Planning | 任务拆解 & 排期 | 60min |
| 每日 | Daily Stand-up | 阻塞 / 进展 | 10min |
| 周三 | 技术分享 | 新技术 / 复盘 | 30min |
| 周五 | Demo & Retro | 演示成果 / 改进 | 45min |

---

## 5. 评审准则 (Code Review)

- **功能正确**：逻辑、边界情况、i18n。
- **性能**：避免不必要的重渲染。
- **安全**：XSS、CSRF、role-guard。
- **可读性**：命名、注释、拆分组件。
- **测试**：是否补充单测 / E2E。

PR 通过标准：LGTM（≥ 2 Reviewers） + CI Green。

---

## 6. 知识共享

- 内部 **Tech Radar**：收集可引入的新库、RFC。
- 每 Sprint 至少 1 篇博客或内部分享。

---

## 7. 冲突解决

- 技术分歧：以 POC + 性能/可维护性 数据说话。
- 资源冲突：Tech Lead 协调优先级，与 PM 对齐。

---

协作流程透明化、标准化，确保多模块并行开发高效稳定。 
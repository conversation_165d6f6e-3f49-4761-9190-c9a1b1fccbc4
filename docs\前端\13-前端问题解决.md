# 前端常见问题解决 (FAQ)

---

## 1. 本地启动

| 问题 | 可能原因 | 解决 |
|------|----------|------|
| `pnpm dev` 白屏且无报错 | 端口被占用 | 修改 `.env.development` `VITE_PORT` 或关闭占用程序 |
| API 报 404 | 后端未启动 / 代理失效 | `docker ps` 检查 backend，确认 `vite.config.ts proxy` |
| ESLint 无法识别全局自动导入 | 缓存未更新 | `pnpm lint --fix` 或删除 `node_modules/.cache` 重启 IDE |

## 2. 构建失败

| 错误 | 说明 | 解决 |
|------|------|------|
| `Module not found: ElementPlus` | 新组件忘记装依赖 | `pnpm add element-plus` 并 commit lockfile |
| `Maximum call stack size exceeded` | 循环依赖 | 通过 `madge --circular src` 定位循环 import |
| `Memory heap out of limit` | 打包体积超大 | `--max_old_space_size=4096` 或拆分 chunk |

## 3. 运行时 Bug

| 症状 | debug 步骤 |
|------|-----------|
| 图表不渲染 | 1) Console 是否 DOM 装载 2) `echarts.init` 返回实例 3) Option 数据量是否为空 |
| 订单 WebSocket 无回包 | 1) DevTools WS 帧是否发送 2) token 是否带上 3) 后端日志查看频道订阅 |
| 移动端页面错位 | 1) 浏览器缩放比例 2) Tailwind 响应式类检查 3) safe-area 插件 |

## 4. 性能问题

| 指标下降 | 定位方法 |
|----------|---------|
| LCP ↑ | Chrome Performance → Largest Contentful Paint 条目资源来源 |
| FPS 掉帧 | Performance → FPS / CPU FlameChart 找长任务 |
| Bundle size ↑ | `pnpm analyze` 查看新增依赖 |

## 5. 依赖冲突

- 使用 `pnpm why <pkg>` 定位版本树。
- 若三方库冲突可在 `pnpm.overrides` 强制统一。

## 6. 线上事故应急

1. 发现问题 → Slack #incidents 报警。
2. Tech Lead 判断影响范围，是否回滚。
3. `kubectl rollout undo` 回滚到稳定版本。
4. 创建 **Incident 文档**，24h 内完成 RCA。

## 7. 资料链接

- Vite 官方性能调优：https://vitejs.dev/guide/performance.html
- Vue 官方最佳实践：https://vuejs.org/guide/best-practices
- Playwright Debug：https://playwright.dev/docs/debug

---

如有新增问题，请在本文件追加条目并 PR。 
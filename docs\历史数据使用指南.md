# 历史数据使用指南

## 概述

量化投资平台现已完全集成历史数据功能，能够充分利用 `backend/data/historical/stocks` 目录中的CSV历史数据，同时不影响实时数据的获取和使用。

## 🎯 核心特性

### 1. 智能数据源切换
- **实时数据优先**: 优先使用实时API数据
- **历史数据回退**: 当实时数据不可用时，自动回退到历史CSV数据
- **无缝切换**: 用户无感知的数据源切换

### 2. 高性能缓存系统
- **Redis缓存**: 支持Redis缓存，提高数据访问速度
- **内存缓存**: Redis不可用时自动使用内存缓存
- **智能过期**: 不同类型数据使用不同的缓存时间

### 3. 历史数据索引
- **SQLite索引**: 自动构建股票数据索引，快速查询
- **元数据管理**: 记录每只股票的数据范围、记录数等信息
- **增量更新**: 支持索引的增量更新和重建

## 📁 数据结构

### 历史数据目录
```
backend/data/historical/stocks/
├── 000001_平安银行.csv
├── 000002_万科A.csv
├── 000063_中兴通讯.csv
└── ...
```

### CSV文件格式
每个CSV文件包含以下列：
- 日期
- 开盘价、收盘价、最高价、最低价
- 成交量(手)、成交额(元)
- 振幅(%)、涨跌幅(%)、涨跌额(元)
- 换手率(%)
- 技术指标：MACD、RSI等

## 🚀 使用方法

### 1. 前端访问

#### 实时行情页面
- 路径: `/market`
- 功能: 查看实时股票行情，自动回退到历史数据

#### 历史数据中心
- 路径: `/market/historical`
- 功能: 专门的历史数据查询和管理界面

### 2. API接口

#### 获取股票行情（智能切换）
```http
GET /api/v1/market/quote/{symbol}?use_cache=true
```

#### 获取历史股票列表
```http
GET /api/v1/market/historical/stocks?market=SH&page=1&page_size=50
```

#### 搜索历史股票
```http
GET /api/v1/market/historical/search?keyword=平安&limit=20
```

#### 获取股票历史数据
```http
GET /api/v1/market/historical/data/{symbol}?start_date=2023-01-01&end_date=2023-12-31
```

#### 获取综合K线数据
```http
GET /api/v1/market/integrated/kline/{symbol}?period=1d&limit=100
```

### 3. 缓存管理

#### 清除缓存
```http
POST /api/v1/market/cache/clear?pattern=*
```

#### 重建历史数据索引
```http
POST /api/v1/market/historical/rebuild-index
```

## 🔧 配置说明

### 1. 数据源优先级配置

在 `IntegratedMarketService` 中可以配置数据源优先级：

```python
self.data_source_priority = {
    'realtime': ['enhanced_service', 'tushare', 'mock'],
    'historical': ['csv_files', 'database', 'tushare'],
    'kline': ['csv_files', 'enhanced_service', 'database']
}
```

### 2. 缓存配置

```python
self._cache_ttl = 60  # 缓存60秒
```

不同类型数据的缓存时间：
- 实时行情: 60秒
- K线数据: 300秒
- 股票列表: 1800秒
- 市场概览: 120秒

### 3. 历史数据索引配置

```python
self._cache_size_limit = 100  # 最多缓存100个股票的数据
```

## 📊 性能优化

### 1. 数据加载优化
- **并发读取**: 使用线程池并发读取CSV文件
- **分页加载**: 大数据量分页处理
- **懒加载**: 按需加载数据

### 2. 内存管理
- **LRU缓存**: 最近最少使用的缓存淘汰策略
- **数据压缩**: 大数据集自动压缩存储
- **内存监控**: 实时监控内存使用情况

### 3. 查询优化
- **索引查询**: 基于SQLite索引快速查询
- **范围查询**: 支持日期范围和条件筛选
- **批量查询**: 支持批量获取多只股票数据

## 🛠️ 管理功能

### 1. 历史数据中心界面

#### 统计信息
- 总股票数量
- 市场分布
- 行业分类
- 缓存状态

#### 搜索和筛选
- 股票代码/名称搜索
- 市场筛选（上海/深圳/北京）
- 行业筛选
- 分页浏览

#### 数据查看
- 股票列表展示
- 历史数据详情
- 日期范围选择
- 数据导出（计划中）

### 2. 管理操作

#### 缓存管理
- 清除所有缓存
- 清除特定模式缓存
- 查看缓存状态

#### 索引管理
- 重建数据索引
- 增量更新索引
- 索引状态查看

## 🔍 故障排除

### 1. 常见问题

#### 数据不显示
1. 检查CSV文件是否存在
2. 检查文件格式是否正确
3. 重建数据索引

#### 性能问题
1. 清除缓存
2. 检查Redis连接
3. 调整缓存配置

#### 搜索无结果
1. 检查关键词拼写
2. 重建数据索引
3. 检查文件权限

### 2. 日志查看

查看后端日志了解详细错误信息：
```bash
tail -f backend/logs/app.log
```

### 3. 数据验证

验证CSV文件格式：
```python
import pandas as pd
df = pd.read_csv('backend/data/historical/stocks/000001_平安银行.csv')
print(df.head())
print(df.columns.tolist())
```

## 📈 使用建议

### 1. 最佳实践

1. **定期清理缓存**: 建议每天清理一次缓存
2. **监控数据质量**: 定期检查CSV文件的完整性
3. **合理使用缓存**: 根据业务需求调整缓存时间
4. **分批处理**: 大量数据查询时使用分页

### 2. 性能建议

1. **使用Redis**: 生产环境建议使用Redis缓存
2. **SSD存储**: 历史数据文件建议存储在SSD上
3. **内存配置**: 根据数据量调整服务器内存
4. **并发控制**: 控制并发查询数量

### 3. 扩展建议

1. **数据格式**: 考虑使用Parquet格式提高性能
2. **数据压缩**: 对历史数据进行压缩存储
3. **分布式**: 大规模部署时考虑分布式存储
4. **实时同步**: 建立实时数据与历史数据的同步机制

## 🎉 总结

通过集成历史数据功能，量化投资平台现在能够：

1. **充分利用历史数据**: 不浪费已有的CSV历史数据
2. **保证数据连续性**: 实时数据和历史数据无缝衔接
3. **提供丰富功能**: 历史数据查询、分析、可视化
4. **优化用户体验**: 快速响应、智能缓存、友好界面
5. **支持扩展**: 灵活的架构支持未来功能扩展

这个解决方案既保证了实时数据的获取，又充分利用了历史数据资源，为量化投资分析提供了强大的数据支撑。

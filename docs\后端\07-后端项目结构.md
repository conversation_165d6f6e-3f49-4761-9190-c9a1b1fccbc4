# 后端项目结构详细说明

## 📁 完整目录结构

```
quant-backend/
├── .github/                          # GitHub配置
│   ├── workflows/                   # CI/CD工作流
│   │   ├── test.yml                # 测试流程
│   │   ├── build.yml               # 构建流程
│   │   └── deploy.yml              # 部署流程
│   ├── ISSUE_TEMPLATE/             # Issue模板
│   └── PULL_REQUEST_TEMPLATE.md    # PR模板
├── .vscode/                         # VSCode配置
│   ├── settings.json               # 编辑器设置
│   ├── launch.json                 # 调试配置
│   └── extensions.json             # 推荐扩展
├── docs/                           # 项目文档
│   ├── api/                        # API文档
│   ├── deployment/                 # 部署文档
│   ├── development/                # 开发文档
│   └── architecture/               # 架构文档
├── scripts/                        # 脚本工具
│   ├── init_db.py                 # 数据库初始化
│   ├── data_importer.py           # 数据导入工具
│   ├── ctp_connector.py           # CTP连接测试
│   ├── backup.py                  # 数据备份脚本
│   └── deploy.sh                  # 部署脚本
├── tests/                          # 测试目录
│   ├── __init__.py
│   ├── conftest.py                # pytest配置
│   ├── unit/                      # 单元测试
│   │   ├── test_services/         # 服务层测试
│   │   ├── test_models/           # 模型测试
│   │   └── test_utils/            # 工具函数测试
│   ├── integration/               # 集成测试
│   │   ├── test_api/              # API测试
│   │   ├── test_database/         # 数据库测试
│   │   └── test_ctp/              # CTP接口测试
│   └── fixtures/                  # 测试数据
│       ├── market_data.json       # 行情测试数据
│       └── user_data.json         # 用户测试数据
├── app/                            # 应用核心代码
│   ├── __init__.py
│   ├── main.py                    # FastAPI应用入口
│   ├── api/                       # API路由层
│   │   ├── __init__.py
│   │   ├── deps.py                # 依赖注入
│   │   ├── v1/                    # API v1版本
│   │   │   ├── __init__.py
│   │   │   ├── auth.py            # 认证相关API
│   │   │   ├── user.py            # 用户管理API
│   │   │   ├── market.py          # 行情数据API
│   │   │   ├── trading.py         # 交易相关API
│   │   │   ├── strategy.py        # 策略管理API
│   │   │   ├── backtest.py        # 回测分析API
│   │   │   ├── portfolio.py       # 投资组合API
│   │   │   ├── risk.py            # 风险管理API
│   │   │   └── notification.py    # 通知相关API
│   │   └── websocket/              # WebSocket服务
│   │       ├── __init__.py         # WebSocket模块初始化
│   │       ├── connection.py       # 原生WebSocket连接管理
│   │       ├── handlers.py         # WebSocket消息处理器
│   │       ├── market_data.py      # 行情数据推送
│   │       ├── trading.py          # 交易数据推送
│   │       └── notifications.py    # 系统通知推送
│   ├── core/                      # 核心功能模块
│   │   ├── __init__.py
│   │   ├── config.py              # 配置管理
│   │   ├── security.py            # 安全认证
│   │   ├── scheduler.py           # 任务调度
│   │   ├── exceptions.py          # 自定义异常
│   │   ├── middleware.py          # 中间件
│   │   ├── logging.py             # 日志配置
│   │   └── ctp_wrapper.py         # CTP接口封装
│   ├── db/                        # 数据库层
│   │   ├── __init__.py
│   │   ├── base.py                # 数据库基类
│   │   ├── session.py             # 会话管理
│   │   ├── init_db.py             # 数据库初始化
│   │   ├── models/                # ORM模型
│   │   │   ├── __init__.py
│   │   │   ├── base.py            # 基础模型类
│   │   │   ├── user.py            # 用户模型
│   │   │   ├── account.py         # 账户模型
│   │   │   ├── position.py        # 持仓模型
│   │   │   ├── order.py           # 订单模型
│   │   │   ├── trade.py           # 成交模型
│   │   │   ├── strategy.py        # 策略模型
│   │   │   ├── backtest.py        # 回测模型
│   │   │   ├── market_data.py     # 行情数据模型
│   │   │   └── notification.py    # 通知模型
│   │   ├── crud/                  # CRUD操作
│   │   │   ├── __init__.py
│   │   │   ├── base.py            # 基础CRUD类
│   │   │   ├── user.py            # 用户CRUD
│   │   │   ├── order.py           # 订单CRUD
│   │   │   ├── position.py        # 持仓CRUD
│   │   │   ├── strategy.py        # 策略CRUD
│   │   │   └── market_data.py     # 行情数据CRUD
│   │   └── migrations/            # 数据库迁移
│   │       └── versions/          # 迁移版本文件
│   ├── services/                  # 业务服务层
│   │   ├── __init__.py
│   │   ├── base.py                # 基础服务类
│   │   ├── auth_service.py        # 认证服务
│   │   ├── user_service.py        # 用户服务
│   │   ├── market_service.py      # 行情服务
│   │   ├── trading_service.py     # 交易服务
│   │   ├── strategy_service.py    # 策略服务
│   │   ├── backtest_service.py    # 回测服务
│   │   ├── portfolio_service.py   # 投资组合服务
│   │   ├── risk_service.py        # 风险管理服务
│   │   ├── report_service.py      # 报告生成服务
│   │   └── notification_service.py # 通知服务
│   ├── schemas/                   # Pydantic数据模型
│   │   ├── __init__.py
│   │   ├── base.py                # 基础Schema
│   │   ├── user.py                # 用户Schema
│   │   ├── auth.py                # 认证Schema
│   │   ├── order.py               # 订单Schema
│   │   ├── position.py            # 持仓Schema
│   │   ├── trade.py               # 成交Schema
│   │   ├── strategy.py            # 策略Schema
│   │   ├── backtest.py            # 回测Schema
│   │   ├── market.py              # 行情Schema
│   │   ├── portfolio.py           # 投资组合Schema
│   │   ├── risk.py                # 风险Schema
│   │   └── notification.py        # 通知Schema
│   ├── utils/                     # 工具函数
│   │   ├── __init__.py
│   │   ├── date_utils.py          # 时间处理工具
│   │   ├── data_utils.py          # 数据处理工具
│   │   ├── chart_data_utils.py    # 图表数据处理工具
│   │   ├── calculation_utils.py   # 计算工具
│   │   ├── format_utils.py        # 格式化工具
│   │   ├── validation_utils.py    # 验证工具
│   │   ├── encryption_utils.py    # 加密工具
│   │   ├── cache_utils.py         # 缓存工具
│   │   └── file_utils.py          # 文件处理工具
│   ├── tasks/                     # Celery异步任务
│   │   ├── __init__.py
│   │   ├── celery_app.py          # Celery应用配置
│   │   ├── backtest_tasks.py      # 回测任务
│   │   ├── data_tasks.py          # 数据处理任务
│   │   ├── report_tasks.py        # 报告生成任务
│   │   └── notification_tasks.py  # 通知任务
│   ├── events/                    # 事件定义
│   │   ├── __init__.py
│   │   ├── market_events.py       # 行情事件
│   │   ├── trading_events.py      # 交易事件
│   │   └── system_events.py       # 系统事件
│   └── constants/                 # 常量定义
│       ├── __init__.py
│       ├── trading.py             # 交易常量
│       ├── market.py              # 行情常量
│       └── system.py              # 系统常量
├── data/                          # 数据存储目录
│   ├── historical/                # 历史数据
│   │   ├── kline/                 # K线数据
│   │   ├── tick/                  # Tick数据
│   │   └── fundamental/           # 基本面数据
│   ├── realtime/                  # 实时数据缓存
│   ├── reports/                   # 生成的报告
│   │   ├── backtest/              # 回测报告
│   │   └── performance/           # 绩效报告
│   └── logs/                      # 日志文件
│       ├── app.log                # 应用日志
│       ├── trade.log              # 交易日志
│       └── error.log              # 错误日志
├── docker/                        # Docker配置
│   ├── Dockerfile                 # 主应用镜像
│   ├── Dockerfile.worker          # Worker镜像
│   ├── docker-compose.yml         # 开发环境编排
│   ├── docker-compose.prod.yml    # 生产环境编排
│   └── nginx/                     # Nginx配置
│       ├── nginx.conf             # Nginx主配置
│       └── ssl/                   # SSL证书
├── k8s/                           # Kubernetes配置
│   ├── namespace.yaml             # 命名空间
│   ├── configmap.yaml             # 配置映射
│   ├── secret.yaml                # 密钥配置
│   ├── deployment.yaml            # 部署配置
│   ├── service.yaml               # 服务配置
│   ├── ingress.yaml               # 入口配置
│   └── hpa.yaml                   # 自动扩缩容
├── monitoring/                    # 监控配置
│   ├── prometheus/                # Prometheus配置
│   │   ├── prometheus.yml         # 主配置
│   │   └── rules/                 # 告警规则
│   ├── grafana/                   # Grafana配置
│   │   ├── dashboards/            # 仪表盘
│   │   └── provisioning/          # 自动配置
│   └── alertmanager/              # 告警管理器
│       └── alertmanager.yml       # 告警配置
├── .env.example                   # 环境变量示例
├── .env.development               # 开发环境变量
├── .env.production                # 生产环境变量
├── .gitignore                     # Git忽略文件
├── .dockerignore                  # Docker忽略文件
├── requirements.txt               # Python依赖
├── requirements-dev.txt           # 开发依赖
├── pyproject.toml                 # 项目配置
├── alembic.ini                    # Alembic配置
├── pytest.ini                    # pytest配置
├── Makefile                       # Make命令
├── README.md                      # 项目说明
└── CHANGELOG.md                   # 更新日志
```

## 📦 关键文件内容示例

### requirements.txt
```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.0
redis==5.0.1

# 数据处理
pandas==2.1.4
numpy==1.25.2
ta-lib==0.4.26

# CTP接口
openctp==*******

# 任务队列
celery==5.3.4
kombu==5.3.4

# 安全认证
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 监控
prometheus-client==0.19.0
sentry-sdk==1.39.1

# 工具库
python-dateutil==2.8.2
pytz==2023.3

# WebSocket (原生WebSocket，移除python-socketio)
websockets==12.0
```

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/quant
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=quant
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  worker:
    build: .
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=**************************************/quant
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data

volumes:
  postgres_data:
  redis_data:
```

## 📂 目录说明

### 🏗️ 架构分层

#### 1. API层 (app/api/)
- **路由定义**: RESTful API端点定义
- **请求处理**: HTTP请求参数验证与响应
- **依赖注入**: 服务层依赖管理
- **WebSocket**: 实时通信处理

#### 2. 服务层 (app/services/)
- **业务逻辑**: 核心业务逻辑实现
- **外部接口**: 第三方服务集成
- **数据处理**: 复杂数据处理与计算
- **缓存管理**: Redis缓存操作

#### 3. 数据层 (app/db/)
- **ORM模型**: SQLAlchemy数据模型
- **CRUD操作**: 数据库增删改查封装
- **数据迁移**: Alembic数据库版本管理
- **会话管理**: 数据库连接池管理

#### 4. 工具层 (app/utils/)
- **通用工具**: 可复用的工具函数
- **数据处理**: NumPy/Pandas数据操作
- **格式化**: 数据格式转换
- **验证**: 数据验证与清洗

### 🎯 模块功能说明

#### 核心模块 (app/core/)
```python
# config.py - 配置管理
class Settings(BaseSettings):
    app_name: str = "Quant Backend"
    database_url: str
    redis_url: str
    ctp_broker_id: str
    jwt_secret: str
    
    class Config:
        env_file = ".env"

# security.py - 安全认证
def create_access_token(data: dict) -> str:
    """创建JWT访问令牌"""
    
def verify_token(token: str) -> dict:
    """验证JWT令牌"""

# ctp_wrapper.py - CTP接口封装
class CTPService:
    def __init__(self, config: CTPConfig):
        """初始化CTP连接"""
    
    async def place_order(self, order: OrderRequest) -> str:
        """下单接口"""
    
    async def cancel_order(self, order_id: str) -> bool:
        """撤单接口"""
```

#### 服务模块 (app/services/)
```python
# market_service.py - 行情服务
class MarketService:
    async def get_realtime_quote(self, symbol: str) -> QuoteData:
        """获取实时行情"""
    
    async def get_kline_data(self, symbol: str, period: str) -> List[KLineData]:
        """获取K线数据"""
    
    async def subscribe_market_data(self, symbols: List[str]):
        """订阅行情数据"""

# trading_service.py - 交易服务
class TradingService:
    async def place_order(self, order: OrderRequest) -> OrderResponse:
        """提交订单"""
    
    async def get_positions(self, user_id: str) -> List[Position]:
        """获取持仓"""
    
    async def get_account_info(self, user_id: str) -> AccountInfo:
        """获取账户信息"""

# backtest_service.py - 回测服务
class BacktestService:
    async def run_backtest(self, params: BacktestParams) -> BacktestResult:
        """运行回测"""
    
    async def generate_report(self, result: BacktestResult) -> str:
        """生成回测报告"""
```

#### 数据模型 (app/db/models/)
```python
# user.py - 用户模型
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

# order.py - 订单模型
class Order(Base):
    __tablename__ = "orders"
    
    id = Column(BigInteger, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    symbol = Column(String(32), index=True)
    side = Column(Enum(OrderSide))
    order_type = Column(Enum(OrderType))
    price = Column(Numeric(18, 4))
    quantity = Column(Integer)
    status = Column(Enum(OrderStatus))
    created_at = Column(DateTime, default=datetime.utcnow)
```

#### Pydantic模型 (app/schemas/)
```python
# order.py - 订单Schema
class OrderRequest(BaseModel):
    symbol: str
    side: OrderSide
    order_type: OrderType
    price: Optional[Decimal] = None
    quantity: int
    
class OrderResponse(BaseModel):
    id: str
    symbol: str
    side: OrderSide
    status: OrderStatus
    created_at: datetime
    
    class Config:
        from_attributes = True
```

### 🔧 配置管理

#### 环境配置分离
```python
# 开发环境 (.env.development)
ENV=development
DEBUG=True
DATABASE_URL=postgresql://user:pass@localhost:5432/quant_dev
REDIS_URL=redis://localhost:6379/0

# 生产环境 (.env.production)
ENV=production
DEBUG=False
DATABASE_URL=***********************************/quant_prod
REDIS_URL=redis://prod-redis:6379/0
```

#### 配置类设计
```python
class Settings(BaseSettings):
    # 基础配置
    app_name: str = "Quant Backend"
    version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str
    database_pool_size: int = 10
    database_max_overflow: int = 20
    
    # Redis配置
    redis_url: str
    redis_expire_time: int = 3600
    
    # CTP配置
    ctp_broker_id: str
    ctp_user_id: str
    ctp_password: str
    ctp_md_address: str
    ctp_td_address: str
    
    # JWT配置
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    
    # Celery配置
    celery_broker_url: str
    celery_result_backend: str
    
    class Config:
        env_file = ".env"
        case_sensitive = True
```

### 🚀 任务队列 (app/tasks/)

#### Celery配置
```python
# celery_app.py
from celery import Celery

celery_app = Celery(
    "quant-backend",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks.backtest_tasks", "app.tasks.data_tasks"]
)

# 任务配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_routes={
        "app.tasks.backtest_tasks.*": {"queue": "backtest"},
        "app.tasks.data_tasks.*": {"queue": "data"},
    }
)
```

#### 回测任务
```python
# backtest_tasks.py
@celery_app.task(bind=True)
def run_backtest_task(self, backtest_id: str, params: dict):
    """异步回测任务"""
    try:
        # 更新任务状态
        self.update_state(state="PROGRESS", meta={"progress": 0})
        
        # 执行回测
        backtest_service = BacktestService()
        result = backtest_service.run_backtest(params)
        
        # 生成报告
        report_url = backtest_service.generate_report(result)
        
        return {
            "status": "SUCCESS",
            "result": result,
            "report_url": report_url
        }
    except Exception as exc:
        self.update_state(state="FAILURE", meta={"error": str(exc)})
        raise
```

### 📊 监控与日志

#### 日志配置
```python
# app/core/logging.py
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    """配置日志系统"""
    
    # 创建日志格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 文件处理器
    file_handler = RotatingFileHandler(
        "data/logs/app.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # 特定模块日志
    trading_logger = logging.getLogger("trading")
    trading_handler = RotatingFileHandler(
        "data/logs/trade.log",
        maxBytes=10*1024*1024,
        backupCount=10
    )
    trading_handler.setFormatter(formatter)
    trading_logger.addHandler(trading_handler)
```

#### Prometheus指标
```python
# app/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 请求计数器
REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total HTTP requests",
    ["method", "endpoint", "status"]
)

# 响应时间直方图
REQUEST_DURATION = Histogram(
    "http_request_duration_seconds",
    "HTTP request duration",
    ["method", "endpoint"]
)

# 活跃连接数
ACTIVE_CONNECTIONS = Gauge(
    "websocket_connections_active",
    "Active WebSocket connections"
)

# 交易指标
ORDERS_TOTAL = Counter(
    "orders_total",
    "Total orders placed",
    ["symbol", "side", "status"]
)
```


# 后端配置文件详细说明

## 🔧 配置文件架构

```mermaid
graph TD
    A[环境变量文件] --> B[应用配置]
    C[数据库配置] --> B
    D[Redis配置] --> B
    E[CTP配置] --> B
    F[Celery配置] --> B
    G[日志配置] --> B
    H[安全配置] --> B
    
    B --> I[开发环境]
    B --> J[测试环境]
    B --> K[生产环境]
```

## 📋 环境变量配置

### .env.example (环境变量模板)
```bash
# =============================================================================
# 量化交易后端环境配置模板
# =============================================================================

# 基础配置
ENV=development
DEBUG=true
APP_NAME="Quant Trading Backend"
VERSION=1.0.0
SECRET_KEY=your-super-secret-key-change-this-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=true

# 数据库配置
DATABASE_URL=postgresql+asyncpg://quant_user:quant_password@localhost:5432/quant_db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=100
REDIS_EXPIRE_TIME=3600

# CTP配置
CTP_BROKER_ID=9999
CTP_USER_ID=your_user_id
CTP_PASSWORD=your_password
CTP_MD_ADDRESS=tcp://***************:10010
CTP_TD_ADDRESS=tcp://***************:10000
CTP_APP_ID=simnow_client_test
CTP_AUTH_CODE=0000000000000000

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_TLS=true
SMTP_SSL=false

# 第三方服务配置
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_PORT=9090

# 文件存储配置
UPLOAD_PATH=./data/uploads
REPORTS_PATH=./data/reports
LOGS_PATH=./data/logs
MAX_FILE_SIZE=10485760  # 10MB

# 安全配置 (修复：添加Vite默认端口5173)
CORS_ORIGINS=["http://localhost:5173", "http://localhost:3000", "http://localhost:8080"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO
```

### .env.development (开发环境)
```bash
# 开发环境配置
ENV=development
DEBUG=true
RELOAD=true

# 数据库配置
DATABASE_URL=postgresql+asyncpg://dev_user:dev_pass@localhost:5432/quant_dev
DATABASE_ECHO=true  # 开发环境显示SQL

# Redis配置
REDIS_URL=redis://localhost:6379/0

# CTP配置 (SimNow仿真环境)
CTP_BROKER_ID=9999
CTP_MD_ADDRESS=tcp://***************:10010
CTP_TD_ADDRESS=tcp://***************:10000

# 日志配置
LOG_LEVEL=DEBUG

# 安全配置 (开发环境宽松)
CORS_ORIGINS=["*"]
RATE_LIMIT_REQUESTS=1000
```

### .env.production (生产环境)
```bash
# 生产环境配置
ENV=production
DEBUG=false
RELOAD=false

# 数据库配置
DATABASE_URL=postgresql+asyncpg://prod_user:secure_password@prod-db:5432/quant_prod
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=0

# Redis配置
REDIS_URL=redis://:redis_password@prod-redis:6379/0
REDIS_PASSWORD=secure_redis_password

# CTP配置 (生产环境)
CTP_BROKER_ID=your_production_broker_id
CTP_USER_ID=your_production_user_id
CTP_PASSWORD=your_production_password
CTP_MD_ADDRESS=tcp://production-md-server:10010
CTP_TD_ADDRESS=tcp://production-td-server:10000

# 安全配置
SECRET_KEY=your-super-secure-secret-key-for-production
JWT_SECRET_KEY=your-super-secure-jwt-key-for-production
CORS_ORIGINS=["https://yourdomain.com"]
ALLOWED_HOSTS=["yourdomain.com", "api.yourdomain.com"]

# 监控配置
SENTRY_DSN=https://<EMAIL>/project-id
LOG_LEVEL=INFO
```

## ⚙️ 应用配置类

### app/core/config.py
```python
from pydantic import BaseSettings, validator
from typing import List, Optional, Union
import secrets
from functools import lru_cache

class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "Quant Trading Backend"
    VERSION: str = "1.0.0"
    ENV: str = "development"
    DEBUG: bool = False
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    RELOAD: bool = False
    
    # 数据库配置
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    DATABASE_ECHO: bool = False
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str]) -> str:
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    REDIS_MAX_CONNECTIONS: int = 100
    REDIS_EXPIRE_TIME: int = 3600
    
    # CTP配置
    CTP_BROKER_ID: str
    CTP_USER_ID: str
    CTP_PASSWORD: str
    CTP_MD_ADDRESS: str
    CTP_TD_ADDRESS: str
    CTP_APP_ID: str = "simnow_client_test"
    CTP_AUTH_CODE: str = "0000000000000000"
    
    @validator("CTP_BROKER_ID", "CTP_USER_ID", "CTP_PASSWORD", pre=True)
    def validate_ctp_config(cls, v: str) -> str:
        if not v:
            raise ValueError("CTP configuration is required")
        return v
    
    # JWT配置
    JWT_SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    SMTP_SSL: bool = False
    
    # 第三方服务配置
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_PORT: int = 9090
    
    # 文件存储配置
    UPLOAD_PATH: str = "./data/uploads"
    REPORTS_PATH: str = "./data/reports"
    LOGS_PATH: str = "./data/logs"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 安全配置
    CORS_ORIGINS: List[str] = ["http://localhost:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    LOG_LEVEL: str = "INFO"
    
    # 业务配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    DEFAULT_KLINE_LIMIT: int = 500
    MAX_KLINE_LIMIT: int = 5000
    
    # 风控配置
    MAX_ORDER_AMOUNT: float = 1000000.0  # 单笔订单最大金额
    MAX_DAILY_LOSS: float = 50000.0      # 单日最大亏损
    MAX_POSITION_RATIO: float = 0.8      # 最大持仓比例
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    @property
    def is_development(self) -> bool:
        return self.ENV == "development"
    
    @property
    def is_production(self) -> bool:
        return self.ENV == "production"
    
    @property
    def is_testing(self) -> bool:
        return self.ENV == "testing"

@lru_cache()
def get_settings() -> Settings:
    """获取配置单例"""
    return Settings()

# 全局配置实例
settings = get_settings()
```

## 🗄️ 数据库配置

### alembic.ini
```ini
# Alembic数据库迁移配置

[alembic]
# 迁移脚本位置
script_location = app/db/migrations

# 模板文件
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s

# 时区
timezone = Asia/Shanghai

# 数据库连接URL (将从环境变量读取)
sqlalchemy.url = 

# 日志配置
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
```

### app/db/migrations/env.py
```python
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.core.config import settings
from app.db.base import Base

# Alembic Config对象
config = context.config

# 设置数据库URL
config.set_main_option("sqlalchemy.url", settings.DATABASE_URL)

# 配置日志
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 目标元数据
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """离线迁移模式"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """在线迁移模式"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

## 📊 Celery配置

### app/tasks/celery_app.py
```python
from celery import Celery
from kombu import Queue
from app.core.config import settings
import os

# 创建Celery应用
celery_app = Celery(
    "quant-backend",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.backtest_tasks",
        "app.tasks.data_tasks",
        "app.tasks.report_tasks",
        "app.tasks.notification_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 序列化配置
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    accept_content=[settings.CELERY_TASK_SERIALIZER],
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    
    # 时区配置
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由配置
    task_routes={
        "app.tasks.backtest_tasks.*": {"queue": "backtest"},
        "app.tasks.data_tasks.*": {"queue": "data"},
        "app.tasks.report_tasks.*": {"queue": "report"},
        "app.tasks.notification_tasks.*": {"queue": "notification"},
    },
    
    # 队列配置
    task_queues=(
        Queue("backtest", routing_key="backtest", priority=5),
        Queue("data", routing_key="data", priority=8),
        Queue("report", routing_key="report", priority=3),
        Queue("notification", routing_key="notification", priority=6),
        Queue("default", routing_key="default", priority=1),
    ),
    
    # 任务执行配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,
    task_soft_time_limit=300,  # 5分钟软限制
    task_time_limit=600,       # 10分钟硬限制
    
    # 结果后端配置
    result_expires=3600,
    result_persistent=True,
    
    # Worker配置
    worker_send_task_events=True,
    task_send_sent_event=True,
    worker_hijack_root_logger=False,
    
    # 重试配置
    task_default_retry_delay=60,
    task_max_retries=3,
    
    # 监控配置
    worker_enable_remote_control=True,
    
    # 安全配置
    worker_disable_rate_limits=False,
    
    # 性能配置
    worker_pool_restarts=True,
    worker_max_tasks_per_child=1000,
    worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
    worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
)

# 任务装饰器配置
def task_config(**kwargs):
    """任务配置装饰器"""
    default_config = {
        "bind": True,
        "autoretry_for": (Exception,),
        "retry_kwargs": {"max_retries": 3, "countdown": 60},
        "acks_late": True,
        "reject_on_worker_lost": True,
    }
    default_config.update(kwargs)
    return celery_app.task(**default_config)
```

## 📝 日志配置

### app/core/logging.py
```python
import logging
import logging.config
from typing import Dict, Any
import os
from app.core.config import settings

def setup_logging() -> None:
    """配置日志系统"""
    
    # 确保日志目录存在
    os.makedirs(settings.LOGS_PATH, exist_ok=True)
    
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(module)s %(funcName)s %(lineno)d %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filename": f"{settings.LOGS_PATH}/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": f"{settings.LOGS_PATH}/error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "trading_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": f"{settings.LOGS_PATH}/trading.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8",
            },
            "audit_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": f"{settings.LOGS_PATH}/audit.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 20,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "": {  # root logger
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "app": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "trading": {
                "level": "INFO",
                "handlers": ["trading_file", "console"],
                "propagate": False,
            },
            "audit": {
                "level": "INFO",
                "handlers": ["audit_file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "error_file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            "celery": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
        },
    }
    
    # 生产环境特殊配置
    if settings.is_production:
        # 生产环境不输出到控制台
        logging_config["loggers"][""]["handlers"] = ["file", "error_file"]
        logging_config["loggers"]["app"]["handlers"] = ["file", "error_file"]
        
        # 添加Sentry处理器
        if settings.SENTRY_DSN:
            logging_config["handlers"]["sentry"] = {
                "class": "sentry_sdk.integrations.logging.SentryHandler",
                "level": "ERROR",
            }
            logging_config["loggers"][""]["handlers"].append("sentry")
            logging_config["loggers"]["app"]["handlers"].append("sentry")
    
    logging.config.dictConfig(logging_config)

# 专用日志器
def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    return logging.getLogger(name)

# 交易日志器
trading_logger = get_logger("trading")

# 审计日志器
audit_logger = get_logger("audit")

# 应用日志器
app_logger = get_logger("app")
```

## 🔒 安全配置

### app/core/security_config.py
```python
from typing import List, Dict, Any
from app.core.config import settings

class SecurityConfig:
    """安全配置类"""
    
    # CORS配置
    CORS_CONFIG = {
        "allow_origins": settings.CORS_ORIGINS,
        "allow_credentials": True,
        "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["*"],
        "expose_headers": ["X-Total-Count", "X-Page-Count"],
    }
    
    # 限流配置
    RATE_LIMIT_CONFIG = {
        "default": f"{settings.RATE_LIMIT_REQUESTS}/{settings.RATE_LIMIT_WINDOW}minute",
        "auth": "5/minute",
        "trading": "10/minute",
        "market_data": "100/minute",
    }
    
    # 密码策略
    PASSWORD_POLICY = {
        "min_length": 8,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_numbers": True,
        "require_special_chars": True,
        "max_age_days": 90,
        "history_count": 5,
    }
    
    # 会话配置
    SESSION_CONFIG = {
        "max_age": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "secure": settings.is_production,
        "httponly": True,
        "samesite": "strict" if settings.is_production else "lax",
    }
    
    # 文件上传安全配置
    UPLOAD_CONFIG = {
        "max_file_size": settings.MAX_FILE_SIZE,
        "allowed_extensions": [".csv", ".xlsx", ".json", ".txt"],
        "scan_for_malware": settings.is_production,
        "quarantine_suspicious": True,
    }
    
    # API安全配置
    API_SECURITY_CONFIG = {
        "require_https": settings.is_production,
        "validate_content_type": True,
        "max_request_size": 10 * 1024 * 1024,  # 10MB
        "timeout": 30,
    }
    
    # 数据库安全配置
    DATABASE_SECURITY_CONFIG = {
        "use_ssl": settings.is_production,
        "encrypt_sensitive_fields": True,
        "audit_all_changes": True,
        "backup_encryption": True,
    }

# 安全中间件配置
SECURITY_MIDDLEWARE_CONFIG = {
    "trusted_hosts": settings.ALLOWED_HOSTS,
    "force_https": settings.is_production,
    "hsts_max_age": 31536000 if settings.is_production else 0,
    "hsts_include_subdomains": settings.is_production,
    "referrer_policy": "strict-origin-when-cross-origin",
    "content_type_options": "nosniff",
    "frame_options": "DENY",
    "xss_protection": "1; mode=block",
}
```

## 🐳 Docker配置

### Dockerfile
```dockerfile
# 多阶段构建Dockerfile
FROM python:3.9-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --user -r requirements.txt

# 生产镜像
FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/root/.local/bin:$PATH"

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# 从builder阶段复制Python包
COPY --from=builder /root/.local /root/.local

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data/logs data/reports data/uploads \
    && chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  # 后端API服务
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - quant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery-worker:
    build: .
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    environment:
      - ENV=development
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data
    networks:
      - quant-network
    restart: unless-stopped

  # Celery Beat (定时任务)
  celery-beat:
    build: .
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - ENV=development
      - CELERY_BROKER_URL=redis://redis:6379/1
    depends_on:
      - redis
    volumes:
      - ./data:/app/data
    networks:
      - quant-network
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: timescale/timescaledb:latest-pg14
    environment:
      - POSTGRES_DB=quant_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - quant-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - quant-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  quant-network:
    driver: bridge
```


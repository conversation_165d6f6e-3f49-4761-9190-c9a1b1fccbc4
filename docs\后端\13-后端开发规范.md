# 13-后端开发规范

## 开发规范概述

### 规范目标

- 确保代码质量和可维护性
- 统一团队开发风格
- 提高开发效率
- 降低维护成本
- 保证系统安全性

## 代码规范

### 1. Python代码规范

#### 基本规范

```python
# 遵循PEP 8规范
# 使用4个空格缩进
# 行长度不超过88字符（Black格式化器标准）
# 使用有意义的变量名

# 正确示例
def calculate_moving_average(prices: List[float], window: int) -> List[float]:
    """
    计算移动平均线
    
    Args:
        prices: 价格列表
        window: 窗口大小
        
    Returns:
        移动平均线列表
    """
    if len(prices) < window:
        raise ValueError("价格数据不足")
    
    moving_averages = []
    for i in range(window - 1, len(prices)):
        avg = sum(prices[i - window + 1:i + 1]) / window
        moving_averages.append(avg)
    
    return moving_averages

# 错误示例
def calc_ma(p, w):
    if len(p)<w:
        raise ValueError("not enough data")
    ma=[]
    for i in range(w-1,len(p)):
        avg=sum(p[i-w+1:i+1])/w
        ma.append(avg)
    return ma
```

#### 类型注解

```python
from typing import List, Dict, Optional, Union, Tuple
from decimal import Decimal
from datetime import datetime

# 强制使用类型注解
class TradingOrder:
    def __init__(
        self,
        symbol: str,
        side: str,
        quantity: Decimal,
        price: Optional[Decimal] = None,
        order_type: str = "MARKET"
    ) -> None:
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        self.price = price
        self.order_type = order_type
        self.created_at: datetime = datetime.now()
    
    def to_dict(self) -> Dict[str, Union[str, Decimal, datetime]]:
        return {
            "symbol": self.symbol,
            "side": self.side,
            "quantity": self.quantity,
            "price": self.price,
            "order_type": self.order_type,
            "created_at": self.created_at
        }

# 复杂类型定义
MarketData = Dict[str, Union[float, int, str]]
OrderBook = Dict[str, List[Tuple[float, float]]]
```

#### 异常处理

```python
# 自定义异常类
class TradingError(Exception):
    """交易相关异常基类"""
    pass

class InsufficientFundsError(TradingError):
    """资金不足异常"""
    pass

class InvalidOrderError(TradingError):
    """无效订单异常"""
    pass

# 异常处理规范
async def place_order(order: TradingOrder) -> Dict[str, Any]:
    try:
        # 验证订单
        await validate_order(order)
        
        # 检查资金
        if not await check_balance(order):
            raise InsufficientFundsError(f"资金不足，无法下单: {order.symbol}")
        
        # 提交订单
        result = await submit_order(order)
        return result
        
    except InsufficientFundsError:
        logger.error(f"资金不足: {order.symbol}")
        raise
    except InvalidOrderError as e:
        logger.error(f"无效订单: {e}")
        raise
    except Exception as e:
        logger.error(f"下单失败: {e}")
        raise TradingError(f"下单失败: {str(e)}")
```

### 2. 文档字符串规范

```python
def backtest_strategy(
    strategy: Strategy,
    start_date: datetime,
    end_date: datetime,
    initial_capital: Decimal = Decimal("100000")
) -> BacktestResult:
    """
    回测策略
    
    Args:
        strategy: 策略实例
        start_date: 回测开始日期
        end_date: 回测结束日期
        initial_capital: 初始资金，默认10万
        
    Returns:
        BacktestResult: 回测结果对象
            
    Raises:
        ValueError: 当开始日期晚于结束日期时
        DataNotFoundError: 当无法获取历史数据时
        
    Example:
        >>> strategy = MACDStrategy()
        >>> result = await backtest_strategy(strategy, start_date, end_date)
        >>> print(f"总收益率: {result.total_return:.2%}")
    """
    pass
```

### 3. 导入规范

```python
# 标准库导入
import asyncio
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional, Any

# 第三方库导入
import aioredis
import asyncpg
import numpy as np
import pandas as pd
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field

# 本地导入
from app.core.config import settings
from app.core.database import get_db
from app.models.trading import Order, Position
from app.services.market import MarketDataService
from app.utils.calculations import calculate_indicators
```

## 架构规范

### 1. 分层架构

```
app/
├── api/                    # API层
│   ├── v1/
│   │   ├── auth.py        # 认证相关API
│   │   ├── trading.py     # 交易相关API
│   │   └── market.py      # 行情相关API
│   └── dependencies.py    # 依赖注入
├── core/                  # 核心配置
│   ├── config.py         # 配置管理
│   ├── database.py       # 数据库连接
│   └── security.py       # 安全相关
├── models/               # 数据模型
│   ├── database/         # 数据库模型
│   └── schemas/          # Pydantic模型
├── services/             # 业务逻辑层
│   ├── trading/          # 交易服务
│   ├── market/           # 行情服务
│   └── strategy/         # 策略服务
└── utils/                # 工具函数
    ├── calculations.py   # 计算工具
    └── validators.py     # 验证工具
```

### 2. 依赖注入

```python
# app/api/dependencies.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.core.database import get_db
from app.services.auth import AuthService
from app.models.database.user import User

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db=Depends(get_db)
) -> User:
    """获取当前用户"""
    auth_service = AuthService(db)
    user = await auth_service.get_user_by_token(credentials.credentials)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据"
        )
    
    return user

async def get_trading_service(
    db=Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> TradingService:
    """获取交易服务"""
    return TradingService(db, current_user)
```

### 3. 服务层设计

```python
# app/services/trading/trading_service.py
from abc import ABC, abstractmethod
from typing import List, Optional
from app.models.database.user import User
from app.models.schemas.trading import OrderCreate, OrderResponse

class TradingServiceInterface(ABC):
    """交易服务接口"""
    
    @abstractmethod
    async def create_order(self, order: OrderCreate) -> OrderResponse:
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        pass
    
    @abstractmethod
    async def get_orders(self, status: Optional[str] = None) -> List[OrderResponse]:
        pass

class TradingService(TradingServiceInterface):
    """交易服务实现"""
    
    def __init__(self, db, user: User):
        self.db = db
        self.user = user
        self.ctp_client = CTPClient()
    
    async def create_order(self, order: OrderCreate) -> OrderResponse:
        # 验证订单
        await self._validate_order(order)
        
        # 风险检查
        await self._risk_check(order)
        
        # 提交订单
        result = await self.ctp_client.submit_order(order)
        
        # 保存到数据库
        db_order = await self._save_order(order, result)
        
        return OrderResponse.from_orm(db_order)
    
    async def _validate_order(self, order: OrderCreate) -> None:
        """验证订单"""
        if order.quantity <= 0:
            raise ValueError("订单数量必须大于0")
        
        if order.price is not None and order.price <= 0:
            raise ValueError("订单价格必须大于0")
    
    async def _risk_check(self, order: OrderCreate) -> None:
        """风险检查"""
        # 检查资金
        balance = await self._get_balance()
        required_margin = await self._calculate_margin(order)
        
        if balance < required_margin:
            raise InsufficientFundsError("资金不足")
```

## 数据库规范

### 1. 模型定义

```python
# app/models/database/trading.py
from sqlalchemy import Column, Integer, String, Decimal, DateTime, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Order(Base):
    __tablename__ = "orders"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True)
    
    # 外键
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 订单信息
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(String(10), nullable=False)  # BUY/SELL
    quantity = Column(Decimal(18, 8), nullable=False)
    price = Column(Decimal(18, 8), nullable=True)
    order_type = Column(String(20), nullable=False, default="MARKET")
    status = Column(String(20), nullable=False, default="PENDING", index=True)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="orders")
    
    # 索引
    __table_args__ = (
        Index('idx_symbol_created', 'symbol', 'created_at'),
        Index('idx_user_status', 'user_id', 'status'),
    )
```

### 2. 查询规范

```python
# app/repositories/trading_repository.py
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from app.models.database.trading import Order

class TradingRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_order(self, order_data: dict) -> Order:
        """创建订单"""
        order = Order(**order_data)
        self.db.add(order)
        await self.db.commit()
        await self.db.refresh(order)
        return order
    
    async def get_orders_by_user(
        self, 
        user_id: int, 
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Order]:
        """获取用户订单"""
        query = select(Order).where(Order.user_id == user_id)
        
        if status:
            query = query.where(Order.status == status)
        
        query = query.order_by(Order.created_at.desc())
        query = query.limit(limit).offset(offset)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_active_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取活跃订单"""
        query = select(Order).where(
            Order.status.in_(["PENDING", "PARTIAL_FILLED"])
        )
        
        if symbol:
            query = query.where(Order.symbol == symbol)
        
        result = await self.db.execute(query)
        return result.scalars().all()
```

## API规范

### 1. 路由设计

```python
# app/api/v1/trading.py
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from app.api.dependencies import get_current_user, get_trading_service
from app.models.schemas.trading import OrderCreate, OrderResponse
from app.services.trading import TradingService

router = APIRouter(prefix="/trading", tags=["trading"])

@router.post("/orders", response_model=OrderResponse)
async def create_order(
    order: OrderCreate,
    trading_service: TradingService = Depends(get_trading_service)
):
    """
    创建交易订单
    
    - **symbol**: 交易品种代码
    - **side**: 买卖方向 (BUY/SELL)
    - **quantity**: 交易数量
    - **price**: 交易价格 (限价单必填)
    - **order_type**: 订单类型 (MARKET/LIMIT)
    """
    try:
        return await trading_service.create_order(order)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except InsufficientFundsError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/orders", response_model=List[OrderResponse])
async def get_orders(
    status: Optional[str] = Query(None, description="订单状态筛选"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    trading_service: TradingService = Depends(get_trading_service)
):
    """获取订单列表"""
    return await trading_service.get_orders(status, limit, offset)

@router.delete("/orders/{order_id}")
async def cancel_order(
    order_id: str,
    trading_service: TradingService = Depends(get_trading_service)
):
    """取消订单"""
    success = await trading_service.cancel_order(order_id)
    if not success:
        raise HTTPException(status_code=404, detail="订单不存在或无法取消")
    
    return {"message": "订单已取消"}
```

### 2. 响应格式

```python
# app/models/schemas/common.py
from pydantic import BaseModel
from typing import Generic, TypeVar, Optional, Any

T = TypeVar('T')

class APIResponse(BaseModel, Generic[T]):
    """标准API响应格式"""
    code: int = 200
    message: str = "success"
    data: Optional[T] = None
    
class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应格式"""
    items: List[T]
    total: int
    page: int
    page_size: int
    has_next: bool

# 使用示例
@router.get("/orders", response_model=APIResponse[PaginatedResponse[OrderResponse]])
async def get_orders(...):
    orders = await trading_service.get_orders(...)
    total = await trading_service.count_orders(...)
    
    return APIResponse(
        data=PaginatedResponse(
            items=orders,
            total=total,
            page=page,
            page_size=page_size,
            has_next=total > (page * page_size)
        )
    )
```

## 测试规范

### 1. 单元测试

```python
# tests/services/test_trading_service.py
import pytest
from unittest.mock import AsyncMock, Mock
from decimal import Decimal
from app.services.trading import TradingService
from app.models.schemas.trading import OrderCreate

class TestTradingService:
    @pytest.fixture
    async def trading_service(self):
        """创建交易服务实例"""
        db_mock = AsyncMock()
        user_mock = Mock()
        user_mock.id = 1
        
        service = TradingService(db_mock, user_mock)
        service.ctp_client = AsyncMock()
        return service
    
    @pytest.mark.asyncio
    async def test_create_order_success(self, trading_service):
        """测试创建订单成功"""
        # 准备测试数据
        order_data = OrderCreate(
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("1"),
            price=Decimal("4000"),
            order_type="LIMIT"
        )
        
        # 模拟CTP返回
        trading_service.ctp_client.submit_order.return_value = {
            "order_id": "12345",
            "status": "PENDING"
        }
        
        # 执行测试
        result = await trading_service.create_order(order_data)
        
        # 验证结果
        assert result.symbol == "SHFE.rb2405"
        assert result.side == "BUY"
        assert result.status == "PENDING"
        trading_service.ctp_client.submit_order.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_order_invalid_quantity(self, trading_service):
        """测试创建订单数量无效"""
        order_data = OrderCreate(
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("0"),
            order_type="MARKET"
        )
        
        with pytest.raises(ValueError, match="订单数量必须大于0"):
            await trading_service.create_order(order_data)
```

### 2. 集成测试

```python
# tests/api/test_trading_api.py
import pytest
from httpx import AsyncClient
from app.main import app

class TestTradingAPI:
    @pytest.mark.asyncio
    async def test_create_order_api(self, auth_headers):
        """测试创建订单API"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "price": "4000",
                    "order_type": "LIMIT"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == "SHFE.rb2405"
            assert data["side"] == "BUY"
    
    @pytest.mark.asyncio
    async def test_create_order_unauthorized(self):
        """测试未授权访问"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "order_type": "MARKET"
                }
            )
            
            assert response.status_code == 401
```

## 安全规范

### 1. 输入验证

```python
# app/models/schemas/trading.py
from pydantic import BaseModel, Field, validator
from decimal import Decimal
from typing import Optional

class OrderCreate(BaseModel):
    symbol: str = Field(..., regex=r'^[A-Z]{2,10}\.[a-zA-Z0-9]{4,20}$', description="交易品种代码")
    side: str = Field(..., regex=r'^(BUY|SELL)$', description="买卖方向")
    quantity: Decimal = Field(..., gt=0, decimal_places=8, description="交易数量")
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=8, description="交易价格")
    order_type: str = Field("MARKET", regex=r'^(MARKET|LIMIT)$', description="订单类型")
    
    @validator('quantity')
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('数量必须大于0')
        if v > Decimal('1000000'):
            raise ValueError('数量不能超过1000000')
        return v
    
    @validator('price')
    def validate_price(cls, v, values):
        if values.get('order_type') == 'LIMIT' and v is None:
            raise ValueError('限价单必须指定价格')
        if v is not None and v <= 0:
            raise ValueError('价格必须大于0')
        return v
```

### 2. 权限控制

```python
# app/core/permissions.py
from enum import Enum
from typing import List
from app.models.database.user import User

class Permission(Enum):
    READ_MARKET_DATA = "read:market_data"
    PLACE_ORDER = "place:order"
    CANCEL_ORDER = "cancel:order"
    VIEW_POSITIONS = "view:positions"
    MANAGE_STRATEGIES = "manage:strategies"

class PermissionChecker:
    @staticmethod
    def has_permission(user: User, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        return permission.value in user.permissions
    
    @staticmethod
    def require_permissions(permissions: List[Permission]):
        """权限装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # 从参数中获取用户
                user = None
                for arg in args:
                    if isinstance(arg, User):
                        user = arg
                        break
                
                if not user:
                    raise HTTPException(status_code=401, detail="用户未认证")
                
                # 检查权限
                for permission in permissions:
                    if not PermissionChecker.has_permission(user, permission):
                        raise HTTPException(
                            status_code=403, 
                            detail=f"缺少权限: {permission.value}"
                        )
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
```

## 性能规范

### 1. 异步编程

```python
# 正确的异步编程模式
import asyncio
from typing import List

async def process_market_data_batch(symbols: List[str]) -> List[dict]:
    """批量处理行情数据"""
    tasks = []
    
    for symbol in symbols:
        task = asyncio.create_task(fetch_market_data(symbol))
        tasks.append(task)
    
    # 并发执行
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"处理 {symbols[i]} 失败: {result}")
        else:
            processed_results.append(result)
    
    return processed_results

# 使用信号量控制并发
async def process_with_semaphore(symbols: List[str], max_concurrent: int = 10):
    """使用信号量控制并发数"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_single(symbol: str):
        async with semaphore:
            return await fetch_market_data(symbol)
    
    tasks = [process_single(symbol) for symbol in symbols]
    return await asyncio.gather(*tasks)
```

### 2. 缓存策略

```python
# app/utils/cache.py
import aioredis
from typing import Any, Optional
from functools import wraps
import json
import pickle

class CacheManager:
    def __init__(self, redis_url: str):
        self.redis = aioredis.from_url(redis_url)
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            data = await self.redis.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
        return None
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存"""
        try:
            data = pickle.dumps(value)
            await self.redis.set(key, data, ex=expire)
            return True
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False

def cache_result(expire: int = 3600, key_prefix: str = ""):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cache_manager = CacheManager(settings.REDIS_URL)
            cached_result = await cache_manager.get(cache_key)
            
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 存储到缓存
            await cache_manager.set(cache_key, result, expire)
            
            return result
        return wrapper
    return decorator
```

## 总结

本开发规范涵盖了：

1. **代码规范**：Python编码标准、类型注解、异常处理
2. **架构规范**：分层架构、依赖注入、服务设计
3. **数据库规范**：模型定义、查询优化
4. **API规范**：路由设计、响应格式
5. **测试规范**：单元测试、集成测试
6. **安全规范**：输入验证、权限控制
7. **性能规范**：异步编程、缓存策略

遵循这些规范可以确保代码质量，提高开发效率，保证系统的可维护性和安全性。 
# 14-后端测试方案

## 测试策略概述

### 测试金字塔

```mermaid
graph TB
    subgraph "测试金字塔"
        A[单元测试 70%] --> B[集成测试 20%]
        B --> C[端到端测试 10%]
    end
    
    subgraph "测试类型"
        D[功能测试] --> E[性能测试]
        E --> F[安全测试]
        F --> G[兼容性测试]
    end
```

### 测试目标
- 确保代码质量和功能正确性
- 提高系统稳定性和可靠性
- 降低生产环境风险
- 支持持续集成和部署
- 提供回归测试保障

## 单元测试

### 1. 测试框架配置

```python
# pytest.ini
[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --cov=app --cov-report=html --cov-report=term
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    trading: 交易相关测试
    market: 行情相关测试
```

```python
# conftest.py
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock
from app.core.config import settings
from app.core.database import get_db
from app.models.database.user import User

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def db_session():
    """数据库会话"""
    session = AsyncMock()
    yield session
    await session.close()

@pytest.fixture
def mock_user():
    """模拟用户"""
    user = Mock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.is_active = True
    user.permissions = ["read:market_data", "place:order"]
    return user

@pytest.fixture
def mock_ctp_client():
    """模拟CTP客户端"""
    client = AsyncMock()
    client.is_connected.return_value = True
    client.submit_order.return_value = {
        "order_id": "12345",
        "status": "PENDING"
    }
    return client
```

### 2. 服务层测试

```python
# tests/services/test_trading_service.py
import pytest
from unittest.mock import AsyncMock, Mock, patch
from decimal import Decimal
from app.services.trading.trading_service import TradingService
from app.models.schemas.trading import OrderCreate, OrderResponse
from app.core.exceptions import InsufficientFundsError, InvalidOrderError

class TestTradingService:
    @pytest.fixture
    async def trading_service(self, db_session, mock_user, mock_ctp_client):
        """创建交易服务实例"""
        service = TradingService(db_session, mock_user)
        service.ctp_client = mock_ctp_client
        return service
    
    @pytest.mark.unit
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_create_order_success(self, trading_service):
        """测试创建订单成功"""
        # 准备测试数据
        order_data = OrderCreate(
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("1"),
            price=Decimal("4000"),
            order_type="LIMIT"
        )
        
        # 模拟数据库保存
        trading_service.db.add = Mock()
        trading_service.db.commit = AsyncMock()
        trading_service.db.refresh = AsyncMock()
        
        # 模拟风险检查通过
        with patch.object(trading_service, '_validate_order', return_value=None):
            with patch.object(trading_service, '_risk_check', return_value=None):
                with patch.object(trading_service, '_save_order') as mock_save:
                    mock_order = Mock()
                    mock_order.id = 1
                    mock_order.symbol = "SHFE.rb2405"
                    mock_order.status = "PENDING"
                    mock_save.return_value = mock_order
                    
                    # 执行测试
                    result = await trading_service.create_order(order_data)
                    
                    # 验证结果
                    assert result.symbol == "SHFE.rb2405"
                    trading_service.ctp_client.submit_order.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_create_order_insufficient_funds(self, trading_service):
        """测试资金不足异常"""
        order_data = OrderCreate(
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("100"),
            order_type="MARKET"
        )
        
        with patch.object(trading_service, '_validate_order', return_value=None):
            with patch.object(trading_service, '_risk_check', 
                            side_effect=InsufficientFundsError("资金不足")):
                
                with pytest.raises(InsufficientFundsError, match="资金不足"):
                    await trading_service.create_order(order_data)
    
    @pytest.mark.unit
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_validate_order_invalid_quantity(self, trading_service):
        """测试订单验证 - 无效数量"""
        order_data = OrderCreate(
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("0"),
            order_type="MARKET"
        )
        
        with pytest.raises(InvalidOrderError, match="订单数量必须大于0"):
            await trading_service._validate_order(order_data)
    
    @pytest.mark.unit
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_cancel_order_success(self, trading_service):
        """测试取消订单成功"""
        order_id = "12345"
        
        # 模拟查找订单
        mock_order = Mock()
        mock_order.status = "PENDING"
        mock_order.user_id = trading_service.user.id
        
        with patch.object(trading_service, '_get_order_by_id', return_value=mock_order):
            trading_service.ctp_client.cancel_order.return_value = True
            
            result = await trading_service.cancel_order(order_id)
            
            assert result is True
            trading_service.ctp_client.cancel_order.assert_called_once_with(order_id)
```

### 3. 策略测试

```python
# tests/services/test_strategy_service.py
import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
from app.services.strategy.macd_strategy import MACDStrategy
from app.services.strategy.base_strategy import StrategySignal

class TestMACDStrategy:
    @pytest.fixture
    def strategy(self):
        """创建MACD策略实例"""
        return MACDStrategy(
            fast_period=12,
            slow_period=26,
            signal_period=9
        )
    
    @pytest.fixture
    def sample_data(self):
        """创建示例数据"""
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        prices = 4000 + np.cumsum(np.random.randn(100) * 10)
        
        return pd.DataFrame({
            'datetime': dates,
            'close': prices,
            'high': prices + np.random.randn(100) * 5,
            'low': prices - np.random.randn(100) * 5,
            'volume': np.random.randint(1000, 10000, 100)
        })
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_calculate_indicators(self, strategy, sample_data):
        """测试指标计算"""
        indicators = await strategy.calculate_indicators(sample_data)
        
        assert 'macd' in indicators.columns
        assert 'signal' in indicators.columns
        assert 'histogram' in indicators.columns
        assert len(indicators) == len(sample_data)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_signals(self, strategy, sample_data):
        """测试信号生成"""
        signals = await strategy.generate_signals(sample_data)
        
        assert isinstance(signals, list)
        for signal in signals:
            assert isinstance(signal, StrategySignal)
            assert signal.action in ['BUY', 'SELL', 'HOLD']
            assert signal.confidence >= 0 and signal.confidence <= 1
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_buy_signal_generation(self, strategy, sample_data):
        """测试买入信号生成"""
        # 构造MACD金叉场景
        sample_data['macd'] = [-1, -0.5, 0.5, 1]
        sample_data['signal'] = [0, 0, 0, 0]
        
        with patch.object(strategy, 'calculate_indicators', return_value=sample_data):
            signals = await strategy.generate_signals(sample_data)
            
            buy_signals = [s for s in signals if s.action == 'BUY']
            assert len(buy_signals) > 0
```

### 4. 工具函数测试

```python
# tests/utils/test_calculations.py
import pytest
import numpy as np
import pandas as pd
from app.utils.calculations import (
    calculate_sma, calculate_ema, calculate_macd,
    calculate_rsi, calculate_bollinger_bands
)

class TestCalculations:
    @pytest.fixture
    def sample_prices(self):
        """示例价格数据"""
        return pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
    
    @pytest.mark.unit
    def test_calculate_sma(self, sample_prices):
        """测试简单移动平均"""
        sma = calculate_sma(sample_prices, window=3)
        
        assert len(sma) == len(sample_prices)
        assert not np.isnan(sma.iloc[-1])  # 最后一个值不应该是NaN
        assert sma.iloc[-1] == sample_prices.iloc[-3:].mean()
    
    @pytest.mark.unit
    def test_calculate_ema(self, sample_prices):
        """测试指数移动平均"""
        ema = calculate_ema(sample_prices, window=3)
        
        assert len(ema) == len(sample_prices)
        assert not np.isnan(ema.iloc[-1])
        assert ema.iloc[-1] != sample_prices.iloc[-3:].mean()  # EMA与SMA不同
    
    @pytest.mark.unit
    def test_calculate_macd(self, sample_prices):
        """测试MACD计算"""
        macd_line, signal_line, histogram = calculate_macd(
            sample_prices, fast=3, slow=6, signal=2
        )
        
        assert len(macd_line) == len(sample_prices)
        assert len(signal_line) == len(sample_prices)
        assert len(histogram) == len(sample_prices)
        
        # 验证MACD = EMA(fast) - EMA(slow)
        ema_fast = calculate_ema(sample_prices, 3)
        ema_slow = calculate_ema(sample_prices, 6)
        expected_macd = ema_fast - ema_slow
        
        np.testing.assert_array_almost_equal(
            macd_line.dropna(), expected_macd.dropna(), decimal=6
        )
    
    @pytest.mark.unit
    def test_calculate_rsi(self, sample_prices):
        """测试RSI计算"""
        rsi = calculate_rsi(sample_prices, window=3)
        
        assert len(rsi) == len(sample_prices)
        assert all(0 <= val <= 100 for val in rsi.dropna())
    
    @pytest.mark.unit
    def test_calculate_bollinger_bands(self, sample_prices):
        """测试布林带计算"""
        upper, middle, lower = calculate_bollinger_bands(
            sample_prices, window=3, std_dev=2
        )
        
        assert len(upper) == len(sample_prices)
        assert len(middle) == len(sample_prices)
        assert len(lower) == len(sample_prices)
        
        # 验证中轨是移动平均
        sma = calculate_sma(sample_prices, 3)
        np.testing.assert_array_almost_equal(
            middle.dropna(), sma.dropna(), decimal=6
        )
        
        # 验证上轨 > 中轨 > 下轨
        valid_idx = ~(upper.isna() | middle.isna() | lower.isna())
        assert all(upper[valid_idx] >= middle[valid_idx])
        assert all(middle[valid_idx] >= lower[valid_idx])
```

## 集成测试

### 1. API集成测试

```python
# tests/api/test_trading_api.py
import pytest
from httpx import AsyncClient
from app.main import app
from app.core.config import settings

class TestTradingAPI:
    @pytest.fixture
    def auth_headers(self):
        """认证头部"""
        return {"Authorization": "Bearer test_token"}
    
    @pytest.mark.integration
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_create_order_api(self, auth_headers):
        """测试创建订单API"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "price": "4000",
                    "order_type": "LIMIT"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == "SHFE.rb2405"
            assert data["side"] == "BUY"
            assert data["status"] in ["PENDING", "FILLED"]
    
    @pytest.mark.integration
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_get_orders_api(self, auth_headers):
        """测试获取订单列表API"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/trading/orders",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
    
    @pytest.mark.integration
    @pytest.mark.trading
    @pytest.mark.asyncio
    async def test_cancel_order_api(self, auth_headers):
        """测试取消订单API"""
        # 先创建订单
        async with AsyncClient(app=app, base_url="http://test") as client:
            create_response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "order_type": "MARKET"
                },
                headers=auth_headers
            )
            
            order_id = create_response.json()["id"]
            
            # 取消订单
            cancel_response = await client.delete(
                f"/api/v1/trading/orders/{order_id}",
                headers=auth_headers
            )
            
            assert cancel_response.status_code == 200
            assert "已取消" in cancel_response.json()["message"]
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_unauthorized_access(self):
        """测试未授权访问"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "order_type": "MARKET"
                }
            )
            
            assert response.status_code == 401
```

### 2. 数据库集成测试

```python
# tests/repositories/test_trading_repository.py
import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.models.database.base import Base
from app.repositories.trading_repository import TradingRepository
from app.models.database.trading import Order

class TestTradingRepository:
    @pytest.fixture
    async def db_session(self):
        """测试数据库会话"""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            echo=True
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            yield session
        
        await engine.dispose()
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_create_order(self, db_session):
        """测试创建订单"""
        repository = TradingRepository(db_session)
        
        order_data = {
            "user_id": 1,
            "symbol": "SHFE.rb2405",
            "side": "BUY",
            "quantity": Decimal("1"),
            "price": Decimal("4000"),
            "order_type": "LIMIT",
            "status": "PENDING"
        }
        
        order = await repository.create_order(order_data)
        
        assert order.id is not None
        assert order.symbol == "SHFE.rb2405"
        assert order.side == "BUY"
        assert order.status == "PENDING"
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_get_orders_by_user(self, db_session):
        """测试获取用户订单"""
        repository = TradingRepository(db_session)
        
        # 创建测试订单
        for i in range(3):
            await repository.create_order({
                "user_id": 1,
                "symbol": f"SHFE.rb240{i}",
                "side": "BUY",
                "quantity": Decimal("1"),
                "order_type": "MARKET",
                "status": "PENDING"
            })
        
        orders = await repository.get_orders_by_user(user_id=1)
        
        assert len(orders) == 3
        assert all(order.user_id == 1 for order in orders)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_get_orders_by_status(self, db_session):
        """测试按状态获取订单"""
        repository = TradingRepository(db_session)
        
        # 创建不同状态的订单
        await repository.create_order({
            "user_id": 1,
            "symbol": "SHFE.rb2405",
            "side": "BUY",
            "quantity": Decimal("1"),
            "order_type": "MARKET",
            "status": "PENDING"
        })
        
        await repository.create_order({
            "user_id": 1,
            "symbol": "SHFE.rb2406",
            "side": "SELL",
            "quantity": Decimal("1"),
            "order_type": "MARKET",
            "status": "FILLED"
        })
        
        pending_orders = await repository.get_orders_by_user(
            user_id=1, status="PENDING"
        )
        filled_orders = await repository.get_orders_by_user(
            user_id=1, status="FILLED"
        )
        
        assert len(pending_orders) == 1
        assert len(filled_orders) == 1
        assert pending_orders[0].status == "PENDING"
        assert filled_orders[0].status == "FILLED"
```

## 性能测试

### 1. 负载测试

```python
# tests/performance/test_api_performance.py
import pytest
import asyncio
import time
from httpx import AsyncClient
from app.main import app

class TestAPIPerformance:
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_concurrent_orders(self):
        """测试并发订单处理性能"""
        async def create_order(client, order_data):
            response = await client.post(
                "/api/v1/trading/orders",
                json=order_data,
                headers={"Authorization": "Bearer test_token"}
            )
            return response.status_code, response.elapsed.total_seconds()
        
        order_data = {
            "symbol": "SHFE.rb2405",
            "side": "BUY",
            "quantity": "1",
            "order_type": "MARKET"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            start_time = time.time()
            
            # 并发发送100个订单请求
            tasks = [
                create_order(client, order_data) 
                for _ in range(100)
            ]
            
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 验证性能指标
            success_count = sum(1 for status, _ in results if status == 200)
            avg_response_time = sum(elapsed for _, elapsed in results) / len(results)
            
            assert success_count >= 95  # 95%成功率
            assert total_time < 10  # 总时间小于10秒
            assert avg_response_time < 0.1  # 平均响应时间小于100ms
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_market_data_throughput(self):
        """测试行情数据吞吐量"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            start_time = time.time()
            
            # 并发获取行情数据
            tasks = [
                client.get(f"/api/v1/market/data/SHFE.rb240{i % 10}")
                for i in range(1000)
            ]
            
            responses = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            success_count = sum(1 for r in responses if r.status_code == 200)
            throughput = success_count / total_time
            
            assert throughput > 100  # 每秒处理100+请求
            assert success_count >= 950  # 95%成功率
```

### 2. 内存和CPU测试

```python
# tests/performance/test_resource_usage.py
import pytest
import psutil
import asyncio
from app.services.strategy.backtest_service import BacktestService

class TestResourceUsage:
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_backtest_memory_usage(self):
        """测试回测服务内存使用"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        backtest_service = BacktestService()
        
        # 运行大量回测
        for i in range(10):
            await backtest_service.run_backtest(
                strategy_name="MACD",
                symbol="SHFE.rb2405",
                start_date="2023-01-01",
                end_date="2023-12-31"
            )
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长不应超过100MB
        assert memory_increase < 100
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_concurrent_strategy_execution(self):
        """测试并发策略执行CPU使用"""
        process = psutil.Process()
        
        async def run_strategy():
            # 模拟策略计算
            await asyncio.sleep(0.1)
            return sum(i * i for i in range(1000))
        
        # 监控CPU使用率
        cpu_percent_before = process.cpu_percent()
        
        # 并发运行50个策略
        tasks = [run_strategy() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        
        cpu_percent_after = process.cpu_percent()
        
        assert len(results) == 50
        assert cpu_percent_after < 80  # CPU使用率不超过80%
```

## 端到端测试

### 1. 完整交易流程测试

```python
# tests/e2e/test_trading_flow.py
import pytest
from httpx import AsyncClient
from app.main import app

class TestTradingFlow:
    @pytest.mark.e2e
    @pytest.mark.asyncio
    async def test_complete_trading_workflow(self):
        """测试完整交易工作流"""
        auth_headers = {"Authorization": "Bearer test_token"}
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 获取市场数据
            market_response = await client.get(
                "/api/v1/market/data/SHFE.rb2405",
                headers=auth_headers
            )
            assert market_response.status_code == 200
            market_data = market_response.json()
            
            # 2. 创建订单
            order_response = await client.post(
                "/api/v1/trading/orders",
                json={
                    "symbol": "SHFE.rb2405",
                    "side": "BUY",
                    "quantity": "1",
                    "price": str(market_data["price"]),
                    "order_type": "LIMIT"
                },
                headers=auth_headers
            )
            assert order_response.status_code == 200
            order = order_response.json()
            
            # 3. 查询订单状态
            status_response = await client.get(
                f"/api/v1/trading/orders/{order['id']}",
                headers=auth_headers
            )
            assert status_response.status_code == 200
            
            # 4. 获取持仓信息
            position_response = await client.get(
                "/api/v1/trading/positions",
                headers=auth_headers
            )
            assert position_response.status_code == 200
            
            # 5. 取消订单（如果还未成交）
            if order["status"] == "PENDING":
                cancel_response = await client.delete(
                    f"/api/v1/trading/orders/{order['id']}",
                    headers=auth_headers
                )
                assert cancel_response.status_code == 200
```

## 测试数据管理

### 1. 测试夹具

```python
# tests/fixtures/trading_fixtures.py
import pytest
from decimal import Decimal
from datetime import datetime, timedelta

@pytest.fixture
def sample_orders():
    """示例订单数据"""
    return [
        {
            "symbol": "SHFE.rb2405",
            "side": "BUY",
            "quantity": Decimal("1"),
            "price": Decimal("4000"),
            "order_type": "LIMIT",
            "status": "PENDING"
        },
        {
            "symbol": "SHFE.rb2406",
            "side": "SELL",
            "quantity": Decimal("2"),
            "price": Decimal("4100"),
            "order_type": "LIMIT",
            "status": "FILLED"
        }
    ]

@pytest.fixture
def sample_market_data():
    """示例市场数据"""
    base_time = datetime.now()
    return [
        {
            "symbol": "SHFE.rb2405",
            "price": 4000.0,
            "volume": 1000,
            "timestamp": base_time - timedelta(minutes=1)
        },
        {
            "symbol": "SHFE.rb2405",
            "price": 4005.0,
            "volume": 1500,
            "timestamp": base_time
        }
    ]
```

### 2. 数据库种子

```python
# tests/fixtures/database_seeds.py
import asyncio
from app.models.database.user import User
from app.models.database.trading import Order

async def seed_test_data(db_session):
    """播种测试数据"""
    # 创建测试用户
    test_user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db_session.add(test_user)
    await db_session.commit()
    
    # 创建测试订单
    test_orders = [
        Order(
            user_id=test_user.id,
            symbol="SHFE.rb2405",
            side="BUY",
            quantity=Decimal("1"),
            price=Decimal("4000"),
            order_type="LIMIT",
            status="PENDING"
        ),
        Order(
            user_id=test_user.id,
            symbol="SHFE.rb2406",
            side="SELL",
            quantity=Decimal("2"),
            order_type="MARKET",
            status="FILLED"
        )
    ]
    
    for order in test_orders:
        db_session.add(order)
    
    await db_session.commit()
    
    return test_user, test_orders
```

## 持续集成

### 1. GitHub Actions配置

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/ -m "unit" --cov=app --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/ -m "integration" --cov=app --cov-report=xml
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost/test_db
        REDIS_URL: redis://localhost:6379
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## 总结

本测试方案包含：

1. **单元测试**：服务层、策略、工具函数的详细测试
2. **集成测试**：API接口、数据库操作的集成测试
3. **性能测试**：负载测试、资源使用测试
4. **端到端测试**：完整业务流程测试
5. **测试数据管理**：夹具和种子数据管理
6. **持续集成**：自动化测试流水线

通过这套完整的测试体系，确保量化交易平台的质量和稳定性。 
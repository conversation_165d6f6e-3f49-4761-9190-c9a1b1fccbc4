# 15-后端接口文档

## 📋 文档概述

本文档详细说明了量化投资平台后端API的所有接口规范，包括认证方式、请求格式、响应格式、错误处理等。

### 基本信息
- **API版本**: v1
- **基础URL**: `http://localhost:8000/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证方式
- **JWT认证**: Bearer Token
- **API密钥**: X-API-Key Header
- **会话认证**: Cookie-based

---

## 🔐 认证模块 (Authentication)

### 基础路径: `/api/v1/auth`

#### 1. 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "Test User"
}
```

**响应示例:**
```json
{
  "message": "用户注册成功",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 2. 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

**响应示例:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

#### 3. 获取当前用户信息
```http
GET /api/v1/auth/me
Authorization: Bearer <access_token>
```

**响应示例:**
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "full_name": "Test User",
  "is_active": true,
  "permissions": ["read", "write", "trade"]
}
```

#### 4. 刷新令牌
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

#### 5. 退出登录
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

---

## 📊 行情模块 (Market Data)

### 基础路径: `/api/v1/market`

#### 1. 获取实时行情
```http
GET /api/v1/market/realtime/{symbol}
Authorization: Bearer <access_token>
```

**响应示例:**
```json
{
  "symbol": "000001",
  "name": "平安银行",
  "price": 12.50,
  "change": 0.15,
  "change_percent": 1.20,
  "volume": 1000000,
  "turnover": 12500000,
  "high": 12.60,
  "low": 12.30,
  "open": 12.35,
  "prev_close": 12.35,
  "timestamp": "2024-01-01T09:30:00Z"
}
```

#### 2. 获取K线数据
```http
GET /api/v1/market/kline/{symbol}?interval=1m&start_time=2024-01-01&end_time=2024-01-02
Authorization: Bearer <access_token>
```

**参数说明:**
- `interval`: 时间间隔 (1m, 5m, 15m, 30m, 1h, 1d)
- `start_time`: 开始时间 (ISO 8601格式)
- `end_time`: 结束时间 (ISO 8601格式)

**响应示例:**
```json
{
  "symbol": "000001",
  "interval": "1m",
  "data": [
    {
      "timestamp": "2024-01-01T09:30:00Z",
      "open": 12.35,
      "high": 12.40,
      "low": 12.30,
      "close": 12.38,
      "volume": 10000,
      "turnover": 123800
    }
  ]
}
```

#### 3. 获取股票列表
```http
GET /api/v1/market/stocks?page=1&size=20&market=SZ
Authorization: Bearer <access_token>
```

#### 4. 搜索股票
```http
GET /api/v1/market/search?query=平安银行
Authorization: Bearer <access_token>
```

#### 5. 获取板块数据
```http
GET /api/v1/market/sectors
Authorization: Bearer <access_token>
```

---

## 💹 交易模块 (Trading)

### 基础路径: `/api/v1/trading`

#### 1. 下单
```http
POST /api/v1/trading/order
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "symbol": "000001",
  "side": "buy",
  "order_type": "limit",
  "quantity": 100,
  "price": 12.50,
  "time_in_force": "GTC"
}
```

**响应示例:**
```json
{
  "order_id": "ORD20240101001",
  "symbol": "000001",
  "side": "buy",
  "order_type": "limit",
  "quantity": 100,
  "price": 12.50,
  "status": "pending",
  "created_at": "2024-01-01T09:30:00Z"
}
```

#### 2. 撤单
```http
DELETE /api/v1/trading/order/{order_id}
Authorization: Bearer <access_token>
```

#### 3. 查询订单
```http
GET /api/v1/trading/order/{order_id}
Authorization: Bearer <access_token>
```

#### 4. 获取订单列表
```http
GET /api/v1/trading/orders?status=all&page=1&size=20
Authorization: Bearer <access_token>
```

#### 5. 获取持仓信息
```http
GET /api/v1/trading/positions
Authorization: Bearer <access_token>
```

**响应示例:**
```json
{
  "positions": [
    {
      "symbol": "000001",
      "quantity": 100,
      "avg_price": 12.45,
      "current_price": 12.50,
      "unrealized_pnl": 5.00,
      "market_value": 1250.00
    }
  ],
  "total_market_value": 1250.00,
  "total_unrealized_pnl": 5.00
}
```

#### 6. 获取账户信息
```http
GET /api/v1/trading/account
Authorization: Bearer <access_token>
```

---

## 🎯 策略模块 (Strategy)

### 基础路径: `/api/v1/strategy`

#### 1. 创建策略
```http
POST /api/v1/strategy
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "MACD策略",
  "description": "基于MACD指标的交易策略",
  "strategy_type": "technical",
  "parameters": {
    "fast_period": 12,
    "slow_period": 26,
    "signal_period": 9
  },
  "symbols": ["000001", "000002"]
}
```

#### 2. 获取策略列表
```http
GET /api/v1/strategy?page=1&size=20&status=active
Authorization: Bearer <access_token>
```

#### 3. 启动策略
```http
POST /api/v1/strategy/{strategy_id}/start
Authorization: Bearer <access_token>
```

#### 4. 停止策略
```http
POST /api/v1/strategy/{strategy_id}/stop
Authorization: Bearer <access_token>
```

#### 5. 获取策略绩效
```http
GET /api/v1/strategy/{strategy_id}/performance
Authorization: Bearer <access_token>
```

---

## 📈 回测模块 (Backtest)

### 基础路径: `/api/v1/backtest`

#### 1. 创建回测
```http
POST /api/v1/backtest
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "MACD策略回测",
  "strategy_id": 1,
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "initial_capital": 100000,
  "benchmark": "000300"
}
```

#### 2. 运行回测
```http
POST /api/v1/backtest/{backtest_id}/run
Authorization: Bearer <access_token>
```

#### 3. 获取回测结果
```http
GET /api/v1/backtest/{backtest_id}/result
Authorization: Bearer <access_token>
```

**响应示例:**
```json
{
  "backtest_id": 1,
  "status": "completed",
  "summary": {
    "total_return": 0.15,
    "annual_return": 0.12,
    "max_drawdown": 0.08,
    "sharpe_ratio": 1.2,
    "win_rate": 0.65
  },
  "trades": [
    {
      "symbol": "000001",
      "side": "buy",
      "quantity": 100,
      "price": 12.50,
      "timestamp": "2023-01-15T09:30:00Z"
    }
  ]
}
```

#### 4. 获取回测列表
```http
GET /api/v1/backtest?page=1&size=20&status=completed
Authorization: Bearer <access_token>
```

---

## 🛡️ 风控模块 (Risk Management)

### 基础路径: `/api/v1/risk`

#### 1. 获取风险指标
```http
GET /api/v1/risk/metrics
Authorization: Bearer <access_token>
```

#### 2. 设置风险限额
```http
POST /api/v1/risk/limits
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "max_position_size": 10000,
  "max_daily_loss": 5000,
  "max_drawdown": 0.1
}
```

#### 3. 获取风险警报
```http
GET /api/v1/risk/alerts
Authorization: Bearer <access_token>
```

---

## 🔌 CTP交易接口

### 基础路径: `/api/v1/ctp`

#### 1. 连接CTP
```http
POST /api/v1/ctp/connect
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "broker_id": "9999",
  "user_id": "your_user_id",
  "password": "your_password",
  "trade_server": "tcp://180.168.146.187:10130",
  "md_server": "tcp://180.168.146.187:10131"
}
```

#### 2. 获取连接状态
```http
GET /api/v1/ctp/status
Authorization: Bearer <access_token>
```

#### 3. 期货下单
```http
POST /api/v1/ctp/order
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "instrument_id": "rb2405",
  "direction": "buy",
  "offset_flag": "open",
  "volume": 1,
  "price": 3500.0,
  "order_type": "limit"
}
```

---

## 📊 监控告警模块

### 基础路径: `/api/v1/monitoring`

#### 1. 获取系统指标
```http
GET /api/v1/monitoring/metrics
Authorization: Bearer <access_token>
```

#### 2. 获取告警列表
```http
GET /api/v1/monitoring/alerts
Authorization: Bearer <access_token>
```

#### 3. 创建告警规则
```http
POST /api/v1/monitoring/alert-rules
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "高CPU使用率告警",
  "metric": "cpu_usage",
  "threshold": 80,
  "operator": "gt",
  "duration": 300
}
```

---

## 🔒 安全管理模块

### 基础路径: `/api/v1/security`

#### 1. 获取安全配置
```http
GET /api/v1/security/config
Authorization: Bearer <access_token>
```

#### 2. 更新安全配置
```http
PUT /api/v1/security/config
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "enable_rate_limiting": true,
  "max_requests_per_minute": 100,
  "enable_ip_whitelist": false
}
```

#### 3. 获取安全事件
```http
GET /api/v1/security/events
Authorization: Bearer <access_token>
```

---

## 🔍 验证码模块

### 基础路径: `/api/v1/captcha`

#### 1. 获取验证码
```http
GET /api/v1/captcha/generate
```

#### 2. 验证验证码
```http
POST /api/v1/captcha/verify
Content-Type: application/json

{
  "captcha_id": "uuid",
  "captcha_code": "ABCD"
}
```

---

## 🌐 WebSocket接口

### 市场数据WebSocket
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/market');

// 订阅行情数据
ws.send(JSON.stringify({
  action: 'subscribe',
  symbol: '000001',
  data_type: 'realtime'
}));

// 接收数据
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Market data:', data);
};
```

### 交易WebSocket
```javascript
// 连接交易WebSocket
const tradingWs = new WebSocket('ws://localhost:8000/ws/trading');

// 订阅订单更新
tradingWs.send(JSON.stringify({
  action: 'subscribe',
  channel: 'order_updates'
}));
```

---

## 📝 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {
      "field": "price",
      "reason": "价格必须大于0"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

## 🔧 错误代码

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 422 | 参数验证失败 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

---

## 📋 请求限制

### 频率限制
- **普通用户**: 100请求/分钟
- **VIP用户**: 500请求/分钟
- **API密钥**: 1000请求/分钟

### 数据限制
- **单次查询**: 最多1000条记录
- **批量操作**: 最多100个项目
- **文件上传**: 最大10MB

---

## 🧪 测试环境

### 测试服务器
- **地址**: `http://test.quantplatform.com`
- **文档**: `http://test.quantplatform.com/docs`
- **测试账户**: 联系管理员获取

### 测试数据
- 提供模拟交易环境
- 包含历史行情数据
- 支持策略回测功能

---

## 📞 技术支持

### 联系方式
- **技术支持**: <EMAIL>
- **API问题**: <EMAIL>
- **紧急联系**: +86-400-123-4567

### 文档更新
- **版本**: v1.0.0
- **更新日期**: 2024-12-15
- **下次更新**: 根据需要

---

## 📚 相关文档

1. [后端项目概述](./06-后端项目概述.md)
2. [后端技术架构](./08-后端技术架构.md)
3. [后端开发规范](./13-后端开发规范.md)
4. [WebSocket文档](./18-WebSocket文档.md)
5. [前后端协作](./17-前后端协作.md)

---

**📌 注意事项:**
- 所有API都需要适当的认证
- 生产环境请使用HTTPS
- 请求和响应都使用UTF-8编码
- 时间格式统一使用ISO 8601标准
- 数值类型注意精度处理 
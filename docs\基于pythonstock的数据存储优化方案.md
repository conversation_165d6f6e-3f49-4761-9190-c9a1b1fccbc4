# 基于pythonstock的数据存储优化方案

## 📋 pythonstock项目数据存储核心特点分析

### 🎯 pythonstock的优秀设计模式

#### 1. **分层数据缓存策略**
```python
# pythonstock的核心缓存机制
- 按天进行数据缓存，存储最近3天数据
- 每天定时清除过期数据
- 使用read_pickle/to_pickle的gzip压缩模式存储
- 防止数据接口被封，减少API调用频率
```

#### 2. **数据压缩存储**
```python
# 高效的数据序列化
import pickle
import gzip

# 压缩存储
def save_data_compressed(data, filepath):
    with gzip.open(filepath, 'wb') as f:
        pickle.dump(data, f)

# 压缩读取
def load_data_compressed(filepath):
    with gzip.open(filepath, 'rb') as f:
        return pickle.load(f)
```

#### 3. **时间分片存储**
- 按交易日分片存储数据
- 支持快速的时间范围查询
- 自动清理过期数据

## 🔍 我们项目当前数据架构分析

### ✅ 现有优势
1. **多层缓存体系**: Redis + 内存缓存 + 本地存储
2. **完善的缓存策略**: 不同数据类型使用不同TTL
3. **数据模型完整**: 覆盖市场、交易、策略等全业务
4. **性能优化**: 连接池、压缩、异步处理

### ⚠️ 可优化点
1. **缺少按天分片的历史数据管理**
2. **没有使用gzip压缩存储**
3. **缺少智能的数据清理机制**
4. **API调用频率控制不够精细**

## 🚀 基于pythonstock的优化方案

### 1. **实现分层数据存储架构**

#### 1.1 热数据层 (实时缓存)
```python
# backend/app/core/data_storage.py
class HotDataManager:
    """热数据管理器 - 实时数据缓存"""
    
    def __init__(self):
        self.redis_client = redis.Redis()
        self.memory_cache = {}
        
    async def store_realtime_data(self, symbol: str, data: dict):
        """存储实时数据 - 30秒TTL"""
        cache_key = f"realtime:{symbol}"
        
        # 内存缓存 (最快访问)
        self.memory_cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
        
        # Redis缓存 (跨进程共享)
        await self.redis_client.setex(
            cache_key, 30, 
            json.dumps(data, default=str)
        )
```

#### 1.2 温数据层 (日内缓存)
```python
class WarmDataManager:
    """温数据管理器 - 日内数据缓存"""
    
    def __init__(self):
        self.data_dir = Path("data/daily_cache")
        self.data_dir.mkdir(exist_ok=True)
        
    async def store_daily_data(self, symbol: str, date: str, data: pd.DataFrame):
        """存储日内数据 - 使用gzip压缩"""
        filepath = self.data_dir / f"{symbol}_{date}.pkl.gz"
        
        # 使用gzip压缩存储 (学习pythonstock)
        with gzip.open(filepath, 'wb') as f:
            pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
        logger.info(f"已压缩存储 {symbol} 日内数据: {filepath}")
        
    async def load_daily_data(self, symbol: str, date: str) -> pd.DataFrame:
        """加载日内数据"""
        filepath = self.data_dir / f"{symbol}_{date}.pkl.gz"
        
        if not filepath.exists():
            return None
            
        with gzip.open(filepath, 'rb') as f:
            return pickle.load(f)
```

#### 1.3 冷数据层 (历史存储)
```python
class ColdDataManager:
    """冷数据管理器 - 历史数据存储"""
    
    def __init__(self):
        self.storage_dir = Path("data/historical")
        self.storage_dir.mkdir(exist_ok=True)
        
    async def store_historical_data(self, symbol: str, data: pd.DataFrame):
        """存储历史数据 - Parquet格式"""
        # 按年月分片存储
        year_month = data.index[0].strftime("%Y%m")
        filepath = self.storage_dir / f"{symbol}_{year_month}.parquet"
        
        # 使用Parquet格式 (高压缩比 + 快速查询)
        data.to_parquet(filepath, compression='gzip')
        
    async def load_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """加载历史数据 - 支持时间范围查询"""
        # 根据时间范围确定需要加载的文件
        files_to_load = self._get_files_in_range(symbol, start_date, end_date)
        
        dfs = []
        for filepath in files_to_load:
            if filepath.exists():
                df = pd.read_parquet(filepath)
                dfs.append(df)
                
        return pd.concat(dfs) if dfs else pd.DataFrame()
```

### 2. **智能数据清理机制**

```python
# backend/app/services/data_cleaner.py
class DataCleanupService:
    """数据清理服务 - 学习pythonstock的清理策略"""
    
    def __init__(self):
        self.cleanup_rules = {
            'realtime': timedelta(hours=1),      # 实时数据保留1小时
            'daily_cache': timedelta(days=3),    # 日内缓存保留3天 (pythonstock策略)
            'reports': timedelta(days=90),       # 报告保留90天
            'logs': timedelta(days=30)           # 日志保留30天
        }
        
    async def cleanup_expired_data(self):
        """清理过期数据"""
        current_time = datetime.now()
        
        for data_type, retention_period in self.cleanup_rules.items():
            cutoff_time = current_time - retention_period
            await self._cleanup_data_type(data_type, cutoff_time)
            
    async def _cleanup_daily_cache(self, cutoff_time: datetime):
        """清理日内缓存 - 保留最近3天 (pythonstock策略)"""
        cache_dir = Path("data/daily_cache")
        
        for file_path in cache_dir.glob("*.pkl.gz"):
            # 从文件名提取日期
            file_date = self._extract_date_from_filename(file_path.name)
            
            if file_date and file_date < cutoff_time.date():
                file_path.unlink()  # 删除过期文件
                logger.info(f"已清理过期缓存文件: {file_path}")
```

### 3. **API调用频率控制**

```python
# backend/app/core/api_rate_limiter.py
class APIRateLimiter:
    """API调用频率控制 - 防止被封"""
    
    def __init__(self):
        self.call_history = defaultdict(list)
        self.limits = {
            'tushare': {'calls': 200, 'period': 60},      # 每分钟200次
            'akshare': {'calls': 100, 'period': 60},      # 每分钟100次
            'baostock': {'calls': 300, 'period': 60}      # 每分钟300次
        }
        
    async def check_rate_limit(self, api_name: str) -> bool:
        """检查是否可以调用API"""
        now = time.time()
        limit_config = self.limits.get(api_name, {'calls': 60, 'period': 60})
        
        # 清理过期的调用记录
        cutoff_time = now - limit_config['period']
        self.call_history[api_name] = [
            call_time for call_time in self.call_history[api_name]
            if call_time > cutoff_time
        ]
        
        # 检查是否超过限制
        if len(self.call_history[api_name]) >= limit_config['calls']:
            return False
            
        # 记录本次调用
        self.call_history[api_name].append(now)
        return True
        
    async def wait_for_rate_limit(self, api_name: str):
        """等待直到可以调用API"""
        while not await self.check_rate_limit(api_name):
            await asyncio.sleep(1)  # 等待1秒后重试
```

### 4. **数据预热和预加载**

```python
# backend/app/services/data_preloader.py
class DataPreloader:
    """数据预加载服务 - 提升响应速度"""
    
    def __init__(self):
        self.popular_symbols = [
            '000001.SZ', '000002.SZ', '600036.SH', '600519.SH',  # 热门股票
            '000300.SH', '399001.SZ', '399006.SZ'                # 主要指数
        ]
        
    async def preload_market_data(self):
        """预加载市场数据"""
        logger.info("开始预加载热门股票数据...")
        
        for symbol in self.popular_symbols:
            try:
                # 预加载实时行情
                await self._preload_realtime_quote(symbol)
                
                # 预加载日K线数据
                await self._preload_daily_kline(symbol)
                
                # 避免API调用过于频繁
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"预加载 {symbol} 数据失败: {e}")
                
    async def _preload_realtime_quote(self, symbol: str):
        """预加载实时行情"""
        # 检查缓存是否存在
        cache_key = f"realtime:{symbol}"
        if await redis_client.exists(cache_key):
            return  # 缓存已存在，无需预加载
            
        # 从API获取数据并缓存
        quote_data = await market_api.get_realtime_quote(symbol)
        await hot_data_manager.store_realtime_data(symbol, quote_data)
```

### 5. **完整的数据存储配置**

```python
# backend/app/core/storage_config.py
@dataclass
class StorageConfig:
    """数据存储配置"""
    
    # 缓存配置
    redis_url: str = "redis://localhost:6379/0"
    memory_cache_size: int = 1000  # 内存缓存条目数
    
    # 压缩配置
    enable_compression: bool = True
    compression_level: int = 6  # gzip压缩级别
    
    # 数据保留策略 (学习pythonstock)
    realtime_data_ttl: int = 30        # 实时数据30秒
    daily_cache_days: int = 3          # 日内缓存3天
    historical_data_years: int = 10    # 历史数据10年
    
    # 文件路径配置
    data_root: Path = Path("data")
    daily_cache_dir: Path = data_root / "daily_cache"
    historical_dir: Path = data_root / "historical"
    reports_dir: Path = data_root / "reports"
    
    # API限制配置
    api_rate_limits: Dict[str, Dict[str, int]] = field(default_factory=lambda: {
        'tushare': {'calls': 200, 'period': 60},
        'akshare': {'calls': 100, 'period': 60},
        'baostock': {'calls': 300, 'period': 60}
    })
```

## 📊 优化效果预期

### 性能提升
| 指标 | 优化前 | 优化后 | 提升幅度 |
|------|--------|--------|----------|
| **数据加载速度** | 500ms | 50ms | 90% ⬆️ |
| **存储空间占用** | 100% | 30% | 70% ⬇️ |
| **API调用次数** | 1000次/天 | 200次/天 | 80% ⬇️ |
| **缓存命中率** | 60% | 95% | 58% ⬆️ |

### 系统稳定性
- ✅ **防止API被封**: 智能频率控制
- ✅ **数据一致性**: 多层缓存同步
- ✅ **存储效率**: gzip压缩节省70%空间
- ✅ **自动清理**: 防止磁盘空间耗尽

### 用户体验
- ✅ **响应速度**: 热门数据预加载
- ✅ **离线能力**: 本地缓存支持
- ✅ **数据完整性**: 分层存储保证
- ✅ **系统稳定性**: 优雅降级机制

## 🎯 实施建议

### 第一阶段 (本周)
1. **实现gzip压缩存储** - 学习pythonstock核心特性
2. **添加按天分片缓存** - 提升查询效率
3. **实现API频率控制** - 防止接口被封

### 第二阶段 (2周内)
1. **完善数据清理机制** - 自动管理存储空间
2. **实现数据预加载** - 提升响应速度
3. **优化缓存策略** - 提高命中率

### 第三阶段 (1月内)
1. **集成多数据源** - 提高数据可靠性
2. **实现智能降级** - 保证系统稳定性
3. **添加监控告警** - 及时发现问题

通过学习pythonstock的优秀设计，我们可以构建一个**高效、稳定、可扩展**的数据存储系统，为专业行情中心提供强有力的数据支撑！

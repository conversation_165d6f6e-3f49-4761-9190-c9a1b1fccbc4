# 安全密钥管理指南

## 概述

本指南描述了量化投资平台中密钥和敏感信息的安全管理最佳实践。

## 🔐 密钥管理原则

### 1. 永不硬编码
- ❌ 不要在代码中硬编码密钥
- ❌ 不要在配置文件中明文存储密钥
- ❌ 不要将密钥提交到版本控制系统

### 2. 环境隔离
- 🔒 开发环境使用测试密钥
- 🔒 生产环境使用强密钥
- 🔒 不同环境密钥完全隔离

### 3. 最小权限原则
- 🔑 每个服务只获取必需的密钥
- 🔑 定期轮换密钥
- 🔑 及时撤销不再使用的密钥

## 🛠️ 密钥管理方案

### 方案1: Kubernetes Secrets (推荐用于开发环境)

#### 创建数据库密钥
```bash
kubectl create secret generic quant-platform-secrets \
  --namespace=quant-platform-dev \
  --from-literal=DATABASE_PASSWORD="$(openssl rand -base64 32)" \
  --from-literal=CTP_DATABASE_URL="postgresql://quant_user:$(openssl rand -base64 32)@postgres-dev:5432/quant_platform_dev"
```

#### 创建Redis密钥
```bash
kubectl create secret generic redis-secrets \
  --namespace=quant-platform-dev \
  --from-literal=REDIS_PASSWORD="$(openssl rand -base64 32)" \
  --from-literal=CTP_REDIS_URL="redis://:$(openssl rand -base64 32)@redis-dev:6379/0"
```

#### 创建JWT密钥
```bash
kubectl create secret generic jwt-secrets \
  --namespace=quant-platform-dev \
  --from-literal=CTP_JWT_SECRET_KEY="$(openssl rand -base64 64)" \
  --from-literal=CTP_ENCRYPTION_KEY="$(openssl rand -base64 32)"
```

#### 创建Docker Registry密钥
```bash
kubectl create secret docker-registry registry-secret \
  --namespace=quant-platform-dev \
  --docker-server=ghcr.io \
  --docker-username=your_github_username \
  --docker-password=your_github_token
```

### 方案2: HashiCorp Vault (推荐用于生产环境)

#### 安装Vault
```bash
# 使用Helm安装Vault
helm repo add hashicorp https://helm.releases.hashicorp.com
helm install vault hashicorp/vault --namespace vault --create-namespace
```

#### 配置Vault密钥
```bash
# 启用KV secrets引擎
vault secrets enable -path=quant-platform kv-v2

# 存储数据库密钥
vault kv put quant-platform/database \
  password="$(openssl rand -base64 32)" \
  url="********************************/db"

# 存储JWT密钥
vault kv put quant-platform/jwt \
  secret_key="$(openssl rand -base64 64)" \
  encryption_key="$(openssl rand -base64 32)"
```

#### Vault Agent配置
```yaml
# vault-agent-config.yaml
vault:
  address: "https://vault.example.com"
  
auto_auth:
  method:
    type: "kubernetes"
    mount_path: "auth/kubernetes"
    config:
      role: "quant-platform"

template:
  - source: "/vault/secrets/database.tpl"
    destination: "/vault/secrets/database.env"
```

### 方案3: AWS Secrets Manager (推荐用于AWS环境)

#### 创建密钥
```bash
# 创建数据库密钥
aws secretsmanager create-secret \
  --name "quant-platform/database" \
  --description "Database credentials for quant platform" \
  --secret-string '{"username":"quant_user","password":"secure_password"}'

# 创建JWT密钥
aws secretsmanager create-secret \
  --name "quant-platform/jwt" \
  --description "JWT secrets for quant platform" \
  --secret-string '{"secret_key":"jwt_secret","encryption_key":"encryption_key"}'
```

#### 在Kubernetes中使用AWS Secrets
```yaml
# 使用External Secrets Operator
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-store
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-west-2
      auth:
        secretRef:
          accessKeyID:
            name: aws-credentials
            key: access-key-id
          secretAccessKey:
            name: aws-credentials
            key: secret-access-key
```

## 🔧 CI/CD 密钥管理

### GitHub Actions Secrets

#### 设置Repository Secrets
```bash
# 在GitHub Repository Settings > Secrets and variables > Actions中添加：
DATABASE_PASSWORD=your_secure_password
CTP_JWT_SECRET_KEY=your_jwt_secret
CTP_ENCRYPTION_KEY=your_encryption_key
DOCKER_REGISTRY_TOKEN=your_registry_token
```

#### 在Workflow中使用
```yaml
# .github/workflows/deploy.yml
- name: Deploy to Kubernetes
  env:
    DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
    JWT_SECRET_KEY: ${{ secrets.CTP_JWT_SECRET_KEY }}
  run: |
    kubectl create secret generic app-secrets \
      --from-literal=DATABASE_PASSWORD="$DATABASE_PASSWORD" \
      --from-literal=JWT_SECRET_KEY="$JWT_SECRET_KEY" \
      --dry-run=client -o yaml | kubectl apply -f -
```

## 🔍 密钥轮换策略

### 自动轮换脚本
```bash
#!/bin/bash
# rotate-secrets.sh

# 生成新密钥
NEW_DB_PASSWORD=$(openssl rand -base64 32)
NEW_JWT_SECRET=$(openssl rand -base64 64)

# 更新Kubernetes密钥
kubectl patch secret quant-platform-secrets \
  --type='json' \
  -p='[{"op": "replace", "path": "/data/DATABASE_PASSWORD", "value": "'$(echo -n $NEW_DB_PASSWORD | base64)'"}]'

# 重启相关服务
kubectl rollout restart deployment/quant-platform-backend
kubectl rollout restart deployment/quant-platform-frontend

echo "密钥轮换完成"
```

### 轮换计划
- **数据库密钥**: 每90天轮换
- **JWT密钥**: 每30天轮换
- **API密钥**: 每60天轮换
- **加密密钥**: 每180天轮换

## 🚨 安全事件响应

### 密钥泄露处理流程

1. **立即响应**
   ```bash
   # 立即撤销泄露的密钥
   kubectl delete secret compromised-secret
   
   # 生成新密钥
   kubectl create secret generic new-secret \
     --from-literal=key="$(openssl rand -base64 32)"
   ```

2. **影响评估**
   - 确定泄露范围
   - 检查访问日志
   - 评估潜在损失

3. **恢复措施**
   - 更新所有相关密钥
   - 重启所有相关服务
   - 通知相关人员

## 📋 密钥管理检查清单

### 开发环境
- [ ] 使用测试密钥
- [ ] 密钥存储在Kubernetes Secrets中
- [ ] 不在代码中硬编码密钥
- [ ] 定期更新密钥

### 生产环境
- [ ] 使用强密钥（至少32字符）
- [ ] 使用专业密钥管理服务（Vault/AWS Secrets Manager）
- [ ] 启用密钥轮换
- [ ] 配置访问审计
- [ ] 设置密钥过期告警

### CI/CD
- [ ] 使用Repository Secrets
- [ ] 密钥注入而非硬编码
- [ ] 限制密钥访问权限
- [ ] 定期审查密钥使用

## 🔗 相关文档

- [Kubernetes Secrets官方文档](https://kubernetes.io/docs/concepts/configuration/secret/)
- [HashiCorp Vault文档](https://www.vaultproject.io/docs)
- [AWS Secrets Manager文档](https://docs.aws.amazon.com/secretsmanager/)
- [GitHub Actions Secrets文档](https://docs.github.com/en/actions/security-guides/encrypted-secrets)

## 📞 联系方式

如有密钥管理相关问题，请联系：
- 安全团队: <EMAIL> (示例)
- DevOps团队: <EMAIL> (示例)
- 紧急联系: <EMAIL> (示例)

> 注：以上为示例联系方式，请根据实际项目情况更新为真实的联系信息。

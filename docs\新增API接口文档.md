# 新增API接口文档

## 概述

本次为后端 `main_simple.py` 文件新增了16个API端点，覆盖市场数据、交易管理、策略回测和风险控制四大核心功能模块。所有API都使用模拟数据，适用于开发和测试环境。

## 📈 市场数据相关API

### 1. GET /api/v1/market/stocks
**功能**: 获取股票列表

**响应示例**:
```json
{
  "success": true,
  "data": [
    {"symbol": "000001", "name": "平安银行", "exchange": "SZSE", "industry": "银行"},
    {"symbol": "600519", "name": "贵州茅台", "exchange": "SSE", "industry": "食品饮料"}
  ],
  "total": 8
}
```

### 2. GET /api/v1/market/quote/{symbol}
**功能**: 获取指定股票的实时报价

**参数**:
- `symbol`: 股票代码 (路径参数)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "symbol": "000001",
    "name": "平安银行",
    "price": 13.45,
    "change": 0.32,
    "changePercent": 2.44,
    "volume": 123456789,
    "turnover": 1660000000.0,
    "high": 13.67,
    "low": 13.12,
    "open": 13.18,
    "close": 13.13,
    "timestamp": "2025-07-27T15:00:00"
  }
}
```

### 3. GET /api/v1/market/kline/{symbol}
**功能**: 获取K线数据

**参数**:
- `symbol`: 股票代码 (路径参数)
- `period`: 时间周期 (可选，默认"1d")
- `start_date`: 开始日期 (可选)
- `end_date`: 结束日期 (可选)  
- `limit`: 返回条数 (可选，默认100)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "symbol": "000001",
    "period": "1d",
    "klines": [
      {
        "timestamp": "2025-07-27 15:00:00",
        "open": 52.50,
        "high": 53.00,
        "low": 52.20,
        "close": 52.80,
        "volume": 1200000,
        "turnover": 63360000.0
      }
    ]
  }
}
```

## 💰 交易相关API

### 4. POST /api/v1/trading/order
**功能**: 提交订单

**请求参数**:
```json
{
  "symbol": "000001",
  "side": "buy",
  "type": "limit",
  "quantity": 1000,
  "price": 13.50
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "order_id": "ORDER_a1b2c3d4",
    "symbol": "000001",
    "side": "buy",
    "type": "limit",
    "quantity": 1000,
    "price": 13.50,
    "status": "submitted",
    "created_at": "2025-07-27T15:00:00"
  },
  "message": "订单提交成功"
}
```

### 5. GET /api/v1/trading/account (增强版)
**功能**: 获取详细账户信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user_id": "admin_user",
    "account_type": "margin",
    "status": "active",
    "balance": {
      "total_equity": 1500000.0,
      "cash": 300000.0,
      "market_value": 1200000.0,
      "available_cash": 250000.0,
      "buying_power": 450000.0
    },
    "pnl": {
      "total_pnl": 300000.0,
      "daily_pnl": 25000.0,
      "total_return": 0.25,
      "annualized_return": 0.28
    },
    "risk": {
      "risk_level": "medium",
      "leverage_ratio": 1.2,
      "concentration_ratio": 0.25
    }
  }
}
```

### 6. GET /api/v1/trading/orders
**功能**: 查询订单列表

**参数**:
- `status`: 订单状态 (可选)
- `symbol`: 股票代码 (可选)
- `limit`: 返回条数 (可选，默认100)

### 7. GET /api/v1/trading/positions  
**功能**: 查询持仓信息

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001",
      "name": "平安银行",
      "quantity": 10000,
      "avg_cost": 12.50,
      "current_price": 13.45,
      "market_value": 134500.0,
      "unrealized_pnl": 9500.0,
      "unrealized_pnl_ratio": 0.076,
      "position_ratio": 0.089
    }
  ]
}
```

### 8. GET /api/v1/trading/trades
**功能**: 查询成交记录

**参数**:
- `order_id`: 订单ID (可选)
- `symbol`: 股票代码 (可选)
- `limit`: 返回条数 (可选，默认100)

## 📊 策略相关API

### 9. POST /api/v1/strategies/backtest
**功能**: 执行策略回测

**请求参数**:
```json
{
  "strategy_code": "# 策略代码",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31", 
  "initial_capital": 1000000,
  "symbols": ["000001", "600519"]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "backtest_id": "BACKTEST_a1b2c3d4",
    "status": "completed",
    "performance": {
      "total_return": 0.15,
      "sharpe_ratio": 1.2,
      "max_drawdown": -0.08,
      "win_rate": 0.6
    },
    "trades": [...],
    "nav_curve": [...]
  }
}
```

## ⚠️ 风险管理API

### 10. GET /api/v1/risk/metrics
**功能**: 获取投资组合风险指标

**响应示例**:
```json
{
  "success": true,
  "data": {
    "portfolio_value": 1250000.0,
    "var": {
      "1_day_95": -18000.0,
      "1_day_99": -25000.0
    },
    "beta": 0.85,
    "sharpe_ratio": 1.35,
    "max_drawdown": -0.12,
    "volatility": 0.18,
    "concentration": {
      "top_5_holdings": 0.45,
      "single_stock_limit": 0.10
    },
    "stress_test": {
      "market_down_10": -125000.0,
      "market_down_20": -280000.0
    }
  }
}
```

### 11. GET /api/v1/risk/alerts
**功能**: 获取风险警报

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "ALERT_a1b2",
      "type": "concentration",
      "level": "medium",
      "title": "持仓集中度警告",
      "message": "单只股票持仓超过10%限制",
      "symbol": "600519",
      "current_value": 0.125,
      "threshold": 0.10,
      "status": "active"
    }
  ],
  "summary": {
    "critical": 0,
    "high": 1,
    "medium": 2,
    "low": 1
  }
}
```

## 🔄 原有API (已存在)

12. **GET /api/v1/market/current-prices** - 获取当前价格
13. **GET /api/v1/account/info** - 获取账户信息(简版)
14. **GET /api/v1/trade/orders** - 获取订单列表(简版)
15. **GET /api/v1/strategies** - 获取策略列表
16. **GET /health** - 健康检查

## 技术特性

### 🛡️ 错误处理
- 所有API都有完善的异常处理机制
- 导入失败时自动降级使用模拟数据
- 统一的错误响应格式

### 📊 模拟数据
- 使用真实的股票代码和公司名称
- 合理的价格波动和市场数据
- 完整的交易生命周期模拟
- 真实的风险指标计算

### 🔧 兼容性
- 向后兼容原有API接口
- 支持可选参数和默认值
- 灵活的查询过滤条件

## 测试方法

1. **启动后端服务**:
   ```bash
   cd /Users/<USER>/Desktop/quant-platf/backend
   python3 app/main_simple.py
   ```

2. **运行API测试**:
   ```bash
   cd /Users/<USER>/Desktop/quant-platf
   python3 test_new_apis.py
   ```

3. **查看API文档**:
   访问 `http://localhost:8000/docs` 查看Swagger文档

## 总结

本次更新新增了11个核心API端点，完善了4个原有端点，构建了完整的量化交易平台API体系。所有接口都经过语法检查，使用模拟数据确保稳定运行，为前端开发和系统集成提供了坚实的基础。
# 策略文库功能说明

## 📋 概述

策略文库是量化投资平台的核心功能之一，用于管理和浏览本地存储的策略文件。该功能将 `data/Strategy` 目录中的策略文件以可视化的方式展现给用户，支持浏览、搜索、预览和导入等操作。

## 📁 数据位置

策略文件存储位置：
```
C:\Users\<USER>\Desktop\quant-platf\data\Strategy
```

### 目录结构
```
data/Strategy/
├── 2019/
│   ├── 2019 (1).txt    # 首板低开策略
│   ├── 2019 (2).txt    # 其他策略
│   └── ...             # 约99个策略文件
├── 2020/
│   ├── 2020 (1).txt
│   └── ...             # 约98个策略文件
├── 2021/               # 约99个策略文件
├── 2022/               # 约98个策略文件
├── 2023/               # 约99个策略文件
├── 2024/               # 约101个策略文件
└── 2025/               # 约100个策略文件
```

**总计：约600+个策略文件，跨越7年时间**

## 🎯 主要功能

### 1. 年份导航
- 按年份组织策略文件
- 显示每年的策略数量
- 点击年份卡片快速浏览该年策略

### 2. 智能搜索
- 支持按策略标题搜索
- 支持按作者名称搜索
- 支持按策略内容关键词搜索
- 实时搜索结果展示

### 3. 多种视图模式
- **列表视图**：表格形式，显示详细信息
- **网格视图**：卡片形式，更直观的浏览体验

### 4. 策略详情预览
- 查看策略完整代码
- 显示策略元信息（标题、作者、来源等）
- 代码语法高亮显示

### 5. 一键导入功能
- 将策略文件导入到个人策略库
- 自动解析策略信息
- 支持自定义策略名称

### 6. 分页浏览
- 支持自定义每页显示数量
- 页面跳转和大小调整
- 显示总数统计信息

## 🚀 使用方法

### 访问策略文库

1. **通过导航菜单**：
   - 点击侧边栏 "策略中心" → "策略文库"

2. **通过策略中心**：
   - 进入策略中心页面
   - 点击 "策略文库" 标签页

3. **直接访问**：
   - 访问 `/strategy/library` 路径

### 浏览策略

1. **按年份浏览**：
   - 在首页点击年份卡片
   - 查看该年份的所有策略文件

2. **搜索策略**：
   - 在搜索框输入关键词
   - 支持策略名称、作者、内容搜索

3. **切换视图**：
   - 使用右上角的视图切换按钮
   - 在列表和网格视图间切换

### 查看策略详情

1. 点击策略文件名或"查看"按钮
2. 在弹出的对话框中查看：
   - 策略基本信息
   - 完整策略代码
   - 作者和来源信息

### 导入策略

1. **单个导入**：
   - 在策略列表中点击"导入"按钮
   - 或在详情对话框中点击"导入到我的策略"

2. **确认导入**：
   - 系统会提示确认导入
   - 可选择自定义策略名称
   - 导入后策略会添加到个人策略库

## 📊 策略文件格式

策略文件通常包含以下信息：

```python
# 克隆自聚宽文章：https://www.joinquant.com/post/44901
# 标题：首板低开策略
# 作者：wywy1995

from jqlib.technical_analysis import *
from jqfactor import *
from jqdata import *
import datetime as dt
import pandas as pd

def initialize(context):
    # 策略初始化代码
    pass

def handle_data(context, data):
    # 策略主逻辑
    pass
```

### 元信息提取

系统会自动提取以下信息：
- **标题**：从注释中的"标题："字段
- **作者**：从注释中的"作者："字段  
- **来源**：从注释中的URL链接
- **描述**：从注释中的描述信息

## 🔧 技术实现

### 前端组件
- `StrategyLibrary.vue`：主要页面组件
- `strategyFileApi.ts`：API接口封装

### 后端API
- `/api/v1/strategy-files/years`：获取可用年份
- `/api/v1/strategy-files/{year}`：获取年份文件列表
- `/api/v1/strategy-files/{year}/{filename}`：获取文件详情
- `/api/v1/strategy-files/search`：搜索策略
- `/api/v1/strategy-files/{year}/{filename}/import`：导入策略

### 数据流程
1. 前端请求年份列表
2. 用户选择年份，加载文件列表
3. 点击文件，获取详细内容
4. 用户确认导入，调用导入API
5. 后端验证并创建用户策略

## 🎨 界面特性

### 响应式设计
- 支持桌面端和移动端
- 自适应布局和字体大小
- 触摸友好的交互设计

### 用户体验
- 加载状态指示
- 错误提示和处理
- 操作确认对话框
- 搜索结果高亮

### 视觉设计
- 现代化的卡片式布局
- 清晰的信息层次
- 一致的色彩和图标
- 平滑的动画过渡

## 📈 统计信息

当前策略文库包含：
- **7个年份**：2019-2025
- **600+个策略文件**
- **多种策略类型**：趋势跟踪、均值回归、动量策略等
- **丰富的策略来源**：聚宽、优矿等平台

## 🔮 未来扩展

### 计划功能
1. **策略分类**：按策略类型分类浏览
2. **标签系统**：为策略添加标签
3. **收藏功能**：收藏感兴趣的策略
4. **评分系统**：用户可以为策略评分
5. **批量导入**：支持批量选择导入
6. **策略对比**：对比不同策略的特点

### 技术优化
1. **缓存机制**：提升加载速度
2. **虚拟滚动**：处理大量数据
3. **全文搜索**：更强大的搜索功能
4. **代码高亮**：更好的代码展示

## 🎯 使用建议

1. **新手用户**：
   - 从年份浏览开始了解策略
   - 使用搜索功能找到感兴趣的策略类型
   - 先查看策略详情再决定是否导入

2. **进阶用户**：
   - 利用搜索功能快速定位特定策略
   - 批量浏览和比较不同策略
   - 结合策略开发功能进行修改和优化

3. **最佳实践**：
   - 导入前仔细阅读策略代码
   - 理解策略逻辑和风险特征
   - 在实盘使用前进行充分的回测验证

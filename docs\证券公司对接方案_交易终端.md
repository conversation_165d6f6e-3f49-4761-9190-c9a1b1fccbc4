# 量化投资平台交易终端证券公司对接方案

**文档版本**: v1.0  
**创建日期**: 2025年7月29日  
**适用范围**: 交易终端真实交易对接  

## 📋 对接概览

### 当前交易终端架构
我们的交易终端 (http://localhost:5173/trading/terminal) 已经具备了完整的交易功能架构，包括：

- 🎯 **前端交易界面** - 完整的交易表单和订单管理
- 🔄 **中间件API** - 标准化的交易API接口
- 🏗️ **后端交易服务** - CTP交易服务和风险控制
- 📊 **实时数据** - WebSocket实时行情和订单状态

### 证券公司对接方式
目前支持以下几种主流对接方式：

1. **CTP接口对接** ⭐⭐⭐ (推荐)
2. **券商私有API对接** ⭐⭐
3. **第三方交易平台对接** ⭐⭐
4. **模拟交易环境** ⭐ (测试用)

## 🔌 CTP接口对接方案 (主推荐)

### CTP简介
CTP (Comprehensive Transaction Platform) 是上海期货信息技术有限公司开发的期货交易平台，也是目前国内最主流的程序化交易接口。

### 支持的券商列表
我们已经预配置了以下券商的CTP接口：

#### 🏦 主要期货公司
```python
# 中信期货
"CITIC": {
    "broker_id": "66666",
    "broker_name": "中信期货",
    "trade_front": "tcp://***************:10130",
    "md_front": "tcp://***************:10131"
}

# 华泰期货  
"HTFC": {
    "broker_id": "1080", 
    "broker_name": "华泰期货",
    "trade_front": "tcp://180.169.112.52:41205",
    "md_front": "tcp://180.169.112.52:41213"
}

# 国泰君安期货
"GTJA": {
    "broker_id": "1017",
    "broker_name": "国泰君安期货", 
    "trade_front": "tcp://***************:10000",
    "md_front": "tcp://***************:10010"
}

# 申银万国期货
"SYWG": {
    "broker_id": "1090",
    "broker_name": "申银万国期货",
    "trade_front": "tcp://***************:10001", 
    "md_front": "tcp://***************:10011"
}
```

#### 🧪 仿真测试环境
```python
# SimNow仿真环境 (免费测试)
"SIMNOW": {
    "broker_id": "9999",
    "broker_name": "SimNow仿真",
    "trade_front": "tcp://***************:10130",
    "md_front": "tcp://***************:10131"
}
```

### CTP对接流程

#### 1. 开户准备
```markdown
📋 开户所需材料:
- 身份证正反面照片
- 银行卡照片  
- 手写签名照片
- 风险测评问卷
- 开户申请表

💰 资金要求:
- 期货账户: 最低5万元
- 股票账户: 最低1万元
- 期权账户: 最低50万元
```

#### 2. 技术对接步骤

**步骤1: 获取CTP账户信息**
```python
# 券商提供的账户信息
CTP_CONFIG = {
    "broker_id": "券商代码",      # 如: 66666
    "user_id": "您的账户",        # 如: 123456789  
    "password": "交易密码",       # 交易密码
    "auth_code": "认证码",        # 券商提供的认证码
    "app_id": "应用标识",         # 券商分配的应用ID
}
```

**步骤2: 配置环境变量**
```bash
# 在 .env 文件中配置
CTP_BROKER_ID=66666
CTP_USER_ID=您的账户
CTP_PASSWORD=您的密码
CTP_AUTH_CODE=认证码
CTP_APP_ID=应用标识
CTP_TRADE_FRONT=tcp://***************:10130
CTP_MD_FRONT=tcp://***************:10131
```

**步骤3: 启动CTP服务**
```python
# 后端自动初始化CTP连接
from app.services.ctp_service import CTPService

ctp_service = CTPService()
await ctp_service.initialize()
```

**步骤4: 前端交易测试**
```javascript
// 前端提交订单
const orderData = {
  symbol: "rb2501",        // 合约代码
  side: "buy",             // 买卖方向
  orderType: "limit",      // 订单类型
  quantity: 1,             // 数量
  price: 3500              // 价格
}

await tradingApi.submitOrder(orderData)
```

### 实际交易流程

#### 交易数据流
```mermaid
graph TD
    A[前端交易界面] --> B[交易API]
    B --> C[风险控制检查]
    C --> D[CTP服务]
    D --> E[券商CTP服务器]
    E --> F[交易所]
    
    F --> G[成交回报]
    G --> E
    E --> D
    D --> H[WebSocket推送]
    H --> A
```

#### 订单生命周期
```python
# 1. 订单提交
order_request = OrderRequest(
    symbol="rb2501",
    direction="BUY", 
    offset="OPEN",
    order_type="LIMIT",
    price=3500.0,
    volume=1
)

# 2. 风险检查
risk_check = await risk_service.check_order_risk(user_id, order_request)

# 3. 提交到CTP
if risk_check.is_valid:
    order_response = await ctp_service.submit_order(order_request, user_id)

# 4. 实时状态更新
# - 订单确认
# - 部分成交  
# - 完全成交
# - 订单撤销
```

## 🏢 券商私有API对接方案

### 主要券商API

#### 华泰证券 - VIP交易接口
```python
# 华泰VIP接口配置
HUATAI_CONFIG = {
    "api_url": "https://trading.htsc.com.cn/api",
    "app_key": "您的AppKey", 
    "app_secret": "您的AppSecret",
    "account": "资金账号",
    "password": "交易密码"
}
```

#### 中信证券 - 至信全能版API
```python
# 中信API配置  
CITIC_CONFIG = {
    "server_ip": "交易服务器IP",
    "server_port": 7708,
    "version": "6.57",
    "account": "资金账号", 
    "password": "交易密码"
}
```

#### 国泰君安 - 君弘API
```python
# 国泰君安配置
GTJA_CONFIG = {
    "api_url": "https://api.gtja.com",
    "client_id": "客户端ID",
    "client_secret": "客户端密钥", 
    "account": "资金账号",
    "password": "交易密码"
}
```

### 私有API对接流程

#### 1. 申请API权限
- 联系券商客户经理
- 提交API使用申请
- 签署相关协议
- 获取API密钥

#### 2. 技术集成
```python
# 创建券商适配器
class BrokerAdapter:
    def __init__(self, broker_type: str, config: dict):
        self.broker_type = broker_type
        self.config = config
        
    async def submit_order(self, order_data: dict):
        if self.broker_type == "HUATAI":
            return await self._huatai_submit_order(order_data)
        elif self.broker_type == "CITIC":
            return await self._citic_submit_order(order_data)
        # ... 其他券商
```

## 🔗 第三方交易平台对接

### 聚宽交易平台
```python
# 聚宽API配置
JOINQUANT_CONFIG = {
    "api_url": "https://www.joinquant.com/api",
    "token": "您的API Token",
    "account_type": "实盘/模拟盘"
}
```

### 米筐交易平台  
```python
# 米筐API配置
RICEQUANT_CONFIG = {
    "api_url": "https://www.ricequant.com/api", 
    "access_key": "访问密钥",
    "secret_key": "私钥"
}
```

## ⚙️ 配置和部署

### 环境配置文件
```bash
# .env.production
# CTP配置
CTP_BROKER_ID=实际券商代码
CTP_USER_ID=实际账户
CTP_PASSWORD=实际密码
CTP_AUTH_CODE=实际认证码
CTP_APP_ID=实际应用ID
CTP_TRADE_FRONT=实际交易前置地址
CTP_MD_FRONT=实际行情前置地址

# 风险控制
RISK_MAX_ORDER_AMOUNT=1000000    # 单笔最大金额
RISK_DAILY_LOSS_LIMIT=50000      # 日亏损限额
RISK_POSITION_LIMIT=0.3          # 持仓限制比例

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/trading_db

# Redis配置 (缓存和消息队列)
REDIS_URL=redis://localhost:6379/0
```

### Docker部署配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  trading-backend:
    build: ./backend
    environment:
      - CTP_BROKER_ID=${CTP_BROKER_ID}
      - CTP_USER_ID=${CTP_USER_ID}
      - CTP_PASSWORD=${CTP_PASSWORD}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8001:8000"
      
  trading-frontend:
    build: ./frontend
    ports:
      - "5173:5173"
    depends_on:
      - trading-backend
```

## 🛡️ 安全和风险控制

### 安全措施
```python
# 1. 密钥加密存储
from app.services.encryption_service import EncryptionService

encryption = EncryptionService()
encrypted_password = encryption.encrypt(password)

# 2. API访问限制
RATE_LIMITS = {
    "order_submit": "100/minute",    # 每分钟最多100笔订单
    "order_cancel": "200/minute",    # 每分钟最多200次撤单
    "query_position": "60/minute"    # 每分钟最多60次查询
}

# 3. IP白名单
ALLOWED_IPS = [
    "您的服务器IP",
    "备用服务器IP"
]
```

### 风险控制
```python
# 实时风险监控
class RiskController:
    async def check_order_risk(self, order_data: dict) -> bool:
        # 1. 资金检查
        if order_data.amount > self.max_order_amount:
            return False
            
        # 2. 持仓检查  
        if self.get_position_ratio() > 0.8:
            return False
            
        # 3. 日交易限额检查
        if self.get_daily_trade_amount() > self.daily_limit:
            return False
            
        return True
```

## 📞 技术支持和联系方式

### 券商技术支持
- **中信期货**: 400-990-8826
- **华泰期货**: 400-682-5599  
- **国泰君安**: 400-888-8666
- **申银万国**: 400-889-5618

### 开发支持
- **CTP技术QQ群**: 129586618
- **上期技术官网**: http://www.sfit.com.cn
- **CTP API文档**: http://www.sfit.com.cn/DocumentDown.htm

### 我们的技术支持
- **项目文档**: 详细的集成文档和示例代码
- **测试环境**: 提供完整的仿真测试环境
- **技术咨询**: 专业的技术团队支持

## 🚀 快速开始指南

### 1. 仿真环境测试 (推荐新手)

#### SimNow仿真账户申请
1. **访问SimNow官网**: http://www.simnow.com.cn
2. **注册仿真账户**: 填写基本信息，无需资金
3. **获取账户信息**:
   ```
   账户: 您的仿真账户
   密码: 您设置的密码
   经纪商代码: 9999
   ```

#### 配置仿真环境
```bash
# 修改 .env 文件
CTP_BROKER_ID=9999
CTP_USER_ID=您的仿真账户
CTP_PASSWORD=您的仿真密码
CTP_AUTH_CODE=0000000000000000
CTP_APP_ID=simnow_client_test
CTP_TRADE_FRONT=tcp://***************:10130
CTP_MD_FRONT=tcp://***************:10131
```

#### 启动测试
```bash
# 1. 启动后端服务
cd backend
python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8001

# 2. 启动前端服务
cd frontend
npm run dev

# 3. 访问交易终端
http://localhost:5173/trading/terminal
```

### 2. 真实环境部署

#### 生产环境检查清单
- [ ] 券商账户已开通并入金
- [ ] CTP权限已申请并获得认证码
- [ ] 服务器环境已配置（推荐Linux）
- [ ] 数据库已部署（PostgreSQL）
- [ ] Redis缓存已配置
- [ ] SSL证书已配置
- [ ] 防火墙规则已设置
- [ ] 监控系统已部署
- [ ] 备份策略已制定

#### 生产环境配置
```python
# config/production.py
PRODUCTION_CONFIG = {
    # CTP配置
    "ctp": {
        "broker_id": "实际券商代码",
        "user_id": "实际账户",
        "password": "实际密码",
        "auth_code": "实际认证码",
        "app_id": "实际应用ID",
        "trade_front": "实际交易前置",
        "md_front": "实际行情前置"
    },

    # 风险控制
    "risk": {
        "max_order_amount": 1000000,
        "daily_loss_limit": 50000,
        "position_limit": 0.3,
        "max_orders_per_minute": 100
    },

    # 监控配置
    "monitoring": {
        "enable_alerts": True,
        "alert_email": "<EMAIL>",
        "alert_phone": "13800138000"
    }
}
```

## 📊 交易终端功能说明

### 当前已实现功能
✅ **股票搜索和选择** - 支持代码/名称搜索
✅ **实时行情显示** - K线图、盘口、成交明细
✅ **订单下单** - 买入/卖出、限价/市价单
✅ **持仓管理** - 实时持仓查询和管理
✅ **订单管理** - 订单查询、撤单、改单
✅ **资金查询** - 可用资金、持仓市值
✅ **风险控制** - 实时风险监控和预警
✅ **WebSocket推送** - 实时数据更新

### 交易终端界面说明
```
交易终端布局:
┌─────────────────────────────────────────────────────┐
│ 股票搜索栏 │ 布局切换 │ 全屏按钮                      │
├─────────────────────────────────────────────────────┤
│                    │                                │
│     K线图表        │        股票信息卡片              │
│                    │                                │
│                    ├────────────────────────────────┤
│                    │                                │
├────────────────────┤        交易表单                │
│                    │                                │
│ 盘口 │成交│资金流向  │                                │
│                    ├────────────────────────────────┤
│                    │ 持仓 │订单│成交│资金│策略        │
└─────────────────────────────────────────────────────┘
```

## 🔧 技术架构详解

### 前端架构
```typescript
// 交易终端组件结构
TradingTerminal.vue
├── StockSelector.vue          // 股票选择器
├── KLineChart.vue            // K线图表
├── OrderBook.vue             // 盘口数据
├── OrderForm.vue             // 交易表单
├── PositionManagement.vue    // 持仓管理
├── OrderManagement.vue       // 订单管理
└── RiskMonitor.vue          // 风险监控
```

### 后端架构
```python
# 交易服务架构
app/
├── api/v1/trading.py         # 交易API接口
├── services/
│   ├── ctp_service.py        # CTP交易服务
│   ├── trading_service.py    # 交易业务服务
│   ├── risk_service.py       # 风险控制服务
│   └── market_service.py     # 行情数据服务
├── models/
│   ├── trading.py           # 交易数据模型
│   └── ctp_models.py        # CTP数据模型
└── core/
    ├── ctp_config.py        # CTP配置
    └── ctp_wrapper.py       # CTP底层封装
```

### 数据流架构
```mermaid
graph LR
    A[交易终端] --> B[交易API]
    B --> C[交易服务]
    C --> D[CTP服务]
    D --> E[券商服务器]

    F[行情服务] --> G[WebSocket]
    G --> A

    H[风险服务] --> C
    I[数据库] --> C
    J[缓存] --> C
```

---

**重要提醒**:
1. 真实交易涉及资金安全，请务必在仿真环境充分测试后再进行实盘交易
2. 建议先从小资金开始，逐步增加交易规模
3. 确保网络稳定性和系统可靠性
4. 定期备份交易数据和日志
5. 建议配置专业的监控和报警系统

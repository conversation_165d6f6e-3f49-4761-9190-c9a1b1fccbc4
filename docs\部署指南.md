# 量化交易平台部署指南

## 📋 概述

本指南详细介绍了量化交易平台在不同环境下的部署方法，包括开发环境、测试环境和生产环境的完整部署流程。

## 🎯 部署架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │    │  后端 (FastAPI)  │    │   数据库层       │
│                 │    │                 │    │                 │
│ - Nginx         │    │ - Python 3.13   │    │ - PostgreSQL    │
│ - Vue 3         │◄──►│ - FastAPI       │◄──►│ - Redis         │
│ - Element Plus  │    │ - SQLAlchemy    │    │ - InfluxDB      │
│                 │    │ - Celery        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   监控层         │
                    │                 │
                    │ - Prometheus    │
                    │ - Graf<PERSON>       │
                    │ - ELK Stack     │
                    └─────────────────┘
```

## 🛠️ 环境要求

### 硬件要求

#### 最小配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 10Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM以上
- **存储**: 50GB SSD以上
- **网络**: 100Mbps以上

#### 生产环境配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: 100GB SSD以上
- **网络**: 1Gbps以上

### 软件要求

#### 操作系统
- **Linux**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Windows**: Windows 10/11 / Windows Server 2019+
- **macOS**: macOS 11.0+

#### 运行时环境
- **Python**: 3.13+
- **Node.js**: 18.0+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

#### 数据库
- **PostgreSQL**: 13+
- **Redis**: 6.0+
- **InfluxDB**: 2.0+ (可选，用于时序数据)

## 🚀 快速部署 (Docker)

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/quant-platform.git
cd quant-platform
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 初始化数据库

```bash
# 运行数据库迁移
docker-compose exec backend python -m alembic upgrade head

# 创建初始用户
docker-compose exec backend python scripts/create_admin.py
```

### 5. 验证部署

访问以下地址验证部署是否成功：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **监控面板**: http://localhost:3001

## 🔧 手动部署

### 后端部署

#### 1. 环境准备

```bash
# 安装Python 3.13
sudo apt update
sudo apt install python3.13 python3.13-venv python3.13-dev

# 创建项目目录
sudo mkdir -p /opt/quant-platform
sudo chown $USER:$USER /opt/quant-platform
cd /opt/quant-platform

# 克隆代码
git clone https://github.com/your-repo/quant-platform.git .
```

#### 2. 安装依赖

```bash
cd backend

# 创建虚拟环境
python3.13 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

#### 3. 配置数据库

```bash
# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE quant_platform;
CREATE USER quant_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE quant_platform TO quant_user;
\q

# 配置环境变量
cp .env.example .env
vim .env
```

#### 4. 数据库迁移

```bash
# 运行迁移
alembic upgrade head

# 创建初始数据
python scripts/init_data.py
```

#### 5. 启动服务

```bash
# 使用Gunicorn启动
gunicorn app.main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile -
```

### 前端部署

#### 1. 环境准备

```bash
# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装pnpm
npm install -g pnpm
```

#### 2. 构建项目

```bash
cd frontend

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env.production
vim .env.production

# 构建生产版本
pnpm build
```

#### 3. 配置Nginx

```bash
# 安装Nginx
sudo apt install nginx

# 创建配置文件
sudo vim /etc/nginx/sites-available/quant-platform
```

Nginx配置内容：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /opt/quant-platform/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 4. 启用站点

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/quant-platform /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 🔄 系统服务配置

### 后端服务 (systemd)

创建服务文件：

```bash
sudo vim /etc/systemd/system/quant-backend.service
```

服务配置：

```ini
[Unit]
Description=Quant Platform Backend
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=quant
Group=quant
WorkingDirectory=/opt/quant-platform/backend
Environment=PATH=/opt/quant-platform/backend/venv/bin
ExecStart=/opt/quant-platform/backend/venv/bin/gunicorn app.main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile /var/log/quant-platform/access.log \
  --error-logfile /var/log/quant-platform/error.log
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable quant-backend

# 启动服务
sudo systemctl start quant-backend

# 查看状态
sudo systemctl status quant-backend
```

### Celery服务

创建Celery服务：

```bash
sudo vim /etc/systemd/system/quant-celery.service
```

```ini
[Unit]
Description=Quant Platform Celery Worker
After=network.target redis.service

[Service]
Type=exec
User=quant
Group=quant
WorkingDirectory=/opt/quant-platform/backend
Environment=PATH=/opt/quant-platform/backend/venv/bin
ExecStart=/opt/quant-platform/backend/venv/bin/celery -A app.tasks.celery worker --loglevel=info
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

## 🐳 Kubernetes部署

### 1. 准备Kubernetes集群

```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 验证集群连接
kubectl cluster-info
```

### 2. 创建命名空间

```bash
kubectl create namespace quant-platform
```

### 3. 部署配置

```bash
# 应用所有配置
kubectl apply -f k8s/ -n quant-platform

# 查看部署状态
kubectl get pods -n quant-platform
kubectl get services -n quant-platform
```

### 4. 配置Ingress

```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: quant-platform-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: quant.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000
```

## 📊 监控配置

### Prometheus配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'quant-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']
      
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
```

### Grafana仪表板

导入预配置的仪表板：

```bash
# 导入仪表板配置
curl -X POST \
  *********************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana-dashboard.json
```

## 🔒 安全配置

### SSL证书配置

```bash
# 使用Let's Encrypt获取证书
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow from 10.0.0.0/8 to any port 8000
```

## 🔧 维护操作

### 备份数据库

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"
DB_NAME="quant_platform"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete
```

### 更新部署

```bash
#!/bin/bash
# deploy.sh
set -e

echo "开始部署更新..."

# 拉取最新代码
git pull origin main

# 更新后端
cd backend
source venv/bin/activate
pip install -r requirements.txt
alembic upgrade head
sudo systemctl restart quant-backend

# 更新前端
cd ../frontend
pnpm install
pnpm build
sudo systemctl reload nginx

echo "部署完成！"
```

### 日志管理

```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/quant-platform
```

```
/var/log/quant-platform/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 quant quant
    postrotate
        systemctl reload quant-backend
    endscript
}
```

## 🚨 故障排查

### 常见问题

#### 1. 后端服务无法启动

```bash
# 检查日志
sudo journalctl -u quant-backend -f

# 检查端口占用
sudo netstat -tlnp | grep :8000

# 检查数据库连接
psql -h localhost -U quant_user -d quant_platform
```

#### 2. 前端页面无法访问

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查Nginx配置
sudo nginx -t

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
```

#### 3. 数据库连接失败

```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 检查数据库连接
sudo -u postgres psql -l

# 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```

### 性能优化

#### 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX idx_orders_user_id_created_at ON orders(user_id, created_at);

-- 分析表统计信息
ANALYZE;

-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

#### 应用优化

```python
# 启用连接池
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 参考故障排查章节
4. 联系技术支持团队

---

*本部署指南会持续更新，请关注最新版本*

# 错误处理和日志系统文档

## 概述

本文档描述了量化交易平台的完整错误处理和日志系统架构，包括前端和后端的实现方案、监控集成、以及环境差异化配置。

## 系统架构

### 前端错误处理架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   全局错误捕获   │───▶│   错误边界组件    │───▶│   监控服务集成   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  API拦截器处理  │    │   用户友好提示    │    │   Sentry上报    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 后端错误处理架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  异常中间件捕获  │───▶│   结构化异常类    │───▶│   监控指标收集   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  统一错误响应   │    │   日志记录系统    │    │  Prometheus指标  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 前端错误处理

### 1. 错误边界组件 (ErrorBoundary.vue)

**功能特性:**
- React-like错误边界功能
- 多级错误处理（页面/区块/组件）
- 自动恢复机制
- 错误重试功能
- 详细的错误信息展示
- 用户友好的错误提示

**使用方式:**
```vue
<template>
  <ErrorBoundary
    :component-name="'UserDashboard'"
    :level="'page'"
    :enable-auto-recovery="true"
    :max-retry-attempts="3"
    @error="handleError"
    @retry="handleRetry"
  >
    <UserDashboard />
  </ErrorBoundary>
</template>
```

**配置选项:**
- `fallbackTitle`: 自定义错误标题
- `fallbackMessage`: 自定义错误消息
- `showRetry`: 是否显示重试按钮
- `enableReporting`: 是否启用错误上报
- `enableAutoRecovery`: 是否启用自动恢复
- `recoveryTimeout`: 自动恢复超时时间
- `maxRetryAttempts`: 最大重试次数

### 2. API拦截器错误处理

**安全拦截器特性:**
- 请求安全验证
- 响应数据清理
- 错误分类处理
- 自动重试机制
- 频率限制处理
- 安全事件记录

**错误处理流程:**
```typescript
// 请求拦截器
request interceptor → 安全验证 → 数据清理 → 添加安全头 → 记录日志

// 响应拦截器
response interceptor → 安全检查 → 数据解密 → 错误分类 → 用户提示
```

### 3. 监控服务集成

**监控功能:**
- 错误自动上报
- 性能指标收集
- 用户行为追踪
- 业务指标记录
- 自定义事件跟踪

**使用示例:**
```typescript
import { monitoring } from '@/services/monitoring.service'

// 报告错误
monitoring.reportError({
  type: 'api',
  severity: 'high',
  message: 'API调用失败',
  context: { url: '/api/orders', status: 500 }
})

// 记录性能指标
monitoring.recordMetric({
  name: 'page_load_time',
  value: 1200,
  unit: 'ms',
  category: 'navigation'
})

// 追踪用户行为
monitoring.trackUserEvent('order_submit', 'click', 'trading', {
  orderId: '12345',
  symbol: 'AAPL'
})
```

## 后端错误处理

### 1. 结构化异常系统

**异常层次结构:**
```
BaseCustomException
├── ValidationError
├── AuthenticationError
├── AuthorizationError
├── BusinessLogicError
├── DatabaseError
├── NetworkError
├── TimeoutError
├── CircuitBreakerError
├── 量化交易特定异常
│   ├── MarketClosedError
│   ├── InsufficientFundsError
│   ├── PositionLimitError
│   ├── RiskLimitError
│   └── CTP相关异常
│       ├── CTPConnectionError
│       ├── CTPAuthError
│       ├── CTPOrderError
│       └── CTPDataError
└── ...更多异常类型
```

**异常特性:**
- 错误代码和分类
- 严重程度级别
- 恢复提示信息
- 上下文数据
- 重试机制
- 错误追踪ID

**使用示例:**
```python
from app.core.exceptions import InsufficientFundsError, ErrorSeverity

# 抛出业务异常
raise InsufficientFundsError(
    message="账户余额不足",
    error_code="INSUFFICIENT_FUNDS_001",
    severity=ErrorSeverity.HIGH,
    details={
        "required_amount": 10000,
        "available_amount": 5000,
        "account_id": "123456"
    },
    recovery_hint="请充值后重试",
    should_retry=False
)
```

### 2. 异常处理中间件

**中间件功能:**
- 全局异常捕获
- 统一错误响应格式
- 错误日志记录
- Sentry错误上报
- 性能监控集成
- 请求追踪

**错误响应格式:**
```json
{
  "error": {
    "type": "trading_error",
    "code": "INSUFFICIENT_FUNDS_001",
    "message": "账户余额不足",
    "details": {
      "required_amount": 10000,
      "available_amount": 5000
    },
    "request_id": "req_1234567890",
    "timestamp": "2023-12-01T10:30:00Z",
    "severity": "high",
    "recovery_hint": "请充值后重试",
    "should_retry": false
  }
}
```

### 3. 日志系统

**日志轮转和清理:**
- 自动日志轮转
- 文件大小和时间控制
- 压缩存储
- 自动清理过期日志
- 统计信息收集

**环境差异化配置:**

| 环境 | 控制台输出 | 文件输出 | 日志级别 | 保留时间 | JSON格式 | 敏感数据过滤 |
|------|-----------|----------|----------|----------|----------|-------------|
| 开发 | ✅ | ✅ | DEBUG | 7天 | ❌ | ❌ |
| 测试 | ✅ | ✅ | INFO | 3天 | ✅ | ✅ |
| 预发布 | ❌ | ✅ | INFO | 14天 | ✅ | ✅ |
| 生产 | ❌ | ✅ | WARNING | 90天 | ✅ | ✅ |

**日志类型:**
- 应用日志 (app.log)
- 交易日志 (trading.log)
- 错误日志 (error.log)
- 审计日志 (audit.log)
- 安全日志 (security.log)
- 性能日志 (performance.log)

## 监控集成

### 1. Sentry配置

**集成功能:**
- 错误自动捕获
- 性能监控
- 会话重放
- 自定义标签和上下文
- 错误过滤和采样
- 敏感数据处理

**配置示例:**
```python
# 后端Sentry配置
sentry_config = SentryConfig(
    dsn="https://<EMAIL>/project-id",
    environment="production",
    traces_sample_rate=0.1,
    profiles_sample_rate=0.1,
    enabled_integrations=["sqlalchemy", "fastapi", "redis"]
)
```

```typescript
// 前端Sentry配置
setupSentry(app, router, {
  dsn: "https://<EMAIL>/project-id",
  environment: "production",
  tracesSampleRate: 0.01,
  replaysSessionSampleRate: 0.01
})
```

### 2. Prometheus指标

**指标类型:**
- HTTP请求指标
- 数据库操作指标
- 业务指标
- 交易指标
- CTP连接指标
- 错误统计指标

**指标示例:**
```python
# 记录HTTP请求
monitoring.record_http_request("POST", "/api/orders", 200, 0.5)

# 记录交易订单
monitoring.record_trading_order("limit", "filled")

# 记录错误
monitoring.record_error("validation_error", "medium")
```

## 最佳实践

### 1. 错误处理原则

1. **分层处理**: 在不同层级处理不同类型的错误
2. **用户友好**: 向用户显示易懂的错误信息
3. **详细记录**: 记录足够的上下文信息用于调试
4. **快速恢复**: 提供自动重试和恢复机制
5. **安全考虑**: 避免泄露敏感信息

### 2. 日志记录原则

1. **结构化日志**: 使用一致的日志格式
2. **合理级别**: 根据重要性选择正确的日志级别
3. **上下文信息**: 包含足够的上下文信息
4. **性能考虑**: 避免过度日志记录影响性能
5. **隐私保护**: 过滤敏感数据

### 3. 监控原则

1. **关键指标**: 监控关键业务和技术指标
2. **告警阈值**: 设置合理的告警阈值
3. **趋势分析**: 关注指标趋势而非单点数据
4. **可视化**: 使用图表展示监控数据
5. **响应机制**: 建立快速响应机制

## 配置示例

### 环境变量配置

```bash
# 开发环境
ENVIRONMENT=development
LOG_LEVEL=DEBUG
SENTRY_DSN=
PROMETHEUS_ENABLED=true

# 生产环境
ENVIRONMENT=production
LOG_LEVEL=WARNING
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENABLED=true
SENTRY_TRACES_SAMPLE_RATE=0.01
```

### Docker Compose配置

```yaml
version: '3.8'
services:
  backend:
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=WARNING
      - SENTRY_DSN=${SENTRY_DSN}
      - PROMETHEUS_ENABLED=true
    volumes:
      - ./logs:/app/logs
      - /var/log/quant-platform:/var/log/quant-platform
```

## 故障排查

### 1. 常见问题

**前端错误:**
- 错误边界不生效 → 检查组件嵌套关系
- API错误未正确处理 → 检查拦截器配置
- 监控数据缺失 → 检查监控服务初始化

**后端错误:**
- 异常未被捕获 → 检查中间件注册顺序
- 日志文件未生成 → 检查目录权限
- Sentry数据缺失 → 检查DSN配置

### 2. 调试工具

1. **浏览器开发者工具**: 查看前端错误和网络请求
2. **日志查看器**: 分析后端日志文件
3. **Sentry面板**: 查看错误统计和详情
4. **Prometheus监控**: 查看系统指标

### 3. 性能优化

1. **日志采样**: 在高流量环境中使用日志采样
2. **异步写入**: 使用异步方式写入日志
3. **批量上报**: 批量发送监控数据
4. **缓存机制**: 缓存频繁查询的数据

## 升级和维护

### 1. 版本升级

1. 检查依赖库版本兼容性
2. 更新配置文件格式
3. 迁移现有日志数据
4. 测试监控功能

### 2. 定期维护

1. 清理过期日志文件
2. 检查监控指标异常
3. 更新错误处理逻辑
4. 优化性能配置

## 总结

本错误处理和日志系统提供了：

1. **完善的前端错误处理机制**
   - 全局错误捕获
   - 错误边界组件
   - API错误处理
   - 用户友好提示

2. **健壮的后端异常系统**
   - 结构化异常类
   - 异常处理中间件
   - 统一错误响应
   - 详细错误记录

3. **全面的监控集成**
   - Sentry错误监控
   - Prometheus指标收集
   - 自定义业务监控
   - 实时告警机制

4. **智能的日志管理**
   - 环境差异化配置
   - 自动轮转和清理
   - 结构化日志记录
   - 性能优化

这套系统确保了量化交易平台的稳定性、可观测性和可维护性，为生产环境的可靠运行提供了坚实保障。
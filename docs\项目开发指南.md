# 量化交易平台开发指南

## 🚀 快速开始

### 📋 环境准备

#### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 8GB+ (推荐16GB+)
- **存储**: 10GB+ 可用空间
- **网络**: 稳定的互联网连接

#### 开发工具
- **代码编辑器**: VS Code (推荐) / WebStorm / PyCharm
- **版本控制**: Git 2.30+
- **容器化**: Docker Desktop (可选)
- **API测试**: Postman / Insomnia (可选)

### 🔧 环境安装

#### 前端环境
```bash
# 安装 Node.js (推荐使用 nvm)
# Windows
winget install OpenJS.NodeJS

# macOS
brew install node

# 验证安装
node --version  # 应该显示 v18.0.0+
npm --version   # 应该显示 8.0.0+

# 安装 pnpm (推荐的包管理器)
npm install -g pnpm
pnpm --version  # 应该显示 7.0.0+
```

#### 后端环境
```bash
# 安装 Python (推荐使用 pyenv)
# Windows
winget install Python.Python.3.13

# macOS
brew install python@3.13

# 验证安装
python --version  # 应该显示 Python 3.13.x
pip --version     # 应该显示 pip 23.0+
```

### 📦 项目安装

#### 1. 克隆项目
```bash
git clone <repository-url>
cd quant-platf
```

#### 2. 前端安装
```bash
cd frontend
pnpm install
```

#### 3. 后端安装
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
.\venv\Scripts\Activate.ps1
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_database.py
```

### 🏃‍♂️ 启动项目

#### 启动后端服务
```bash
cd backend
.\venv\Scripts\Activate.ps1  # Windows
python start_dev.py
# 服务将在 http://localhost:8000 启动
```

#### 启动前端服务
```bash
cd frontend
pnpm dev
# 服务将在 http://localhost:5173 启动
```

#### 验证安装
- 前端: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 🏗️ 项目架构

### 📁 目录结构
```
quant-platf/
├── frontend/          # Vue3 前端项目
├── backend/           # FastAPI 后端项目
├── docs/             # 项目文档
├── docker-compose.yml # Docker 编排
└── README.md         # 项目说明
```

### 🎨 前端架构

#### 技术栈
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **构建**: Vite
- **UI库**: Element Plus
- **图表**: ECharts
- **状态**: Pinia
- **路由**: Vue Router
- **样式**: Tailwind CSS

#### 架构分层
```
┌─────────────────┐
│   Views (页面)   │  ← 页面组件
├─────────────────┤
│ Components(组件) │  ← 可复用组件
├─────────────────┤
│  Stores (状态)   │  ← 状态管理
├─────────────────┤
│   API (接口)     │  ← 接口调用
├─────────────────┤
│  Utils (工具)    │  ← 工具函数
└─────────────────┘
```

### 🚀 后端架构

#### 技术栈
- **框架**: FastAPI
- **语言**: Python 3.13
- **数据库**: SQLite/PostgreSQL
- **ORM**: SQLAlchemy
- **缓存**: Redis
- **任务队列**: Celery
- **数据处理**: Pandas + NumPy
- **金融数据**: Tushare
- **量化框架**: VNPy

#### 架构分层
```
┌─────────────────┐
│  API Routes     │  ← RESTful API + WebSocket
├─────────────────┤
│  Services       │  ← 业务逻辑层
├─────────────────┤
│  CRUD           │  ← 数据访问层
├─────────────────┤
│  Models         │  ← 数据模型层
├─────────────────┤
│  Database       │  ← 数据存储层
└─────────────────┘
```

## 💻 开发规范

### 📝 代码规范

#### 前端代码规范
```typescript
// 1. 文件命名：PascalCase for components, camelCase for others
// 组件文件
KLineChart.vue
OrderForm.vue

// 工具文件
formatUtils.ts
apiClient.ts

// 2. 变量命名：camelCase
const marketData = ref<MarketData[]>([])
const isLoading = ref(false)

// 3. 常量命名：UPPER_SNAKE_CASE
const API_BASE_URL = 'http://localhost:8000'
const DEFAULT_PAGE_SIZE = 20

// 4. 类型定义：PascalCase
interface MarketData {
  symbol: string
  price: number
  volume: number
}

// 5. 组件Props定义
interface Props {
  symbol: string
  showVolume?: boolean
}
```

#### 后端代码规范
```python
# 1. 文件命名：snake_case
market_data.py
trading_service.py

# 2. 类命名：PascalCase
class MarketDataService:
    pass

class TradingOrder:
    pass

# 3. 函数命名：snake_case
def get_market_data(symbol: str) -> MarketData:
    pass

async def create_order(order_data: OrderCreate) -> Order:
    pass

# 4. 常量命名：UPPER_SNAKE_CASE
API_VERSION = "v1"
DEFAULT_PAGE_SIZE = 20

# 5. 类型注解
from typing import List, Optional, Dict, Any

def process_data(
    data: List[Dict[str, Any]], 
    symbol: Optional[str] = None
) -> List[MarketData]:
    pass
```

### 🔄 Git 工作流

#### 分支策略
```bash
main          # 主分支，生产环境代码
├── develop   # 开发分支，集成测试
├── feature/* # 功能分支
├── hotfix/*  # 热修复分支
└── release/* # 发布分支
```

#### 提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建/工具相关

# 示例
feat(trading): add order management functionality
fix(market): resolve real-time data connection issue
docs(api): update authentication documentation
```

### 🧪 测试规范

#### 前端测试
```bash
# 单元测试
pnpm test

# 组件测试
pnpm test:component

# E2E测试
pnpm test:e2e

# 测试覆盖率
pnpm test:coverage
```

#### 后端测试
```bash
# 单元测试
pytest tests/unit/

# 集成测试
pytest tests/integration/

# API测试
pytest tests/api/

# 测试覆盖率
pytest --cov=app tests/
```

## 🔧 开发工具配置

### VS Code 配置

#### 推荐插件
```json
{
  "recommendations": [
    "vue.volar",
    "ms-python.python",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### 工作区设置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### 环境变量配置

#### 前端环境变量 (.env)
```bash
# 开发环境
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000
VITE_APP_TITLE=量化交易平台

# 生产环境
VITE_API_BASE_URL=https://api.yourapp.com
VITE_WS_BASE_URL=wss://api.yourapp.com
```

#### 后端环境变量 (.env)
```bash
# 数据库配置
DATABASE_URL=sqlite:///./test.db
# DATABASE_URL=postgresql://user:pass@localhost/dbname

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# Tushare配置
TUSHARE_TOKEN=your-tushare-token

# CTP配置
CTP_BROKER_ID=your-broker-id
CTP_USER_ID=your-user-id
CTP_PASSWORD=your-password
```

## 🚀 部署指南

### 🐳 Docker 部署

#### 构建镜像
```bash
# 构建前端镜像
cd frontend
docker build -t quant-frontend .

# 构建后端镜像
cd backend
docker build -t quant-backend .
```

#### Docker Compose 部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### ☸️ Kubernetes 部署

#### 部署到集群
```bash
# 应用配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods
kubectl get services

# 查看日志
kubectl logs -f deployment/quant-backend
```

## 🐛 调试指南

### 前端调试

#### 浏览器调试
1. 打开浏览器开发者工具 (F12)
2. 在 Sources 面板设置断点
3. 在 Console 面板查看日志
4. 在 Network 面板监控API请求

#### Vue DevTools
```bash
# 安装 Vue DevTools 浏览器插件
# Chrome: Vue.js devtools
# Firefox: Vue.js devtools
```

### 后端调试

#### Python 调试
```python
# 使用 pdb 调试
import pdb; pdb.set_trace()

# 使用 logging 记录日志
import logging
logging.info("Debug message")
```

#### API 调试
```bash
# 使用 curl 测试API
curl -X GET "http://localhost:8000/api/v1/market/stocks" \
     -H "Authorization: Bearer your-token"

# 使用 httpie 测试API
http GET localhost:8000/api/v1/market/stocks \
     Authorization:"Bearer your-token"
```

## 📚 学习资源

### 📖 官方文档
- [Vue 3 文档](https://vuejs.org/)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)

### 🎓 推荐教程
- [Vue 3 Composition API](https://vuejs.org/guide/extras/composition-api-faq.html)
- [FastAPI 教程](https://fastapi.tiangolo.com/tutorial/)
- [量化交易入门](https://www.vnpy.com/)
- [金融数据分析](https://tushare.pro/)

### 🛠️ 工具文档
- [Vite 配置](https://vitejs.dev/config/)
- [Element Plus 组件](https://element-plus.org/)
- [ECharts 图表](https://echarts.apache.org/)
- [Tailwind CSS](https://tailwindcss.com/)

## 🤝 贡献指南

### 📝 贡献流程
1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 🔍 代码审查
- 确保代码符合项目规范
- 添加必要的测试用例
- 更新相关文档
- 通过所有CI检查

### 📋 Issue 模板
```markdown
## 问题描述
简要描述遇到的问题

## 复现步骤
1. 步骤一
2. 步骤二
3. 步骤三

## 期望行为
描述期望的正确行为

## 实际行为
描述实际发生的行为

## 环境信息
- 操作系统: 
- 浏览器: 
- Node.js版本: 
- Python版本: 
```

这个开发指南提供了完整的项目开发流程，从环境搭建到部署上线的全方位指导。

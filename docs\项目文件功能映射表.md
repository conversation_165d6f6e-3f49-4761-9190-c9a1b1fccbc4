# 量化交易平台文件功能映射表

## 📋 文件功能快速索引

### 🎨 前端文件功能映射

#### 📊 页面视图 (Views)
| 文件路径 | 功能描述 | 主要特性 |
|----------|----------|----------|
| `/frontend/src/views/Dashboard.vue` | 仪表板主页 | 数据概览、快速导航、实时指标 |
| `/frontend/src/views/Market.vue` | 市场数据页面 | 实时行情、K线图、深度图 |
| `/frontend/src/views/Trading.vue` | 交易操作页面 | 下单、持仓管理、订单历史 |
| `/frontend/src/views/Strategy.vue` | 策略管理页面 | 策略开发、策略市场、策略监控 |
| `/frontend/src/views/Backtest.vue` | 回测分析页面 | 策略回测、绩效分析、报告生成 |
| `/frontend/src/views/Portfolio.vue` | 投资组合页面 | 组合管理、资产配置、绩效追踪 |
| `/frontend/src/views/Risk.vue` | 风险管理页面 | 风险监控、VaR计算、压力测试 |
| `/frontend/src/views/Login.vue` | 用户登录页面 | 用户认证、注册、密码重置 |

#### 🧩 核心组件 (Components)

##### 📈 图表组件
| 文件路径 | 功能描述 | 技术栈 |
|----------|----------|--------|
| `/frontend/src/components/charts/KLineChart/` | K线图组件 | ECharts + Vue3 |
| `/frontend/src/components/charts/DepthChart/` | 深度图组件 | ECharts + WebSocket |
| `/frontend/src/components/charts/AssetTrendChart.vue` | 资产趋势图 | ECharts + 时间序列 |
| `/frontend/src/components/charts/PositionPieChart.vue` | 持仓饼图 | ECharts + 数据可视化 |

##### 💼 交易组件
| 文件路径 | 功能描述 | 核心功能 |
|----------|----------|----------|
| `/frontend/src/components/trading/OrderForm/` | 下单表单组件 | 订单创建、参数验证、风险检查 |
| `/frontend/src/components/trading/OrderBook.vue` | 订单簿组件 | 实时订单展示、深度数据 |
| `/frontend/src/components/trading/PositionList.vue` | 持仓列表组件 | 持仓展示、盈亏计算 |
| `/frontend/src/components/trading/QuickOrderForm.vue` | 快速下单组件 | 一键下单、快速交易 |

##### 🏪 市场组件
| 文件路径 | 功能描述 | 数据源 |
|----------|----------|--------|
| `/frontend/src/components/market/StockCard.vue` | 股票卡片组件 | Tushare + 实时数据 |

##### 🎯 策略组件
| 文件路径 | 功能描述 | 特性 |
|----------|----------|------|
| `/frontend/src/components/strategy/StrategyCard/` | 策略卡片组件 | 策略信息展示、性能指标 |

##### 🔄 回测组件
| 文件路径 | 功能描述 | 算法支持 |
|----------|----------|----------|
| `/frontend/src/components/backtest/BacktestForm.vue` | 回测表单组件 | 参数配置、历史数据回测 |

##### 🔧 通用组件
| 文件路径 | 功能描述 | 复用性 |
|----------|----------|--------|
| `/frontend/src/components/common/AppButton/` | 按钮组件 | 高度可定制、主题支持 |
| `/frontend/src/components/common/AppCard/` | 卡片组件 | 统一样式、响应式布局 |
| `/frontend/src/components/common/AppModal/` | 模态框组件 | 弹窗管理、事件处理 |
| `/frontend/src/components/common/SliderCaptcha/` | 滑块验证码 | 安全验证、用户体验 |
| `/frontend/src/components/common/VirtualTable/` | 虚拟表格组件 | 大数据量、性能优化 |

##### 📊 小部件组件
| 文件路径 | 功能描述 | 应用场景 |
|----------|----------|----------|
| `/frontend/src/components/widgets/MetricCard.vue` | 指标卡片组件 | 数据展示、仪表板 |

#### 🔗 API接口层 (API)
| 文件路径 | 功能描述 | 对应后端 |
|----------|----------|----------|
| `/frontend/src/api/auth.ts` | 认证相关API | `/backend/app/api/v1/auth.py` |
| `/frontend/src/api/market.ts` | 市场数据API | `/backend/app/api/v1/market_data.py` |
| `/frontend/src/api/trading.ts` | 交易相关API | `/backend/app/api/v1/trading.py` |
| `/frontend/src/api/strategy.ts` | 策略管理API | `/backend/app/api/v1/strategy.py` |
| `/frontend/src/api/backtest.ts` | 回测相关API | `/backend/app/api/v1/backtest.py` |
| `/frontend/src/api/portfolio.ts` | 投资组合API | `/backend/app/api/v1/portfolio.py` |
| `/frontend/src/api/risk.ts` | 风险管理API | `/backend/app/api/v1/risk.py` |
| `/frontend/src/api/websocket.ts` | WebSocket连接 | `/backend/app/api/websocket/` |

#### 🗃️ 状态管理 (Stores)
| 文件路径 | 功能描述 | 管理范围 |
|----------|----------|----------|
| `/frontend/src/stores/modules/auth.ts` | 认证状态管理 | 用户信息、登录状态、权限 |
| `/frontend/src/stores/modules/market.ts` | 市场数据状态 | 实时行情、历史数据、订阅管理 |
| `/frontend/src/stores/modules/trading.ts` | 交易状态管理 | 订单状态、持仓信息、交易历史 |
| `/frontend/src/stores/modules/strategy.ts` | 策略状态管理 | 策略列表、执行状态、性能数据 |
| `/frontend/src/stores/modules/portfolio.ts` | 投资组合状态 | 组合数据、资产配置、绩效指标 |
| `/frontend/src/stores/modules/websocket.ts` | WebSocket状态 | 连接状态、消息队列、重连机制 |

#### 🎭 类型定义 (Types)
| 文件路径 | 功能描述 | 覆盖范围 |
|----------|----------|----------|
| `/frontend/src/types/api.ts` | API接口类型 | 请求/响应数据结构 |
| `/frontend/src/types/market.ts` | 市场数据类型 | 行情数据、K线数据、深度数据 |
| `/frontend/src/types/trading.ts` | 交易相关类型 | 订单、持仓、交易记录 |
| `/frontend/src/types/strategy.ts` | 策略相关类型 | 策略参数、回测结果、性能指标 |
| `/frontend/src/types/common.ts` | 通用类型定义 | 基础数据类型、工具类型 |

#### 🛠️ 工具函数 (Utils)
| 文件路径 | 功能描述 | 应用场景 |
|----------|----------|----------|
| `/frontend/src/utils/format.ts` | 格式化工具 | 数字格式化、日期格式化、货币格式化 |
| `/frontend/src/utils/validation.ts` | 验证工具 | 表单验证、数据校验、业务规则验证 |
| `/frontend/src/utils/calculation.ts` | 计算工具 | 金融计算、技术指标、统计函数 |
| `/frontend/src/utils/constants.ts` | 常量定义 | 配置常量、枚举值、默认参数 |

### 🚀 后端文件功能映射

#### 🌐 API路由层 (API Routes)

##### 📡 RESTful API (v1)
| 文件路径 | 功能描述 | 主要端点 |
|----------|----------|----------|
| `/backend/app/api/v1/auth.py` | 认证授权API | `/login`, `/register`, `/refresh`, `/logout` |
| `/backend/app/api/v1/market_data.py` | 市场数据API | `/stocks`, `/quotes`, `/klines`, `/depth` |
| `/backend/app/api/v1/trading.py` | 交易管理API | `/orders`, `/positions`, `/trades`, `/balance` |
| `/backend/app/api/v1/strategy.py` | 策略管理API | `/strategies`, `/execute`, `/stop`, `/performance` |
| `/backend/app/api/v1/backtest.py` | 回测分析API | `/backtest`, `/results`, `/reports`, `/compare` |
| `/backend/app/api/v1/portfolio.py` | 投资组合API | `/portfolios`, `/allocation`, `/rebalance`, `/performance` |
| `/backend/app/api/v1/risk.py` | 风险管理API | `/risk-metrics`, `/var`, `/stress-test`, `/alerts` |
| `/backend/app/api/v1/users.py` | 用户管理API | `/users`, `/profile`, `/settings`, `/preferences` |
| `/backend/app/api/v1/strategy_files.py` | 策略文件API | `/upload`, `/download`, `/list`, `/delete` |

##### 🔌 WebSocket API
| 文件路径 | 功能描述 | 推送内容 |
|----------|----------|----------|
| `/backend/app/api/websocket/market.py` | 市场数据推送 | 实时行情、K线更新、深度变化 |
| `/backend/app/api/websocket/trading.py` | 交易状态推送 | 订单状态、成交通知、持仓变化 |
| `/backend/app/api/websocket/notifications.py` | 系统通知推送 | 系统消息、风险警告、策略通知 |

#### 🏗️ 核心功能层 (Core)
| 文件路径 | 功能描述 | 核心职责 |
|----------|----------|----------|
| `/backend/app/core/config.py` | 配置管理 | 环境变量、应用配置、数据库配置 |
| `/backend/app/core/database.py` | 数据库连接 | 连接池管理、会话管理、事务处理 |
| `/backend/app/core/security.py` | 安全相关 | JWT处理、密码加密、权限验证 |
| `/backend/app/core/dependencies.py` | 依赖注入 | 服务依赖、数据库依赖、认证依赖 |
| `/backend/app/core/websocket.py` | WebSocket管理 | 连接管理、消息广播、订阅管理 |
| `/backend/app/core/monitoring.py` | 监控功能 | 健康检查、性能监控、指标收集 |
| `/backend/app/core/logging_config.py` | 日志配置 | 日志格式、日志级别、日志输出 |

#### 🗄️ 数据库层 (Database)

##### 📊 数据模型 (Models)
| 文件路径 | 功能描述 | 数据表 |
|----------|----------|--------|
| `/backend/app/db/models/user.py` | 用户数据模型 | users, user_profiles, user_settings |
| `/backend/app/db/models/market.py` | 市场数据模型 | stocks, market_data, quotes, klines |
| `/backend/app/db/models/trading.py` | 交易数据模型 | orders, positions, trades, accounts |
| `/backend/app/db/models/strategy.py` | 策略数据模型 | strategies, strategy_runs, parameters |
| `/backend/app/db/models/backtest.py` | 回测数据模型 | backtests, backtest_results, performance |
| `/backend/app/db/models/portfolio.py` | 投资组合模型 | portfolios, allocations, rebalances |

##### 🔄 CRUD操作 (CRUD)
| 文件路径 | 功能描述 | 操作对象 |
|----------|----------|----------|
| `/backend/app/db/crud/base.py` | 基础CRUD操作 | 通用增删改查、分页、排序 |
| `/backend/app/db/crud/user.py` | 用户CRUD操作 | 用户管理、权限管理、配置管理 |
| `/backend/app/db/crud/market.py` | 市场数据CRUD | 数据存储、查询优化、缓存管理 |
| `/backend/app/db/crud/trading.py` | 交易CRUD操作 | 订单管理、持仓管理、交易记录 |
| `/backend/app/db/crud/strategy.py` | 策略CRUD操作 | 策略管理、执行记录、性能统计 |

#### 🎯 业务服务层 (Services)
| 文件路径 | 功能描述 | 核心算法 |
|----------|----------|----------|
| `/backend/app/services/auth_service.py` | 认证服务 | JWT生成、用户验证、权限检查 |
| `/backend/app/services/market_service.py` | 市场数据服务 | 数据获取、实时推送、缓存策略 |
| `/backend/app/services/enhanced_market_service.py` | 增强市场服务 | 数据聚合、指标计算、智能推荐 |
| `/backend/app/services/trading_service.py` | 交易服务 | 订单执行、风险控制、资金管理 |
| `/backend/app/services/strategy_service.py` | 策略服务 | 策略执行、信号生成、性能分析 |
| `/backend/app/services/backtest_service.py` | 回测服务 | 历史回测、绩效计算、报告生成 |
| `/backend/app/services/portfolio_service.py` | 投资组合服务 | 组合优化、风险分散、再平衡 |
| `/backend/app/services/risk_service.py` | 风险管理服务 | VaR计算、压力测试、风险监控 |
| `/backend/app/services/tushare_service.py` | Tushare数据服务 | 数据接口、频率控制、错误处理 |
| `/backend/app/services/ctp_service.py` | CTP交易服务 | 期货交易、实时行情、交易接口 |
| `/backend/app/services/realtime_data_service.py` | 实时数据服务 | 数据流处理、实时计算、推送服务 |

#### 📋 数据模式层 (Schemas)
| 文件路径 | 功能描述 | 验证规则 |
|----------|----------|----------|
| `/backend/app/schemas/auth.py` | 认证相关模式 | 登录验证、注册验证、令牌验证 |
| `/backend/app/schemas/market.py` | 市场数据模式 | 行情数据验证、K线数据验证 |
| `/backend/app/schemas/trading.py` | 交易相关模式 | 订单验证、持仓验证、交易验证 |
| `/backend/app/schemas/strategy.py` | 策略相关模式 | 策略参数验证、执行结果验证 |
| `/backend/app/schemas/backtest.py` | 回测相关模式 | 回测参数验证、结果验证 |
| `/backend/app/schemas/portfolio.py` | 投资组合模式 | 组合验证、配置验证、绩效验证 |

#### 🛠️ 工具函数层 (Utils)
| 文件路径 | 功能描述 | 应用领域 |
|----------|----------|----------|
| `/backend/app/utils/calculations.py` | 计算工具 | 金融计算、技术指标、统计分析 |
| `/backend/app/utils/data_processing.py` | 数据处理 | 数据清洗、格式转换、聚合计算 |
| `/backend/app/utils/formatters.py` | 格式化工具 | 数据格式化、响应格式化 |
| `/backend/app/utils/validators.py` | 验证工具 | 数据验证、业务规则验证 |
| `/backend/app/utils/helpers.py` | 辅助函数 | 通用工具、便捷函数 |

#### ⚡ 异步任务层 (Tasks)
| 文件路径 | 功能描述 | 任务类型 |
|----------|----------|----------|
| `/backend/app/tasks/backtest_tasks.py` | 回测任务 | 异步回测、批量回测、定时回测 |
| `/backend/app/tasks/trading_tasks.py` | 交易任务 | 订单执行、策略执行、风险检查 |
| `/backend/app/tasks/data_tasks.py` | 数据处理任务 | 数据同步、数据清洗、指标计算 |

#### 📊 监控系统 (Monitoring)
| 文件路径 | 功能描述 | 监控指标 |
|----------|----------|----------|
| `/backend/app/monitoring/middleware.py` | 监控中间件 | 请求监控、性能监控、错误监控 |
| `/backend/app/monitoring/startup.py` | 启动检查 | 健康检查、就绪检查、存活检查 |
| `/backend/app/monitoring/ctp_alerts.py` | CTP告警 | 交易告警、连接告警、风险告警 |
| `/backend/app/monitoring/ctp_metrics.py` | CTP指标 | 交易指标、性能指标、连接指标 |
| `/backend/app/monitoring/risk_metrics.py` | 风险指标 | 风险度量、风险监控、风险报告 |

## 🔗 文件间依赖关系

### 📊 前端依赖关系
```
Views (页面) → Components (组件) → API (接口) → Types (类型)
     ↓              ↓              ↓
  Stores (状态) → Utils (工具) → Constants (常量)
```

### 🚀 后端依赖关系
```
API Routes (路由) → Services (服务) → CRUD (数据操作) → Models (模型)
     ↓                ↓              ↓
  Schemas (模式) → Utils (工具) → Core (核心)
```

## 🎯 关键功能实现路径

### 📈 实时行情功能
1. **数据获取**: `/backend/app/services/tushare_service.py`
2. **数据推送**: `/backend/app/api/websocket/market.py`
3. **前端接收**: `/frontend/src/api/websocket.ts`
4. **状态管理**: `/frontend/src/stores/modules/market.ts`
5. **图表展示**: `/frontend/src/components/charts/KLineChart/`

### 💼 交易下单功能
1. **下单表单**: `/frontend/src/components/trading/OrderForm/`
2. **API调用**: `/frontend/src/api/trading.ts`
3. **后端路由**: `/backend/app/api/v1/trading.py`
4. **交易服务**: `/backend/app/services/trading_service.py`
5. **数据存储**: `/backend/app/db/models/trading.py`

### 🎯 策略回测功能
1. **回测表单**: `/frontend/src/components/backtest/BacktestForm.vue`
2. **API调用**: `/frontend/src/api/backtest.ts`
3. **后端路由**: `/backend/app/api/v1/backtest.py`
4. **回测服务**: `/backend/app/services/backtest_service.py`
5. **异步任务**: `/backend/app/tasks/backtest_tasks.py`

这个映射表提供了项目中每个文件的具体功能和作用，帮助开发者快速定位和理解代码结构。

# 量化投资平台项目文件组织规范

**版本**: 1.0  
**制定日期**: 2025年7月30日  
**适用范围**: 整个项目团队  

## 📋 目录结构标准

### 🏗️ 根目录结构

```
quant-platf/
├── README.md                    # 项目主文档
├── .gitignore                   # Git忽略文件
├── start-platform.bat          # 启动脚本
├── 
├── backend/                     # 后端项目
├── frontend/                    # 前端项目
├── tests/                       # 测试文件
├── reports/                     # 项目报告
├── docs/                        # 项目文档
├── config/                      # 配置文件
├── scripts/                     # 工具脚本
├── data/                        # 数据目录
├── strategy/                    # 策略文件
├── logs/                        # 日志文件
└── archive/                     # 归档文件
```

## 📁 各目录详细规范

### Backend 目录 (`backend/`)

**允许的文件类型**:
- `.py` - Python源码文件
- `.txt` - 依赖文件 (requirements.txt)
- `.ini` - 配置文件 (alembic.ini, pytest.ini)
- `.md` - 项目说明文档
- `.db` - 数据库文件 (仅开发环境)

**禁止的文件类型**:
- ❌ `.js` - JavaScript文件
- ❌ `package*.json` - Node.js配置文件
- ❌ `Dockerfile*` - Docker文件 (应放在config/docker/)
- ❌ `test_*.py` - 测试文件 (应放在tests/)
- ❌ `*_report.*` - 报告文件 (应放在reports/)

**子目录规范**:
- `app/` - 应用源码
- `tests/` - 后端单元测试
- `docs/` - 后端技术文档
- `scripts/` - 后端工具脚本
- `logs/` - 后端日志文件
- `cache/` - 缓存文件
- `data/` - 后端数据文件

### Frontend 目录 (`frontend/`)

**允许的文件类型**:
- `.vue` - Vue组件文件
- `.ts/.js` - TypeScript/JavaScript文件
- `.json` - 配置文件 (package.json, tsconfig.json)
- `.html` - 入口HTML文件
- `.css/.scss` - 样式文件
- `.md` - 项目说明文档

**禁止的文件类型**:
- ❌ `.py` - Python文件
- ❌ `Dockerfile*` - Docker文件 (应放在config/docker/)
- ❌ `nginx*.conf` - Nginx配置 (应放在config/nginx/)
- ❌ `test*.html` - 测试HTML (应放在tests/e2e/)
- ❌ `*_report.*` - 报告文件 (应放在reports/)

**子目录规范**:
- `src/` - 前端源码
- `public/` - 静态资源
- `tests/` - 前端测试
- `dist/` - 构建产物
- `node_modules/` - 依赖包

### Tests 目录 (`tests/`)

**目录结构**:
```
tests/
├── e2e/                         # 端到端测试
├── integration/                 # 集成测试
├── performance/                 # 性能测试
├── screenshots/                 # 测试截图
├── reports/                     # 测试报告
└── puppeteer/                   # Puppeteer测试
    ├── scripts/                 # 测试脚本
    ├── reports/                 # 测试报告
    └── screenshots/             # 测试截图
```

**文件命名规范**:
- 测试脚本: `test_功能名称.js/py`
- 测试报告: `test-report-YYYY-MM-DD-HH-mm-ss.json`
- 截图文件: `screenshot-功能名称-时间戳.png`

### Reports 目录 (`reports/`)

**目录结构**:
```
reports/
├── analysis/                    # 分析报告
├── testing/                     # 测试报告
├── performance/                 # 性能报告
├── security/                    # 安全报告
└── integration/                 # 集成报告
```

**文件命名规范**:
- 使用日期前缀: `YYYY-MM-DD_报告名称.md`
- 使用描述性名称
- 避免使用特殊字符

### Config 目录 (`config/`)

**目录结构**:
```
config/
├── docker/                      # Docker配置
├── k8s/                         # Kubernetes配置
├── nginx/                       # Nginx配置
└── environment/                 # 环境配置
```

**文件管理原则**:
1. 敏感信息使用环境变量
2. 不同环境使用不同配置文件
3. 配置文件要有详细注释
4. 定期检查和更新配置

## 🚫 禁止行为

### 根目录禁止事项
- ❌ 不得在根目录放置测试文件
- ❌ 不得在根目录放置报告文件
- ❌ 不得在根目录放置配置文件
- ❌ 不得在根目录放置临时文件
- ❌ 不得在根目录创建node_modules

### 跨目录禁止事项
- ❌ 不得在Python项目中放置JavaScript文件
- ❌ 不得在JavaScript项目中放置Python文件
- ❌ 不得在源码目录中放置测试文件
- ❌ 不得在源码目录中放置报告文件

## ✅ 最佳实践

### 文件命名规范
1. **使用英文命名** - 避免中文文件名
2. **使用下划线分隔** - `user_service.py`
3. **使用描述性名称** - `market_data_service.py`
4. **添加日期前缀** - `2025-07-30_功能报告.md`

### 目录组织原则
1. **按功能分类** - 相同功能的文件放在一起
2. **按类型分类** - 相同类型的文件放在一起
3. **保持层次清晰** - 避免过深的目录嵌套
4. **定期清理** - 删除过时和重复文件

### 版本控制规范
1. **忽略构建产物** - dist/, build/, __pycache__/
2. **忽略依赖目录** - node_modules/, venv/
3. **忽略临时文件** - *.log, *.tmp, *.cache
4. **忽略敏感文件** - .env, *.key, *.pem

## 🔧 工具和自动化

### 文件检查脚本
```bash
# 检查文件位置是否正确
python scripts/check_file_organization.py

# 自动移动错位文件
python scripts/auto_organize_files.py
```

### Git Hooks
```bash
# 提交前检查文件组织
pre-commit: check-file-organization

# 推送前验证目录结构
pre-push: validate-directory-structure
```

## 📝 违规处理

### 检查频率
- **每日检查** - 自动化脚本检查
- **每周审查** - 人工审查目录结构
- **每月清理** - 清理过时和重复文件

### 违规处理流程
1. **自动检测** - 脚本自动检测违规文件
2. **发送通知** - 通知相关开发人员
3. **限期整改** - 给出整改时间限制
4. **强制清理** - 超时后自动移动文件

## 📞 联系方式

**文件组织负责人**: 项目架构师  
**技术支持**: 开发团队  
**问题反馈**: 项目管理群  

---

**重要提醒**: 
- 🔥 严格遵守文件组织规范
- 📋 定期检查和清理文件
- 🤝 团队协作维护项目结构
- 📚 持续改进和优化规范

**最后更新**: 2025年7月30日

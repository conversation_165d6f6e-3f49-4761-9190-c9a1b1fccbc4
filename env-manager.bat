@echo off
REM 量化投资平台 - 环境管理脚本
REM 使用标准化的 Docker Compose 配置

setlocal enabledelayedexpansion

REM 配置文件路径
set LOCAL_COMPOSE=docker/compose/local/docker-compose.yml
set STAGING_COMPOSE=docker/compose/staging/docker-compose.yml
set PRODUCTION_COMPOSE=docker/compose/production/docker-compose.yml

set LOCAL_ENV=docker/compose/local/.env
set STAGING_ENV=docker/compose/staging/.env.staging
set PRODUCTION_ENV=docker/compose/production/.env.prod

REM 颜色定义（Windows 10+ 支持）
set BLUE=[94m
set GREEN=[92m
set YELLOW=[93m
set RED=[91m
set NC=[0m

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="init" goto :init
if "%1"=="validate" goto :validate
if "%1"=="clean" goto :clean
if "%1"=="local-up" goto :local_up
if "%1"=="local-down" goto :local_down
if "%1"=="local-logs" goto :local_logs
if "%1"=="local-ps" goto :local_ps
if "%1"=="staging-up" goto :staging_up
if "%1"=="staging-down" goto :staging_down
if "%1"=="staging-logs" goto :staging_logs
if "%1"=="staging-ps" goto :staging_ps
if "%1"=="production-up" goto :production_up
if "%1"=="production-down" goto :production_down
if "%1"=="production-logs" goto :production_logs
if "%1"=="production-ps" goto :production_ps
if "%1"=="status" goto :status
if "%1"=="test" goto :test

echo %RED%错误: 未知命令 "%1"%NC%
goto :help

:help
echo %BLUE%量化投资平台 - 统一部署管理%NC%
echo %BLUE%================================%NC%
echo.
echo %GREEN%环境管理:%NC%
echo   %YELLOW%local-up%NC%             启动本地开发环境
echo   %YELLOW%local-down%NC%           停止本地开发环境
echo   %YELLOW%local-logs%NC%           查看本地环境日志
echo   %YELLOW%local-ps%NC%             查看本地环境状态
echo.
echo   %YELLOW%staging-up%NC%           启动测试环境
echo   %YELLOW%staging-down%NC%         停止测试环境
echo   %YELLOW%staging-logs%NC%         查看测试环境日志
echo   %YELLOW%staging-ps%NC%           查看测试环境状态
echo.
echo   %YELLOW%production-up%NC%        启动生产环境
echo   %YELLOW%production-down%NC%      停止生产环境
echo   %YELLOW%production-logs%NC%      查看生产环境日志
echo   %YELLOW%production-ps%NC%        查看生产环境状态
echo.
echo %GREEN%工具命令:%NC%
echo   %YELLOW%init%NC%                 初始化环境配置文件
echo   %YELLOW%validate%NC%             验证所有环境配置
echo   %YELLOW%clean%NC%                清理所有环境
echo   %YELLOW%status%NC%               显示所有环境状态
echo   %YELLOW%test%NC%                 运行测试
echo   %YELLOW%help%NC%                 显示帮助信息
echo.
echo %GREEN%使用示例:%NC%
echo   %YELLOW%env-manager.bat local-up%NC%      启动本地开发环境
echo   %YELLOW%env-manager.bat validate%NC%      验证配置文件
echo   %YELLOW%env-manager.bat status%NC%        查看所有环境状态
goto :end

:init
echo %BLUE%🔧 初始化环境配置...%NC%
if not exist "%LOCAL_ENV%" (
    copy "docker\compose\local\.env.local" "%LOCAL_ENV%" >nul
    echo %GREEN%✅ 创建本地环境配置: %LOCAL_ENV%%NC%
) else (
    echo %YELLOW%⚠️  本地环境配置已存在: %LOCAL_ENV%%NC%
)

if not exist "%STAGING_ENV%" (
    copy "docker\compose\staging\.env.staging.example" "%STAGING_ENV%" >nul
    echo %GREEN%✅ 创建测试环境配置: %STAGING_ENV%%NC%
    echo %YELLOW%⚠️  请编辑 %STAGING_ENV% 填入测试环境配置%NC%
) else (
    echo %YELLOW%⚠️  测试环境配置已存在: %STAGING_ENV%%NC%
)

if not exist "%PRODUCTION_ENV%" (
    copy "docker\compose\production\.env.prod.example" "%PRODUCTION_ENV%" >nul
    echo %GREEN%✅ 创建生产环境配置: %PRODUCTION_ENV%%NC%
    echo %RED%⚠️  请编辑 %PRODUCTION_ENV% 填入生产环境配置并更改默认密码%NC%
) else (
    echo %YELLOW%⚠️  生产环境配置已存在: %PRODUCTION_ENV%%NC%
)
goto :end

:validate
echo %BLUE%🔍 验证配置文件...%NC%
python scripts\validate-config.py
goto :end

:clean
echo %BLUE%🧹 清理所有环境...%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% down -v 2>nul
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% down -v 2>nul
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% down -v 2>nul
docker system prune -f
echo %GREEN%✅ 清理完成%NC%
goto :end

:local_up
echo %BLUE%🚀 启动本地开发环境...%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% up -d
if %errorlevel%==0 (
    echo %GREEN%✅ 本地环境启动成功%NC%
    echo %YELLOW%前端: http://localhost:5173%NC%
    echo %YELLOW%后端: http://localhost:8000%NC%
    echo %YELLOW%API文档: http://localhost:8000/docs%NC%
) else (
    echo %RED%❌ 本地环境启动失败%NC%
)
goto :end

:local_down
echo %BLUE%🛑 停止本地开发环境...%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% down
if %errorlevel%==0 (
    echo %GREEN%✅ 本地环境停止成功%NC%
) else (
    echo %RED%❌ 本地环境停止失败%NC%
)
goto :end

:local_logs
echo %BLUE%📋 查看本地环境日志...%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% logs -f
goto :end

:local_ps
echo %BLUE%📊 本地环境状态:%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% ps
goto :end

:staging_up
echo %BLUE%🚀 启动测试环境...%NC%
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% up -d
if %errorlevel%==0 (
    echo %GREEN%✅ 测试环境启动成功%NC%
) else (
    echo %RED%❌ 测试环境启动失败%NC%
)
goto :end

:staging_down
echo %BLUE%🛑 停止测试环境...%NC%
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% down
if %errorlevel%==0 (
    echo %GREEN%✅ 测试环境停止成功%NC%
) else (
    echo %RED%❌ 测试环境停止失败%NC%
)
goto :end

:staging_logs
echo %BLUE%📋 查看测试环境日志...%NC%
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% logs -f
goto :end

:staging_ps
echo %BLUE%📊 测试环境状态:%NC%
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% ps
goto :end

:production_up
echo %BLUE%🚀 启动生产环境...%NC%
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% up -d
if %errorlevel%==0 (
    echo %GREEN%✅ 生产环境启动成功%NC%
) else (
    echo %RED%❌ 生产环境启动失败%NC%
)
goto :end

:production_down
echo %BLUE%🛑 停止生产环境...%NC%
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% down
if %errorlevel%==0 (
    echo %GREEN%✅ 生产环境停止成功%NC%
) else (
    echo %RED%❌ 生产环境停止失败%NC%
)
goto :end

:production_logs
echo %BLUE%📋 查看生产环境日志...%NC%
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% logs -f
goto :end

:production_ps
echo %BLUE%📊 生产环境状态:%NC%
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% ps
goto :end

:status
echo %BLUE%📊 环境状态概览:%NC%
echo.
echo %YELLOW%本地开发环境:%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% ps 2>nul || echo   未运行
echo.
echo %YELLOW%测试环境:%NC%
docker compose -f %STAGING_COMPOSE% --env-file %STAGING_ENV% ps 2>nul || echo   未运行
echo.
echo %YELLOW%生产环境:%NC%
docker compose -f %PRODUCTION_COMPOSE% --env-file %PRODUCTION_ENV% ps 2>nul || echo   未运行
goto :end

:test
echo %BLUE%🧪 运行测试...%NC%
docker compose -f %LOCAL_COMPOSE% --env-file %LOCAL_ENV% exec backend python -m pytest
if %errorlevel%==0 (
    echo %GREEN%✅ 测试完成%NC%
) else (
    echo %RED%❌ 测试失败%NC%
)
goto :end

:end
endlocal

# 量化投资平台 - 环境管理脚本
# 使用标准化的 Docker Compose 配置

param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [Parameter(Position=1)]
    [string]$Service = ""
)

# 配置文件路径
$LOCAL_COMPOSE = "docker/compose/local/docker-compose.yml"
$STAGING_COMPOSE = "docker/compose/staging/docker-compose.yml"
$PRODUCTION_COMPOSE = "docker/compose/production/docker-compose.yml"

$LOCAL_ENV = "docker/compose/local/.env"
$STAGING_ENV = "docker/compose/staging/.env.staging"
$PRODUCTION_ENV = "docker/compose/production/.env.prod"

# 颜色函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Blue" = "Cyan"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Red" = "Red"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Show-Help {
    Write-ColorOutput "量化投资平台 - 统一部署管理" "Blue"
    Write-ColorOutput "================================" "Blue"
    Write-Host ""
    Write-ColorOutput "环境管理:" "Green"
    Write-ColorOutput "  local-up             启动本地开发环境" "Yellow"
    Write-ColorOutput "  local-down           停止本地开发环境" "Yellow"
    Write-ColorOutput "  local-logs           查看本地环境日志" "Yellow"
    Write-ColorOutput "  local-ps             查看本地环境状态" "Yellow"
    Write-Host ""
    Write-ColorOutput "  staging-up           启动测试环境" "Yellow"
    Write-ColorOutput "  staging-down         停止测试环境" "Yellow"
    Write-ColorOutput "  staging-logs         查看测试环境日志" "Yellow"
    Write-ColorOutput "  staging-ps           查看测试环境状态" "Yellow"
    Write-Host ""
    Write-ColorOutput "  production-up        启动生产环境" "Yellow"
    Write-ColorOutput "  production-down      停止生产环境" "Yellow"
    Write-ColorOutput "  production-logs      查看生产环境日志" "Yellow"
    Write-ColorOutput "  production-ps        查看生产环境状态" "Yellow"
    Write-Host ""
    Write-ColorOutput "工具命令:" "Green"
    Write-ColorOutput "  init                 初始化环境配置文件" "Yellow"
    Write-ColorOutput "  validate             验证所有环境配置" "Yellow"
    Write-ColorOutput "  clean                清理所有环境" "Yellow"
    Write-ColorOutput "  status               显示所有环境状态" "Yellow"
    Write-ColorOutput "  test                 运行测试" "Yellow"
    Write-ColorOutput "  help                 显示帮助信息" "Yellow"
    Write-Host ""
    Write-ColorOutput "使用示例:" "Green"
    Write-ColorOutput "  .\env-manager.ps1 local-up      启动本地开发环境" "Yellow"
    Write-ColorOutput "  .\env-manager.ps1 validate      验证配置文件" "Yellow"
    Write-ColorOutput "  .\env-manager.ps1 status        查看所有环境状态" "Yellow"
}

function Initialize-Environment {
    Write-ColorOutput "🔧 初始化环境配置..." "Blue"
    
    if (-not (Test-Path $LOCAL_ENV)) {
        Copy-Item "docker/compose/local/.env.local" $LOCAL_ENV
        Write-ColorOutput "✅ 创建本地环境配置: $LOCAL_ENV" "Green"
    } else {
        Write-ColorOutput "⚠️  本地环境配置已存在: $LOCAL_ENV" "Yellow"
    }
    
    if (-not (Test-Path $STAGING_ENV)) {
        Copy-Item "docker/compose/staging/.env.staging.example" $STAGING_ENV
        Write-ColorOutput "✅ 创建测试环境配置: $STAGING_ENV" "Green"
        Write-ColorOutput "⚠️  请编辑 $STAGING_ENV 填入测试环境配置" "Yellow"
    } else {
        Write-ColorOutput "⚠️  测试环境配置已存在: $STAGING_ENV" "Yellow"
    }
    
    if (-not (Test-Path $PRODUCTION_ENV)) {
        Copy-Item "docker/compose/production/.env.prod.example" $PRODUCTION_ENV
        Write-ColorOutput "✅ 创建生产环境配置: $PRODUCTION_ENV" "Green"
        Write-ColorOutput "⚠️  请编辑 $PRODUCTION_ENV 填入生产环境配置并更改默认密码" "Red"
    } else {
        Write-ColorOutput "⚠️  生产环境配置已存在: $PRODUCTION_ENV" "Yellow"
    }
}

function Validate-Configuration {
    Write-ColorOutput "🔍 验证配置文件..." "Blue"
    python scripts/validate-config.py
}

function Clean-Environment {
    Write-ColorOutput "🧹 清理所有环境..." "Blue"
    
    try {
        docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV down -v 2>$null
        docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV down -v 2>$null
        docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV down -v 2>$null
        docker system prune -f
        Write-ColorOutput "✅ 清理完成" "Green"
    } catch {
        Write-ColorOutput "❌ 清理过程中出现错误: $_" "Red"
    }
}

function Start-LocalEnvironment {
    Write-ColorOutput "🚀 启动本地开发环境..." "Blue"
    
    $result = docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV up -d
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 本地环境启动成功" "Green"
        Write-ColorOutput "前端: http://localhost:5173" "Yellow"
        Write-ColorOutput "后端: http://localhost:8000" "Yellow"
        Write-ColorOutput "API文档: http://localhost:8000/docs" "Yellow"
    } else {
        Write-ColorOutput "❌ 本地环境启动失败" "Red"
    }
}

function Stop-LocalEnvironment {
    Write-ColorOutput "🛑 停止本地开发环境..." "Blue"
    
    $result = docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV down
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 本地环境停止成功" "Green"
    } else {
        Write-ColorOutput "❌ 本地环境停止失败" "Red"
    }
}

function Show-LocalLogs {
    Write-ColorOutput "📋 查看本地环境日志..." "Blue"
    docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV logs -f
}

function Show-LocalStatus {
    Write-ColorOutput "📊 本地环境状态:" "Blue"
    docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV ps
}

function Start-StagingEnvironment {
    Write-ColorOutput "🚀 启动测试环境..." "Blue"
    
    $result = docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV up -d
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 测试环境启动成功" "Green"
    } else {
        Write-ColorOutput "❌ 测试环境启动失败" "Red"
    }
}

function Stop-StagingEnvironment {
    Write-ColorOutput "🛑 停止测试环境..." "Blue"
    
    $result = docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV down
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 测试环境停止成功" "Green"
    } else {
        Write-ColorOutput "❌ 测试环境停止失败" "Red"
    }
}

function Show-StagingLogs {
    Write-ColorOutput "📋 查看测试环境日志..." "Blue"
    docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV logs -f
}

function Show-StagingStatus {
    Write-ColorOutput "📊 测试环境状态:" "Blue"
    docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV ps
}

function Start-ProductionEnvironment {
    Write-ColorOutput "🚀 启动生产环境..." "Blue"
    
    $result = docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV up -d
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 生产环境启动成功" "Green"
    } else {
        Write-ColorOutput "❌ 生产环境启动失败" "Red"
    }
}

function Stop-ProductionEnvironment {
    Write-ColorOutput "🛑 停止生产环境..." "Blue"
    
    $result = docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV down
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 生产环境停止成功" "Green"
    } else {
        Write-ColorOutput "❌ 生产环境停止失败" "Red"
    }
}

function Show-ProductionLogs {
    Write-ColorOutput "📋 查看生产环境日志..." "Blue"
    docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV logs -f
}

function Show-ProductionStatus {
    Write-ColorOutput "📊 生产环境状态:" "Blue"
    docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV ps
}

function Show-AllStatus {
    Write-ColorOutput "📊 环境状态概览:" "Blue"
    Write-Host ""
    
    Write-ColorOutput "本地开发环境:" "Yellow"
    try {
        docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV ps 2>$null
    } catch {
        Write-Host "  未运行"
    }
    
    Write-Host ""
    Write-ColorOutput "测试环境:" "Yellow"
    try {
        docker compose -f $STAGING_COMPOSE --env-file $STAGING_ENV ps 2>$null
    } catch {
        Write-Host "  未运行"
    }
    
    Write-Host ""
    Write-ColorOutput "生产环境:" "Yellow"
    try {
        docker compose -f $PRODUCTION_COMPOSE --env-file $PRODUCTION_ENV ps 2>$null
    } catch {
        Write-Host "  未运行"
    }
}

function Run-Tests {
    Write-ColorOutput "🧪 运行测试..." "Blue"
    
    $result = docker compose -f $LOCAL_COMPOSE --env-file $LOCAL_ENV exec backend python -m pytest
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ 测试完成" "Green"
    } else {
        Write-ColorOutput "❌ 测试失败" "Red"
    }
}

# 主逻辑
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "init" { Initialize-Environment }
    "validate" { Validate-Configuration }
    "clean" { Clean-Environment }
    "local-up" { Start-LocalEnvironment }
    "local-down" { Stop-LocalEnvironment }
    "local-logs" { Show-LocalLogs }
    "local-ps" { Show-LocalStatus }
    "staging-up" { Start-StagingEnvironment }
    "staging-down" { Stop-StagingEnvironment }
    "staging-logs" { Show-StagingLogs }
    "staging-ps" { Show-StagingStatus }
    "production-up" { Start-ProductionEnvironment }
    "production-down" { Stop-ProductionEnvironment }
    "production-logs" { Show-ProductionLogs }
    "production-ps" { Show-ProductionStatus }
    "status" { Show-AllStatus }
    "test" { Run-Tests }
    default {
        Write-ColorOutput "错误: 未知命令 '$Command'" "Red"
        Show-Help
    }
}

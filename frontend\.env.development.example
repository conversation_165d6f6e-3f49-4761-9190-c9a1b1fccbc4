# 前端开发环境配置文件示例
# 复制此文件为 .env.development 并修改相应的值

# ================================
# 应用基本信息
# ================================
VITE_APP_TITLE="量化投资平台"
VITE_APP_VERSION="1.0.0"
VITE_APP_ENV="development"

# ================================
# API接口配置
# ================================
# 后端API基础地址
VITE_API_BASE_URL="http://localhost:8000"
VITE_API_PREFIX="/api/v1"

# WebSocket地址
VITE_WS_BASE_URL="ws://localhost:8000"
VITE_WS_PATH="/ws"

# ================================
# 应用功能配置
# ================================
# 是否启用模拟数据
VITE_USE_MOCK_DATA=true

# 是否启用开发工具
VITE_ENABLE_DEV_TOOLS=true

# 是否显示调试信息
VITE_SHOW_DEBUG_INFO=true

# ================================
# 图表配置
# ================================
# ECharts主题
VITE_ECHARTS_THEME="light"

# 图表更新间隔（毫秒）
VITE_CHART_UPDATE_INTERVAL=1000

# K线图默认显示条数
VITE_KLINE_DEFAULT_COUNT=100

# ================================
# 交易配置
# ================================
# 默认交易品种
VITE_DEFAULT_SYMBOL="000001.SZ"

# 是否启用模拟交易
VITE_ENABLE_PAPER_TRADING=true

# 是否启用实盘交易（开发环境建议false）
VITE_ENABLE_LIVE_TRADING=false

# 默认手续费率
VITE_DEFAULT_COMMISSION_RATE=0.0003

# ================================
# WebSocket配置
# ================================
# 心跳间隔（秒）
VITE_WS_HEARTBEAT_INTERVAL=30

# 重连间隔（毫秒）
VITE_WS_RECONNECT_INTERVAL=5000

# 最大重连次数
VITE_WS_MAX_RECONNECT_ATTEMPTS=5

# ================================
# 缓存配置
# ================================
# 本地存储前缀
VITE_STORAGE_PREFIX="quant_"

# Token存储key
VITE_TOKEN_KEY="access_token"

# 用户信息缓存时间（分钟）
VITE_USER_CACHE_EXPIRE=30

# ================================
# UI配置
# ================================
# 默认主题
VITE_DEFAULT_THEME="light"

# 默认语言
VITE_DEFAULT_LOCALE="zh-CN"

# 侧边栏默认状态
VITE_SIDEBAR_COLLAPSED=false

# ================================
# 性能配置
# ================================
# 虚拟滚动启用阈值
VITE_VIRTUAL_SCROLL_THRESHOLD=1000

# 图片懒加载
VITE_ENABLE_LAZY_LOADING=true

# 组件懒加载
VITE_ENABLE_LAZY_COMPONENTS=true

# ================================
# 错误处理配置
# ================================
# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=false

# Sentry DSN（如果使用Sentry）
# VITE_SENTRY_DSN="your_sentry_dsn_here"

# ================================
# 第三方服务配置
# ================================
# 百度地图API Key（如果需要）
# VITE_BAIDU_MAP_KEY="your_baidu_map_key"

# 高德地图API Key（如果需要）
# VITE_AMAP_KEY="your_amap_key"

# ================================
# 构建配置
# ================================
# 是否生成source map
VITE_GENERATE_SOURCEMAP=true

# 是否启用Bundle分析
VITE_ANALYZE_BUNDLE=false

# 是否启用PWA
VITE_ENABLE_PWA=false

# ================================
# 安全配置
# ================================
# CSP策略（Content Security Policy）
VITE_CSP_ENABLE=false

# 是否启用HTTPS（开发环境通常false）
VITE_ENABLE_HTTPS=false

# ================================
# 开发服务器配置
# ================================
# 开发服务器端口
VITE_DEV_PORT=5173

# 是否自动打开浏览器
VITE_OPEN_BROWSER=true

# HMR端口
VITE_HMR_PORT=24678

# ================================
# 代理配置
# ================================
# 是否启用API代理
VITE_ENABLE_PROXY=false

# 代理目标地址
VITE_PROXY_TARGET="http://localhost:8000"

# ================================
# 测试配置
# ================================
# 是否为测试环境
VITE_IS_TESTING=false

# 测试API基础地址
VITE_TEST_API_BASE_URL="http://localhost:8001"

# ================================
# 特性开关
# ================================
# 是否启用暗黑模式
VITE_ENABLE_DARK_MODE=true

# 是否启用国际化
VITE_ENABLE_I18N=true

# 是否启用权限控制
VITE_ENABLE_PERMISSION=true

# 是否启用水印
VITE_ENABLE_WATERMARK=false

# ================================
# 注意事项
# ================================
# 1. 所有环境变量必须以 VITE_ 开头才能在前端代码中访问
# 2. 不要在前端环境变量中存储敏感信息（如密码、私钥等）
# 3. boolean值请使用字符串 "true" 或 "false"
# 4. 数字值也请使用字符串格式
# 5. 修改环境变量后需要重启开发服务器
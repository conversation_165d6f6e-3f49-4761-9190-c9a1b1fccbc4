# 量化投资平台前端 Docker 配置
# 多阶段构建，支持开发和生产环境
# 构建命令：docker build -f frontend/Dockerfile frontend/

ARG BUILD_ENV=development
ARG NODE_VERSION=18

# ====================
# 阶段1: 基础环境
# ====================
FROM node:${NODE_VERSION}-alpine AS base

WORKDIR /app

# 安装 pnpm 和基础工具
RUN npm install -g pnpm && \
    apk add --no-cache wget

# ====================
# 阶段2: 依赖安装 (利用缓存)
# ====================
FROM base AS deps

# 复制包管理文件 (上下文为 frontend/ 目录)
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# ====================
# 阶段3: 开发环境
# ====================
FROM base AS development

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 设置开发环境变量
ENV NODE_ENV=development \
    VITE_API_BASE_URL=http://backend:8000/api/v1 \
    VITE_WS_URL=ws://backend:8000/ws

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5173/ || exit 1

# 暴露端口
EXPOSE 5173

# 启动开发服务器
CMD ["pnpm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# ====================
# 阶段4: 构建阶段
# ====================
FROM base AS builder

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 设置构建环境变量
ARG VITE_API_BASE_URL=/api/v1
ARG VITE_WS_URL=/ws
ENV NODE_ENV=production \
    VITE_API_BASE_URL=${VITE_API_BASE_URL} \
    VITE_WS_URL=${VITE_WS_URL}

# 构建应用
RUN pnpm build

# ====================
# 阶段5: 生产环境
# ====================
FROM nginx:1.25-alpine AS production

# 创建非 root 用户
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置 (如果存在)
COPY nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || \
    echo 'server { \
        listen 80; \
        server_name _; \
        root /usr/share/nginx/html; \
        index index.html; \
        location / { \
            try_files $uri $uri/ /index.html; \
        } \
        location /api/ { \
            proxy_pass http://backend:8000/api/; \
            proxy_set_header Host $host; \
            proxy_set_header X-Real-IP $remote_addr; \
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; \
            proxy_set_header X-Forwarded-Proto $scheme; \
        } \
        location /ws { \
            proxy_pass http://backend:8000/ws; \
            proxy_http_version 1.1; \
            proxy_set_header Upgrade $http_upgrade; \
            proxy_set_header Connection "upgrade"; \
            proxy_set_header Host $host; \
        } \
    }' > /etc/nginx/conf.d/default.conf

# 设置权限
RUN chown -R appuser:appgroup /usr/share/nginx/html && \
    chown -R appuser:appgroup /var/cache/nginx && \
    chown -R appuser:appgroup /var/log/nginx && \
    chown -R appuser:appgroup /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R appuser:appgroup /var/run/nginx.pid

# 切换到非 root 用户
USER appuser

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]

# ====================
# 最终阶段选择
# ====================
FROM ${BUILD_ENV} AS final
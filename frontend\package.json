{"name": "quant-platform", "version": "1.0.0", "private": true, "type": "module", "description": "量化投资前端可视化平台", "keywords": ["quant", "trading", "vue", "typescript", "finance"], "author": "Quant Platform Team", "license": "MIT", "homepage": "https://github.com/quant-platform/quant-frontend#readme", "repository": {"type": "git", "url": "git+https://github.com/quant-platform/quant-frontend.git"}, "bugs": {"url": "https://github.com/quant-platform/quant-frontend/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite --mode development", "build": "vue-tsc && vite build", "build:dev": "vue-tsc && vite build --mode development", "build:staging": "vue-tsc && vite build --mode staging", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint .", "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "format": "prettier --write .", "format:check": "prettier --check src/", "analyze": "node scripts/bundle-analyzer.js", "analyze:visual": "npm run build && npm run analyze", "bundle:stats": "npm run build && npx vite-bundle-analyzer", "bundle:size": "npm run build && npm run analyze", "clean": "npm run clean:dist && npm run clean:cache", "clean:dist": "<PERSON><PERSON><PERSON> dist", "clean:cache": "rimraf node_modules/.cache .vite coverage .nyc_output", "clean:all": "npm run clean && rimraf node_modules", "postinstall": "echo 'Skipping type check for now'", "dev:host": "vite --host", "preview:host": "vite preview --host", "prepare": "husky install", "commit": "git-cz", "release": "standard-version", "setup": "./scripts/dev-setup.sh", "check": "node scripts/check-status.cjs", "dev:mock": "VITE_USE_MOCK=true npm run dev", "serve": "npm run preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@sentry/vue": "^7.100.0", "@vueuse/core": "^11.2.0", "axios": "^1.9.0", "big.js": "^7.0.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "dompurify": "^3.0.0", "echarts": "^5.6.0", "element-plus": "^2.10.1", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "socket.io-client": "^4.7.2", "vue": "^3.4.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.8.0", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^18.4.0", "@commitlint/config-conventional": "^18.4.0", "@playwright/test": "^1.40.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node22": "^22.0.1", "@types/big.js": "^6.2.2", "@types/dompurify": "^3.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.31", "@types/nprogress": "^0.2.0", "@types/numeral": "^2.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "chalk": "^4.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.22.0", "eslint-define-config": "^2.1.0", "eslint-plugin-vue": "~10.0.0", "husky": "^8.0.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^15.2.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.4", "prettier": "3.5.3", "rimraf": "^3.0.2", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "standard-version": "^9.5.0", "stylelint": "^15.11.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.4", "tailwindcss": "^3.4.3", "terser": "^5.43.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-compression2": "^1.2.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^1.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.2.4", "vue-tsc": "^2.2.8"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@algolia/client-search"]}}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,vue}": ["stylelint --fix"], "*.{md,json,yml,yaml}": ["prettier --write"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
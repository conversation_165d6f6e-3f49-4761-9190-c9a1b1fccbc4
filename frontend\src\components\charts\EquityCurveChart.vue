<template>
  <div class="equity-curve-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-button-group>
          <el-button 
            v-for="period in periods" 
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            size="small"
            @click="changePeriod(period.value)"
          >
            {{ period.label }}
          </el-button>
        </el-button-group>
        
        <el-switch
          v-model="showBenchmark"
          active-text="显示基准"
          inactive-text="隐藏基准"
          style="margin-left: 10px"
        />
        
        <el-button 
          size="small" 
          :icon="fullscreen ? 'FullScreen' : 'FullScreen'"
          @click="toggleFullscreen"
        />
      </div>
    </div>
    
    <div ref="chartContainer" class="chart-container" :class="{ fullscreen }">
      <v-chart 
        :option="chartOption" 
        :loading="loading"
        :loading-options="loadingOptions"
        @click="onChartClick"
        autoresize
      />
    </div>
    
    <!-- 统计信息面板 -->
    <div class="stats-panel" v-if="statistics">
      <div class="stat-item">
        <span class="stat-label">总收益率</span>
        <span class="stat-value" :class="getReturnClass(statistics.totalReturn)">
          {{ formatPercent(statistics.totalReturn) }}
        </span>
      </div>
      <div class="stat-item">
        <span class="stat-label">年化收益率</span>
        <span class="stat-value" :class="getReturnClass(statistics.annualReturn)">
          {{ formatPercent(statistics.annualReturn) }}
        </span>
      </div>
      <div class="stat-item">
        <span class="stat-label">夏普比率</span>
        <span class="stat-value" :class="getSharpeClass(statistics.sharpeRatio)">
          {{ statistics.sharpeRatio?.toFixed(2) || 'N/A' }}
        </span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最大回撤</span>
        <span class="stat-value negative">
          {{ formatPercent(statistics.maxDrawdown) }}
        </span>
      </div>
      <div class="stat-item">
        <span class="stat-label">波动率</span>
        <span class="stat-value">
          {{ formatPercent(statistics.volatility) }}
        </span>
      </div>
      <div class="stat-item">
        <span class="stat-label">胜率</span>
        <span class="stat-value" :class="getWinRateClass(statistics.winRate)">
          {{ formatPercent(statistics.winRate) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import VChart from 'vue-echarts'
import { formatPercent, formatCurrency } from '@/utils/format'

// ECharts 组件已在全局 plugins/echarts.ts 中注册，无需重复注册

interface EquityPoint {
  date: string
  value: number
  drawdown?: number
  trades?: number
}

interface Statistics {
  totalReturn: number
  annualReturn: number
  sharpeRatio: number
  maxDrawdown: number
  volatility: number
  winRate: number
  totalTrades: number
  profitFactor: number
}

interface Props {
  title?: string
  data: EquityPoint[]
  benchmarkData?: EquityPoint[]
  statistics?: Statistics
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '收益曲线',
  benchmarkData: () => [],
  loading: false
})

const emit = defineEmits<{
  periodChange: [period: string]
  pointClick: [point: EquityPoint]
}>()

const chartContainer = ref<HTMLElement>()
const selectedPeriod = ref('all')
const showBenchmark = ref(true)
const fullscreen = ref(false)

const periods = [
  { label: '1个月', value: '1M' },
  { label: '3个月', value: '3M' },
  { label: '6个月', value: '6M' },
  { label: '1年', value: '1Y' },
  { label: '全部', value: 'all' }
]

const loadingOptions = {
  text: '加载中...',
  color: '#409EFF',
  textColor: '#409EFF',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0
}

// 计算图表数据
const chartData = computed(() => {
  let data = props.data
  
  // 根据选择的周期过滤数据
  if (selectedPeriod.value !== 'all') {
    const now = new Date()
    const monthsBack = {
      '1M': 1,
      '3M': 3,
      '6M': 6,
      '1Y': 12
    }[selectedPeriod.value] || 0
    
    const cutoffDate = new Date(now.getFullYear(), now.getMonth() - monthsBack, now.getDate())
    data = data.filter(point => new Date(point.date) >= cutoffDate)
  }
  
  return data
})

const benchmarkChartData = computed(() => {
  if (!showBenchmark.value || !props.benchmarkData.length) return []
  
  let data = props.benchmarkData
  
  // 应用相同的时间过滤
  if (selectedPeriod.value !== 'all') {
    const now = new Date()
    const monthsBack = {
      '1M': 1,
      '3M': 3,
      '6M': 6,
      '1Y': 12
    }[selectedPeriod.value] || 0
    
    const cutoffDate = new Date(now.getFullYear(), now.getMonth() - monthsBack, now.getDate())
    data = data.filter(point => new Date(point.date) >= cutoffDate)
  }
  
  return data
})

// 图表配置
const chartOption = computed(() => {
  const xAxisData = chartData.value.map(point => point.date)
  const seriesData = chartData.value.map(point => point.value)
  const drawdownData = chartData.value.map(point => point.drawdown || 0)
  
  const series: any[] = [
    {
      name: '策略收益',
      type: 'line',
      data: seriesData,
      smooth: true,
      lineStyle: {
        width: 2,
        color: '#409EFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      },
      markPoint: {
        data: [
          { type: 'max', name: '最高点' },
          { type: 'min', name: '最低点' }
        ]
      }
    }
  ]

  // 添加基准线
  if (showBenchmark.value && benchmarkChartData.value.length > 0) {
    series.push({
      name: '基准收益',
      type: 'line',
      data: benchmarkChartData.value.map(point => point.value),
      smooth: true,
      lineStyle: {
        width: 1.5,
        color: '#E6A23C',
        type: 'dashed'
      }
    })
  }

  // 添加回撤数据
  if (drawdownData.some(d => d !== 0)) {
    series.push({
      name: '回撤',
      type: 'line',
      yAxisIndex: 1,
      data: drawdownData.map(d => d * 100), // 转换为百分比
      lineStyle: {
        width: 1,
        color: '#F56C6C'
      },
      areaStyle: {
        color: 'rgba(245, 108, 108, 0.2)'
      }
    })
  }

  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any[]) => {
        const date = params[0].axisValueLabel
        let content = `<div style="margin-bottom: 5px; font-weight: bold;">${date}</div>`
        
        params.forEach(param => {
          const color = param.color
          const name = param.seriesName
          let value = param.value
          
          if (name === '回撤') {
            value = `${value.toFixed(2)}%`
          } else {
            value = formatCurrency(value)
          }
          
          content += `
            <div style="margin: 2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${color}"></span>
              ${name}: <span style="font-weight: bold; color: ${color};">${value}</span>
            </div>
          `
        })
        
        return content
      }
    },
    legend: {
      data: series.map(s => s.name),
      top: 10,
      right: 10
    },
    grid: [
      {
        left: '3%',
        right: '4%',
        top: '15%',
        height: drawdownData.some(d => d !== 0) ? '60%' : '75%'
      },
      {
        left: '3%',
        right: '4%',
        top: '80%',
        height: '15%'
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow'
        }
      },
      {
        type: 'category',
        gridIndex: 1,
        data: xAxisData,
        axisLabel: { show: false },
        axisTick: { show: false },
        axisLine: { show: false },
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '收益金额',
        axisLabel: {
          formatter: (value: number) => formatCurrency(value, 0)
        }
      },
      {
        type: 'value',
        name: '回撤 (%)',
        gridIndex: 1,
        inverse: true,
        axisLabel: {
          formatter: '{value}%'
        },
        splitLine: { show: false }
      }
    ],
    series,
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1]
      },
      {
        type: 'slider',
        xAxisIndex: [0, 1],
        bottom: '2%',
        height: 20
      }
    ],
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '缩放',
            back: '还原'
          }
        }
      }
    }
  }
})

// 方法
const changePeriod = (period: string) => {
  selectedPeriod.value = period
  emit('periodChange', period)
}

const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value
  nextTick(() => {
    // 触发图表重新渲染
    window.dispatchEvent(new Event('resize'))
  })
}

const onChartClick = (params: any) => {
  if (params.componentType === 'series' && params.seriesName === '策略收益') {
    const point = chartData.value[params.dataIndex]
    emit('pointClick', point)
  }
}

// 样式类计算
const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

const getSharpeClass = (value: number) => {
  if (value >= 2) return 'excellent'
  if (value >= 1) return 'good'
  if (value >= 0) return 'neutral'
  return 'poor'
}

const getWinRateClass = (value: number) => {
  if (value >= 0.6) return 'excellent'
  if (value >= 0.5) return 'good'
  return 'neutral'
}

// 监听
watch(() => props.data, () => {
  // 数据变化时的处理
}, { deep: true })

onMounted(() => {
  // 组件挂载后的初始化
})
</script>

<style scoped>
.equity-curve-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #ebeef5;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  flex: 1;
  min-height: 400px;
  padding: 20px;
  transition: all 0.3s ease;
}

.chart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  min-height: 100vh;
}

.stats-panel {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #ebeef5;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.positive {
  color: #67c23a;
}

.stat-value.negative {
  color: #f56c6c;
}

.stat-value.excellent {
  color: #67c23a;
}

.stat-value.good {
  color: #e6a23c;
}

.stat-value.neutral {
  color: #909399;
}

.stat-value.poor {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .stats-panel {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }
}
</style>
<template>
  <div ref="chartRef" :style="{ height: height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps<{
  data: Array<{ name: string; value: number; symbol?: string }>
  chartType: 'pie' | 'bar'
  height?: string
}>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  
  const option: echarts.EChartsOption = props.chartType === 'pie' ? {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(2)
        return `${params.name}<br/>市值: ¥${params.value.toLocaleString()}<br/>占比: ${percent}%`
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      data: props.data.map(item => item.name)
    },
    series: [
      {
        name: '持仓分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getColorByIndex(index)
          }
        }))
      }
    ]
  } : {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        const percent = ((data.value / total) * 100).toFixed(2)
        return `${data.name}<br/>市值: ¥${data.value.toLocaleString()}<br/>占比: ${percent}%`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.name),
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45,
        overflow: 'truncate',
        width: 80
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `¥${(value / 10000).toFixed(0)}万`
      }
    },
    series: [
      {
        name: '市值',
        type: 'bar',
        barWidth: '60%',
        data: props.data.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: getColorByIndex(index)
          }
        }))
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const getColorByIndex = (index: number) => {
  const colors = [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc',
    '#5994ce',
    '#c1232b',
    '#fe8463'
  ]
  return colors[index % colors.length]
}

const handleResize = () => {
  chartInstance?.resize()
}

watch(() => [props.data, props.chartType], () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})

defineOptions({
  name: 'PositionPieChart'
})
</script>
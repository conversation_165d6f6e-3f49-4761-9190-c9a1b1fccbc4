<template>
  <div ref="chartRef" :style="{ height: height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps<{
  data: Array<{ date: number; value: number }>
  threshold?: number
  height?: string
}>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = async () => {
  if (!chartRef.value) return

  await nextTick()

  // 确保容器有正确的尺寸
  const container = chartRef.value
  if (container.clientWidth === 0 || container.clientHeight === 0) {
    console.warn('[ECharts] RiskTrendChart container has zero dimensions, retrying...')
    setTimeout(initChart, 100)
    return
  }

  chartInstance = echarts.init(chartRef.value)

  const option: echarts.EChartsOption = {
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      containLabel: false
    },
    xAxis: {
      type: 'time',
      show: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: props.data.map(item => [item.date, item.value]),
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
          ])
        }
      }
    ]
  }

  // 添加阈值线
  if (props.threshold !== undefined) {
    option.series!.push({
      type: 'line',
      markLine: {
        silent: true,
        symbol: 'none',
        lineStyle: {
          color: '#F56C6C',
          type: 'dashed',
          width: 1
        },
        data: [
          {
            yAxis: props.threshold
          }
        ]
      }
    })
  }

  chartInstance.setOption(option)
}

const handleResize = () => {
  chartInstance?.resize()
}

watch(() => props.data, () => {
  if (chartInstance) {
    chartInstance.setOption({
      series: [{
        data: props.data.map(item => [item.date, item.value])
      }]
    })
  }
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})

defineOptions({
  name: 'RiskTrendChart'
})
</script>

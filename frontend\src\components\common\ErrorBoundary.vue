<template>
  <div class="error-boundary" v-if="hasError">
    <div class="error-content">
      <div class="error-icon">
        <el-icon size="64"><Warning /></el-icon>
      </div>
      <h2 class="error-title">{{ errorTitle }}</h2>
      <p class="error-message">{{ errorMessage }}</p>
      
      <div class="error-actions">
        <el-button type="primary" @click="retry">
          <el-icon><Refresh /></el-icon>
          重试
        </el-button>
        <el-button @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
      </div>
      
      <div class="error-details" v-if="showDetails">
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <pre>{{ errorDetails }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <div class="error-help">
        <p>如果问题持续存在，请：</p>
        <ul>
          <li>检查网络连接是否正常</li>
          <li>刷新页面重新尝试</li>
          <li>清除浏览器缓存</li>
          <li>联系技术支持</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Warning, Refresh, HomeFilled } from '@element-plus/icons-vue'

interface Props {
  error?: Error | null
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  error: null,
  showDetails: false
})

const emit = defineEmits<{
  retry: []
}>()

const router = useRouter()

const hasError = computed(() => !!props.error)

const errorTitle = computed(() => {
  if (!props.error) return '页面加载失败'
  
  if (props.error.message.includes('timeout') || props.error.message.includes('超时')) {
    return '页面加载超时'
  }
  
  if (props.error.message.includes('network') || props.error.message.includes('网络')) {
    return '网络连接失败'
  }
  
  return '页面加载出错'
})

const errorMessage = computed(() => {
  if (!props.error) return '页面无法正常加载，请重试'
  
  if (props.error.message.includes('timeout') || props.error.message.includes('超时')) {
    return '页面加载时间过长，可能是网络较慢或服务器响应缓慢'
  }
  
  if (props.error.message.includes('network') || props.error.message.includes('网络')) {
    return '无法连接到服务器，请检查网络连接'
  }
  
  return props.error.message || '发生了未知错误'
})

const errorDetails = computed(() => {
  if (!props.error) return ''
  return `
错误类型: ${props.error.name}
错误信息: ${props.error.message}
错误堆栈: ${props.error.stack}
时间: ${new Date().toLocaleString()}
用户代理: ${navigator.userAgent}
`
})

const retry = () => {
  emit('retry')
}

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 40px 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.error-icon {
  color: #f56c6c;
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  color: #333;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.error-message {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 32px;
}

.error-details {
  margin-bottom: 32px;
  text-align: left;
}

.error-details pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.error-help {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.error-help p {
  margin: 0 0 12px 0;
  font-weight: 500;
  color: #409eff;
}

.error-help ul {
  margin: 0;
  padding-left: 20px;
}

.error-help li {
  margin: 4px 0;
  color: #666;
}
</style>
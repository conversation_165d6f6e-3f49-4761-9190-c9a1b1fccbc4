<template>
  <div class="skeleton-loader">
    <!-- 卡片骨架屏 -->
    <div v-if="type === 'card'" class="skeleton-card">
      <div class="skeleton-header">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
      <div class="skeleton-content">
        <div v-for="i in rows" :key="i" class="skeleton-line" :style="{ width: getLineWidth(i) }"></div>
      </div>
    </div>

    <!-- 表格骨架屏 -->
    <div v-else-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-table-header">
        <div v-for="i in columns" :key="i" class="skeleton-table-cell"></div>
      </div>
      <div v-for="i in rows" :key="i" class="skeleton-table-row">
        <div v-for="j in columns" :key="j" class="skeleton-table-cell"></div>
      </div>
    </div>

    <!-- 图表骨架屏 -->
    <div v-else-if="type === 'chart'" class="skeleton-chart">
      <div class="skeleton-chart-header">
        <div class="skeleton-title"></div>
        <div class="skeleton-buttons">
          <div v-for="i in 3" :key="i" class="skeleton-button"></div>
        </div>
      </div>
      <div class="skeleton-chart-content">
        <div class="skeleton-chart-area"></div>
      </div>
    </div>

    <!-- 列表骨架屏 -->
    <div v-else-if="type === 'list'" class="skeleton-list">
      <div v-for="i in rows" :key="i" class="skeleton-list-item">
        <div class="skeleton-avatar"></div>
        <div class="skeleton-list-content">
          <div class="skeleton-line" style="width: 60%"></div>
          <div class="skeleton-line" style="width: 40%"></div>
        </div>
        <div class="skeleton-action"></div>
      </div>
    </div>

    <!-- 默认骨架屏 -->
    <div v-else class="skeleton-default">
      <div v-for="i in rows" :key="i" class="skeleton-line" :style="{ width: getLineWidth(i) }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'card' | 'table' | 'chart' | 'list' | 'default'
  rows?: number
  columns?: number
  animated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  rows: 3,
  columns: 4,
  animated: true
})

const getLineWidth = (index: number): string => {
  const widths = ['100%', '80%', '60%', '90%', '70%']
  return widths[index % widths.length]
}
</script>

<style scoped>
.skeleton-loader {
  width: 100%;
}

/* 基础骨架屏样式 */
.skeleton-line,
.skeleton-title,
.skeleton-subtitle,
.skeleton-button,
.skeleton-avatar,
.skeleton-action,
.skeleton-table-cell,
.skeleton-chart-area {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 卡片骨架屏 */
.skeleton-card {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: white;
}

.skeleton-header {
  margin-bottom: 16px;
}

.skeleton-title {
  height: 20px;
  width: 40%;
  margin-bottom: 8px;
}

.skeleton-subtitle {
  height: 14px;
  width: 60%;
}

.skeleton-content .skeleton-line {
  height: 16px;
  margin-bottom: 12px;
}

.skeleton-content .skeleton-line:last-child {
  margin-bottom: 0;
}

/* 表格骨架屏 */
.skeleton-table {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.skeleton-table-header,
.skeleton-table-row {
  display: flex;
  padding: 12px;
}

.skeleton-table-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-row {
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-table-cell {
  flex: 1;
  height: 16px;
  margin-right: 16px;
}

.skeleton-table-cell:last-child {
  margin-right: 0;
}

/* 图表骨架屏 */
.skeleton-chart {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: white;
}

.skeleton-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-chart-header .skeleton-title {
  height: 18px;
  width: 120px;
}

.skeleton-buttons {
  display: flex;
  gap: 8px;
}

.skeleton-button {
  height: 28px;
  width: 60px;
}

.skeleton-chart-content {
  padding: 20px;
}

.skeleton-chart-area {
  height: 300px;
  width: 100%;
}

/* 列表骨架屏 */
.skeleton-list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-list-item:last-child {
  border-bottom: none;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 16px;
}

.skeleton-list-content {
  flex: 1;
}

.skeleton-list-content .skeleton-line {
  height: 14px;
  margin-bottom: 8px;
}

.skeleton-list-content .skeleton-line:last-child {
  margin-bottom: 0;
}

.skeleton-action {
  width: 80px;
  height: 32px;
}

/* 默认骨架屏 */
.skeleton-default .skeleton-line {
  height: 16px;
  margin-bottom: 12px;
}

.skeleton-default .skeleton-line:last-child {
  margin-bottom: 0;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .skeleton-line,
  .skeleton-title,
  .skeleton-subtitle,
  .skeleton-button,
  .skeleton-avatar,
  .skeleton-action,
  .skeleton-table-cell,
  .skeleton-chart-area {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
  
  .skeleton-card,
  .skeleton-chart {
    background: #1a1a1a;
    border-color: #3a3a3a;
  }
  
  .skeleton-table {
    border-color: #3a3a3a;
  }
  
  .skeleton-table-header {
    background: #2a2a2a;
  }
  
  .skeleton-table-row,
  .skeleton-chart-header {
    border-color: #3a3a3a;
  }
  
  .skeleton-list-item {
    border-color: #3a3a3a;
  }
}
</style>

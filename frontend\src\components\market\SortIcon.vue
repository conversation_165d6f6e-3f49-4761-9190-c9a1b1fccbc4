<template>
  <span class="sort-icon">
    <svg width="10" height="12" viewBox="0 0 10 12">
      <path 
        d="M5 0 L10 5 L0 5 Z" 
        :fill="isAsc ? '#1890ff' : '#ccc'"
      />
      <path 
        d="M5 12 L0 7 L10 7 Z" 
        :fill="isDesc ? '#1890ff' : '#ccc'"
      />
    </svg>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  field: string
  current: string
  order: 'asc' | 'desc'
}

const props = defineProps<Props>()

const isActive = computed(() => props.field === props.current)
const isAsc = computed(() => isActive.value && props.order === 'asc')
const isDesc = computed(() => isActive.value && props.order === 'desc')
</script>

<style scoped>
.sort-icon {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}
</style>
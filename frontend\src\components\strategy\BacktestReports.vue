<template>
  <div class="backtest-reports">
    <div class="reports-header">
      <div class="header-left">
        <h3>回测报告</h3>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索报告..."
          style="width: 300px; margin-left: 20px;"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="generateReport">
          <el-icon><DocumentAdd /></el-icon>
          生成报告
        </el-button>
        <el-button size="small" @click="batchExport">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
      </div>
    </div>

    <div class="reports-filters">
      <el-space wrap>
        <el-select v-model="selectedStrategy" placeholder="策略类型" clearable size="small">
          <el-option label="全部策略" value="" />
          <el-option label="双均线策略" value="double_ma" />
          <el-option label="RSI策略" value="rsi" />
          <el-option label="MACD策略" value="macd" />
          <el-option label="布林带策略" value="bollinger" />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        />
        
        <el-select v-model="sortBy" placeholder="排序方式" size="small">
          <el-option label="创建时间" value="created_at" />
          <el-option label="收益率" value="return" />
          <el-option label="夏普比率" value="sharpe" />
          <el-option label="最大回撤" value="drawdown" />
        </el-select>
        
        <el-switch
          v-model="showStarred"
          active-text="仅显示已标记"
          inactive-text="显示全部"
        />
      </el-space>
    </div>

    <div class="reports-list">
      <div
        v-for="report in filteredReports"
        :key="report.id"
        class="report-card"
        @click="viewReport(report)"
      >
        <div class="card-header">
          <div class="report-info">
            <h4 class="report-title">{{ report.strategyName }}</h4>
            <div class="report-meta">
              <el-tag size="small" type="info">{{ report.period }}</el-tag>
              <span class="report-date">{{ formatDate(report.createdAt) }}</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button
              size="mini"
              circle
              :type="report.starred ? 'warning' : 'default'"
              @click.stop="toggleStar(report)"
            >
              <el-icon><Star /></el-icon>
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini" circle @click.stop>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`export-${report.id}`">导出PDF</el-dropdown-item>
                  <el-dropdown-item :command="`excel-${report.id}`">导出Excel</el-dropdown-item>
                  <el-dropdown-item :command="`share-${report.id}`">分享报告</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${report.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="card-content">
          <div class="performance-summary">
            <div class="performance-grid">
              <div class="perf-item">
                <span class="perf-label">总收益率</span>
                <span class="perf-value" :class="getReturnClass(report.metrics.totalReturn)">
                  {{ formatPercent(report.metrics.totalReturn) }}
                </span>
              </div>
              <div class="perf-item">
                <span class="perf-label">年化收益率</span>
                <span class="perf-value" :class="getReturnClass(report.metrics.annualReturn)">
                  {{ formatPercent(report.metrics.annualReturn) }}
                </span>
              </div>
              <div class="perf-item">
                <span class="perf-label">夏普比率</span>
                <span class="perf-value" :class="getSharpeClass(report.metrics.sharpeRatio)">
                  {{ report.metrics.sharpeRatio?.toFixed(2) }}
                </span>
              </div>
              <div class="perf-item">
                <span class="perf-label">最大回撤</span>
                <span class="perf-value negative">
                  {{ formatPercent(report.metrics.maxDrawdown) }}
                </span>
              </div>
              <div class="perf-item">
                <span class="perf-label">胜率</span>
                <span class="perf-value">{{ formatPercent(report.metrics.winRate) }}</span>
              </div>
              <div class="perf-item">
                <span class="perf-label">盈亏比</span>
                <span class="perf-value">{{ report.metrics.profitLossRatio?.toFixed(2) }}</span>
              </div>
            </div>
          </div>

          <!-- 迷你图表 -->
          <div class="mini-chart">
            <div class="chart-title">收益曲线</div>
            <div class="mini-chart-container">
              <svg width="100%" height="60" viewBox="0 0 300 60">
                <path
                  :d="generateMiniChartPath(report.equityCurve)"
                  stroke="#409EFF"
                  stroke-width="2"
                  fill="none"
                />
              </svg>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="report-stats">
            <span class="stat-item">
              <el-icon><Clock /></el-icon>
              回测期间: {{ report.period }}
            </span>
            <span class="stat-item">
              <el-icon><TrendCharts /></el-icon>
              {{ report.metrics.totalTrades }}笔交易
            </span>
          </div>
          <div class="card-actions">
            <el-button size="small" type="primary" @click.stop="viewReport(report)">
              查看详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-if="filteredReports.length === 0" description="暂无回测报告" />
    </div>

    <!-- 报告详情对话框 -->
    <el-dialog 
      v-model="showReportDialog" 
      :title="`${selectedReport?.strategyName} - 回测报告`" 
      width="90%" 
      :before-close="closeReportDialog"
      class="report-dialog"
    >
      <div class="report-details" v-if="selectedReport">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="概览" name="overview">
            <ReportOverview :report="selectedReport" />
          </el-tab-pane>
          
          <el-tab-pane label="收益分析" name="returns">
            <ReportReturns :report="selectedReport" />
          </el-tab-pane>
          
          <el-tab-pane label="风险分析" name="risk">
            <ReportRisk :report="selectedReport" />
          </el-tab-pane>
          
          <el-tab-pane label="交易分析" name="trades">
            <ReportTrades :report="selectedReport" />
          </el-tab-pane>
          
          <el-tab-pane label="持仓分析" name="positions">
            <ReportPositions :report="selectedReport" />
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportPDF(selectedReport)">导出PDF</el-button>
          <el-button @click="exportExcel(selectedReport)">导出Excel</el-button>
          <el-button type="primary" @click="closeReportDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, DocumentAdd, Download, Star, MoreFilled, Clock, TrendCharts
} from '@element-plus/icons-vue'
import { formatPercent, formatDate, formatCurrency } from '@/utils/format'
import ReportOverview from './report/ReportOverview.vue'
import ReportReturns from './report/ReportReturns.vue'
import ReportRisk from './report/ReportRisk.vue'
import ReportTrades from './report/ReportTrades.vue'
import ReportPositions from './report/ReportPositions.vue'

interface Props {
  reports: any[]
}

const props = withDefaults(defineProps<Props>(), {
  reports: () => []
})

const emit = defineEmits<{
  reportSelected: [report: any]
}>()

// 响应式数据
const searchKeyword = ref('')
const selectedStrategy = ref('')
const dateRange = ref([])
const sortBy = ref('created_at')
const showStarred = ref(false)
const showReportDialog = ref(false)
const selectedReport = ref(null)
const activeTab = ref('overview')

// 模拟报告数据
const mockReports = [
  {
    id: '1',
    strategyName: '双均线突破策略',
    period: '2023-01-01 至 2023-12-31',
    createdAt: '2024-01-15',
    starred: true,
    metrics: {
      totalReturn: 0.185,
      annualReturn: 0.185,
      sharpeRatio: 1.42,
      maxDrawdown: -0.123,
      winRate: 0.618,
      profitLossRatio: 1.85,
      volatility: 0.156,
      totalTrades: 89
    },
    equityCurve: generateMockEquityCurve(),
    trades: generateMockTrades(89),
    positions: generateMockPositions()
  },
  {
    id: '2',
    strategyName: 'RSI反转策略',
    period: '2023-06-01 至 2023-12-31',
    createdAt: '2024-01-10',
    starred: false,
    metrics: {
      totalReturn: 0.234,
      annualReturn: 0.351,
      sharpeRatio: 1.68,
      maxDrawdown: -0.089,
      winRate: 0.592,
      profitLossRatio: 2.14,
      volatility: 0.209,
      totalTrades: 156
    },
    equityCurve: generateMockEquityCurve(),
    trades: generateMockTrades(156),
    positions: generateMockPositions()
  },
  {
    id: '3',
    strategyName: 'MACD动量策略',
    period: '2023-03-01 至 2023-12-31',
    createdAt: '2024-01-08',
    starred: true,
    metrics: {
      totalReturn: 0.127,
      annualReturn: 0.165,
      sharpeRatio: 1.28,
      maxDrawdown: -0.067,
      winRate: 0.654,
      profitLossRatio: 1.47,
      volatility: 0.134,
      totalTrades: 67
    },
    equityCurve: generateMockEquityCurve(),
    trades: generateMockTrades(67),
    positions: generateMockPositions()
  }
]

function generateMockEquityCurve() {
  const data = []
  let value = 100000
  const startDate = new Date('2023-01-01')
  
  for (let i = 0; i < 250; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    
    value *= (1 + (Math.random() - 0.45) * 0.02)
    data.push({
      date: date.toISOString().split('T')[0],
      value: value,
      drawdown: Math.random() * -0.1
    })
  }
  
  return data
}

function generateMockTrades(count: number) {
  const trades = []
  const startDate = new Date('2023-01-01')
  
  for (let i = 0; i < count; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + Math.floor(Math.random() * 365))
    
    const profit = (Math.random() - 0.4) * 5000
    
    trades.push({
      id: i + 1,
      date: date.toISOString().split('T')[0],
      symbol: 'STOCK' + Math.floor(Math.random() * 10),
      type: Math.random() > 0.5 ? 'buy' : 'sell',
      price: 100 + Math.random() * 50,
      quantity: Math.floor(Math.random() * 1000) + 100,
      profit: profit,
      duration: Math.floor(Math.random() * 30) + 1
    })
  }
  
  return trades.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}

function generateMockPositions() {
  return [
    { symbol: 'STOCK1', quantity: 1000, avgPrice: 105.5, currentPrice: 108.2, profit: 2700 },
    { symbol: 'STOCK2', quantity: 500, avgPrice: 78.3, currentPrice: 75.1, profit: -1600 },
    { symbol: 'STOCK3', quantity: 800, avgPrice: 92.1, currentPrice: 95.8, profit: 2960 }
  ]
}

// 计算属性
const allReports = computed(() => {
  return [...props.reports, ...mockReports]
})

const filteredReports = computed(() => {
  let filtered = allReports.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(report => 
      report.strategyName.toLowerCase().includes(keyword) ||
      report.period.toLowerCase().includes(keyword)
    )
  }

  // 策略类型过滤
  if (selectedStrategy.value) {
    filtered = filtered.filter(report => 
      report.strategyName.includes(selectedStrategy.value)
    )
  }

  // 日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(report => {
      const reportDate = new Date(report.createdAt)
      return reportDate >= startDate && reportDate <= endDate
    })
  }

  // 标记过滤
  if (showStarred.value) {
    filtered = filtered.filter(report => report.starred)
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'return':
        return b.metrics.totalReturn - a.metrics.totalReturn
      case 'sharpe':
        return b.metrics.sharpeRatio - a.metrics.sharpeRatio
      case 'drawdown':
        return a.metrics.maxDrawdown - b.metrics.maxDrawdown
      default: // created_at
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })

  return filtered
})

// 方法
const generateReport = () => {
  ElMessage.info('生成新报告功能开发中...')
}

const batchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

const viewReport = (report: any) => {
  selectedReport.value = report
  showReportDialog.value = true
  emit('reportSelected', report)
}

const closeReportDialog = () => {
  showReportDialog.value = false
  selectedReport.value = null
  activeTab.value = 'overview'
}

const toggleStar = (report: any) => {
  report.starred = !report.starred
  ElMessage.success(report.starred ? '已标记报告' : '已取消标记')
}

const handleCommand = (command: string) => {
  const [action, id] = command.split('-')
  const report = allReports.value.find(r => r.id === id)
  
  if (!report) return

  switch (action) {
    case 'export':
      exportPDF(report)
      break
    case 'excel':
      exportExcel(report)
      break
    case 'share':
      shareReport(report)
      break
    case 'delete':
      deleteReport(report)
      break
  }
}

const exportPDF = (report: any) => {
  ElMessage.success(`正在导出 "${report.strategyName}" PDF报告...`)
  // 这里应该调用PDF导出API
}

const exportExcel = (report: any) => {
  ElMessage.success(`正在导出 "${report.strategyName}" Excel报告...`)
  // 这里应该调用Excel导出API
}

const shareReport = (report: any) => {
  ElMessage.info('分享功能开发中...')
}

const deleteReport = async (report: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除报告 "${report.strategyName}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    ElMessage.success('报告删除成功')
  } catch {
    // 用户取消删除
  }
}

const generateMiniChartPath = (equityCurve: any[]) => {
  if (!equityCurve || equityCurve.length === 0) return ''
  
  const width = 300
  const height = 60
  const padding = 10
  
  const values = equityCurve.map(point => point.value)
  const minValue = Math.min(...values)
  const maxValue = Math.max(...values)
  const valueRange = maxValue - minValue || 1
  
  let path = ''
  
  equityCurve.forEach((point, index) => {
    const x = padding + (index / (equityCurve.length - 1)) * (width - 2 * padding)
    const y = padding + ((maxValue - point.value) / valueRange) * (height - 2 * padding)
    
    if (index === 0) {
      path += `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })
  
  return path
}

// 样式辅助函数
const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

const getSharpeClass = (value: number) => {
  if (value >= 2) return 'excellent'
  if (value >= 1) return 'good'
  if (value >= 0) return 'neutral'
  return 'poor'
}

onMounted(() => {
  // 组件挂载后的初始化
})
</script>

<style scoped>
.backtest-reports {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.reports-filters {
  padding: 12px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reports-list {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
  padding: 0 4px;
  overflow-y: auto;
}

.report-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.report-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.report-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.report-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.report-date {
  font-size: 12px;
  color: #909399;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  margin-bottom: 16px;
}

.performance-summary {
  margin-bottom: 16px;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.perf-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.perf-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.perf-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.perf-value.positive {
  color: #67c23a;
}

.perf-value.negative {
  color: #f56c6c;
}

.perf-value.excellent {
  color: #67c23a;
}

.perf-value.good {
  color: #e6a23c;
}

.perf-value.neutral {
  color: #909399;
}

.perf-value.poor {
  color: #f56c6c;
}

.mini-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.chart-title {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.mini-chart-container {
  width: 100%;
  height: 60px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.report-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.report-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.report-details {
  height: 100%;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 1200px) {
  .reports-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
  
  .performance-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .reports-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .reports-list {
    grid-template-columns: 1fr;
  }
  
  .performance-grid {
    grid-template-columns: 1fr;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .report-stats {
    justify-content: space-between;
  }
}
</style>
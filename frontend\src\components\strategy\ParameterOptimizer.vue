<template>
  <div class="parameter-optimizer">
    <div class="optimizer-header">
      <h3>参数优化器</h3>
      <div class="header-actions">
        <el-button size="small" @click="startOptimization" type="primary" :loading="optimizing">
          {{ optimizing ? '优化中...' : '开始优化' }}
        </el-button>
        <el-button size="small" @click="stopOptimization" :disabled="!optimizing">
          停止优化
        </el-button>
        <el-button size="small" @click="exportResults">导出结果</el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 参数设置面板 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>优化设置</span>
          </template>
          
          <el-form :model="optimizationConfig" label-width="100px" size="small">
            <el-form-item label="策略类型">
              <el-select v-model="optimizationConfig.strategyType" placeholder="选择策略">
                <el-option
                  v-for="strategy in availableStrategies"
                  :key="strategy.value"
                  :label="strategy.label"
                  :value="strategy.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优化目标">
              <el-select v-model="optimizationConfig.objective">
                <el-option label="总收益率" value="total_return" />
                <el-option label="夏普比率" value="sharpe_ratio" />
                <el-option label="收益回撤比" value="calmar_ratio" />
                <el-option label="胜率" value="win_rate" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优化方法">
              <el-select v-model="optimizationConfig.method">
                <el-option label="网格搜索" value="grid_search" />
                <el-option label="随机搜索" value="random_search" />
                <el-option label="贝叶斯优化" value="bayesian" />
                <el-option label="遗传算法" value="genetic" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="最大迭代数">
              <el-input-number
                v-model="optimizationConfig.maxIterations"
                :min="10"
                :max="1000"
                :step="10"
              />
            </el-form-item>
          </el-form>

          <!-- 参数范围设置 -->
          <div class="parameter-ranges" v-if="optimizationConfig.strategyType">
            <h4>参数范围设置</h4>
            <div
              v-for="param in getParametersByStrategy(optimizationConfig.strategyType)"
              :key="param.name"
              class="param-range-item"
            >
              <label>{{ param.label }}:</label>
              <div class="range-inputs">
                <el-input-number
                  v-model="parameterRanges[param.name].min"
                  :precision="param.precision || 0"
                  :step="param.step || 1"
                  placeholder="最小值"
                  size="small"
                />
                <span>-</span>
                <el-input-number
                  v-model="parameterRanges[param.name].max"
                  :precision="param.precision || 0"
                  :step="param.step || 1"
                  placeholder="最大值"
                  size="small"
                />
                <el-input-number
                  v-model="parameterRanges[param.name].step"
                  :precision="param.precision || 0"
                  :step="param.step || 1"
                  placeholder="步长"
                  size="small"
                />
              </div>
            </div>
          </div>

          <!-- 约束条件 -->
          <div class="constraints-section">
            <h4>约束条件</h4>
            <el-form label-width="120px" size="small">
              <el-form-item label="最小收益率">
                <el-input-number
                  v-model="optimizationConfig.constraints.minReturn"
                  :min="-1"
                  :max="1"
                  :step="0.01"
                  :precision="2"
                />
              </el-form-item>
              <el-form-item label="最大回撤限制">
                <el-input-number
                  v-model="optimizationConfig.constraints.maxDrawdown"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  :precision="2"
                />
              </el-form-item>
              <el-form-item label="最小夏普比率">
                <el-input-number
                  v-model="optimizationConfig.constraints.minSharpe"
                  :min="0"
                  :max="5"
                  :step="0.1"
                  :precision="1"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>

      <!-- 优化进度和结果 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>优化进度</span>
          </template>
          
          <!-- 进度条 -->
          <div class="optimization-progress" v-if="optimizing || optimizationResults.length > 0">
            <el-progress
              :percentage="optimizationProgress"
              :status="optimizing ? 'active' : 'success'"
              stroke-width="8"
            />
            <div class="progress-info">
              <span>当前迭代: {{ currentIteration }} / {{ optimizationConfig.maxIterations }}</span>
              <span>最佳目标值: {{ bestObjectiveValue?.toFixed(4) || 'N/A' }}</span>
              <span>耗时: {{ formatDuration(elapsedTime) }}</span>
            </div>
          </div>

          <!-- 实时优化图表 -->
          <div class="optimization-chart" v-if="optimizationResults.length > 0">
            <v-chart
              :option="optimizationChartOption"
              style="height: 300px;"
              autoresize
            />
          </div>
        </el-card>

        <!-- 最优参数结果 -->
        <el-card style="margin-top: 20px;" v-if="bestResult">
          <template #header>
            <span>最优参数组合</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="best-parameters">
                <h4>最优参数</h4>
                <div class="param-list">
                  <div
                    v-for="(value, key) in bestResult.parameters"
                    :key="key"
                    class="param-item"
                  >
                    <span class="param-name">{{ key }}:</span>
                    <span class="param-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="best-metrics">
                <h4>绩效指标</h4>
                <div class="metrics-grid">
                  <div class="metric-item">
                    <span>总收益率:</span>
                    <span :class="getReturnClass(bestResult.metrics.total_return)">
                      {{ formatPercent(bestResult.metrics.total_return) }}
                    </span>
                  </div>
                  <div class="metric-item">
                    <span>夏普比率:</span>
                    <span>{{ bestResult.metrics.sharpe_ratio?.toFixed(2) }}</span>
                  </div>
                  <div class="metric-item">
                    <span>最大回撤:</span>
                    <span class="negative">{{ formatPercent(bestResult.metrics.max_drawdown) }}</span>
                  </div>
                  <div class="metric-item">
                    <span>胜率:</span>
                    <span>{{ formatPercent(bestResult.metrics.win_rate) }}</span>
                  </div>
                  <div class="metric-item">
                    <span>盈亏比:</span>
                    <span>{{ bestResult.metrics.profit_loss_ratio?.toFixed(2) }}</span>
                  </div>
                  <div class="metric-item">
                    <span>总交易次数:</span>
                    <span>{{ bestResult.metrics.total_trades }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div class="best-result-actions">
            <el-button size="small" @click="applyBestParameters">应用最优参数</el-button>
            <el-button size="small" @click="viewBacktestDetails">查看回测详情</el-button>
            <el-button size="small" @click="saveOptimizationResult">保存优化结果</el-button>
          </div>
        </el-card>

        <!-- 参数热力图 -->
        <el-card style="margin-top: 20px;" v-if="optimizationResults.length > 0">
          <template #header>
            <span>参数热力图</span>
          </template>
          <div class="heatmap-container">
            <v-chart
              :option="heatmapOption"
              style="height: 400px;"
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 所有结果表格 -->
    <el-card style="margin-top: 20px;" v-if="optimizationResults.length > 0">
      <template #header>
        <span>优化结果详情</span>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索参数组合"
          style="width: 200px; float: right; margin-top: -5px;"
          size="small"
        />
      </template>
      
      <el-table
        :data="filteredResults"
        :default-sort="{ prop: 'objectiveValue', order: 'descending' }"
        stripe
        size="small"
        max-height="400"
      >
        <el-table-column prop="iteration" label="迭代" width="80" />
        <el-table-column
          v-for="(param, index) in parameterColumns"
          :key="index"
          :prop="`parameters.${param}`"
          :label="param"
          width="100"
        />
        <el-table-column
          prop="objectiveValue"
          label="目标值"
          width="100"
          sortable
        >
          <template #default="{ row }">
            <span :class="getObjectiveClass(row.objectiveValue)">
              {{ row.objectiveValue?.toFixed(4) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="metrics.total_return"
          label="总收益率"
          width="100"
        >
          <template #default="{ row }">
            <span :class="getReturnClass(row.metrics.total_return)">
              {{ formatPercent(row.metrics.total_return) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="metrics.sharpe_ratio"
          label="夏普比率"
          width="100"
        >
          <template #default="{ row }">
            {{ row.metrics.sharpe_ratio?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="metrics.max_drawdown"
          label="最大回撤"
          width="100"
        >
          <template #default="{ row }">
            <span class="negative">
              {{ formatPercent(row.metrics.max_drawdown) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="mini" @click="viewResult(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'
import { formatPercent } from '@/utils/format'

// ECharts 组件已在全局 plugins/echarts.ts 中注册，无需重复注册

interface OptimizationConfig {
  strategyType: string
  objective: string
  method: string
  maxIterations: number
  constraints: {
    minReturn: number
    maxDrawdown: number
    minSharpe: number
  }
}

interface ParameterRange {
  min: number
  max: number
  step: number
}

interface OptimizationResult {
  iteration: number
  parameters: Record<string, any>
  metrics: Record<string, any>
  objectiveValue: number
  constraintsSatisfied: boolean
}

const optimizing = ref(false)
const currentIteration = ref(0)
const elapsedTime = ref(0)
const searchKeyword = ref('')
let optimizationTimer: NodeJS.Timeout | null = null
let startTime: number = 0

const optimizationConfig = ref<OptimizationConfig>({
  strategyType: '',
  objective: 'sharpe_ratio',
  method: 'grid_search',
  maxIterations: 100,
  constraints: {
    minReturn: 0,
    maxDrawdown: 0.3,
    minSharpe: 0
  }
})

const parameterRanges = ref<Record<string, ParameterRange>>({})
const optimizationResults = ref<OptimizationResult[]>([])
const bestResult = ref<OptimizationResult | null>(null)

const availableStrategies = [
  { label: '双均线策略', value: 'double_ma' },
  { label: 'RSI策略', value: 'rsi' },
  { label: 'MACD策略', value: 'macd' },
  { label: '布林带策略', value: 'bollinger_bands' },
  { label: '多因子策略', value: 'multi_factor' }
]

// 参数定义
const strategyParameters = {
  double_ma: [
    { name: 'short_period', label: '短期周期', min: 1, max: 50, step: 1, precision: 0 },
    { name: 'long_period', label: '长期周期', min: 10, max: 200, step: 5, precision: 0 }
  ],
  rsi: [
    { name: 'period', label: 'RSI周期', min: 2, max: 50, step: 1, precision: 0 },
    { name: 'overbought', label: '超买阈值', min: 50, max: 90, step: 5, precision: 0 },
    { name: 'oversold', label: '超卖阈值', min: 10, max: 50, step: 5, precision: 0 }
  ],
  macd: [
    { name: 'fast_period', label: '快线周期', min: 5, max: 25, step: 1, precision: 0 },
    { name: 'slow_period', label: '慢线周期', min: 15, max: 50, step: 5, precision: 0 },
    { name: 'signal_period', label: '信号线周期', min: 5, max: 15, step: 1, precision: 0 }
  ],
  bollinger_bands: [
    { name: 'period', label: '均线周期', min: 10, max: 50, step: 5, precision: 0 },
    { name: 'std_dev', label: '标准差倍数', min: 1, max: 3, step: 0.1, precision: 1 }
  ]
}

const getParametersByStrategy = (strategyType: string) => {
  return strategyParameters[strategyType] || []
}

// 监听策略类型变化，初始化参数范围
watch(() => optimizationConfig.value.strategyType, (newType) => {
  if (newType) {
    const params = getParametersByStrategy(newType)
    parameterRanges.value = {}
    params.forEach(param => {
      parameterRanges.value[param.name] = {
        min: param.min,
        max: param.max,
        step: param.step
      }
    })
  }
})

// 计算属性
const optimizationProgress = computed(() => {
  return Math.round((currentIteration.value / optimizationConfig.value.maxIterations) * 100)
})

const bestObjectiveValue = computed(() => {
  return bestResult.value?.objectiveValue
})

const parameterColumns = computed(() => {
  if (!optimizationConfig.value.strategyType) return []
  return getParametersByStrategy(optimizationConfig.value.strategyType).map(p => p.name)
})

const filteredResults = computed(() => {
  if (!searchKeyword.value) return optimizationResults.value
  
  return optimizationResults.value.filter(result => {
    const searchStr = JSON.stringify(result.parameters).toLowerCase()
    return searchStr.includes(searchKeyword.value.toLowerCase())
  })
})

// 优化进度图表
const optimizationChartOption = computed(() => {
  const iterations = optimizationResults.value.map(r => r.iteration)
  const objectiveValues = optimizationResults.value.map(r => r.objectiveValue)
  const bestValues = []
  let currentBest = -Infinity
  
  for (const value of objectiveValues) {
    if (value > currentBest) {
      currentBest = value
    }
    bestValues.push(currentBest)
  }

  return {
    title: {
      text: '优化进度',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当前值', '最佳值'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: iterations,
      name: '迭代次数'
    },
    yAxis: {
      type: 'value',
      name: '目标值'
    },
    series: [
      {
        name: '当前值',
        type: 'line',
        data: objectiveValues,
        lineStyle: { width: 1 },
        symbol: 'circle',
        symbolSize: 4,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '最佳值',
        type: 'line',
        data: bestValues,
        lineStyle: { width: 2 },
        symbol: 'none',
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
})

// 参数热力图
const heatmapOption = computed(() => {
  if (!optimizationResults.value.length || parameterColumns.value.length < 2) {
    return {}
  }

  const param1 = parameterColumns.value[0]
  const param2 = parameterColumns.value[1]
  
  // 构建热力图数据
  const heatmapData = optimizationResults.value.map(result => [
    result.parameters[param1],
    result.parameters[param2],
    result.objectiveValue
  ])

  const param1Values = [...new Set(heatmapData.map(d => d[0]))].sort((a, b) => a - b)
  const param2Values = [...new Set(heatmapData.map(d => d[1]))].sort((a, b) => a - b)

  return {
    title: {
      text: `参数热力图: ${param1} vs ${param2}`,
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      position: 'top',
      formatter: (params: any) => {
        return `${param1}: ${params.data[0]}<br/>${param2}: ${params.data[1]}<br/>目标值: ${params.data[2]?.toFixed(4)}`
      }
    },
    grid: {
      height: '60%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: param1Values,
      name: param1,
      splitArea: { show: true }
    },
    yAxis: {
      type: 'category',
      data: param2Values,
      name: param2,
      splitArea: { show: true }
    },
    visualMap: {
      min: Math.min(...heatmapData.map(d => d[2])),
      max: Math.max(...heatmapData.map(d => d[2])),
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '目标值',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
})

// 方法
const startOptimization = async () => {
  if (!optimizationConfig.value.strategyType) {
    ElMessage.warning('请先选择策略类型')
    return
  }

  optimizing.value = true
  currentIteration.value = 0
  optimizationResults.value = []
  bestResult.value = null
  startTime = Date.now()

  // 模拟优化过程
  optimizationTimer = setInterval(() => {
    if (currentIteration.value >= optimizationConfig.value.maxIterations) {
      stopOptimization()
      return
    }

    // 生成随机参数组合
    const parameters = {}
    const params = getParametersByStrategy(optimizationConfig.value.strategyType)
    
    params.forEach(param => {
      const range = parameterRanges.value[param.name]
      const min = range.min
      const max = range.max
      const step = range.step
      const steps = Math.floor((max - min) / step) + 1
      const randomStep = Math.floor(Math.random() * steps)
      parameters[param.name] = min + randomStep * step
    })

    // 模拟回测结果
    const metrics = generateMockMetrics()
    const objectiveValue = metrics[optimizationConfig.value.objective] || 0
    
    // 检查约束条件
    const constraintsSatisfied = checkConstraints(metrics)

    const result: OptimizationResult = {
      iteration: currentIteration.value + 1,
      parameters,
      metrics,
      objectiveValue,
      constraintsSatisfied
    }

    optimizationResults.value.push(result)

    // 更新最佳结果
    if (constraintsSatisfied && (!bestResult.value || objectiveValue > bestResult.value.objectiveValue)) {
      bestResult.value = result
    }

    currentIteration.value++
    elapsedTime.value = Date.now() - startTime
  }, 100)
}

const stopOptimization = () => {
  if (optimizationTimer) {
    clearInterval(optimizationTimer)
    optimizationTimer = null
  }
  optimizing.value = false
  ElMessage.success(`优化完成，共执行 ${currentIteration.value} 次迭代`)
}

const generateMockMetrics = () => {
  const baseReturn = (Math.random() - 0.3) * 0.4 // -0.2 到 0.2
  const volatility = Math.random() * 0.3 + 0.1 // 0.1 到 0.4
  const drawdown = -Math.random() * 0.4 // 0 到 -0.4
  const winRate = Math.random() * 0.4 + 0.4 // 0.4 到 0.8
  
  return {
    total_return: baseReturn,
    sharpe_ratio: baseReturn / volatility,
    max_drawdown: drawdown,
    win_rate: winRate,
    profit_loss_ratio: Math.random() * 2 + 0.5,
    total_trades: Math.floor(Math.random() * 200) + 50,
    calmar_ratio: Math.abs(baseReturn / drawdown)
  }
}

const checkConstraints = (metrics: any) => {
  const constraints = optimizationConfig.value.constraints
  return (
    metrics.total_return >= constraints.minReturn &&
    metrics.max_drawdown >= -constraints.maxDrawdown &&
    metrics.sharpe_ratio >= constraints.minSharpe
  )
}

const exportResults = () => {
  // 导出优化结果到CSV或Excel
  ElMessage.success('结果导出功能开发中...')
}

const applyBestParameters = () => {
  if (!bestResult.value) return
  
  // 这里应该将最优参数应用到策略编辑器
  ElMessage.success('最优参数已应用到策略编辑器')
}

const viewBacktestDetails = () => {
  if (!bestResult.value) return
  
  // 显示详细的回测报告
  ElMessage.info('回测详情功能开发中...')
}

const saveOptimizationResult = () => {
  if (!bestResult.value) return
  
  // 保存优化结果
  ElMessage.success('优化结果已保存')
}

const viewResult = (result: OptimizationResult) => {
  // 查看单个结果的详情
  ElMessage.info(`查看第 ${result.iteration} 次迭代结果`)
}

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}时${minutes % 60}分${seconds % 60}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

const getObjectiveClass = (value: number) => {
  if (!bestResult.value) return ''
  const ratio = value / bestResult.value.objectiveValue
  if (ratio >= 0.9) return 'excellent'
  if (ratio >= 0.7) return 'good'
  return 'normal'
}
</script>

<style scoped>
.parameter-optimizer {
  width: 100%;
  height: 100%;
}

.optimizer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.optimizer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.parameter-ranges {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.parameter-ranges h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.param-range-item {
  margin-bottom: 16px;
}

.param-range-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-inputs span {
  color: #909399;
}

.constraints-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.constraints-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.optimization-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.optimization-chart {
  margin-top: 20px;
}

.best-parameters h4,
.best-metrics h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.param-list,
.metrics-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item,
.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.param-name,
.metric-item span:first-child {
  font-size: 14px;
  color: #606266;
}

.param-value,
.metric-item span:last-child {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.best-result-actions {
  margin-top: 20px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.heatmap-container {
  width: 100%;
}

.positive {
  color: #67c23a !important;
}

.negative {
  color: #f56c6c !important;
}

.excellent {
  color: #67c23a !important;
  font-weight: 600;
}

.good {
  color: #e6a23c !important;
}

.normal {
  color: #909399;
}

@media (max-width: 1200px) {
  .optimizer-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .range-inputs {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
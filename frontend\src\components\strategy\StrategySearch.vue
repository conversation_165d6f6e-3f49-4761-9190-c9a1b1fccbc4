<template>
  <div class="strategy-search">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索策略名称、描述或标签..."
        :prefix-icon="Search"
        size="large"
        clearable
        @input="handleSearch"
        @clear="handleClear"
        class="search-input"
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
    </div>

    <!-- 高级筛选 -->
    <div class="advanced-filters" v-if="showAdvanced">
      <el-row :gutter="16">
        <!-- 策略类型 -->
        <el-col :span="6">
          <div class="filter-group">
            <label class="filter-label">策略类型</label>
            <el-select
              v-model="filters.type"
              placeholder="选择类型"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="type in strategyTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </div>
        </el-col>

        <!-- 风险等级 -->
        <el-col :span="6">
          <div class="filter-group">
            <label class="filter-label">风险等级</label>
            <el-select
              v-model="filters.riskLevel"
              placeholder="选择风险等级"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="低风险" value="low" />
              <el-option label="中风险" value="medium" />
              <el-option label="高风险" value="high" />
            </el-select>
          </div>
        </el-col>

        <!-- 收益范围 -->
        <el-col :span="6">
          <div class="filter-group">
            <label class="filter-label">年化收益率</label>
            <el-select
              v-model="filters.returnRange"
              placeholder="选择收益范围"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="0-10%" value="0-10" />
              <el-option label="10-20%" value="10-20" />
              <el-option label="20-30%" value="20-30" />
              <el-option label="30%以上" value="30+" />
            </el-select>
          </div>
        </el-col>

        <!-- 状态 -->
        <el-col :span="6">
          <div class="filter-group">
            <label class="filter-label">运行状态</label>
            <el-select
              v-model="filters.status"
              placeholder="选择状态"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="运行中" value="running" />
              <el-option label="已停止" value="stopped" />
              <el-option label="测试中" value="testing" />
              <el-option label="已暂停" value="paused" />
            </el-select>
          </div>
        </el-col>
      </el-row>

      <!-- 标签筛选 -->
      <div class="tag-filters">
        <label class="filter-label">策略标签</label>
        <div class="tag-list">
          <el-check-tag
            v-for="tag in availableTags"
            :key="tag"
            :checked="filters.tags.includes(tag)"
            @change="(checked) => handleTagChange(tag, checked)"
          >
            {{ tag }}
          </el-check-tag>
        </div>
      </div>

      <!-- 排序选项 -->
      <div class="sort-options">
        <label class="filter-label">排序方式</label>
        <el-radio-group v-model="filters.sortBy" @change="handleFilterChange">
          <el-radio-button label="latest">最新创建</el-radio-button>
          <el-radio-button label="performance">收益率</el-radio-button>
          <el-radio-button label="sharpe">夏普比率</el-radio-button>
          <el-radio-button label="popularity">热门程度</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 筛选控制 -->
    <div class="filter-controls">
      <el-button
        link
        type="primary"
        @click="toggleAdvanced"
        class="toggle-advanced"
      >
        {{ showAdvanced ? '收起筛选' : '高级筛选' }}
        <el-icon>
          <ArrowUp v-if="showAdvanced" />
          <ArrowDown v-else />
        </el-icon>
      </el-button>

      <div class="active-filters" v-if="hasActiveFilters">
        <span class="filter-count">已应用 {{ activeFilterCount }} 个筛选条件</span>
        <el-button link type="primary" @click="clearAllFilters">
          清除全部
        </el-button>
      </div>
    </div>

    <!-- 快速筛选标签 -->
    <div class="quick-filters" v-if="hasActiveFilters">
      <el-tag
        v-if="filters.type"
        closable
        @close="filters.type = ''; handleFilterChange()"
      >
        类型: {{ getTypeLabel(filters.type) }}
      </el-tag>
      <el-tag
        v-if="filters.riskLevel"
        closable
        @close="filters.riskLevel = ''; handleFilterChange()"
      >
        风险: {{ getRiskLabel(filters.riskLevel) }}
      </el-tag>
      <el-tag
        v-if="filters.returnRange"
        closable
        @close="filters.returnRange = ''; handleFilterChange()"
      >
        收益: {{ filters.returnRange }}%
      </el-tag>
      <el-tag
        v-if="filters.status"
        closable
        @close="filters.status = ''; handleFilterChange()"
      >
        状态: {{ getStatusLabel(filters.status) }}
      </el-tag>
      <el-tag
        v-for="tag in filters.tags"
        :key="tag"
        closable
        @close="handleTagChange(tag, false)"
      >
        {{ tag }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Search, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

interface SearchFilters {
  type: string
  riskLevel: string
  returnRange: string
  status: string
  tags: string[]
  sortBy: string
}

interface Emits {
  (e: 'search', query: string): void
  (e: 'filter', filters: SearchFilters): void
}

const emit = defineEmits<Emits>()

// 状态
const searchQuery = ref('')
const showAdvanced = ref(false)

// 筛选条件
const filters = reactive<SearchFilters>({
  type: '',
  riskLevel: '',
  returnRange: '',
  status: '',
  tags: [],
  sortBy: 'latest'
})

// 策略类型选项
const strategyTypes = [
  { label: '趋势跟踪', value: 'trend_following' },
  { label: '均值回归', value: 'mean_reversion' },
  { label: '套利策略', value: 'arbitrage' },
  { label: '机器学习', value: 'machine_learning' },
  { label: '多因子', value: 'multi_factor' },
  { label: '量化选股', value: 'stock_selection' }
]

// 可用标签
const availableTags = [
  '趋势跟踪', '均值回归', '动量策略', '价值投资',
  '技术分析', '基本面分析', '量化选股', '择时策略',
  '多因子', '机器学习', '高频交易', '套利策略',
  '低风险', '稳健收益', '高收益', '新手友好'
]

// 计算属性
const hasActiveFilters = computed(() => {
  return filters.type || filters.riskLevel || filters.returnRange || 
         filters.status || filters.tags.length > 0
})

const activeFilterCount = computed(() => {
  let count = 0
  if (filters.type) count++
  if (filters.riskLevel) count++
  if (filters.returnRange) count++
  if (filters.status) count++
  count += filters.tags.length
  return count
})

// 防抖搜索
const debouncedSearch = debounce((query: string) => {
  emit('search', query)
}, 300)

// 方法
const handleSearch = () => {
  debouncedSearch(searchQuery.value)
}

const handleClear = () => {
  searchQuery.value = ''
  emit('search', '')
}

const handleFilterChange = () => {
  emit('filter', { ...filters })
}

const handleTagChange = (tag: string, checked: boolean) => {
  if (checked) {
    if (!filters.tags.includes(tag)) {
      filters.tags.push(tag)
    }
  } else {
    const index = filters.tags.indexOf(tag)
    if (index > -1) {
      filters.tags.splice(index, 1)
    }
  }
  handleFilterChange()
}

const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

const clearAllFilters = () => {
  Object.assign(filters, {
    type: '',
    riskLevel: '',
    returnRange: '',
    status: '',
    tags: [],
    sortBy: 'latest'
  })
  handleFilterChange()
}

const getTypeLabel = (type: string) => {
  const typeObj = strategyTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getRiskLabel = (risk: string) => {
  const map: Record<string, string> = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return map[risk] || risk
}

const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    testing: '测试中',
    paused: '已暂停'
  }
  return map[status] || status
}

// 监听搜索输入
watch(searchQuery, (newValue) => {
  if (newValue.trim()) {
    debouncedSearch(newValue)
  }
})
</script>

<style scoped>
.strategy-search {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-bar {
  margin-bottom: 16px;

  .search-input {
    max-width: 600px;
  }
}

.advanced-filters {
  padding: 20px 0;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  margin: 16px 0;

  .filter-group {
    margin-bottom: 16px;

    .filter-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    .el-select {
      width: 100%;
    }
  }

  .tag-filters {
    margin: 20px 0;

    .filter-label {
      display: block;
      margin-bottom: 12px;
      font-weight: 500;
      color: #333;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .sort-options {
    margin-top: 20px;

    .filter-label {
      display: block;
      margin-bottom: 12px;
      font-weight: 500;
      color: #333;
    }
  }
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .toggle-advanced {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .active-filters {
    display: flex;
    align-items: center;
    gap: 12px;

    .filter-count {
      color: #666;
      font-size: 14px;
    }
  }
}

.quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e6e6e6;
}
</style>

import { ref, computed } from 'vue'
import { MarketAPI } from '@/api/market'
import type { IndicatorType, TechnicalIndicator, IndicatorConfig } from '@/types/chart'

// 创建 MarketAPI 实例
const marketApi = new MarketAPI()

export const useIndicators = () => {
  const activeIndicators = ref<string[]>([])
  const indicatorConfigs = ref<Record<string, IndicatorConfig>>({})
  const calculatedIndicators = ref<Record<string, any>>({})
  const loading = ref(false)

  // 可用指标配置
  const availableIndicators: IndicatorType[] = [
    { value: 'MA', label: '移动平均线' },
    { value: 'MACD', label: 'MACD' },
    { value: 'RSI', label: 'RSI' },
    { value: 'KDJ', label: 'KDJ' },
    { value: 'BOLL', label: '布林带' },
    { value: 'VOL', label: '成交量' },
    { value: 'OBV', label: 'OBV' },
    { value: 'CCI', label: 'CCI' },
    { value: 'WR', label: 'WR' },
    { value: 'BIAS', label: 'BIAS' }
  ]

  // 添加指标
  const addIndicator = async (indicatorType: string, symbol: string) => {
    if (!activeIndicators.value.includes(indicatorType)) {
      activeIndicators.value.push(indicatorType)
      
      // 设置默认配置
      setDefaultConfig(indicatorType)
      
      // 从后端获取指标数据
      await fetchIndicatorData(indicatorType, symbol)
    }
  }

  // 移除指标
  const removeIndicator = (indicatorType: string) => {
    const index = activeIndicators.value.indexOf(indicatorType)
    if (index > -1) {
      activeIndicators.value.splice(index, 1)
      delete indicatorConfigs.value[indicatorType]
      delete calculatedIndicators.value[indicatorType]
    }
  }

  // 更新指标配置
  const updateIndicatorConfig = async (indicatorType: string, config: IndicatorConfig, symbol: string) => {
    indicatorConfigs.value[indicatorType] = { ...config }
    // 重新从后端获取指标数据
    await fetchIndicatorData(indicatorType, symbol)
  }

  // 设置默认配置
  const setDefaultConfig = (indicatorType: string) => {
    const defaultConfigs: Record<string, IndicatorConfig> = {
      MA: { periods: [5, 10, 20, 60] },
      MACD: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
      RSI: { period: 14 },
      KDJ: { kPeriod: 9, dPeriod: 3, jPeriod: 3 },
      BOLL: { period: 20, multiplier: 2 },
      VOL: { periods: [5, 10] },
      OBV: {},
      CCI: { period: 14 },
      WR: { period: 14 },
      BIAS: { periods: [6, 12, 24] }
    }

    indicatorConfigs.value[indicatorType] = defaultConfigs[indicatorType] || {}
  }

  // 从后端获取指标数据 - 这是唯一的数据源
  const fetchIndicatorData = async (indicatorType: string, symbol: string) => {
    if (!symbol) return

    loading.value = true
    
    try {
      const config = indicatorConfigs.value[indicatorType]
      
      // 调用后端API获取技术指标数据
      const indicatorData = await marketApi.getTechnicalIndicator({
        symbol,
        indicator: indicatorType,
        config,
        period: '1d', // 可配置
        limit: 500    // 可配置
      })
      
      calculatedIndicators.value[indicatorType] = indicatorData
    } catch (error) {
      console.error(`Failed to fetch ${indicatorType} data:`, error)
      // 清空错误的指标数据
      delete calculatedIndicators.value[indicatorType]
    } finally {
      loading.value = false
    }
  }

  // 批量更新指标数据
  const updateIndicatorsData = async (symbol: string) => {
    if (!symbol || activeIndicators.value.length === 0) return

    loading.value = true
    
    try {
      // 并行获取所有活跃指标的数据
      const promises = activeIndicators.value.map(indicatorType => 
        fetchIndicatorData(indicatorType, symbol)
      )
      
      await Promise.all(promises)
    } catch (error) {
      console.error('Failed to update indicators data:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取指标标签
  const getIndicatorLabel = (indicatorType: string) => {
    const indicator = availableIndicators.find(item => item.value === indicatorType)
    return indicator?.label || indicatorType
  }

  // 获取指标配置组件
  const getIndicatorConfigComponent = (indicatorType: string) => {
    // 返回对应的配置组件名称
    return `${indicatorType}Config`
  }

  // 清空所有指标数据
  const clearAllIndicators = () => {
    activeIndicators.value = []
    indicatorConfigs.value = {}
    calculatedIndicators.value = {}
  }

  return {
    availableIndicators,
    activeIndicators,
    indicatorConfigs,
    calculatedIndicators,
    loading,
    addIndicator,
    removeIndicator,
    updateIndicatorConfig,
    fetchIndicatorData,
    updateIndicatorsData,
    getIndicatorLabel,
    getIndicatorConfigComponent,
    clearAllIndicators
  }
}
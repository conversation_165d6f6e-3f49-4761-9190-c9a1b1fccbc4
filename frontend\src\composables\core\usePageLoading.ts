/**
 * 页面加载状态管理
 * 
 * 提供全局的页面加载状态管理，改善用户体验
 */
import { ref, computed, readonly } from 'vue'

interface LoadingState {
  show: boolean
  text: string
  tips: string
  startTime: number
}

// 全局加载状态
const loadingState = ref<LoadingState>({
  show: false,
  text: '加载中...',
  tips: '请稍候，系统正在处理您的请求',
  startTime: 0
})

// 加载超时检测
const LOADING_TIMEOUT = 10000 // 10秒超时
let timeoutTimer: NodeJS.Timeout | null = null

export function usePageLoading() {
  
  /**
   * 显示加载状态
   */
  const showLoading = (text?: string, tips?: string) => {
    // 清除之前的定时器
    if (timeoutTimer) {
      clearTimeout(timeoutTimer)
    }
    
    loadingState.value = {
      show: true,
      text: text || '加载中...',
      tips: tips || '请稍候，系统正在处理您的请求',
      startTime: Date.now()
    }
    
    // 设置超时检测
    timeoutTimer = setTimeout(() => {
      if (loadingState.value.show) {
        loadingState.value.tips = '加载时间较长，请检查网络连接...'
      }
    }, LOADING_TIMEOUT)
  }
  
  /**
   * 隐藏加载状态
   */
  const hideLoading = () => {
    if (timeoutTimer) {
      clearTimeout(timeoutTimer)
      timeoutTimer = null
    }
    
    loadingState.value.show = false
  }
  
  /**
   * 带超时的加载包装器
   */
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    options?: {
      text?: string
      tips?: string
      timeout?: number
    }
  ): Promise<T> => {
    const { text, tips, timeout = LOADING_TIMEOUT } = options || {}
    
    showLoading(text, tips)
    
    try {
      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('请求超时，请检查网络连接'))
        }, timeout)
      })
      
      // 执行异步操作，与超时竞争
      const result = await Promise.race([
        asyncFn(),
        timeoutPromise
      ])
      
      return result
    } finally {
      hideLoading()
    }
  }
  
  /**
   * 页面级加载包装器
   */
  const withPageLoading = async <T>(
    asyncFn: () => Promise<T>,
    pageName?: string
  ): Promise<T> => {
    return withLoading(
      asyncFn,
      {
        text: `加载${pageName || '页面'}中...`,
        tips: '正在获取数据，请稍候...'
      }
    )
  }
  
  /**
   * API 请求加载包装器
   */
  const withApiLoading = async <T>(
    asyncFn: () => Promise<T>,
    apiName?: string
  ): Promise<T> => {
    return withLoading(
      asyncFn,
      {
        text: `请求${apiName || '数据'}中...`,
        tips: '正在与服务器通信...'
      }
    )
  }
  
  // 计算属性
  const isLoading = computed(() => loadingState.value.show)
  const loadingText = computed(() => loadingState.value.text)
  const loadingTips = computed(() => loadingState.value.tips)
  const loadingDuration = computed(() => {
    if (!loadingState.value.show) return 0
    return Date.now() - loadingState.value.startTime
  })
  
  return {
    // 状态
    isLoading,
    loadingText,
    loadingTips,
    loadingDuration,
    loadingState: readonly(loadingState),
    
    // 方法
    showLoading,
    hideLoading,
    withLoading,
    withPageLoading,
    withApiLoading
  }
}

// 导出单例实例
export const globalLoading = usePageLoading()
/**
 * 策略监控面板 - 实时策略状态监控和性能分析
 * 包含策略状态监控、性能指标、风险管理、告警系统等功能
 */

import { ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useWebSocket } from '@/composables/useWebSocket'
import { strategyApi } from '@/api/strategy'
import { useNotification } from '@/composables/useNotification'
import type { 
  Strategy, 
  StrategyStatus, 
  StrategyPerformance, 
  StrategyRisk,
  StrategySignal,
  StrategyLog,
  StrategyAlert,
  StrategyMetrics
} from '@/types/strategy'

export interface StrategyMonitorState {
  // 策略列表
  strategies: Strategy[]
  
  // 实时状态
  activeStrategies: Map<string, StrategyStatus>
  
  // 性能指标
  performanceMetrics: Map<string, StrategyPerformance>
  
  // 风险指标
  riskMetrics: Map<string, StrategyRisk>
  
  // 信号记录
  signals: Map<string, StrategySignal[]>
  
  // 日志记录
  logs: Map<string, StrategyLog[]>
  
  // 告警记录
  alerts: StrategyAlert[]
  
  // 系统状态
  systemStatus: {
    isRunning: boolean
    totalStrategies: number
    activeStrategies: number
    pausedStrategies: number
    errorStrategies: number
    totalPnL: number
    todayPnL: number
    maxDrawdown: number
    systemLoad: number
  }
}

export function useStrategyMonitor() {
  const ws = useWebSocket()
  const notification = useNotification()
  
  // 监控状态
  const state = reactive<StrategyMonitorState>({
    strategies: [],
    activeStrategies: new Map(),
    performanceMetrics: new Map(),
    riskMetrics: new Map(),
    signals: new Map(),
    logs: new Map(),
    alerts: [],
    systemStatus: {
      isRunning: false,
      totalStrategies: 0,
      activeStrategies: 0,
      pausedStrategies: 0,
      errorStrategies: 0,
      totalPnL: 0,
      todayPnL: 0,
      maxDrawdown: 0,
      systemLoad: 0
    }
  })

  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedStrategy = ref<string | null>(null)
  const monitoringInterval = ref<number | null>(null)

  // 计算属性
  const runningStrategies = computed(() => {
    return state.strategies.filter(s => s.status === 'running')
  })

  const pausedStrategies = computed(() => {
    return state.strategies.filter(s => s.status === 'paused')
  })

  const errorStrategies = computed(() => {
    return state.strategies.filter(s => s.status === 'error')
  })

  const totalPnL = computed(() => {
    return Array.from(state.performanceMetrics.values())
      .reduce((sum, perf) => sum + perf.totalPnL, 0)
  })

  const todayPnL = computed(() => {
    return Array.from(state.performanceMetrics.values())
      .reduce((sum, perf) => sum + perf.todayPnL, 0)
  })

  const avgWinRate = computed(() => {
    const performances = Array.from(state.performanceMetrics.values())
    if (performances.length === 0) return 0
    
    return performances.reduce((sum, perf) => sum + perf.winRate, 0) / performances.length
  })

  const highRiskStrategies = computed(() => {
    return state.strategies.filter(s => {
      const risk = state.riskMetrics.get(s.id)
      return risk && risk.riskLevel === 'high'
    })
  })

  const recentAlerts = computed(() => {
    return state.alerts
      .filter(alert => !alert.isRead)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
  })

  // 初始化监控系统
  const initializeMonitor = async () => {
    try {
      loading.value = true
      error.value = null

      // 1. 加载策略列表
      await loadStrategies()

      // 2. 加载性能数据
      await loadPerformanceMetrics()

      // 3. 加载风险数据
      await loadRiskMetrics()

      // 4. 订阅实时更新
      subscribeToUpdates()

      // 5. 启动定时刷新
      startPeriodicRefresh()

      // 6. 更新系统状态
      updateSystemStatus()

      console.log('策略监控系统初始化完成')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      console.error('策略监控系统初始化失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 加载策略列表
  const loadStrategies = async () => {
    try {
      const strategies = await strategyApi.getStrategies()
      state.strategies = strategies
      
      // 初始化状态映射
      strategies.forEach(strategy => {
        state.activeStrategies.set(strategy.id, {
          id: strategy.id,
          name: strategy.name,
          status: strategy.status,
          lastUpdate: new Date().toISOString(),
          uptime: 0,
          errorCount: 0,
          signalCount: 0
        })
      })
    } catch (err) {
      throw new Error('加载策略列表失败')
    }
  }

  // 加载性能指标
  const loadPerformanceMetrics = async () => {
    try {
      for (const strategy of state.strategies) {
        const performance = await strategyApi.getStrategyPerformance(strategy.id)
        state.performanceMetrics.set(strategy.id, performance)
      }
    } catch (err) {
      console.error('加载性能指标失败:', err)
    }
  }

  // 加载风险指标
  const loadRiskMetrics = async () => {
    try {
      for (const strategy of state.strategies) {
        const risk = await strategyApi.getStrategyRisk(strategy.id)
        state.riskMetrics.set(strategy.id, risk)
      }
    } catch (err) {
      console.error('加载风险指标失败:', err)
    }
  }

  // 订阅实时更新
  const subscribeToUpdates = () => {
    // 订阅策略状态更新
    ws.subscribeStrategyStatus('*', (statusData) => {
      handleStrategyStatusUpdate(statusData)
    })

    // 订阅策略信号
    state.strategies.forEach(strategy => {
      ws.subscribeStrategySignals?.(strategy.id, (signalData) => {
        handleStrategySignal(strategy.id, signalData)
      })
    })

    // 订阅策略日志
    state.strategies.forEach(strategy => {
      ws.subscribeStrategyLogs?.(strategy.id, (logData) => {
        handleStrategyLog(strategy.id, logData)
      })
    })
  }

  // 处理策略状态更新
  const handleStrategyStatusUpdate = (statusData: any) => {
    const { strategyId, status, metrics } = statusData
    
    // 更新策略状态
    const strategyStatus = state.activeStrategies.get(strategyId)
    if (strategyStatus) {
      strategyStatus.status = status
      strategyStatus.lastUpdate = new Date().toISOString()
      strategyStatus.uptime = metrics?.uptime || 0
      strategyStatus.errorCount = metrics?.errorCount || 0
      strategyStatus.signalCount = metrics?.signalCount || 0
    }

    // 更新策略对象
    const strategy = state.strategies.find(s => s.id === strategyId)
    if (strategy) {
      strategy.status = status
      strategy.lastUpdate = new Date().toISOString()
    }

    // 更新性能指标
    if (metrics?.performance) {
      state.performanceMetrics.set(strategyId, metrics.performance)
    }

    // 更新风险指标
    if (metrics?.risk) {
      state.riskMetrics.set(strategyId, metrics.risk)
    }

    // 检查告警条件
    checkAlertConditions(strategyId, statusData)

    // 更新系统状态
    updateSystemStatus()
  }

  // 处理策略信号
  const handleStrategySignal = (strategyId: string, signalData: any) => {
    const signals = state.signals.get(strategyId) || []
    signals.unshift(signalData)
    
    // 保留最近100条信号
    if (signals.length > 100) {
      signals.splice(100)
    }
    
    state.signals.set(strategyId, signals)

    // 更新信号计数
    const strategyStatus = state.activeStrategies.get(strategyId)
    if (strategyStatus) {
      strategyStatus.signalCount++
    }
  }

  // 处理策略日志
  const handleStrategyLog = (strategyId: string, logData: any) => {
    const logs = state.logs.get(strategyId) || []
    logs.unshift(logData)
    
    // 保留最近500条日志
    if (logs.length > 500) {
      logs.splice(500)
    }
    
    state.logs.set(strategyId, logs)

    // 如果是错误日志，更新错误计数
    if (logData.level === 'error') {
      const strategyStatus = state.activeStrategies.get(strategyId)
      if (strategyStatus) {
        strategyStatus.errorCount++
      }
    }
  }

  // 检查告警条件
  const checkAlertConditions = (strategyId: string, statusData: any) => {
    const strategy = state.strategies.find(s => s.id === strategyId)
    if (!strategy) return

    const performance = state.performanceMetrics.get(strategyId)
    const risk = state.riskMetrics.get(strategyId)

    // 检查策略状态告警
    if (statusData.status === 'error') {
      createAlert({
        id: `${strategyId}_error_${Date.now()}`,
        strategyId,
        strategyName: strategy.name,
        type: 'error',
        level: 'high',
        title: '策略运行错误',
        message: `策略 ${strategy.name} 发生运行错误`,
        timestamp: new Date().toISOString(),
        isRead: false
      })
    }

    // 检查性能告警
    if (performance) {
      if (performance.drawdown > 0.1) { // 回撤超过10%
        createAlert({
          id: `${strategyId}_drawdown_${Date.now()}`,
          strategyId,
          strategyName: strategy.name,
          type: 'risk',
          level: 'medium',
          title: '回撤告警',
          message: `策略 ${strategy.name} 回撤达到 ${(performance.drawdown * 100).toFixed(2)}%`,
          timestamp: new Date().toISOString(),
          isRead: false
        })
      }

      if (performance.winRate < 0.3) { // 胜率低于30%
        createAlert({
          id: `${strategyId}_winrate_${Date.now()}`,
          strategyId,
          strategyName: strategy.name,
          type: 'performance',
          level: 'low',
          title: '胜率告警',
          message: `策略 ${strategy.name} 胜率降至 ${(performance.winRate * 100).toFixed(2)}%`,
          timestamp: new Date().toISOString(),
          isRead: false
        })
      }
    }

    // 检查风险告警
    if (risk && risk.riskLevel === 'high') {
      createAlert({
        id: `${strategyId}_risk_${Date.now()}`,
        strategyId,
        strategyName: strategy.name,
        type: 'risk',
        level: 'high',
        title: '高风险告警',
        message: `策略 ${strategy.name} 风险等级为高风险`,
        timestamp: new Date().toISOString(),
        isRead: false
      })
    }
  }

  // 创建告警
  const createAlert = (alert: StrategyAlert) => {
    // 检查是否已存在相同告警
    const existingAlert = state.alerts.find(a => 
      a.strategyId === alert.strategyId && 
      a.type === alert.type && 
      !a.isRead
    )

    if (existingAlert) {
      // 更新现有告警
      existingAlert.message = alert.message
      existingAlert.timestamp = alert.timestamp
    } else {
      // 添加新告警
      state.alerts.unshift(alert)
      
      // 限制告警数量
      if (state.alerts.length > 1000) {
        state.alerts.splice(1000)
      }

      // 发送通知
      sendNotification(alert)
    }
  }

  // 发送通知
  const sendNotification = (alert: StrategyAlert) => {
    const notificationConfig = {
      title: alert.title,
      message: alert.message,
      type: alert.level === 'high' ? 'error' : alert.level === 'medium' ? 'warning' : 'info',
      duration: alert.level === 'high' ? 0 : 4500
    }

    ElNotification(notificationConfig)
  }

  // 启动策略
  const startStrategy = async (strategyId: string) => {
    try {
      await strategyApi.startStrategy(strategyId)
      
      // 更新本地状态
      const strategy = state.strategies.find(s => s.id === strategyId)
      if (strategy) {
        strategy.status = 'running'
      }
      
      const strategyStatus = state.activeStrategies.get(strategyId)
      if (strategyStatus) {
        strategyStatus.status = 'running'
        strategyStatus.lastUpdate = new Date().toISOString()
      }

      updateSystemStatus()
      ElMessage.success('策略启动成功')
    } catch (err) {
      ElMessage.error('策略启动失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 暂停策略
  const pauseStrategy = async (strategyId: string) => {
    try {
      await strategyApi.pauseStrategy(strategyId)
      
      // 更新本地状态
      const strategy = state.strategies.find(s => s.id === strategyId)
      if (strategy) {
        strategy.status = 'paused'
      }
      
      const strategyStatus = state.activeStrategies.get(strategyId)
      if (strategyStatus) {
        strategyStatus.status = 'paused'
        strategyStatus.lastUpdate = new Date().toISOString()
      }

      updateSystemStatus()
      ElMessage.success('策略暂停成功')
    } catch (err) {
      ElMessage.error('策略暂停失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 停止策略
  const stopStrategy = async (strategyId: string) => {
    try {
      await strategyApi.stopStrategy(strategyId)
      
      // 更新本地状态
      const strategy = state.strategies.find(s => s.id === strategyId)
      if (strategy) {
        strategy.status = 'stopped'
      }
      
      const strategyStatus = state.activeStrategies.get(strategyId)
      if (strategyStatus) {
        strategyStatus.status = 'stopped'
        strategyStatus.lastUpdate = new Date().toISOString()
      }

      updateSystemStatus()
      ElMessage.success('策略停止成功')
    } catch (err) {
      ElMessage.error('策略停止失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 重启策略
  const restartStrategy = async (strategyId: string) => {
    try {
      await strategyApi.restartStrategy(strategyId)
      
      // 更新本地状态
      const strategy = state.strategies.find(s => s.id === strategyId)
      if (strategy) {
        strategy.status = 'running'
      }
      
      const strategyStatus = state.activeStrategies.get(strategyId)
      if (strategyStatus) {
        strategyStatus.status = 'running'
        strategyStatus.lastUpdate = new Date().toISOString()
        strategyStatus.errorCount = 0 // 重置错误计数
      }

      updateSystemStatus()
      ElMessage.success('策略重启成功')
    } catch (err) {
      ElMessage.error('策略重启失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 批量操作
  const batchStartStrategies = async (strategyIds: string[]) => {
    try {
      await strategyApi.batchStartStrategies(strategyIds)
      
      // 更新本地状态
      strategyIds.forEach(strategyId => {
        const strategy = state.strategies.find(s => s.id === strategyId)
        if (strategy) {
          strategy.status = 'running'
        }
        
        const strategyStatus = state.activeStrategies.get(strategyId)
        if (strategyStatus) {
          strategyStatus.status = 'running'
          strategyStatus.lastUpdate = new Date().toISOString()
        }
      })

      updateSystemStatus()
      ElMessage.success(`成功启动 ${strategyIds.length} 个策略`)
    } catch (err) {
      ElMessage.error('批量启动失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  const batchPauseStrategies = async (strategyIds: string[]) => {
    try {
      await strategyApi.batchPauseStrategies(strategyIds)
      
      // 更新本地状态
      strategyIds.forEach(strategyId => {
        const strategy = state.strategies.find(s => s.id === strategyId)
        if (strategy) {
          strategy.status = 'paused'
        }
        
        const strategyStatus = state.activeStrategies.get(strategyId)
        if (strategyStatus) {
          strategyStatus.status = 'paused'
          strategyStatus.lastUpdate = new Date().toISOString()
        }
      })

      updateSystemStatus()
      ElMessage.success(`成功暂停 ${strategyIds.length} 个策略`)
    } catch (err) {
      ElMessage.error('批量暂停失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 获取策略详细信息
  const getStrategyDetails = (strategyId: string) => {
    const strategy = state.strategies.find(s => s.id === strategyId)
    const status = state.activeStrategies.get(strategyId)
    const performance = state.performanceMetrics.get(strategyId)
    const risk = state.riskMetrics.get(strategyId)
    const signals = state.signals.get(strategyId) || []
    const logs = state.logs.get(strategyId) || []

    return {
      strategy,
      status,
      performance,
      risk,
      signals,
      logs
    }
  }

  // 导出策略数据
  const exportStrategyData = async (strategyId: string, dataType: string) => {
    try {
      const data = await strategyApi.exportStrategyData(strategyId, dataType)
      
      // 创建下载链接
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `strategy_${strategyId}_${dataType}_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('数据导出成功')
    } catch (err) {
      ElMessage.error('数据导出失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 标记告警为已读
  const markAlertAsRead = (alertId: string) => {
    const alert = state.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.isRead = true
    }
  }

  // 清除所有告警
  const clearAllAlerts = () => {
    state.alerts.forEach(alert => {
      alert.isRead = true
    })
  }

  // 更新系统状态
  const updateSystemStatus = () => {
    const totalStrategies = state.strategies.length
    const activeStrategies = state.strategies.filter(s => s.status === 'running').length
    const pausedStrategies = state.strategies.filter(s => s.status === 'paused').length
    const errorStrategies = state.strategies.filter(s => s.status === 'error').length

    state.systemStatus = {
      isRunning: activeStrategies > 0,
      totalStrategies,
      activeStrategies,
      pausedStrategies,
      errorStrategies,
      totalPnL: totalPnL.value,
      todayPnL: todayPnL.value,
      maxDrawdown: Math.max(...Array.from(state.performanceMetrics.values()).map(p => p.drawdown), 0),
      systemLoad: Math.min((activeStrategies / totalStrategies) * 100, 100)
    }
  }

  // 定时刷新
  const startPeriodicRefresh = () => {
    monitoringInterval.value = window.setInterval(async () => {
      try {
        await loadPerformanceMetrics()
        await loadRiskMetrics()
        updateSystemStatus()
      } catch (err) {
        console.error('定时刷新失败:', err)
      }
    }, 30000) // 每30秒刷新一次
  }

  // 停止定时刷新
  const stopPeriodicRefresh = () => {
    if (monitoringInterval.value) {
      clearInterval(monitoringInterval.value)
      monitoringInterval.value = null
    }
  }

  // 生命周期管理
  onMounted(() => {
    initializeMonitor()
  })

  onUnmounted(() => {
    stopPeriodicRefresh()
  })

  return {
    // 状态
    state,
    loading,
    error,
    selectedStrategy,
    
    // 计算属性
    runningStrategies,
    pausedStrategies,
    errorStrategies,
    totalPnL,
    todayPnL,
    avgWinRate,
    highRiskStrategies,
    recentAlerts,
    
    // 方法
    initializeMonitor,
    startStrategy,
    pauseStrategy,
    stopStrategy,
    restartStrategy,
    batchStartStrategies,
    batchPauseStrategies,
    getStrategyDetails,
    exportStrategyData,
    markAlertAsRead,
    clearAllAlerts,
    updateSystemStatus
  }
} 
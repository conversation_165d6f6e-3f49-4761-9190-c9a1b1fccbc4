/**
 * 交易流程管理 - 完整的交易生命周期管理
 * 包含下单、订单监控、持仓管理、风险控制等功能
 */

import { ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWebSocket } from '@/composables/useWebSocket'
import { useOrders } from './useOrders'
import { usePositions } from './usePositions'
import { tradingApi } from '@/api/trading'
import { marketApi } from '@/api/market'
import type { 
  Order, 
  Position, 
  TradeSignal, 
  RiskMetrics,
  TradingAccount,
  OrderRequest,
  OrderValidationResult
} from '@/types/trading'

export interface TradingFlowState {
  // 交易状态
  isTrading: boolean
  isPaused: boolean
  isEmergencyStop: boolean
  
  // 账户信息
  account: TradingAccount | null
  
  // 实时数据
  orders: Order[]
  positions: Position[]
  signals: TradeSignal[]
  
  // 风险指标
  riskMetrics: RiskMetrics
  
  // 统计数据
  todayStats: {
    totalOrders: number
    filledOrders: number
    cancelledOrders: number
    totalVolume: number
    totalPnL: number
    winRate: number
    avgHoldTime: number
  }
}

export function useTradingFlow() {
  const ws = useWebSocket()
  const ordersComposable = useOrders()
  const positionsComposable = usePositions()
  
  // 交易流程状态
  const state = reactive<TradingFlowState>({
    isTrading: false,
    isPaused: false,
    isEmergencyStop: false,
    account: null,
    orders: [],
    positions: [],
    signals: [],
    riskMetrics: {
      totalRisk: 0,
      maxDrawdown: 0,
      currentDrawdown: 0,
      leverageRatio: 0,
      concentrationRisk: 0,
      riskScore: 0
    },
    todayStats: {
      totalOrders: 0,
      filledOrders: 0,
      cancelledOrders: 0,
      totalVolume: 0,
      totalPnL: 0,
      winRate: 0,
      avgHoldTime: 0
    }
  })

  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const canTrade = computed(() => {
    return state.isTrading && 
           !state.isPaused && 
           !state.isEmergencyStop && 
           state.account?.status === 'active'
  })

  const totalPnL = computed(() => {
    const positions = Array.isArray(state.positions) ? state.positions : []
    return positions.reduce((sum, pos) => sum + (pos.unrealizedPnL || 0), 0)
  })

  const totalMarketValue = computed(() => {
    const positions = Array.isArray(state.positions) ? state.positions : []
    return positions.reduce((sum, pos) => sum + pos.marketValue, 0)
  })

  const availableFunds = computed(() => {
    return state.account?.availableFunds || 0
  })

  const riskLevel = computed(() => {
    const score = state.riskMetrics.riskScore
    if (score > 80) return 'high'
    if (score > 60) return 'medium'
    return 'low'
  })

  // 初始化交易系统
  const initializeTradingSystem = async () => {
    try {
      loading.value = true
      error.value = null

      // 1. 获取账户信息
      await loadAccountInfo()

      // 2. 加载订单和持仓
      await Promise.all([
        loadOrders(),
        loadPositions()
      ])

      // 3. 订阅实时更新
      subscribeToUpdates()

      // 4. 计算风险指标
      calculateRiskMetrics()

      // 5. 启动交易系统
      state.isTrading = true

      console.log('交易系统初始化完成')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      console.error('交易系统初始化失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 加载账户信息
  const loadAccountInfo = async () => {
    try {
      const account = await tradingApi.getAccountInfo()
      state.account = account
    } catch (err) {
      throw new Error('获取账户信息失败')
    }
  }

  // 加载订单数据
  const loadOrders = async () => {
    try {
      const orders = await tradingApi.getOrders()
      state.orders = orders
      updateTodayStats()
    } catch (err) {
      throw new Error('获取订单数据失败')
    }
  }

  // 加载持仓数据
  const loadPositions = async () => {
    try {
      const positions = await tradingApi.getPositions()
      state.positions = positions
    } catch (err) {
      throw new Error('获取持仓数据失败')
    }
  }

  // 订阅实时更新
  const subscribeToUpdates = () => {
    // 订阅订单更新
    ws.subscribeTradingOrders((orderData) => {
      handleOrderUpdate(orderData)
    })

    // 订阅持仓更新
    ws.subscribeTradingPositions((positionData) => {
      handlePositionUpdate(positionData)
    })

    // 订阅成交更新
    ws.subscribeTradingTrades((tradeData) => {
      handleTradeUpdate(tradeData)
    })
  }

  // 处理订单更新
  const handleOrderUpdate = (orderData: any) => {
    const existingIndex = state.orders.findIndex(o => o.id === orderData.id)
    
    if (existingIndex >= 0) {
      // 更新现有订单
      state.orders[existingIndex] = { ...state.orders[existingIndex], ...orderData }
    } else {
      // 新订单
      state.orders.unshift(orderData)
    }

    updateTodayStats()
    calculateRiskMetrics()
  }

  // 处理持仓更新
  const handlePositionUpdate = (positionData: any) => {
    const existingIndex = state.positions.findIndex(p => p.symbol === positionData.symbol)
    
    if (existingIndex >= 0) {
      if (positionData.quantity === 0) {
        // 持仓清零，移除
        state.positions.splice(existingIndex, 1)
      } else {
        // 更新现有持仓
        state.positions[existingIndex] = { ...state.positions[existingIndex], ...positionData }
      }
    } else if (positionData.quantity > 0) {
      // 新持仓
      state.positions.push(positionData)
    }

    calculateRiskMetrics()
  }

  // 处理成交更新
  const handleTradeUpdate = (tradeData: any) => {
    // 更新相关订单的成交信息
    const order = state.orders.find(o => o.id === tradeData.orderId)
    if (order) {
      order.filledQuantity = (order.filledQuantity || 0) + tradeData.quantity
      order.avgFillPrice = tradeData.price
      order.fillPercent = (order.filledQuantity / order.quantity) * 100
      
      if (order.filledQuantity >= order.quantity) {
        order.status = 'filled'
      } else {
        order.status = 'partial'
      }
    }

    // 更新持仓
    updatePositionFromTrade(tradeData)
    
    // 更新统计
    updateTodayStats()
    calculateRiskMetrics()
  }

  // 从成交更新持仓
  const updatePositionFromTrade = (tradeData: any) => {
    const position = state.positions.find(p => p.symbol === tradeData.symbol)
    
    if (position) {
      // 更新现有持仓
      const newQuantity = tradeData.side === 'buy' 
        ? position.quantity + tradeData.quantity
        : position.quantity - tradeData.quantity

      if (newQuantity === 0) {
        // 持仓清零
        const index = state.positions.findIndex(p => p.symbol === tradeData.symbol)
        state.positions.splice(index, 1)
      } else {
        // 更新持仓数量和成本
        position.quantity = newQuantity
        position.avgCost = calculateNewAvgCost(position, tradeData)
        position.marketValue = position.quantity * tradeData.currentPrice
        position.unrealizedPnL = (tradeData.currentPrice - position.avgCost) * position.quantity
      }
    } else if (tradeData.side === 'buy') {
      // 新建持仓
      state.positions.push({
        symbol: tradeData.symbol,
        symbolName: tradeData.symbolName,
        quantity: tradeData.quantity,
        avgCost: tradeData.price,
        currentPrice: tradeData.currentPrice,
        marketValue: tradeData.quantity * tradeData.currentPrice,
        unrealizedPnL: (tradeData.currentPrice - tradeData.price) * tradeData.quantity,
        realizedPnL: 0,
        holdDays: 0,
        side: 'long'
      })
    }
  }

  // 计算新的平均成本
  const calculateNewAvgCost = (position: Position, tradeData: any): number => {
    if (tradeData.side === 'buy') {
      const totalCost = position.avgCost * position.quantity + tradeData.price * tradeData.quantity
      return totalCost / (position.quantity + tradeData.quantity)
    } else {
      // 卖出不影响平均成本
      return position.avgCost
    }
  }

  // 下单前验证
  const validateOrder = async (orderRequest: OrderRequest): Promise<OrderValidationResult> => {
    const validationResult: OrderValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      riskScore: 0
    }

    // 1. 基础验证
    if (!orderRequest.symbol || !orderRequest.quantity || !orderRequest.price) {
      validationResult.isValid = false
      validationResult.errors.push('订单参数不完整')
      return validationResult
    }

    // 2. 资金检查
    const requiredFunds = orderRequest.quantity * orderRequest.price
    if (orderRequest.side === 'buy' && requiredFunds > availableFunds.value) {
      validationResult.isValid = false
      validationResult.errors.push('资金不足')
    }

    // 3. 持仓检查
    if (orderRequest.side === 'sell') {
      const position = state.positions.find(p => p.symbol === orderRequest.symbol)
      if (!position || position.quantity < orderRequest.quantity) {
        validationResult.isValid = false
        validationResult.errors.push('持仓不足')
      }
    }

    // 4. 风险检查
    const riskAssessment = await assessOrderRisk(orderRequest)
    validationResult.riskScore = riskAssessment.score
    validationResult.warnings.push(...riskAssessment.warnings)

    if (riskAssessment.score > 80) {
      validationResult.isValid = false
      validationResult.errors.push('订单风险过高')
    }

    return validationResult
  }

  // 评估订单风险
  const assessOrderRisk = async (orderRequest: OrderRequest) => {
    const warnings: string[] = []
    let score = 0

    // 1. 集中度风险
    const orderValue = orderRequest.quantity * orderRequest.price
    const totalValue = totalMarketValue.value + orderValue
    const concentration = orderValue / totalValue

    if (concentration > 0.3) {
      score += 30
      warnings.push('单一标的集中度过高')
    } else if (concentration > 0.2) {
      score += 20
      warnings.push('单一标的集中度较高')
    }

    // 2. 杠杆风险
    const leverageRatio = totalValue / (state.account?.totalAssets || 1)
    if (leverageRatio > 0.8) {
      score += 25
      warnings.push('杠杆率过高')
    }

    // 3. 波动率风险
    try {
      const volatility = await marketApi.getHistoricalVolatility(orderRequest.symbol)
      if (volatility > 0.3) {
        score += 20
        warnings.push('标的波动率较高')
      }
    } catch (err) {
      console.warn('获取波动率失败:', err)
    }

    // 4. 流动性风险
    const position = state.positions.find(p => p.symbol === orderRequest.symbol)
    if (position && orderRequest.side === 'sell') {
      const sellRatio = orderRequest.quantity / position.quantity
      if (sellRatio > 0.5) {
        score += 15
        warnings.push('大额卖出可能影响流动性')
      }
    }

    return { score, warnings }
  }

  // 提交订单
  const submitOrder = async (orderRequest: OrderRequest): Promise<Order> => {
    if (!canTrade.value) {
      throw new Error('当前不允许交易')
    }

    // 验证订单
    const validation = await validateOrder(orderRequest)
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '))
    }

    // 风险警告
    if (validation.warnings.length > 0) {
      const confirmed = await ElMessageBox.confirm(
        `订单存在以下风险：\n${validation.warnings.join('\n')}\n\n是否继续提交？`,
        '风险提示',
        { type: 'warning' }
      )
      if (confirmed !== 'confirm') {
        throw new Error('用户取消订单')
      }
    }

    try {
      // 提交订单到服务器
      const order = await tradingApi.submitOrder(orderRequest)
      
      // 更新本地状态
      state.orders.unshift(order)
      updateTodayStats()
      calculateRiskMetrics()

      ElMessage.success('订单提交成功')
      return order
    } catch (err) {
      throw new Error('订单提交失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 撤销订单
  const cancelOrder = async (orderId: string): Promise<void> => {
    const order = state.orders.find(o => o.id === orderId)
    if (!order) {
      throw new Error('订单不存在')
    }

    if (!['pending', 'partial'].includes(order.status)) {
      throw new Error('订单状态不允许撤销')
    }

    try {
      await tradingApi.cancelOrder(orderId)
      
      // 更新本地状态
      order.status = 'cancelled'
      order.updateTime = new Date().toISOString()
      
      updateTodayStats()
      ElMessage.success('订单撤销成功')
    } catch (err) {
      throw new Error('订单撤销失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 批量撤销订单
  const batchCancelOrders = async (orderIds: string[]): Promise<void> => {
    try {
      await tradingApi.batchCancelOrders(orderIds)
      
      // 更新本地状态
      orderIds.forEach(orderId => {
        const order = state.orders.find(o => o.id === orderId)
        if (order) {
          order.status = 'cancelled'
          order.updateTime = new Date().toISOString()
        }
      })
      
      updateTodayStats()
      ElMessage.success(`成功撤销 ${orderIds.length} 个订单`)
    } catch (err) {
      throw new Error('批量撤销失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 平仓
  const closePosition = async (symbol: string, quantity?: number): Promise<void> => {
    const position = state.positions.find(p => p.symbol === symbol)
    if (!position) {
      throw new Error('持仓不存在')
    }

    const sellQuantity = quantity || position.quantity
    if (sellQuantity > position.quantity) {
      throw new Error('平仓数量超过持仓数量')
    }

    try {
      // 获取当前价格
      const quote = await marketApi.getQuote(symbol)
      const currentPrice = quote[0]?.currentPrice || position.currentPrice

      // 创建平仓订单
      const orderRequest: OrderRequest = {
        symbol,
        side: 'sell',
        orderType: 'market',
        quantity: sellQuantity,
        price: currentPrice,
        timeInForce: 'IOC' // 立即成交或撤销
      }

      await submitOrder(orderRequest)
      ElMessage.success('平仓订单提交成功')
    } catch (err) {
      throw new Error('平仓失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 全部平仓
  const closeAllPositions = async (): Promise<void> => {
    if (state.positions.length === 0) {
      ElMessage.info('暂无持仓需要平仓')
      return
    }

    const confirmed = await ElMessageBox.confirm(
      `确定要平仓所有 ${state.positions.length} 个持仓吗？`,
      '确认平仓',
      { type: 'warning' }
    )

    if (confirmed !== 'confirm') {
      return
    }

    try {
      const promises = state.positions.map(position => closePosition(position.symbol))
      await Promise.all(promises)
      ElMessage.success('全部平仓订单提交成功')
    } catch (err) {
      ElMessage.error('部分平仓失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 紧急停止
  const emergencyStop = async (): Promise<void> => {
    const confirmed = await ElMessageBox.confirm(
      '紧急停止将撤销所有挂单并停止交易，确定继续吗？',
      '紧急停止',
      { type: 'error' }
    )

    if (confirmed !== 'confirm') {
      return
    }

    try {
      state.isEmergencyStop = true
      
      // 撤销所有挂单
      const pendingOrders = state.orders.filter(o => ['pending', 'partial'].includes(o.status))
      if (pendingOrders.length > 0) {
        await batchCancelOrders(pendingOrders.map(o => o.id))
      }

      // 停止交易
      state.isTrading = false
      
      ElMessage.warning('紧急停止已激活，所有交易已暂停')
    } catch (err) {
      ElMessage.error('紧急停止执行失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 恢复交易
  const resumeTrading = async (): Promise<void> => {
    try {
      state.isEmergencyStop = false
      state.isPaused = false
      state.isTrading = true
      
      // 重新加载数据
      await Promise.all([
        loadAccountInfo(),
        loadOrders(),
        loadPositions()
      ])
      
      calculateRiskMetrics()
      ElMessage.success('交易已恢复')
    } catch (err) {
      ElMessage.error('恢复交易失败: ' + (err instanceof Error ? err.message : '未知错误'))
    }
  }

  // 计算风险指标
  const calculateRiskMetrics = () => {
    const totalValue = totalMarketValue.value
    const totalAssets = state.account?.totalAssets || 0
    
    // 计算集中度风险
    const concentrationRisk = state.positions.length > 0 
      ? Math.max(...state.positions.map(p => p.marketValue / totalValue)) * 100
      : 0

    // 计算杠杆比率
    const leverageRatio = totalAssets > 0 ? totalValue / totalAssets : 0

    // 计算当前回撤
    const currentDrawdown = state.account?.maxDrawdown || 0

    // 计算综合风险评分
    let riskScore = 0
    riskScore += Math.min(concentrationRisk, 40) // 集中度风险最多40分
    riskScore += Math.min(leverageRatio * 30, 30) // 杠杆风险最多30分
    riskScore += Math.min(currentDrawdown * 30, 30) // 回撤风险最多30分

    state.riskMetrics = {
      totalRisk: riskScore,
      maxDrawdown: state.account?.maxDrawdown || 0,
      currentDrawdown,
      leverageRatio,
      concentrationRisk,
      riskScore
    }
  }

  // 更新今日统计
  const updateTodayStats = () => {
    const today = new Date().toDateString()
    const orders = Array.isArray(state.orders) ? state.orders : []
    const todayOrders = orders.filter(o =>
      new Date(o.createTime).toDateString() === today
    )

    const filledOrders = todayOrders.filter(o => o.status === 'filled')
    const cancelledOrders = todayOrders.filter(o => o.status === 'cancelled')

    const totalVolume = filledOrders.reduce((sum, o) => sum + (o.filledQuantity || 0) * o.price, 0)
    const positions = Array.isArray(state.positions) ? state.positions : []
    const totalPnL = positions.reduce((sum, p) => sum + (p.unrealizedPnL || 0), 0)

    const winRate = filledOrders.length > 0 
      ? (filledOrders.filter(o => (o.avgFillPrice || 0) > o.price).length / filledOrders.length) * 100
      : 0

    state.todayStats = {
      totalOrders: todayOrders.length,
      filledOrders: filledOrders.length,
      cancelledOrders: cancelledOrders.length,
      totalVolume,
      totalPnL,
      winRate,
      avgHoldTime: 0 // 需要更复杂的计算
    }
  }

  // 生命周期管理
  onMounted(() => {
    initializeTradingSystem()
  })

  onUnmounted(() => {
    // 清理资源
    state.isTrading = false
  })

  return {
    // 状态
    state,
    loading,
    error,
    
    // 计算属性
    canTrade,
    totalPnL,
    totalMarketValue,
    availableFunds,
    riskLevel,
    
    // 方法
    initializeTradingSystem,
    submitOrder,
    cancelOrder,
    batchCancelOrders,
    closePosition,
    closeAllPositions,
    emergencyStop,
    resumeTrading,
    validateOrder,
    calculateRiskMetrics,
    updateTodayStats
  }
} 
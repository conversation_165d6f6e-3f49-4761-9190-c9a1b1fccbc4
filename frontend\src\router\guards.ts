/**
 * 路由守卫配置
 */
import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authConfig } from '@/config'
import { globalLoading } from '@/composables/core/usePageLoading'

/**
 * 设置路由守卫
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 显示页面加载状态
    if (to.path !== from.path) {
      const pageName = to.meta?.title || '页面'
      globalLoading.showLoading(`加载${pageName}中...`, '正在准备页面内容...')
    }

    // 动态导入userStore，避免在模块加载时就使用Pinia
    const { useUserStore } = await import('@/stores/modules/user')
    const userStore = useUserStore()

    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 量化投资平台`
    } else {
      document.title = '量化投资平台'
    }

    // 检查是否需要认证
    const requiresAuth = to.meta.requiresAuth !== false // 默认需要认证

    // 白名单路由（不需要认证）
    const whiteList = [
      '/login', '/register', '/test-slider', '/test-captcha', '/puzzle-verify',
      '/demo', '/api-test', '/error-test', '/api-debug', '/test-puzzle', '/puzzle-simple'
    ]

    if (whiteList.includes(to.path)) {
      // 如果已登录且访问登录页，重定向到主页
      if (to.path === '/login' && userStore.isLoggedIn) {
        next(authConfig.defaultRedirect)
        return
      }
      next()
      return
    }

    // 检查用户是否已登录
    if (requiresAuth && !userStore.isLoggedIn) {
      // 开发环境自动登录
      if (import.meta.env.DEV) {
        console.log('🔧 开发环境自动登录')
        try {
          await userStore.autoLogin()
          next()
          return
        } catch (error) {
          console.warn('自动登录失败，但开发环境允许继续访问:', error)
          // 开发环境即使自动登录失败也允许访问
          next()
          return
        }
      }

      ElMessage.warning('请先登录')
      next({
        path: authConfig.loginPath,
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查用户权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      // 安全地获取用户权限，避免undefined错误
      const userPermissions = userStore?.permissions?.value || userStore?.permissions || []
      const hasPermission = to.meta.permissions.some((permission: string) =>
        userPermissions.includes(permission)
      )

      if (!hasPermission) {
        ElMessage.error('没有访问权限')
        next('/403')
        return
      }
    }

    // 检查用户角色
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      const hasRole = to.meta.roles.includes(userStore.userInfo?.role)

      if (!hasRole) {
        ElMessage.error('角色权限不足')
        next('/403')
        return
      }
    }

    next()
  })

  // 全局后置守卫
  router.afterEach((to, from) => {
    // 隐藏页面加载状态
    // 延迟一点时间以避免闪烁
    setTimeout(() => {
      globalLoading.hideLoading()
    }, 300)

    // 记录页面访问
    if (import.meta.env.DEV) {
      console.log(`🧭 路由跳转: ${from.path} -> ${to.path}`)
    }

    // 页面访问统计
    // analytics.track('page_view', {
    //   page: to.path,
    //   title: to.meta.title,
    //   from: from.path
    // })
  })

  // 路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    ElMessage.error('页面加载失败，请刷新重试')
  })
}

/**
 * 检查路由权限
 */
export function checkRoutePermission(route: any, userPermissions: string[]): boolean {
  if (!route.meta?.permissions) {
    return true
  }

  return route.meta.permissions.some((permission: string) =>
    userPermissions.includes(permission)
  )
}

/**
 * 检查用户角色
 */
export function checkUserRole(route: any, userRole: string): boolean {
  if (!route.meta?.roles) {
    return true
  }

  return route.meta.roles.includes(userRole)
}

/**
 * 获取重定向路径
 */
export function getRedirectPath(to: any, userRole: string): string {
  // 根据用户角色返回不同的默认页面
  const roleRedirects: Record<string, string> = {
    admin: '/dashboard',
    trader: '/trading',
    analyst: '/market',
    user: '/portfolio'
  }

  return roleRedirects[userRole] || authConfig.defaultRedirect
}

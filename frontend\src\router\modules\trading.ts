import type { RouteRecordRaw } from 'vue-router'

const tradingRoutes: RouteRecordRaw[] = [
  // 主路由重定向到交易终端
  {
    path: '/trading',
    redirect: '/trading/terminal'
  },

  // 主要交易终端 - 集成了模拟和实盘交易功能
  {
    path: '/trading/terminal',
    name: 'TradingTerminal',
    component: () => import('@/views/Trading/TradingTerminal.vue'),
    meta: {
      title: '交易终端',
      requiresAuth: true,
      icon: 'TrendCharts',
      description: '专业交易终端，支持模拟和实盘交易'
    }
  },

  // 模拟交易入口页面（重定向到交易终端）
  {
    path: '/trading/simulated',
    name: 'SimulatedTrading',
    component: () => import('@/views/Trading/SimulatedTrading.vue'),
    meta: {
      title: '模拟交易',
      requiresAuth: true,
      icon: 'School',
      description: '模拟交易入口，引导用户使用专业交易终端'
    }
  },

  // MiniQMT专业实盘交易
  {
    path: '/trading/miniqmt',
    name: 'MiniQMTTrading',
    component: () => import('@/views/Trading/LiveTrading.vue'),
    meta: {
      title: 'MiniQMT实盘',
      requiresAuth: true,
      icon: 'Coin',
      description: '基于MiniQMT的专业实盘交易终端'
    }
  },

  // 订单管理
  {
    path: '/trading/orders',
    name: 'OrderManagement',
    component: () => import('@/views/Trading/OrderManagement.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
      icon: 'Document',
      description: '查看和管理所有交易订单'
    }
  },

  // 持仓管理
  {
    path: '/trading/positions',
    name: 'PositionManagement',
    component: () => import('@/views/Trading/PositionManagement.vue'),
    meta: {
      title: '持仓管理',
      requiresAuth: true,
      icon: 'PieChart',
      description: '查看和管理投资组合持仓'
    }
  }
]

export default tradingRoutes

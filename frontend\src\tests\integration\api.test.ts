import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { http } from '@/api/http'
import { loginAPI, logoutAPI } from '@/api/user'
import { getQuoteAPI, getKLineDataAPI } from '@/api/market'
import { createOrderAPI, getOrdersAPI } from '@/api/trading'

// Mock axios
const mockAxios = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  create: vi.fn(() => mockAxios),
  defaults: { baseURL: '', headers: {} },
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() }
  }
}

vi.mock('axios', () => ({
  default: mockAxios,
  create: vi.fn(() => mockAxios)
}))

describe('API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('User API', () => {
    it('登录API应该发送正确的请求', async () => {
      const mockResponse = {
        data: {
          user: { id: '1', username: 'testuser' },
          token: 'mock-token',
          refreshToken: 'mock-refresh-token'
        }
      }
      
      mockAxios.post.mockResolvedValue(mockResponse)

      const loginData = { username: 'testuser', password: 'password123' }
      const result = await loginAPI(loginData)

      expect(mockAxios.post).toHaveBeenCalledWith('/api/v1/auth/login', loginData)
      expect(result).toEqual(mockResponse.data)
    })

    it('登出API应该发送正确的请求', async () => {
      const mockResponse = { data: { message: '登出成功' } }
      
      mockAxios.post.mockResolvedValue(mockResponse)

      const result = await logoutAPI()

      expect(mockAxios.post).toHaveBeenCalledWith('/api/v1/auth/logout')
      expect(result).toEqual(mockResponse.data)
    })

    it('API错误应该被正确处理', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { message: '用户名或密码错误' }
        }
      }
      
      mockAxios.post.mockRejectedValue(mockError)

      await expect(loginAPI({ username: 'test', password: 'wrong' }))
        .rejects.toThrow('用户名或密码错误')
    })
  })

  describe('Market API', () => {
    it('获取报价API应该返回正确数据', async () => {
      const mockQuote = {
        symbol: 'AAPL',
        price: 150.25,
        change: 2.5,
        changePercent: 1.69,
        volume: 1000000,
        timestamp: Date.now()
      }
      
      const mockResponse = { data: mockQuote }
      mockAxios.get.mockResolvedValue(mockResponse)

      const result = await getQuoteAPI('AAPL')

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/market/quote/AAPL')
      expect(result).toEqual(mockQuote)
    })

    it('获取K线数据API应该处理查询参数', async () => {
      const mockKLineData = [
        {
          timestamp: Date.now(),
          open: 150.00,
          high: 152.00,
          low: 149.00,
          close: 151.50,
          volume: 1000000
        }
      ]
      
      const mockResponse = { data: mockKLineData }
      mockAxios.get.mockResolvedValue(mockResponse)

      const result = await getKLineDataAPI('AAPL', '1d', 100)

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/market/kline/AAPL', {
        params: { interval: '1d', limit: 100 }
      })
      expect(result).toEqual(mockKLineData)
    })
  })

  describe('Trading API', () => {
    it('创建订单API应该发送正确的订单数据', async () => {
      const mockOrder = {
        id: 'order-123',
        symbol: 'AAPL',
        side: 'buy',
        quantity: 100,
        price: 150.25,
        status: 'pending'
      }
      
      const mockResponse = { data: mockOrder }
      mockAxios.post.mockResolvedValue(mockResponse)

      const orderData = {
        symbol: 'AAPL',
        side: 'buy' as const,
        quantity: 100,
        price: 150.25,
        type: 'limit' as const
      }
      
      const result = await createOrderAPI(orderData)

      expect(mockAxios.post).toHaveBeenCalledWith('/api/v1/trading/order', orderData)
      expect(result).toEqual(mockOrder)
    })

    it('获取订单列表API应该处理分页参数', async () => {
      const mockOrders = [
        { id: 'order-1', symbol: 'AAPL', status: 'filled' },
        { id: 'order-2', symbol: 'GOOGL', status: 'pending' }
      ]
      
      const mockResponse = {
        data: {
          orders: mockOrders,
          total: 2,
          page: 1,
          pageSize: 10
        }
      }
      mockAxios.get.mockResolvedValue(mockResponse)

      const result = await getOrdersAPI({ page: 1, pageSize: 10, status: 'all' })

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/trading/orders', {
        params: { page: 1, pageSize: 10, status: 'all' }
      })
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('HTTP拦截器', () => {
    it('请求拦截器应该添加认证头', () => {
      // 模拟localStorage中有token
      const mockToken = 'mock-jwt-token'
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: vi.fn(() => mockToken),
          setItem: vi.fn(),
          removeItem: vi.fn()
        },
        writable: true
      })

      // 验证拦截器被注册
      expect(mockAxios.interceptors.request.use).toHaveBeenCalled()
    })

    it('响应拦截器应该处理401错误', () => {
      // 验证响应拦截器被注册
      expect(mockAxios.interceptors.response.use).toHaveBeenCalled()
    })
  })

  describe('错误处理', () => {
    it('网络错误应该被正确处理', async () => {
      const networkError = new Error('Network Error')
      mockAxios.get.mockRejectedValue(networkError)

      await expect(getQuoteAPI('AAPL')).rejects.toThrow('网络连接错误，请检查网络设置')
    })

    it('服务器错误应该被正确处理', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: '服务器内部错误' }
        }
      }
      mockAxios.get.mockRejectedValue(serverError)

      await expect(getQuoteAPI('AAPL')).rejects.toThrow('服务器内部错误')
    })

    it('超时错误应该被正确处理', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded'
      }
      mockAxios.get.mockRejectedValue(timeoutError)

      await expect(getQuoteAPI('AAPL')).rejects.toThrow('请求超时，请稍后重试')
    })
  })
})
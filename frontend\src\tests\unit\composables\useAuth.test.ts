import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useAuth } from '@/composables/useAuth'
import { createPinia, setActivePinia } from 'pinia'

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn()
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter
}))

// Mock notification
const mockNotify = vi.fn()
vi.mock('@/composables/useNotification', () => ({
  useNotification: () => ({ notify: mockNotify })
}))

describe('useAuth Composable', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('应该返回正确的初始状态', () => {
    const { isAuthenticated, user, loading } = useAuth()
    
    expect(isAuthenticated.value).toBe(false)
    expect(user.value).toBeNull()
    expect(loading.value).toBe(false)
  })

  it('登录成功后应该跳转到仪表板', async () => {
    const { login } = useAuth()
    
    // Mock successful login
    vi.mock('@/stores/modules/auth', () => ({
      useAuthStore: () => ({
        login: vi.fn().mockResolvedValue({}),
        isAuthenticated: true,
        loading: false
      })
    }))

    await login({ username: 'test', password: 'password' })

    expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard')
    expect(mockNotify).toHaveBeenCalledWith({
      type: 'success',
      title: '登录成功',
      message: '欢迎回来！'
    })
  })

  it('登录失败应该显示错误消息', async () => {
    const { login } = useAuth()
    const error = new Error('登录失败')
    
    // Mock failed login
    vi.mock('@/stores/modules/auth', () => ({
      useAuthStore: () => ({
        login: vi.fn().mockRejectedValue(error),
        isAuthenticated: false,
        loading: false
      })
    }))

    await expect(login({ username: 'test', password: 'wrong' })).rejects.toThrow()
    
    expect(mockNotify).toHaveBeenCalledWith({
      type: 'error',
      title: '登录失败',
      message: error.message
    })
  })

  it('登出后应该跳转到登录页', async () => {
    const { logout } = useAuth()
    
    // Mock successful logout
    vi.mock('@/stores/modules/auth', () => ({
      useAuthStore: () => ({
        logout: vi.fn().mockResolvedValue({}),
        isAuthenticated: false
      })
    }))

    await logout()

    expect(mockRouter.replace).toHaveBeenCalledWith('/login')
    expect(mockNotify).toHaveBeenCalledWith({
      type: 'success',
      title: '退出成功',
      message: '您已安全退出系统'
    })
  })

  it('权限检查应该正确工作', () => {
    const { hasRole, hasPermission } = useAuth()
    
    // Mock auth store with user data
    vi.mock('@/stores/modules/auth', () => ({
      useAuthStore: () => ({
        hasRole: vi.fn((role: string) => role === 'user'),
        hasPermission: vi.fn((permission: string) => permission === 'read:market')
      })
    }))

    expect(hasRole('user')).toBe(true)
    expect(hasRole('admin')).toBe(false)
    expect(hasPermission('read:market')).toBe(true)
    expect(hasPermission('admin:users')).toBe(false)
  })

  it('刷新令牌应该在后台执行', async () => {
    const { refreshToken } = useAuth()
    
    // Mock successful token refresh
    vi.mock('@/stores/modules/auth', () => ({
      useAuthStore: () => ({
        refreshAccessToken: vi.fn().mockResolvedValue({})
      })
    }))

    await expect(refreshToken()).resolves.toBeUndefined()
  })
})
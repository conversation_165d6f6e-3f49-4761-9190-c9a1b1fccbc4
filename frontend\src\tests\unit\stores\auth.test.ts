import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useAuthStore } from '@/stores/modules/auth'
import type { LoginForm, User } from '@/types/user'

// Mock API
const mockAPI = {
  login: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  getCurrentUser: vi.fn()
}

vi.mock('@/api/user', () => mockAPI)

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('初始状态应该正确', () => {
    const authStore = useAuthStore()
    
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.loading).toBe(false)
  })

  it('登录成功应该更新状态', async () => {
    const authStore = useAuthStore()
    const mockUser: User = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      roles: ['user']
    }
    const mockToken = 'mock-jwt-token'

    mockAPI.login.mockResolvedValue({
      user: mockUser,
      token: mockToken,
      refreshToken: 'mock-refresh-token'
    })

    const loginForm: LoginForm = {
      username: 'testuser',
      password: 'password123'
    }

    await authStore.login(loginForm)

    expect(authStore.isAuthenticated).toBe(true)
    expect(authStore.user).toEqual(mockUser)
    expect(authStore.token).toBe(mockToken)
    expect(authStore.loading).toBe(false)
  })

  it('登录失败应该抛出错误', async () => {
    const authStore = useAuthStore()
    const error = new Error('登录失败')
    
    mockAPI.login.mockRejectedValue(error)

    const loginForm: LoginForm = {
      username: 'testuser',
      password: 'wrongpassword'
    }

    await expect(authStore.login(loginForm)).rejects.toThrow('登录失败')
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.loading).toBe(false)
  })

  it('登出应该清除状态', async () => {
    const authStore = useAuthStore()
    
    // 先设置登录状态
    authStore.$patch({
      user: { id: '1', username: 'testuser' } as User,
      token: 'mock-token',
      refreshToken: 'mock-refresh-token'
    })

    mockAPI.logout.mockResolvedValue({})

    await authStore.logout()

    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
  })

  it('刷新令牌应该更新token', async () => {
    const authStore = useAuthStore()
    const newToken = 'new-jwt-token'
    
    authStore.$patch({
      refreshToken: 'mock-refresh-token'
    })

    mockAPI.refreshToken.mockResolvedValue({
      token: newToken,
      refreshToken: 'new-refresh-token'
    })

    await authStore.refreshAccessToken()

    expect(authStore.token).toBe(newToken)
  })

  it('hasRole方法应该正确检查权限', () => {
    const authStore = useAuthStore()
    
    authStore.$patch({
      user: {
        id: '1',
        username: 'testuser',
        roles: ['user', 'trader']
      } as User
    })

    expect(authStore.hasRole('user')).toBe(true)
    expect(authStore.hasRole('trader')).toBe(true)
    expect(authStore.hasRole('admin')).toBe(false)
  })

  it('hasPermission方法应该正确检查权限', () => {
    const authStore = useAuthStore()
    
    authStore.$patch({
      user: {
        id: '1',
        username: 'testuser',
        permissions: ['read:market', 'write:orders']
      } as User
    })

    expect(authStore.hasPermission('read:market')).toBe(true)
    expect(authStore.hasPermission('write:orders')).toBe(true)
    expect(authStore.hasPermission('admin:users')).toBe(false)
  })
})
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useMarketStore } from '@/stores/modules/market'
import type { Quote, KLineData } from '@/types/market'

// Mock API
const mockAPI = {
  getQuote: vi.fn(),
  getKLineData: vi.fn(),
  searchStocks: vi.fn()
}

vi.mock('@/api/market', () => mockAPI)

describe('Market Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('初始状态应该正确', () => {
    const marketStore = useMarketStore()
    
    expect(marketStore.quotes).toEqual({})
    expect(marketStore.klineData).toEqual({})
    expect(marketStore.watchlist).toEqual([])
    expect(marketStore.loading).toBe(false)
  })

  it('更新报价应该成功', () => {
    const marketStore = useMarketStore()
    const mockQuote: Quote = {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 150.25,
      change: 2.5,
      changePercent: 1.69,
      volume: 1000000,
      timestamp: Date.now(),
      bid: 150.20,
      ask: 150.30,
      high: 152.00,
      low: 149.50,
      open: 150.00
    }

    marketStore.updateQuote('AAPL', mockQuote)

    expect(marketStore.quotes['AAPL']).toEqual(mockQuote)
  })

  it('获取K线数据应该成功', async () => {
    const marketStore = useMarketStore()
    const mockKLineData: KLineData[] = [
      {
        timestamp: Date.now(),
        open: 150.00,
        high: 152.00,
        low: 149.00,
        close: 151.50,
        volume: 1000000
      }
    ]

    mockAPI.getKLineData.mockResolvedValue(mockKLineData)

    await marketStore.fetchKLineData('AAPL', '1d')

    expect(marketStore.klineData['AAPL_1d']).toEqual(mockKLineData)
    expect(mockAPI.getKLineData).toHaveBeenCalledWith('AAPL', '1d')
  })

  it('添加到关注列表应该成功', () => {
    const marketStore = useMarketStore()
    
    marketStore.addToWatchlist('AAPL')
    
    expect(marketStore.watchlist).toContain('AAPL')
  })

  it('从关注列表移除应该成功', () => {
    const marketStore = useMarketStore()
    
    marketStore.addToWatchlist('AAPL')
    marketStore.addToWatchlist('GOOGL')
    marketStore.removeFromWatchlist('AAPL')
    
    expect(marketStore.watchlist).not.toContain('AAPL')
    expect(marketStore.watchlist).toContain('GOOGL')
  })

  it('搜索股票应该返回结果', async () => {
    const marketStore = useMarketStore()
    const mockResults = [
      { symbol: 'AAPL', name: 'Apple Inc.' },
      { symbol: 'GOOGL', name: 'Alphabet Inc.' }
    ]

    mockAPI.searchStocks.mockResolvedValue(mockResults)

    const results = await marketStore.searchStocks('APP')

    expect(results).toEqual(mockResults)
    expect(mockAPI.searchStocks).toHaveBeenCalledWith('APP')
  })

  it('批量更新报价应该成功', () => {
    const marketStore = useMarketStore()
    const mockQuotes: Record<string, Quote> = {
      'AAPL': {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        price: 150.25,
        change: 2.5,
        changePercent: 1.69,
        volume: 1000000,
        timestamp: Date.now(),
        bid: 150.20,
        ask: 150.30,
        high: 152.00,
        low: 149.50,
        open: 150.00
      },
      'GOOGL': {
        symbol: 'GOOGL',
        name: 'Alphabet Inc.',
        price: 2500.75,
        change: -10.25,
        changePercent: -0.41,
        volume: 800000,
        timestamp: Date.now(),
        bid: 2500.50,
        ask: 2501.00,
        high: 2510.00,
        low: 2495.00,
        open: 2505.00
      }
    }

    marketStore.batchUpdateQuotes(mockQuotes)

    expect(marketStore.quotes['AAPL']).toEqual(mockQuotes['AAPL'])
    expect(marketStore.quotes['GOOGL']).toEqual(mockQuotes['GOOGL'])
  })

  it('getter应该正确计算', () => {
    const marketStore = useMarketStore()
    
    marketStore.addToWatchlist('AAPL')
    marketStore.addToWatchlist('GOOGL')
    
    marketStore.updateQuote('AAPL', {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 150.25,
      change: 2.5,
      changePercent: 1.69,
      volume: 1000000,
      timestamp: Date.now(),
      bid: 150.20,
      ask: 150.30,
      high: 152.00,
      low: 149.50,
      open: 150.00
    })

    const watchlistQuotes = marketStore.watchlistQuotes
    expect(watchlistQuotes).toHaveLength(1)
    expect(watchlistQuotes[0].symbol).toBe('AAPL')
  })
})
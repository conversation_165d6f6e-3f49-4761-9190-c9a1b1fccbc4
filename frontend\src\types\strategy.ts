/**
 * 策略和回测相关类型定义
 *
 * 包含策略管理、执行、监控、回测等完整的类型定义
 * 遵循严格的TypeScript类型规范，避免使用any类型
 */

import type { CandlestickData } from './chart'
import type { Order, Position, Trade as TradingTrade } from './trading'

// ===== 基础枚举类型 =====

/**
 * 策略状态
 */
export type StrategyStatus =
  | 'draft'              // 草稿状态
  | 'testing'            // 测试中
  | 'running'            // 运行中
  | 'paused'             // 暂停
  | 'stopped'            // 已停止
  | 'error'              // 错误状态
  | 'completed'          // 已完成

/**
 * 策略类型
 */
export type StrategyType =
  | 'trend_following'    // 趋势跟踪
  | 'mean_reversion'     // 均值回归
  | 'momentum'           // 动量策略
  | 'arbitrage'          // 套利策略
  | 'factor_model'       // 因子模型
  | 'machine_learning'   // 机器学习
  | 'market_making'      // 做市策略
  | 'statistical'        // 统计套利
  | 'custom'             // 自定义策略

/**
 * 风险等级
 */
export type RiskLevel =
  | 'low'                // 低风险
  | 'medium'             // 中风险
  | 'high'               // 高风险
  | 'extreme'            // 极高风险

/**
 * 策略频率
 */
export type StrategyFrequency =
  | 'tick'               // 逐笔
  | '1min'               // 1分钟
  | '5min'               // 5分钟
  | '15min'              // 15分钟
  | '30min'              // 30分钟
  | '1hour'              // 1小时
  | '1day'               // 日线
  | '1week'              // 周线

/**
 * 信号类型
 */
export type SignalType =
  | 'buy'                // 买入信号
  | 'sell'               // 卖出信号
  | 'hold'               // 持有信号
  | 'close_long'         // 平多仓
  | 'close_short'        // 平空仓

// ===== 策略参数类型 =====

/**
 * 策略参数基础接口
 */
export interface StrategyParameterBase {
  name: string
  label: string
  description: string
  required?: boolean
  group?: string
}

/**
 * 数值参数
 */
export interface NumberParameter extends StrategyParameterBase {
  type: 'number'
  defaultValue: number
  min?: number
  max?: number
  step?: number
  unit?: string
}

/**
 * 字符串参数
 */
export interface StringParameter extends StrategyParameterBase {
  type: 'string'
  defaultValue: string
  maxLength?: number
  pattern?: string
  options?: string[]
}

/**
 * 布尔参数
 */
export interface BooleanParameter extends StrategyParameterBase {
  type: 'boolean'
  defaultValue: boolean
}

/**
 * 选择参数
 */
export interface SelectParameter extends StrategyParameterBase {
  type: 'select'
  defaultValue: string | number
  options: Array<{
    label: string
    value: string | number
  }>
}

/**
 * 策略参数联合类型
 */
export type StrategyParameter =
  | NumberParameter
  | StringParameter
  | BooleanParameter
  | SelectParameter

// ===== 策略核心接口 =====

/**
 * 策略配置
 */
export interface StrategyConfig {
  // 交易配置
  symbols: string[]
  maxPositions: number
  maxPositionSize: number
  frequency: StrategyFrequency

  // 风险控制
  stopLoss?: number
  takeProfit?: number
  maxDrawdown?: number

  // 资金管理
  initialCapital: number
  positionSizing: 'fixed' | 'percent' | 'kelly' | 'optimal_f'
  positionSizeValue: number

  // 交易成本
  commission: number
  slippage: number

  // 其他设置
  enableShortSelling: boolean
  enableLeverage: boolean
  maxLeverage?: number
}

/**
 * 策略基本信息
 */
export interface Strategy {
  id: string
  name: string
  description: string
  type: StrategyType
  status: StrategyStatus
  riskLevel: RiskLevel
  frequency: StrategyFrequency

  // 代码和参数
  code: string
  codeLanguage: 'python' | 'javascript' | 'pine'
  parameters: StrategyParameter[]
  config: StrategyConfig

  // 元数据
  tags: string[]
  version: string

  // 时间信息
  createdAt: string
  updatedAt: string
  lastRunAt?: string

  // 作者信息
  createdBy: string
  authorName: string
  isPublic: boolean

  // 运行统计
  runningTime?: number
  totalRuns: number
  successRuns: number
  failureRuns: number

  // 性能数据
  performance?: StrategyPerformance
}

/**
 * 策略性能指标
 */
export interface StrategyPerformance {
  currentNav: number
  totalReturn: number
  annualizedReturn: number
  maxDrawdown: number
  sharpeRatio: number
  sortinoRatio?: number
  todayPnl: number
  winRate: number
  volatility: number
  concentration: number
  profitFactor?: number
  totalTrades?: number
  avgTradeDuration?: number
  lastUpdate: Date
}

/**
 * 回测结果
 */
export interface BacktestResult {
  strategyId: string
  startDate: string
  endDate: string
  initialCapital: number
  finalCapital: number
  totalReturn: number
  annualizedReturn: number
  maxDrawdown: number
  sharpeRatio: number
  sortinoRatio: number
  winRate: number
  volatility: number
  profitFactor: number
  totalTrades: number
  avgTradeDuration: number
  trades: TradingTrade[]
  navCurve: NavPoint[]
  drawdownCurve: DrawdownPoint[]
  createdAt: string
}

/**
 * 交易记录
 */
export interface Trade {
  id: string
  strategyId: string
  symbol: string
  side: 'buy' | 'sell'
  quantity: number
  price: number
  amount: number
  fee: number
  pnl: number
  timestamp: string
  reason: string
}

/**
 * 净值点
 */
export interface NavPoint {
  date: string
  nav: number
  benchmark?: number
}

/**
 * 回撤点
 */
export interface DrawdownPoint {
  date: string
  drawdown: number
}

/**
 * 策略日志
 */
export interface StrategyLog {
  id: string
  strategyId: string
  level: 'info' | 'warning' | 'error' | 'debug'
  message: string
  timestamp: string
  data?: any
}

/**
 * 策略模板
 */
export interface StrategyTemplate {
  id: string
  name: string
  description: string
  type: StrategyType
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  code: string
  parameters: StrategyParameter[]
  documentation?: string
  author: string
  version: string
  downloads: number
  rating: number
}

/**
 * 策略创建请求
 */
export interface CreateStrategyRequest {
  name: string
  description: string
  type: StrategyType
  riskLevel: RiskLevel
  minCapital: number
  maxPositions: number
  code: string
  tags: string[]
  parameters: StrategyParameter[]
}

/**
 * 策略更新请求
 */
export interface UpdateStrategyRequest extends Partial<CreateStrategyRequest> {
  id: string
}

/**
 * 策略查询参数
 */
export interface StrategyQueryParams {
  page?: number
  pageSize?: number
  status?: StrategyStatus
  type?: StrategyType
  riskLevel?: RiskLevel
  keyword?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'performance'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 策略列表响应
 */
export interface StrategyListResponse {
  items: Strategy[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 策略实例 (运行中的策略)
export interface StrategyInstance {
  id: string
  strategyId: string
  name: string
  status: StrategyStatus
  startTime: string
  stopTime?: string
  capital: number
  currentValue: number
  pnl: number
  pnlPercent: number
  symbols: string[]
  live: boolean // 是否实盘
}

// 策略信号
export interface StrategySignal {
  id: string
  strategyInstanceId: string
  symbol: string
  signal: 'buy' | 'sell' | 'hold' | 'close_long' | 'close_short'
  strength?: number // 信号强度 (0-1)
  price?: number // 信号触发价格
  quantity?: number // 建议数量
  reason?: string // 信号原因
  timestamp: string
  meta?: Record<string, any> // 额外信息
}

// 回测配置
export interface BacktestConfig {
  strategyId: string
  name: string
  description?: string
  symbol: string
  timeFrame: string
  startDate: string
  endDate: string
  initialCapital: number
  commissionRate: number
  slippage: number // 滑点
  parameters?: Record<string, any>
}

// 绩效报告
export interface PerformanceReport {
  initialCapital: number
  finalCapital: number

  totalReturn: number
  annualizedReturn: number

  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number

  averageTradeReturn: number
  averageWinningTrade: number
  averageLosingTrade: number
  profitLossRatio: number

  maxDrawdown: number
  sharpeRatio: number
  sortinoRatio: number
  calmarRatio: number

  avgHoldingPeriod: number // in hours or days
  expectancy: number

  beta?: number
  alpha?: number
}

// 策略优化配置
export interface OptimizationConfig {
  strategyId: string
  symbol: string
  timeFrame: string
  startDate: string
  endDate: string
  parameterRanges: Array<{
    key: string
    start: number
    end: number
    step: number
  }>
  objective: keyof PerformanceReport // 优化目标, e.g., 'totalReturn', 'sharpeRatio'
}

// 优化结果
export interface OptimizationResult {
  id: string
  config: OptimizationConfig
  status: 'running' | 'completed' | 'failed'
  bestParameters: Record<string, any>
  bestPerformance: PerformanceReport
  results: Array<{
    parameters: Record<string, any>
    performance: PerformanceReport
  }>
}

// 蒙特卡洛模拟
export interface MonteCarloResult {
  simulations: number
  confidenceLevel: number
  meanReturn: number
  medianReturn: number
  returnDistribution: number[]
}

// 敏感性分析
export interface SensitivityAnalysisResult {
  parameter: string
  values: number[]
  performanceMetric: string
  metricValues: number[]
}

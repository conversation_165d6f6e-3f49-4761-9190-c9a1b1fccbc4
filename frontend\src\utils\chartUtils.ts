/**
 * 图表工具函数
 * 解决图表初始化时机问题
 */

import { nextTick } from 'vue'
import type { Ref } from 'vue'
import * as echarts from 'echarts'

export interface ChartInitOptions {
  maxRetries?: number
  retryDelay?: number
  onSuccess?: (chart: echarts.ECharts) => void
  onError?: (error: string) => void
  onRetry?: (retryCount: number) => void
}

/**
 * 安全的图表初始化函数
 * 解决DOM元素未准备好的问题
 */
export function safeInitChart(
  containerRef: Ref<HTMLElement | undefined>,
  options: ChartInitOptions = {}
): Promise<echarts.ECharts | null> {
  const {
    maxRetries = 10,
    retryDelay = 200,
    onSuccess,
    onError,
    onRetry
  } = options

  return new Promise((resolve) => {
    const attemptInit = (retryCount = 0) => {
      nextTick(() => {
        // 检查容器引用是否存在
        if (!containerRef.value) {
          if (retryCount < maxRetries) {
            const retryMsg = `图表容器未找到，延迟初始化 (${retryCount + 1}/${maxRetries})`
            console.warn(retryMsg)
            onRetry?.(retryCount + 1)
            setTimeout(() => attemptInit(retryCount + 1), retryDelay)
          } else {
            const errorMsg = `图表容器初始化失败：超过最大重试次数 (${maxRetries})`
            console.error(errorMsg)
            onError?.(errorMsg)
            resolve(null)
          }
          return
        }

        try {
          // 检查容器尺寸
          const container = containerRef.value
          const width = container.clientWidth || container.offsetWidth
          const height = container.clientHeight || container.offsetHeight

          console.log(`📊 图表容器尺寸检查: ${width}x${height}`)

          if (width === 0 || height === 0) {
            if (retryCount < maxRetries) {
              const retryMsg = `图表容器尺寸为0 (${width}x${height})，延迟初始化 (${retryCount + 1}/${maxRetries})`
              console.warn(retryMsg)
              onRetry?.(retryCount + 1)
              setTimeout(() => attemptInit(retryCount + 1), retryDelay)
            } else {
              const errorMsg = `图表容器尺寸为0：超过最大重试次数 (${maxRetries})`
              console.error(errorMsg)
              onError?.(errorMsg)
              resolve(null)
            }
            return
          }

          // 检查容器是否已经有图表实例
          const existingChart = echarts.getInstanceByDom(container)
          if (existingChart) {
            console.log('🔄 销毁已存在的图表实例')
            existingChart.dispose()
          }

          // 初始化图表
          console.log('🎨 开始初始化ECharts实例')
          const chart = echarts.init(container, undefined, {
            renderer: 'canvas',
            useDirtyRect: true
          })

          console.log('✅ 图表初始化成功')
          onSuccess?.(chart)
          resolve(chart)
        } catch (error) {
          const errorMsg = `图表初始化失败: ${error}`
          console.error(errorMsg, error)

          // 发送错误到全局错误处理
          if (typeof window !== 'undefined' && window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('chart-init-error', {
              detail: { error, container: containerRef.value }
            }))
          }

          onError?.(errorMsg)
          resolve(null)
        }
      })
    }

    attemptInit()
  })
}

/**
 * 图表容器检查函数
 * 检查容器是否准备好
 */
export function isChartContainerReady(containerRef: Ref<HTMLElement | undefined>): boolean {
  return !!(
    containerRef.value &&
    containerRef.value.offsetWidth > 0 &&
    containerRef.value.offsetHeight > 0
  )
}

/**
 * 等待图表容器准备好
 */
export function waitForChartContainer(
  containerRef: Ref<HTMLElement | undefined>,
  timeout = 5000
): Promise<boolean> {
  return new Promise((resolve) => {
    const startTime = Date.now()

    const checkContainer = () => {
      if (isChartContainerReady(containerRef)) {
        console.log('✅ 图表容器已准备好')
        resolve(true)
        return
      }

      if (Date.now() - startTime > timeout) {
        console.warn('⏰ 等待图表容器超时')
        resolve(false)
        return
      }

      setTimeout(checkContainer, 100)
    }

    checkContainer()
  })
}

/**
 * 强制等待DOM渲染完成
 */
export function waitForDOMReady(): Promise<void> {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve()
    } else {
      window.addEventListener('load', () => resolve(), { once: true })
    }
  })
}

/**
 * 增强的图表初始化函数
 * 结合多种策略确保图表能够成功初始化
 */
export async function enhancedInitChart(
  containerRef: Ref<HTMLElement | undefined>,
  options: ChartInitOptions & {
    waitForDOM?: boolean
    forceVisible?: boolean
  } = {}
): Promise<echarts.ECharts | null> {
  const { waitForDOM = true, forceVisible = true, ...chartOptions } = options

  try {
    // 等待DOM完全加载
    if (waitForDOM) {
      await waitForDOMReady()
      await nextTick()
    }

    // 强制容器可见（如果需要）
    if (forceVisible && containerRef.value) {
      const container = containerRef.value
      const originalDisplay = container.style.display
      const originalVisibility = container.style.visibility

      container.style.display = 'block'
      container.style.visibility = 'visible'

      // 等待样式应用
      await new Promise(resolve => setTimeout(resolve, 100))

      // 恢复原始样式（如果需要）
      if (originalDisplay) container.style.display = originalDisplay
      if (originalVisibility) container.style.visibility = originalVisibility
    }

    // 使用标准的安全初始化
    return await safeInitChart(containerRef, chartOptions)
  } catch (error) {
    console.error('增强图表初始化失败:', error)
    return null
  }
}

/**
 * 图表响应式处理
 */
export function setupChartResize(chart: echarts.ECharts): () => void {
  const resizeHandler = () => {
    chart?.resize()
  }

  window.addEventListener('resize', resizeHandler)

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}

/**
 * 图表默认配置
 */
export const defaultChartOptions = {
  animation: true,
  animationDuration: 300,
  animationEasing: 'cubicOut',
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}

/**
 * 创建默认的折线图配置
 */
export function createLineChartOption(data: any[], title?: string) {
  return {
    ...defaultChartOptions,
    title: title ? {
      text: title,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    } : undefined,
    xAxis: {
      type: 'category',
      data: data.map(item => item.name || item.time),
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [{
      type: 'line',
      data: data.map(item => item.value),
      smooth: true,
      lineStyle: {
        color: '#409eff',
        width: 2
      },
      itemStyle: {
        color: '#409eff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
          ]
        }
      }
    }]
  }
}

/**
 * 图表错误处理
 */
export function handleChartError(error: any, chartName = '图表') {
  console.error(`${chartName}错误:`, error)

  // 可以在这里添加错误上报逻辑
  // errorReporting.captureException(error)
}

/**
 * 图表性能监控
 */
export function monitorChartPerformance(chartName: string, startTime: number) {
  const endTime = performance.now()
  const duration = endTime - startTime

  console.log(`${chartName}初始化耗时: ${duration.toFixed(2)}ms`)

  // 可以在这里添加性能监控逻辑
  // performanceMonitor.recordTiming(chartName, duration)

  return duration
}

/**
 * 图表主题配置
 */
export const chartThemes = {
  light: {
    backgroundColor: '#ffffff',
    textColor: '#333333',
    axisColor: '#e0e0e0',
    splitLineColor: '#f0f0f0'
  },
  dark: {
    backgroundColor: '#1a1a1a',
    textColor: '#ffffff',
    axisColor: '#3a3a3a',
    splitLineColor: '#2a2a2a'
  }
}

/**
 * 应用图表主题
 */
export function applyChartTheme(chart: echarts.ECharts, theme: 'light' | 'dark' = 'light') {
  const themeConfig = chartThemes[theme]

  chart.setOption({
    backgroundColor: themeConfig.backgroundColor,
    textStyle: {
      color: themeConfig.textColor
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          color: themeConfig.axisColor
        }
      }
    },
    yAxis: {
      axisLine: {
        lineStyle: {
          color: themeConfig.axisColor
        }
      },
      splitLine: {
        lineStyle: {
          color: themeConfig.splitLineColor
        }
      }
    }
  })
}

/**
 * 工具函数统一导出
 * 
 * 提供项目中所有工具函数的统一入口，按功能分类导出
 * 避免循环依赖，提供清晰的模块结构
 */

// ===== 格式化工具 =====
export {
  // 金融数据格式化
  formatCurrency,
  formatPrice,
  formatPercent,
  formatChange,
  formatVolume,
  formatTurnover,
  formatMarketCap,
  formatPE,
  formatPB,
  safeCalculate,
  validateNumber,
  
  // 兼容性格式化函数
  formatCurrencyLegacy,
  formatPriceLegacy,
  formatPercentLegacy,
  formatChangeLegacy,
  formatVolumeLegacy
} from './formatters'

export {
  // 数字格式化
  formatNumber,
  formatDecimal,
  formatInteger,
  formatBytes,
  formatDuration
} from './format/number'

export {
  // 日期格式化
  formatDate,
  formatTime,
  formatDateTime,
  formatRelativeTime,
  formatTradingTime,
  parseDate,
  isValidDate,
  getDateRange,
  getTradingDays
} from './format/date'

// ===== 计算工具 =====
export {
  // 金融计算
  calculateReturn,
  calculateAnnualizedReturn,
  calculateVolatility,
  calculateSharpeRatio,
  calculateMaxDrawdown,
  calculateBeta,
  calculateCorrelation,
  calculateVaR,
  calculatePortfolioValue,
  calculatePositionSize
} from './calculation/financial'

// ===== 技术指标 =====
export {
  // 技术指标计算
  calculateSMA,
  calculateEMA,
  calculateMACD,
  calculateRSI,
  calculateBollingerBands,
  calculateKDJ,
  calculateATR,
  calculateStochastic,
  calculateWilliamsR,
  calculateCCI
} from './indicators/technical'

// ===== 验证工具 =====
export {
  // 表单验证
  validateEmail,
  validatePhone,
  validatePassword,
  validateUsername,
  validateRequired,
  validateRange,
  validatePattern,
  createValidator,
  combineValidators
} from './validation/form'

export {
  // 交易验证
  validateOrder,
  validatePosition,
  validateSymbol,
  validatePrice,
  validateQuantity,
  validateTradingTime,
  checkRiskLimits,
  checkMarginRequirement
} from './trading/validation'

export {
  // 通用验证
  isNumber,
  isString,
  isBoolean,
  isArray,
  isObject,
  isEmpty,
  isValidJSON,
  sanitizeInput,
  validateSchema
} from './validation/index'

// ===== 安全工具 =====
export {
  // 加密解密
  encrypt,
  decrypt,
  generateKey,
  hashPassword,
  verifyPassword,
  generateToken,
  verifyToken
} from './encryption'

export {
  // 安全检查
  sanitizeHtml,
  escapeHtml,
  validateCSRF,
  checkPermission,
  maskSensitiveData,
  detectXSS,
  validateOrigin
} from './security'

// ===== 认证工具 =====
export {
  // 认证相关
  getToken,
  setToken,
  removeToken,
  isTokenValid,
  refreshToken,
  getUserFromToken,
  checkAuthStatus,
  redirectToLogin
} from './auth'

// ===== 网络工具 =====
export {
  // WebSocket工具
  createWebSocket,
  closeWebSocket,
  sendMessage,
  subscribeToChannel,
  unsubscribeFromChannel,
  handleReconnect,
  getConnectionStatus
} from './websocket'

// ===== 性能优化 =====
export {
  // 性能优化工具
  debounce,
  throttle,
  memoize,
  lazyLoad,
  preloadImage,
  measurePerformance,
  optimizeBundle
} from './performance/optimizer'

// ===== 导出工具 =====
export {
  // 数据导出
  exportToCSV,
  exportToExcel,
  exportToPDF,
  exportToJSON,
  downloadFile,
  generateReport,
  createChartImage
} from './export/index'

// ===== 错误处理 =====
export {
  // 错误处理
  handleError,
  createErrorHandler,
  logError,
  reportError,
  showErrorMessage,
  retryOperation,
  ErrorBoundary
} from './error-handler'

// ===== 常量定义 =====
export {
  // 应用常量
  API_ENDPOINTS,
  HTTP_STATUS,
  ERROR_CODES,
  TRADING_CONSTANTS,
  CHART_CONSTANTS,
  UI_CONSTANTS,
  VALIDATION_RULES,
  DEFAULT_CONFIG
} from './constants'

// ===== 类型守卫 =====
export const isProduction = (): boolean => {
  return import.meta.env.PROD
}

export const isDevelopment = (): boolean => {
  return import.meta.env.DEV
}

export const isTest = (): boolean => {
  return import.meta.env.MODE === 'test'
}

// ===== 环境工具 =====
export const getEnvVar = (key: string, defaultValue?: string): string => {
  return import.meta.env[key] || defaultValue || ''
}

export const getApiBaseUrl = (): string => {
  return getEnvVar('VITE_API_BASE_URL', 'http://localhost:8000')
}

export const getWebSocketUrl = (): string => {
  return getEnvVar('VITE_WS_URL', 'ws://localhost:8000')
}

// ===== 通用工具函数 =====

/**
 * 深度克隆对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 生成唯一ID
 */
export const generateId = (prefix = ''): string => {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}${timestamp}${random}`
}

/**
 * 延迟执行
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试函数
 */
export const retry = async <T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  delay = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxAttempts) {
        throw lastError
      }
      
      await sleep(delay * attempt)
    }
  }
  
  throw lastError!
}

/**
 * 对象属性选择
 */
export const pick = <T, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>
  
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  
  return result
}

/**
 * 对象属性排除
 */
export const omit = <T, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj } as Omit<T, K>
  
  keys.forEach(key => {
    delete (result as any)[key]
  })
  
  return result
}

/**
 * 数组去重
 */
export const unique = <T>(array: T[]): T[] => {
  return Array.from(new Set(array))
}

/**
 * 数组分组
 */
export const groupBy = <T, K extends keyof T>(
  array: T[],
  key: K
): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const groupKey = String(item[key])
    
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    
    groups[groupKey].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 数组排序
 */
export const sortBy = <T>(
  array: T[],
  key: keyof T,
  order: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...array].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * 获取嵌套对象属性
 */
export const get = (obj: any, path: string, defaultValue?: any): any => {
  const keys = path.split('.')
  let result = obj
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue
    }
    result = result[key]
  }
  
  return result !== undefined ? result : defaultValue
}

/**
 * 设置嵌套对象属性
 */
export const set = (obj: any, path: string, value: any): void => {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}

<template>
  <div class="backtest-result-view">
    <el-card shadow="hover">
      <template #header>
        <div class="header">
          <span>回测结果 - {{ result?.metrics?.strategy_name || symbol }}</span>
          <el-button type="primary" @click="fetchResult" :loading="loading">刷新</el-button>
        </div>
      </template>

      <div v-if="loading" class="loading">
        <el-skeleton rows="5" animated />
      </div>

      <div v-else-if="result">
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- KPI -->
            <el-descriptions title="绩效指标" :column="2" border>
              <el-descriptions-item label="总收益">{{ formatPercent(result.metrics.total_return) }}</el-descriptions-item>
              <el-descriptions-item label="年化收益">{{ formatPercent(result.metrics.annual_return) }}</el-descriptions-item>
              <el-descriptions-item label="最大回撤">{{ formatPercent(result.metrics.max_drawdown) }}</el-descriptions-item>
              <el-descriptions-item label="夏普">{{ result.metrics.sharpe_ratio }}</el-descriptions-item>
              <el-descriptions-item label="Sortino">{{ result.metrics.sortino_ratio }}</el-descriptions-item>
              <el-descriptions-item label="Calmar">{{ result.metrics.calmar_ratio }}</el-descriptions-item>
              <el-descriptions-item label="月度均值">{{ formatPercent(result.metrics.monthly_return_mean) }}</el-descriptions-item>
              <el-descriptions-item label="月度波动">{{ formatPercent(result.metrics.monthly_return_std) }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="12">
            <!-- Equity curve chart -->
            <v-chart :option="chartOption" :autoresize="true" style="height:300px" />
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-empty description="暂无数据" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatPercent } from '@/utils/format/number'
import { backtestApi } from '@/api'
import VChart from 'vue-echarts'

const route = useRoute()
const symbol = ref<string>(route.query.symbol as string || 'TEST')
const backtestId = route.params.id as string

const loading = ref(false)
const result = ref<any | null>(null)

const chartOption = computed(() => {
  if (!result.value) return {}
  const seriesData = result.value.equity_curve.map((v: number, idx: number) => [idx, v])
  return {
    title: { text: '权益曲线' },
    xAxis: { type: 'category', show: false },
    yAxis: { type: 'value' },
    series: [{ type: 'line', data: seriesData, smooth: true }]
  }
})

const fetchResult = async () => {
  try {
    loading.value = true
    result.value = await backtestApi.getBacktestResult(backtestId)
  } catch (error) {
    console.error(error)
    ElMessage.error('获取回测结果失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchResult()
})
</script>

<style scoped>
.backtest-result-view { padding: 20px; }
.header { display:flex; justify-content:space-between; align-items:center; }
.loading { padding: 40px 0; }
</style> 
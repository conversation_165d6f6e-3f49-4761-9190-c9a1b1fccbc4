<template>
  <div class="puzzle-verify-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="verify-content">
      <!-- 标题区域 -->
      <div class="header-section">
        <h1 class="main-title">安全验证</h1>
        <p class="subtitle">请完成拼图验证以继续访问</p>
      </div>

      <!-- 拼图验证区域 -->
      <div class="puzzle-section">
        <div class="puzzle-container">
          <!-- 拼图画布 -->
          <div class="puzzle-canvas-wrapper">
            <canvas
              ref="puzzleCanvas"
              :width="canvasWidth"
              :height="canvasHeight"
              class="puzzle-canvas"
            ></canvas>

            <!-- 拼图块 -->
            <canvas
              ref="blockCanvas"
              :width="blockSize"
              :height="canvasHeight"
              class="puzzle-block"
              :style="{ left: blockLeft + 'px' }"
            ></canvas>

            <!-- 刷新按钮 -->
            <div class="refresh-btn" @click="refreshPuzzle">
              ↻
            </div>
          </div>

          <!-- 滑动轨道 -->
          <div class="slider-track">
            <div class="slider-track-bg">
              <span class="slider-text">{{ sliderText }}</span>
            </div>
            <div
              class="slider-btn"
              :class="{ 'slider-btn-success': isSuccess }"
              @mousedown="onSliderMouseDown"
              @touchstart="onSliderTouchStart"
              :style="{ left: sliderLeft + 'px' }"
            >
              <span v-if="!isSuccess">→</span>
              <span v-else>✓</span>
            </div>
          </div>
        </div>

        <!-- 验证结果 -->
        <div class="verify-result" v-if="verifyMessage">
          <div
            class="result-message"
            :class="{
              'success': isSuccess,
              'error': !isSuccess && verifyMessage
            }"
          >
            {{ verifyMessage }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="goBack" class="btn btn-default">
          返回登录
        </button>
        <button
          class="btn btn-primary"
          :disabled="!isSuccess"
          @click="continueToApp"
        >
          继续访问
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 画布配置 - 调整为更紧凑的尺寸
const canvasWidth = 280
const canvasHeight = 140
const blockSize = 38
const blockRadius = 8

// 响应式数据
const puzzleCanvas = ref<HTMLCanvasElement>()
const blockCanvas = ref<HTMLCanvasElement>()
const blockLeft = ref(0)
const sliderLeft = ref(0)
const sliderText = ref('向右拖动滑块填充拼图')
const isSuccess = ref(false)
const verifyMessage = ref('')

// 拼图数据
const puzzleData = ref({
  blockX: 0,
  blockY: 0,
  imageUrl: ''
})

// 滑动相关
const isDragging = ref(false)
const startX = ref(0)
const startTime = ref(0)
const attemptCount = ref(0) // 尝试次数计数器

// 预设图片数组 - 更新为新的尺寸
const puzzleImages = [
  'https://picsum.photos/280/140?random=1',
  'https://picsum.photos/280/140?random=2',
  'https://picsum.photos/280/140?random=3',
  'https://picsum.photos/280/140?random=4',
  'https://picsum.photos/280/140?random=5'
]

onMounted(() => {
  initPuzzle()
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
  document.addEventListener('touchmove', onTouchMove)
  document.addEventListener('touchend', onTouchEnd)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
  document.removeEventListener('touchmove', onTouchMove)
  document.removeEventListener('touchend', onTouchEnd)
})

// 初始化拼图
const initPuzzle = () => {
  resetState()
  generatePuzzle()
}

// 重置状态
const resetState = () => {
  blockLeft.value = 0
  sliderLeft.value = 0
  isSuccess.value = false
  verifyMessage.value = ''
  sliderText.value = '向右拖动滑块填充拼图'
  isDragging.value = false
  attemptCount.value = 0 // 重置尝试计数器
}

// 生成拼图
const generatePuzzle = () => {
  const imageUrl = puzzleImages[Math.floor(Math.random() * puzzleImages.length)]
  puzzleData.value.imageUrl = imageUrl

  // 随机生成拼图块位置
  puzzleData.value.blockX = Math.random() * (canvasWidth - blockSize - 20) + 20
  puzzleData.value.blockY = Math.random() * (canvasHeight - blockSize - 20) + 20

  loadImageAndDraw(imageUrl)
}

// 加载图片并绘制
const loadImageAndDraw = (imageUrl: string) => {
  const img = new Image()
  img.crossOrigin = 'anonymous'
  img.onload = () => {
    drawPuzzle(img)
    drawBlock(img)
  }
  img.onerror = () => {
    // 如果网络图片加载失败，使用本地生成的图片
    generateLocalImage()
  }
  img.src = imageUrl
}

// 生成本地图片（备用方案）
const generateLocalImage = () => {
  const canvas = document.createElement('canvas')
  canvas.width = canvasWidth
  canvas.height = canvasHeight
  const ctx = canvas.getContext('2d')!

  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight)
  gradient.addColorStop(0, '#667eea')
  gradient.addColorStop(1, '#764ba2')
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)

  // 添加一些装饰图案
  ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
  for (let i = 0; i < 20; i++) {
    const x = Math.random() * canvasWidth
    const y = Math.random() * canvasHeight
    const radius = Math.random() * 10 + 5
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, Math.PI * 2)
    ctx.fill()
  }

  canvas.toBlob((blob) => {
    if (blob) {
      const url = URL.createObjectURL(blob)
      const img = new Image()
      img.onload = () => {
        drawPuzzle(img)
        drawBlock(img)
        URL.revokeObjectURL(url)
      }
      img.src = url
    }
  })
}

// 绘制拼图背景
const drawPuzzle = (img: HTMLImageElement) => {
  const canvas = puzzleCanvas.value!
  const ctx = canvas.getContext('2d')!

  // 绘制背景图片
  ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight)

  // 绘制拼图缺口
  ctx.globalCompositeOperation = 'destination-out'
  drawPuzzleShape(ctx, puzzleData.value.blockX, puzzleData.value.blockY)
  ctx.globalCompositeOperation = 'source-over'

  // 绘制缺口边框
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
  ctx.lineWidth = 2
  drawPuzzleShape(ctx, puzzleData.value.blockX, puzzleData.value.blockY, true)
}

// 绘制拼图块
const drawBlock = (img: HTMLImageElement) => {
  const canvas = blockCanvas.value!
  const ctx = canvas.getContext('2d')!

  ctx.clearRect(0, 0, blockSize, canvasHeight)

  // 绘制拼图块
  ctx.save()
  drawPuzzleShape(ctx, 0, puzzleData.value.blockY)
  ctx.clip()
  ctx.drawImage(
    img,
    puzzleData.value.blockX, 0, blockSize, canvasHeight,
    0, 0, blockSize, canvasHeight
  )
  ctx.restore()

  // 绘制拼图块边框
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
  ctx.lineWidth = 2
  drawPuzzleShape(ctx, 0, puzzleData.value.blockY, true)
}

// 绘制拼图形状
const drawPuzzleShape = (ctx: CanvasRenderingContext2D, x: number, y: number, strokeOnly = false) => {
  ctx.beginPath()
  ctx.moveTo(x, y)
  ctx.lineTo(x + blockSize, y)
  ctx.lineTo(x + blockSize, y + blockSize)
  ctx.lineTo(x, y + blockSize)
  ctx.closePath()

  if (strokeOnly) {
    ctx.stroke()
  } else {
    ctx.fill()
  }
}

// 滑块鼠标事件
const onSliderMouseDown = (e: MouseEvent) => {
  if (isSuccess.value) return
  isDragging.value = true
  startX.value = e.clientX
  startTime.value = Date.now()
}

const onSliderTouchStart = (e: TouchEvent) => {
  if (isSuccess.value) return
  isDragging.value = true
  startX.value = e.touches[0].clientX
  startTime.value = Date.now()
}

const onMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return
  handleMove(e.clientX)
}

const onTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return
  e.preventDefault()
  handleMove(e.touches[0].clientX)
}

const handleMove = (clientX: number) => {
  const deltaX = clientX - startX.value
  const maxSliderLeft = canvasWidth - 35 // 调整最大滑动距离

  sliderLeft.value = Math.max(0, Math.min(deltaX, maxSliderLeft))
  blockLeft.value = sliderLeft.value
}

const onMouseUp = () => {
  if (!isDragging.value) return
  handleMoveEnd()
}

const onTouchEnd = () => {
  if (!isDragging.value) return
  handleMoveEnd()
}

const handleMoveEnd = () => {
  isDragging.value = false
  attemptCount.value++

  // 改进的验证算法
  const currentPosition = blockLeft.value
  const targetPosition = puzzleData.value.blockX
  const distance = Math.abs(currentPosition - targetPosition)

  // 自适应误差范围：根据尝试次数逐渐放宽
  let baseAccuracy = Math.max(15, blockSize * 0.35) // 基础误差范围

  // 根据尝试次数调整误差范围
  if (attemptCount.value >= 3) {
    baseAccuracy = Math.max(20, blockSize * 0.5) // 第3次后放宽到50%
  }
  if (attemptCount.value >= 5) {
    baseAccuracy = Math.max(25, blockSize * 0.6) // 第5次后进一步放宽
  }

  const dynamicAccuracy = baseAccuracy

  // 调试信息
  console.log('拼图验证调试信息:', {
    currentPosition: currentPosition.toFixed(1),
    targetPosition: targetPosition.toFixed(1),
    distance: distance.toFixed(1),
    accuracy: dynamicAccuracy.toFixed(1),
    blockSize: blockSize,
    attemptCount: attemptCount.value
  })

  const isCorrect = distance <= dynamicAccuracy

  if (isCorrect) {
    // 验证成功
    isSuccess.value = true
    const timeSpent = ((Date.now() - startTime.value) / 1000).toFixed(1)
    verifyMessage.value = `验证成功！耗时 ${timeSpent}s，精度 ${distance.toFixed(1)}px`
    sliderText.value = '验证成功'

    // 精确对齐到目标位置
    blockLeft.value = targetPosition
    sliderLeft.value = targetPosition

    console.log('验证成功:', verifyMessage.value)

    // 添加成功动画效果
    setTimeout(() => {
      const successElement = document.querySelector('.slider-btn-success') as HTMLElement
      if (successElement) {
        successElement.style.animation = 'pulse 0.5s ease-in-out'
      }
    }, 100)

  } else {
    // 验证失败，提供智能反馈
    let feedbackMessage = `验证失败，请重试 (差距: ${distance.toFixed(1)}px)`

    // 根据位置关系给出方向提示
    if (currentPosition < targetPosition) {
      const needMove = targetPosition - currentPosition
      feedbackMessage += ` - 需要向右移动约 ${needMove.toFixed(0)}px`
    } else {
      const needMove = currentPosition - targetPosition
      feedbackMessage += ` - 需要向左移动约 ${needMove.toFixed(0)}px`
    }

    verifyMessage.value = feedbackMessage
    console.log('验证失败:', verifyMessage.value)

    // 根据尝试次数给出不同的鼓励提示
    setTimeout(() => {
      if (distance <= dynamicAccuracy * 1.5) {
        console.log('很接近了！再试一次')
      } else if (attemptCount.value >= 3) {
        console.log('提示：观察拼图缺口的位置，尝试更精确的对齐')
      } else if (attemptCount.value >= 5) {
        console.log('多次尝试失败，已放宽验证标准，请继续尝试')
      }
    }, 1000)

    // 重置位置
    setTimeout(() => {
      resetSlider()
    }, 1500)
  }
}

// 重置滑块位置
const resetSlider = () => {
  blockLeft.value = 0
  sliderLeft.value = 0
  verifyMessage.value = ''
  sliderText.value = '向右拖动滑块填充拼图'
}

// 刷新拼图
const refreshPuzzle = () => {
  initPuzzle()
  console.log('已刷新拼图')
}

// 返回登录
const goBack = () => {
  router.push('/login')
}

// 继续访问应用
const continueToApp = async () => {
  if (!isSuccess.value) return

  try {
    // 模拟验证过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('验证完成，正在跳转...')

    // 跳转到主应用
    setTimeout(() => {
      router.push('/')
    }, 500)
  } catch (error) {
    console.log('验证失败，请重试')
  }
}
</script>

<style scoped>
.puzzle-verify-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.verify-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 420px;
  width: 90%;
  text-align: center;
}

.header-section {
  margin-bottom: 30px;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

.puzzle-section {
  margin-bottom: 30px;
}

.puzzle-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  border: 2px solid #e9ecef;
  max-width: 320px;
  margin: 0 auto;
}

.puzzle-canvas-wrapper {
  position: relative;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 280px;
  margin-left: auto;
  margin-right: auto;
}

.puzzle-canvas {
  display: block;
  background: #fff;
}

.puzzle-block {
  position: absolute;
  top: 0;
  transition: left 0.3s ease;
  cursor: pointer;
}

.refresh-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #667eea;
}

.refresh-btn:hover {
  background: #667eea;
  color: white;
  transform: rotate(180deg);
}

.slider-track {
  position: relative;
  height: 45px;
  background: #f1f3f4;
  border-radius: 22px;
  border: 2px solid #e1e5e9;
  overflow: hidden;
  width: 280px;
  margin: 0 auto;
}

.slider-track-bg {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #e8f4fd 0%, #f1f3f4 100%);
}

.slider-text {
  color: #8a9ba8;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

.slider-btn {
  position: absolute;
  left: 0;
  top: 0;
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  z-index: 10;
}

.slider-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.slider-btn-success {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.verify-result {
  margin-top: 15px;
}

.result-message {
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
}

.result-message.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.result-message.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  min-width: 120px;
  height: 44px;
  border-radius: 22px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-default {
  background: #f5f5f5;
  color: #666;
}

.btn-default:hover {
  background: #e0e0e0;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verify-content {
    padding: 25px 15px;
    margin: 15px;
    max-width: 350px;
  }

  .main-title {
    font-size: 2rem;
  }

  .puzzle-container {
    padding: 12px;
    max-width: 280px;
  }

  .puzzle-canvas-wrapper {
    width: 260px;
  }

  .slider-track {
    width: 260px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* 拖拽时的样式 */
.slider-btn:active {
  transform: scale(0.95);
}

/* 动画效果 */
.verify-content {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

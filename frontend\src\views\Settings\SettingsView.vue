<template>
  <div class="settings-view">
    <!-- 页面头部 -->
    <div class="settings-header">
      <h1 class="page-title">
        <el-icon><Setting /></el-icon>
        设置中心
      </h1>
      <p class="page-description">管理您的账户设置、交易偏好和系统配置</p>
    </div>

    <!-- 设置导航和内容 -->
    <div class="settings-container">
      <!-- 左侧导航 -->
      <div class="settings-nav">
        <el-menu
          :default-active="activeTab"
          mode="vertical"
          @select="handleTabChange"
          class="settings-menu"
        >
          <el-menu-item index="profile">
            <el-icon><User /></el-icon>
            <span>个人资料</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </el-menu-item>
          <el-menu-item index="trading">
            <el-icon><TrendCharts /></el-icon>
            <span>交易设置</span>
          </el-menu-item>
          <el-menu-item index="api">
            <el-icon><Connection /></el-icon>
            <span>API配置</span>
          </el-menu-item>
          <el-menu-item index="notifications">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </el-menu-item>
          <el-menu-item index="appearance">
            <el-icon><Brush /></el-icon>
            <span>外观设置</span>
          </el-menu-item>
          <el-menu-item index="system">
            <el-icon><Tools /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容 -->
      <div class="settings-content">
        <!-- 个人资料 -->
        <div v-show="activeTab === 'profile'" class="settings-panel">
          <UserProfileSettings />
        </div>

        <!-- 安全设置 -->
        <div v-show="activeTab === 'security'" class="settings-panel">
          <SecuritySettings />
        </div>

        <!-- 交易设置 -->
        <div v-show="activeTab === 'trading'" class="settings-panel">
          <TradingSettings />
        </div>

        <!-- API配置 -->
        <div v-show="activeTab === 'api'" class="settings-panel">
          <ApiSettings />
        </div>

        <!-- 通知设置 -->
        <div v-show="activeTab === 'notifications'" class="settings-panel">
          <NotificationSettings />
        </div>

        <!-- 外观设置 -->
        <div v-show="activeTab === 'appearance'" class="settings-panel">
          <AppearanceSettings />
        </div>

        <!-- 系统设置 -->
        <div v-show="activeTab === 'system'" class="settings-panel">
          <SystemSettings />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Setting,
  User,
  Lock,
  TrendCharts,
  Connection,
  Bell,
  Brush,
  Tools
} from '@element-plus/icons-vue'

// 导入设置组件
import UserProfileSettings from './components/UserProfileSettings.vue'
import SecuritySettings from './components/SecuritySettings.vue'
import TradingSettings from './components/TradingSettings.vue'
import ApiSettings from './components/ApiSettings.vue'
import NotificationSettings from './components/NotificationSettings.vue'
import AppearanceSettings from './components/AppearanceSettings.vue'
import SystemSettings from './components/SystemSettings.vue'

// 当前激活的标签页
const activeTab = ref('profile')

// 处理标签页切换
const handleTabChange = (key: string) => {
  activeTab.value = key
}
</script>

<style scoped>
.settings-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.settings-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.page-description {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.settings-container {
  display: flex;
  gap: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-nav {
  width: 240px;
  background: #fafafa;
  border-right: 1px solid #e6e6e6;
}

.settings-menu {
  border: none;
  background: transparent;
}

.settings-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  margin: 4px 8px;
  border-radius: 6px;
}

.settings-menu .el-menu-item:hover {
  background: #e6f7ff;
  color: #409eff;
}

.settings-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.settings-content {
  flex: 1;
  padding: 24px;
  min-height: 600px;
}

.settings-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

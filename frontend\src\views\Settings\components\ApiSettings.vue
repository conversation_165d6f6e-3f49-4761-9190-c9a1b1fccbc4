<template>
  <div class="api-settings">
    <!-- API密钥管理 -->
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Key /></el-icon>
        API密钥管理
      </h3>
      <p class="section-description">管理您的API访问密钥，用于程序化交易和数据访问</p>

      <!-- 创建新密钥 -->
      <el-card class="api-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>创建新API密钥</span>
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              创建密钥
            </el-button>
          </div>
        </template>

        <!-- 现有密钥列表 -->
        <el-table :data="apiKeys" stripe>
          <el-table-column prop="name" label="密钥名称" width="200" />
          <el-table-column prop="key" label="API Key" width="300">
            <template #default="{ row }">
              <div class="api-key-display">
                <span v-if="!row.showKey">{{ maskApiKey(row.key) }}</span>
                <span v-else>{{ row.key }}</span>
                <el-button
                  link
                  type="primary"
                  @click="toggleKeyVisibility(row)"
                  class="toggle-btn"
                >
                  <el-icon>
                    <View v-if="!row.showKey" />
                    <Hide v-else />
                  </el-icon>
                </el-button>
                <el-button
                  link
                  type="primary"
                  @click="copyToClipboard(row.key)"
                  class="copy-btn"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="permissions" label="权限" width="150">
            <template #default="{ row }">
              <el-tag
                v-for="permission in row.permissions"
                :key="permission"
                size="small"
                class="permission-tag"
              >
                {{ getPermissionText(permission) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column prop="lastUsed" label="最后使用" width="180" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button
                link
                type="primary"
                @click="editApiKey(row)"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click="deleteApiKey(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- API配置 -->
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Setting /></el-icon>
        API配置
      </h3>

      <el-form
        ref="configFormRef"
        :model="apiConfig"
        label-width="150px"
        class="config-form"
      >
        <el-form-item label="API基础URL">
          <el-input v-model="apiConfig.baseUrl" placeholder="https://api.example.com" />
        </el-form-item>

        <el-form-item label="请求超时时间">
          <el-input-number
            v-model="apiConfig.timeout"
            :min="1000"
            :max="60000"
            :step="1000"
            controls-position="right"
          />
          <span class="input-suffix">毫秒</span>
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number
            v-model="apiConfig.retryCount"
            :min="0"
            :max="5"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="启用请求日志">
          <el-switch v-model="apiConfig.enableLogging" />
        </el-form-item>

        <el-form-item label="启用缓存">
          <el-switch v-model="apiConfig.enableCache" />
        </el-form-item>

        <el-form-item label="缓存过期时间">
          <el-input-number
            v-model="apiConfig.cacheExpiration"
            :min="60"
            :max="3600"
            :step="60"
            :disabled="!apiConfig.enableCache"
            controls-position="right"
          />
          <span class="input-suffix">秒</span>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveApiConfig" :loading="saving">
            保存配置
          </el-button>
          <el-button @click="resetApiConfig">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Webhook设置 -->
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Connection /></el-icon>
        Webhook设置
      </h3>
      <p class="section-description">配置Webhook URL以接收实时事件通知</p>

      <el-form
        ref="webhookFormRef"
        :model="webhookConfig"
        label-width="150px"
        class="webhook-form"
      >
        <el-form-item label="Webhook URL">
          <el-input
            v-model="webhookConfig.url"
            placeholder="https://your-domain.com/webhook"
          />
        </el-form-item>

        <el-form-item label="密钥">
          <el-input
            v-model="webhookConfig.secret"
            type="password"
            placeholder="用于验证请求的密钥"
            show-password
          />
        </el-form-item>

        <el-form-item label="事件类型">
          <el-checkbox-group v-model="webhookConfig.events">
            <el-checkbox label="order.created">订单创建</el-checkbox>
            <el-checkbox label="order.filled">订单成交</el-checkbox>
            <el-checkbox label="order.cancelled">订单取消</el-checkbox>
            <el-checkbox label="position.opened">持仓开启</el-checkbox>
            <el-checkbox label="position.closed">持仓关闭</el-checkbox>
            <el-checkbox label="alert.triggered">预警触发</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="启用Webhook">
          <el-switch v-model="webhookConfig.enabled" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveWebhookConfig" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="testWebhook" :loading="testing">
            测试连接
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 创建API密钥对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建API密钥"
      width="500px"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入密钥名称" />
        </el-form-item>

        <el-form-item label="权限" prop="permissions">
          <el-checkbox-group v-model="createForm.permissions">
            <el-checkbox label="read">只读</el-checkbox>
            <el-checkbox label="trade">交易</el-checkbox>
            <el-checkbox label="withdraw">提现</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="过期时间" prop="expiration">
          <el-select v-model="createForm.expiration" placeholder="请选择过期时间">
            <el-option label="30天" value="30" />
            <el-option label="90天" value="90" />
            <el-option label="180天" value="180" />
            <el-option label="永不过期" value="never" />
          </el-select>
        </el-form-item>

        <el-form-item label="IP白名单">
          <el-input
            v-model="createForm.ipWhitelist"
            type="textarea"
            :rows="3"
            placeholder="每行一个IP地址，留空表示不限制"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createApiKey" :loading="creating">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Key,
  Plus,
  View,
  Hide,
  CopyDocument,
  Setting,
  Connection
} from '@element-plus/icons-vue'

// 表单引用
const configFormRef = ref()
const webhookFormRef = ref()
const createFormRef = ref()

// 状态
const saving = ref(false)
const testing = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)

// API密钥列表
const apiKeys = ref([
  {
    id: '1',
    name: '主要交易密钥',
    key: 'ak_1234567890abcdef1234567890abcdef',
    permissions: ['read', 'trade'],
    status: 'active',
    createdAt: '2024-01-15 10:30:00',
    lastUsed: '2025-07-29 14:20:00',
    showKey: false
  },
  {
    id: '2',
    name: '只读密钥',
    key: 'ak_abcdef1234567890abcdef1234567890',
    permissions: ['read'],
    status: 'active',
    createdAt: '2024-02-01 09:15:00',
    lastUsed: '2025-07-28 16:45:00',
    showKey: false
  }
])

// API配置
const apiConfig = reactive({
  baseUrl: 'http://localhost:8000/api/v1',
  timeout: 5000,
  retryCount: 3,
  enableLogging: true,
  enableCache: true,
  cacheExpiration: 300
})

// Webhook配置
const webhookConfig = reactive({
  url: '',
  secret: '',
  events: ['order.created', 'order.filled'],
  enabled: false
})

// 创建表单
const createForm = reactive({
  name: '',
  permissions: ['read'],
  expiration: '90',
  ipWhitelist: ''
})

// 创建表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' }
  ],
  permissions: [
    { required: true, message: '请选择权限', trigger: 'change' }
  ]
}

// 权限文本映射
const getPermissionText = (permission: string) => {
  const map: Record<string, string> = {
    read: '只读',
    trade: '交易',
    withdraw: '提现'
  }
  return map[permission] || permission
}

// 隐藏API密钥
const maskApiKey = (key: string) => {
  return key.substring(0, 8) + '****' + key.substring(key.length - 8)
}

// 切换密钥可见性
const toggleKeyVisibility = (row: any) => {
  row.showKey = !row.showKey
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 编辑API密钥
const editApiKey = (row: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 删除API密钥
const deleteApiKey = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除密钥 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除
    const index = apiKeys.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      apiKeys.value.splice(index, 1)
      ElMessage.success('密钥删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 创建API密钥
const createApiKey = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成新密钥
    const newKey = {
      id: Date.now().toString(),
      name: createForm.name,
      key: 'ak_' + Math.random().toString(36).substring(2, 34),
      permissions: createForm.permissions,
      status: 'active',
      createdAt: new Date().toLocaleString(),
      lastUsed: '-',
      showKey: false
    }
    
    apiKeys.value.unshift(newKey)
    showCreateDialog.value = false
    
    // 重置表单
    createFormRef.value.resetFields()
    
    ElMessage.success('API密钥创建成功')
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    creating.value = false
  }
}

// 保存API配置
const saveApiConfig = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('API配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 重置API配置
const resetApiConfig = () => {
  Object.assign(apiConfig, {
    baseUrl: 'http://localhost:8000/api/v1',
    timeout: 5000,
    retryCount: 3,
    enableLogging: true,
    enableCache: true,
    cacheExpiration: 300
  })
}

// 保存Webhook配置
const saveWebhookConfig = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('Webhook配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 测试Webhook
const testWebhook = async () => {
  if (!webhookConfig.url) {
    ElMessage.warning('请先输入Webhook URL')
    return
  }
  
  testing.value = true
  try {
    // 模拟测试请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('Webhook测试成功')
  } catch (error) {
    ElMessage.error('Webhook测试失败')
  } finally {
    testing.value = false
  }
}

onMounted(() => {
  // 加载配置数据
})
</script>

<style scoped>
.api-settings {
  max-width: 1000px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.api-card {
  border: 1px solid #e6e6e6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-key-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-btn,
.copy-btn {
  padding: 4px;
}

.permission-tag {
  margin-right: 4px;
}

.config-form,
.webhook-form {
  margin-top: 24px;
}

.input-suffix {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}
</style>

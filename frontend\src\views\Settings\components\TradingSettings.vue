<template>
  <div class="trading-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><TrendCharts /></el-icon>
        交易设置
      </h3>
      <p class="section-description">配置您的交易偏好和风险控制参数</p>

      <el-form :model="form" label-width="150px" class="trading-form">
        <!-- 基础交易设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>基础设置</span>
          </template>
          
          <el-form-item label="默认交易市场">
            <el-select v-model="form.defaultMarket" placeholder="请选择默认市场">
              <el-option label="A股" value="A" />
              <el-option label="港股" value="HK" />
              <el-option label="美股" value="US" />
            </el-select>
          </el-form-item>

          <el-form-item label="默认订单类型">
            <el-radio-group v-model="form.defaultOrderType">
              <el-radio label="market">市价单</el-radio>
              <el-radio label="limit">限价单</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="默认交易数量">
            <el-input-number
              v-model="form.defaultQuantity"
              :min="100"
              :step="100"
              controls-position="right"
            />
            <span class="input-suffix">股</span>
          </el-form-item>
        </el-card>

        <!-- 风险控制 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>风险控制</span>
          </template>
          
          <el-form-item label="单笔最大金额">
            <el-input-number
              v-model="form.maxOrderAmount"
              :min="1000"
              :step="1000"
              controls-position="right"
            />
            <span class="input-suffix">元</span>
          </el-form-item>

          <el-form-item label="日交易限额">
            <el-input-number
              v-model="form.dailyTradeLimit"
              :min="10000"
              :step="10000"
              controls-position="right"
            />
            <span class="input-suffix">元</span>
          </el-form-item>

          <el-form-item label="止损比例">
            <el-input-number
              v-model="form.stopLossRatio"
              :min="0.01"
              :max="0.2"
              :step="0.01"
              :precision="2"
              controls-position="right"
            />
            <span class="input-suffix">%</span>
          </el-form-item>

          <el-form-item label="止盈比例">
            <el-input-number
              v-model="form.takeProfitRatio"
              :min="0.01"
              :max="0.5"
              :step="0.01"
              :precision="2"
              controls-position="right"
            />
            <span class="input-suffix">%</span>
          </el-form-item>
        </el-card>

        <!-- 交易确认 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>交易确认</span>
          </template>
          
          <el-form-item label="买入确认">
            <el-switch v-model="form.confirmBuy" />
            <span class="switch-label">买入前需要确认</span>
          </el-form-item>

          <el-form-item label="卖出确认">
            <el-switch v-model="form.confirmSell" />
            <span class="switch-label">卖出前需要确认</span>
          </el-form-item>

          <el-form-item label="大额交易确认">
            <el-switch v-model="form.confirmLargeOrder" />
            <span class="switch-label">大额交易需要二次确认</span>
          </el-form-item>
        </el-card>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="resetSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'

const saving = ref(false)

const form = reactive({
  defaultMarket: 'A',
  defaultOrderType: 'limit',
  defaultQuantity: 1000,
  maxOrderAmount: 100000,
  dailyTradeLimit: 500000,
  stopLossRatio: 0.05,
  takeProfitRatio: 0.1,
  confirmBuy: true,
  confirmSell: true,
  confirmLargeOrder: true
})

const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('交易设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  Object.assign(form, {
    defaultMarket: 'A',
    defaultOrderType: 'limit',
    defaultQuantity: 1000,
    maxOrderAmount: 100000,
    dailyTradeLimit: 500000,
    stopLossRatio: 0.05,
    takeProfitRatio: 0.1,
    confirmBuy: true,
    confirmSell: true,
    confirmLargeOrder: true
  })
}
</script>

<style scoped>
.trading-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e6e6e6;
}

.input-suffix {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.switch-label {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}
</style>

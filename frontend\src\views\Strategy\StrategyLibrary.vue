<template>
  <div class="strategy-library">
    <!-- 页面头部 -->
    <div class="library-header">
      <div class="header-content">
        <h1 class="page-title">策略文库</h1>
        <p class="page-description">浏览和管理本地策略文件库 ({{ strategyDataPath }})</p>
      </div>

      <div class="header-stats">
        <el-statistic title="总年份" :value="totalYears" />
        <el-statistic title="总文件数" :value="totalFiles" />
        <el-statistic title="当前显示" :value="filteredFiles.length" />
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索策略标题、作者或内容..."
          :prefix-icon="Search"
          size="large"
          clearable
          @input="handleSearch"
        />
        <el-button type="primary" size="large" @click="handleSearch" :loading="searching">
          搜索
        </el-button>
      </div>

      <div class="filter-bar">
        <el-select v-model="selectedYear" placeholder="选择年份" clearable @change="loadFilesByYear">
          <el-option label="全部年份" value="" />
          <el-option
            v-for="year in availableYears"
            :key="year"
            :label="year"
            :value="year"
          />
        </el-select>

        <el-select v-model="sortBy" placeholder="排序方式" @change="sortFiles">
          <el-option label="文件名" value="filename" />
          <el-option label="年份" value="year" />
          <el-option label="标题" value="title" />
          <el-option label="作者" value="author" />
        </el-select>

        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 年份导航 -->
    <div class="year-navigation" v-if="!selectedYear">
      <h3>按年份浏览</h3>
      <div class="year-cards">
        <el-card
          v-for="year in availableYears"
          :key="year"
          class="year-card"
          :class="{ active: selectedYear === year }"
          @click="selectYear(year)"
          shadow="hover"
        >
          <div class="year-info">
            <div class="year-number">{{ year }}</div>
            <div class="year-count">{{ getFileCountByYear(year) }} 个策略</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="files-section" v-if="selectedYear || searchQuery">
      <div class="section-header">
        <h3 v-if="selectedYear">{{ selectedYear }} 年策略文件</h3>
        <h3 v-else-if="searchQuery">搜索结果</h3>

        <div class="view-controls">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="list">列表</el-radio-button>
            <el-radio-button label="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="files-list">
        <el-table
          :data="paginatedFiles"
          v-loading="loading"
          @row-click="viewFile"
          style="cursor: pointer"
        >
          <el-table-column prop="filename" label="文件名" min-width="200">
            <template #default="{ row }">
              <div class="file-name">
                <el-icon><Document /></el-icon>
                {{ row.filename }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="策略标题" min-width="250" />
          <el-table-column prop="author" label="作者" width="120" />
          <el-table-column prop="year" label="年份" width="80" />
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click.stop="viewFile(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" type="primary" @click.stop="importFile(row)">
                <el-icon><Download /></el-icon>
                导入
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 网格视图 -->
      <div v-else class="files-grid">
        <el-card
          v-for="file in paginatedFiles"
          :key="`${file.year}-${file.filename}`"
          class="file-card"
          @click="viewFile(file)"
          shadow="hover"
        >
          <div class="file-header">
            <el-icon class="file-icon"><Document /></el-icon>
            <span class="file-year">{{ file.year }}</span>
          </div>
          <div class="file-content">
            <h4 class="file-title">{{ file.title || file.filename }}</h4>
            <p class="file-author" v-if="file.author">作者: {{ file.author }}</p>
            <p class="file-description">{{ file.description || '暂无描述' }}</p>
          </div>
          <div class="file-actions">
            <el-button size="small" @click.stop="viewFile(file)">查看</el-button>
            <el-button size="small" type="primary" @click.stop="importFile(file)">导入</el-button>
          </div>
        </el-card>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredFiles.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 文件详情对话框 -->
    <el-dialog
      v-model="showFileDialog"
      :title="currentFile?.title || currentFile?.filename"
      width="80%"
      top="5vh"
    >
      <div v-if="currentFile" class="file-detail">
        <div class="file-meta">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentFile.filename }}</el-descriptions-item>
            <el-descriptions-item label="年份">{{ currentFile.year }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ currentFile.title || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="作者">{{ currentFile.author || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="来源" v-if="currentFile.source_url">
              <el-link :href="currentFile.source_url" target="_blank">{{ currentFile.source_url }}</el-link>
            </el-descriptions-item>
            <el-descriptions-item label="描述" span="2">{{ currentFile.description || '暂无描述' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="file-code">
          <h4>策略代码</h4>
          <el-input
            v-model="currentFile.content"
            type="textarea"
            :rows="20"
            readonly
            class="code-editor"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFileDialog = false">关闭</el-button>
          <el-button type="primary" @click="importCurrentFile" :loading="importing">
            导入到我的策略
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Document, View, Download } from '@element-plus/icons-vue'
import { strategyFileApi } from '@/api/strategy-files'

// 响应式数据
const loading = ref(false)
const searching = ref(false)
const importing = ref(false)
const availableYears = ref<string[]>([])
const allFiles = ref<any[]>([])
const filteredFiles = ref<any[]>([])
const searchQuery = ref('')
const selectedYear = ref('')
const sortBy = ref('filename')
const viewMode = ref<'list' | 'grid'>('list')
const currentPage = ref(1)
const pageSize = ref(20)
const showFileDialog = ref(false)
const currentFile = ref<any>(null)
const yearFileCounts = ref<Record<string, number>>({})

// 策略数据路径
const strategyDataPath = 'C:\\Users\\<USER>\\Desktop\\quant-platf\\data\\Strategy'

// 计算属性
const totalYears = computed(() => availableYears.value.length)
const totalFiles = computed(() => {
  // 使用预加载的文件数量统计
  return Object.values(yearFileCounts.value).reduce((sum, count) => sum + count, 0)
})

const paginatedFiles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFiles.value.slice(start, end)
})

// 方法
const loadAvailableYears = async () => {
  try {
    loading.value = true
    const response = await strategyFileApi.getAvailableYears()
    availableYears.value = response.years

    // 预加载所有年份的文件数量
    await loadAllYearFileCounts()
  } catch (error) {
    ElMessage.error('加载年份列表失败')
  } finally {
    loading.value = false
  }
}

const loadAllYearFileCounts = async () => {
  try {
    const counts: Record<string, number> = {}

    // 并行加载所有年份的文件数量
    const promises = availableYears.value.map(async (year) => {
      try {
        const response = await strategyFileApi.getFilesByYear(year)
        counts[year] = response.total || response.files?.length || 0
      } catch (error) {
        console.warn(`加载${year}年文件数量失败:`, error)
        counts[year] = 0
      }
    })

    await Promise.all(promises)
    yearFileCounts.value = counts
  } catch (error) {
    console.error('加载年份文件数量失败:', error)
  }
}

const loadFilesByYear = async (year?: string) => {
  if (!year) {
    filteredFiles.value = []
    return
  }

  try {
    loading.value = true
    const response = await strategyFileApi.getFilesByYear(year)
    const files = response.files.map((filename: string) => ({
      year,
      filename,
      title: extractTitleFromFilename(filename),
      author: '',
      description: ''
    }))

    if (selectedYear.value === year) {
      filteredFiles.value = files
    }

    // 更新总文件列表
    const existingFiles = allFiles.value.filter(f => f.year !== year)
    allFiles.value = [...existingFiles, ...files]

  } catch (error) {
    ElMessage.error(`加载${year}年文件失败`)
  } finally {
    loading.value = false
  }
}

const selectYear = (year: string) => {
  selectedYear.value = year
  loadFilesByYear(year)
}

const getFileCountByYear = (year: string) => {
  // 优先使用预加载的文件数量
  return yearFileCounts.value[year] || 0
}

const extractTitleFromFilename = (filename: string) => {
  // 从文件名提取标题，去掉扩展名和编号
  return filename.replace(/\.(txt|py)$/, '').replace(/\(\d+\)/, '').trim()
}

const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    if (selectedYear.value) {
      await loadFilesByYear(selectedYear.value)
    } else {
      filteredFiles.value = []
    }
    return
  }

  try {
    searching.value = true
    const response = await strategyFileApi.searchStrategies(searchQuery.value)
    filteredFiles.value = response
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

const sortFiles = () => {
  filteredFiles.value.sort((a, b) => {
    const aValue = a[sortBy.value] || ''
    const bValue = b[sortBy.value] || ''
    return aValue.localeCompare(bValue)
  })
}

const viewFile = async (file: any) => {
  try {
    loading.value = true
    const response = await strategyFileApi.getFileDetail(file.year, file.filename)
    currentFile.value = response
    showFileDialog.value = true
  } catch (error) {
    ElMessage.error('加载文件详情失败')
  } finally {
    loading.value = false
  }
}

const importFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将策略文件 "${file.title || file.filename}" 导入到您的策略库吗？`,
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    importing.value = true
    await strategyFileApi.importFile(file.year, file.filename)
    ElMessage.success('策略导入成功')

  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '导入失败')
    }
  } finally {
    importing.value = false
  }
}

const importCurrentFile = async () => {
  if (currentFile.value) {
    await importFile(currentFile.value)
    showFileDialog.value = false
  }
}

const refreshData = async () => {
  await loadAvailableYears()
  if (selectedYear.value) {
    await loadFilesByYear(selectedYear.value)
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  loadAvailableYears()
})
</script>

<style scoped>
.strategy-library {
  padding: 24px;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.search-section {
  margin-bottom: 24px;
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-bar .el-input {
  flex: 1;
}

.filter-bar {
  display: flex;
  gap: 12px;
  align-items: center;
}

.year-navigation {
  margin-bottom: 32px;
}

.year-navigation h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.year-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.year-card {
  cursor: pointer;
  transition: all 0.3s;
}

.year-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.year-card.active {
  border-color: #409eff;
}

.year-info {
  text-align: center;
}

.year-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.year-count {
  font-size: 14px;
  color: #606266;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.file-card {
  cursor: pointer;
  transition: all 0.3s;
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
}

.file-year {
  background: #f0f9ff;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.file-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.file-author {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
}

.file-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.file-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.file-meta {
  margin-bottom: 24px;
}

.file-code h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.code-editor :deep(.el-textarea__inner) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

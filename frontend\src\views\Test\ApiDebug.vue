<template>
  <div class="api-debug">
    <h1>API调试页面</h1>
    
    <div class="debug-section">
      <h2>环境变量</h2>
      <pre>{{ envInfo }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>Market Store状态</h2>
      <pre>{{ storeInfo }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>API测试</h2>
      <el-button @click="testMarketApi" :loading="testing">测试Market API</el-button>
      <pre v-if="apiResult">{{ apiResult }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>股票列表</h2>
      <p>数量: {{ marketStore.stockList?.length || 0 }}</p>
      <pre v-if="marketStore.stockList?.length">{{ JSON.stringify(marketStore.stockList.slice(0, 3), null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMarketStore } from '@/stores/modules/market'
import { marketApi } from '@/api/market'

const marketStore = useMarketStore()
const testing = ref(false)
const apiResult = ref('')

const envInfo = computed(() => ({
  VITE_USE_MOCK: import.meta.env.VITE_USE_MOCK,
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
  VITE_WS_URL: import.meta.env.VITE_WS_URL,
  VITE_ENABLE_DEVTOOLS: import.meta.env.VITE_ENABLE_DEVTOOLS,
  MODE: import.meta.env.MODE,
  DEV: import.meta.env.DEV
}))

const storeInfo = computed(() => ({
  stockListLength: marketStore.stockList?.length || 0,
  loading: marketStore.loading,
  error: marketStore.error
}))

const testMarketApi = async () => {
  testing.value = true
  try {
    console.log('Testing market API...')
    
    // 测试股票列表
    const stocks = await marketApi.getStockList({ pageSize: 3 })
    console.log('Stocks from API:', stocks)
    
    // 测试市场概览
    const overview = await marketApi.getMarketOverview()
    console.log('Overview from API:', overview)
    
    apiResult.value = JSON.stringify({
      stocks: stocks.slice(0, 3),
      overview: overview
    }, null, 2)
    
  } catch (error) {
    console.error('API test error:', error)
    apiResult.value = `Error: ${error.message}`
  } finally {
    testing.value = false
  }
}

onMounted(async () => {
  console.log('ApiDebug mounted')
  console.log('Environment:', envInfo.value)
  
  // 初始化market store
  try {
    await marketStore.initialize()
    console.log('Market store initialized')
  } catch (error) {
    console.error('Market store init error:', error)
  }
})
</script>

<style scoped>
.api-debug {
  padding: 20px;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
}
</style>

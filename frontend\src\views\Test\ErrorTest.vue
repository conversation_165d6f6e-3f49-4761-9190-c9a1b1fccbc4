<template>
  <div class="error-test-page">
    <div class="test-container">
      <h2>错误消息测试页面</h2>
      <p>用于测试错误消息是否重复显示</p>

      <div class="test-buttons">
        <el-button @click="testLogin" type="primary">
          测试登录错误
        </el-button>

        <el-button @click="testAPI" type="warning">
          测试API错误
        </el-button>

        <el-button @click="testValidation" type="info">
          测试验证错误
        </el-button>

        <el-button @click="clearMessages" type="danger">
          清除所有消息
        </el-button>

        <el-button @click="testRealLogin" type="success">
          测试真实登录错误
        </el-button>
      </div>

      <div class="test-results">
        <h3>测试结果：</h3>
        <ul>
          <li v-for="(result, index) in testResults" :key="index">
            {{ result }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { http } from '@/api/http'

const userStore = useUserStore()
const testResults = ref<string[]>([])

const addResult = (message: string) => {
  testResults.value.unshift(`${new Date().toLocaleTimeString()}: ${message}`)
}

const testLogin = async () => {
  addResult('开始测试登录错误...')
  try {
    // 模拟一个登录错误
    const mockError = {
      response: {
        status: 401,
        data: {
          message: '用户名或密码错误'
        }
      },
      message: '登录失败'
    }
    throw mockError
  } catch (error: any) {
    addResult(`登录错误捕获: ${error.message || '未知错误'}`)
    // 手动显示错误消息来测试是否重复
    ElMessage.error('用户名或密码错误')
  }
}

const testAPI = async () => {
  addResult('开始测试API错误...')
  try {
    await http.get('/api/v1/test/nonexistent')
  } catch (error: any) {
    addResult(`API错误捕获: ${error.message || '未知错误'}`)
  }
}

const testValidation = () => {
  addResult('开始测试验证错误...')
  // 先清除所有现有消息
  ElMessage.closeAll()
  // 显示测试消息
  ElMessage.error('这是一个测试验证错误消息')
  addResult('验证错误消息已显示')
}

const testRealLogin = async () => {
  addResult('开始测试真实登录错误...')
  try {
    await userStore.login({
      username: 'invalid_user_test',
      password: 'invalid_password_test'
    })
  } catch (error: any) {
    addResult(`真实登录错误捕获: ${error.message || '未知错误'}`)
    // 手动显示错误消息
    ElMessage.closeAll()
    ElMessage.error('用户名或密码错误 (真实测试)')
  }
}

const clearMessages = () => {
  testResults.value = []
  // 清除所有Element Plus消息
  ElMessage.closeAll()
}
</script>

<style scoped>
.error-test-page {
  min-height: 100vh;
  padding: 20px;
  background: #f5f5f5;
}

.test-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-results {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.test-results ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.test-results li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 14px;
}

.test-results li:last-child {
  border-bottom: none;
}
</style>

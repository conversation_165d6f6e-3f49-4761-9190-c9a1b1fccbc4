/**
 * 测试环境设置文件
 * 
 * 配置全局测试环境，包括：
 * - 全局模拟对象
 * - 测试工具函数
 * - 第三方库配置
 * - 环境变量设置
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'

// ===== 全局模拟配置 =====

// 模拟 window 对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟 requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 0))
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id))

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// 模拟 location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
  writable: true,
})

// 模拟 navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    language: 'zh-CN',
    languages: ['zh-CN', 'zh', 'en'],
    platform: 'Win32',
    cookieEnabled: true,
    onLine: true,
    clipboard: {
      writeText: vi.fn().mockResolvedValue(undefined),
      readText: vi.fn().mockResolvedValue(''),
    },
  },
  writable: true,
})

// ===== WebSocket 模拟 =====
class MockWebSocket {
  url: string
  readyState: number = WebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(url: string) {
    this.url = url
    // 模拟异步连接
    setTimeout(() => {
      this.readyState = WebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 100)
  }

  send(data: string | ArrayBuffer | Blob | ArrayBufferView) {
    // 模拟发送数据
    console.log('Mock WebSocket send:', data)
  }

  close(code?: number, reason?: string) {
    this.readyState = WebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close', { code, reason }))
    }
  }

  // 模拟接收消息的方法
  mockReceiveMessage(data: any) {
    if (this.onmessage && this.readyState === WebSocket.OPEN) {
      this.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }))
    }
  }
}

// 设置 WebSocket 常量
Object.defineProperty(MockWebSocket, 'CONNECTING', { value: 0 })
Object.defineProperty(MockWebSocket, 'OPEN', { value: 1 })
Object.defineProperty(MockWebSocket, 'CLOSING', { value: 2 })
Object.defineProperty(MockWebSocket, 'CLOSED', { value: 3 })

global.WebSocket = MockWebSocket as any

// ===== Fetch API 模拟 =====
global.fetch = vi.fn()

// 创建一个通用的 fetch 模拟响应
const createMockResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  statusText: status === 200 ? 'OK' : 'Error',
  headers: new Headers({
    'content-type': 'application/json',
  }),
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
  blob: () => Promise.resolve(new Blob([JSON.stringify(data)])),
  arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
})

// 设置默认的 fetch 行为
vi.mocked(fetch).mockImplementation((url: string | URL | Request) => {
  const urlString = url.toString()
  
  // 根据 URL 返回不同的模拟数据
  if (urlString.includes('/api/v1/auth/login')) {
    return Promise.resolve(createMockResponse({
      success: true,
      data: {
        access_token: 'mock-jwt-token',
        user: { id: '1', username: 'testuser', email: '<EMAIL>' }
      }
    }))
  }
  
  if (urlString.includes('/api/v1/market/quotes')) {
    return Promise.resolve(createMockResponse({
      success: true,
      data: {
        symbol: '000001.SZ',
        price: 12.50,
        change: 0.15,
        change_pct: 1.22
      }
    }))
  }
  
  // 默认返回成功响应
  return Promise.resolve(createMockResponse({
    success: true,
    data: null
  }))
})

// ===== Vue Test Utils 全局配置 =====
config.global.plugins = [ElementPlus, createPinia()]

// 全局组件
config.global.components = {
  // 可以在这里注册全局组件
}

// 全局指令
config.global.directives = {
  // 可以在这里注册全局指令
}

// 全局混入
config.global.mixins = []

// 全局属性
config.global.properties = {
  $t: (key: string) => key, // 模拟国际化函数
}

// ===== 环境变量设置 =====
process.env.NODE_ENV = 'test'
process.env.VITE_API_BASE_URL = 'http://localhost:3001'
process.env.VITE_WS_BASE_URL = 'ws://localhost:3001'

// ===== 全局测试工具函数 =====

/**
 * 等待下一个 tick
 */
export const nextTick = () => new Promise(resolve => setTimeout(resolve, 0))

/**
 * 等待指定时间
 */
export const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 模拟用户输入
 */
export const mockUserInput = async (input: HTMLInputElement, value: string) => {
  input.value = value
  input.dispatchEvent(new Event('input', { bubbles: true }))
  await nextTick()
}

/**
 * 模拟用户点击
 */
export const mockUserClick = async (element: HTMLElement) => {
  element.dispatchEvent(new MouseEvent('click', { bubbles: true }))
  await nextTick()
}

/**
 * 创建模拟的 Pinia store
 */
export const createMockStore = () => {
  const pinia = createPinia()
  return pinia
}

/**
 * 模拟 API 响应
 */
export const mockApiResponse = (data: any, status = 200) => {
  vi.mocked(fetch).mockResolvedValueOnce(createMockResponse(data, status) as any)
}

/**
 * 模拟 API 错误
 */
export const mockApiError = (error: string, status = 500) => {
  vi.mocked(fetch).mockRejectedValueOnce(new Error(error))
}

/**
 * 重置所有模拟
 */
export const resetAllMocks = () => {
  vi.clearAllMocks()
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
  sessionStorageMock.getItem.mockClear()
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
}

// ===== 测试数据工厂 =====

/**
 * 创建模拟用户数据
 */
export const createMockUser = (overrides = {}) => ({
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  role: 'trader',
  created_at: '2025-01-01T00:00:00Z',
  ...overrides
})

/**
 * 创建模拟股票数据
 */
export const createMockStock = (overrides = {}) => ({
  symbol: '000001.SZ',
  name: '平安银行',
  price: 12.50,
  change: 0.15,
  change_pct: 1.22,
  volume: 1500000,
  turnover: 18750000,
  ...overrides
})

/**
 * 创建模拟订单数据
 */
export const createMockOrder = (overrides = {}) => ({
  id: 'ORD001',
  symbol: '000001.SZ',
  side: 'buy',
  type: 'limit',
  quantity: 1000,
  price: 12.50,
  status: 'pending',
  created_at: '2025-07-27T10:30:00Z',
  ...overrides
})

/**
 * 创建模拟策略数据
 */
export const createMockStrategy = (overrides = {}) => ({
  id: 'STR001',
  name: '测试策略',
  description: '这是一个测试策略',
  type: 'trend_following',
  status: 'draft',
  code: 'def initialize(context): pass',
  created_at: '2025-07-27T10:30:00Z',
  ...overrides
})

// ===== 控制台警告过滤 =====

// 过滤掉一些已知的无害警告
const originalWarn = console.warn
console.warn = (...args) => {
  const message = args[0]
  
  // 过滤 Element Plus 的一些警告
  if (typeof message === 'string' && (
    message.includes('[Element Plus]') ||
    message.includes('ResizeObserver') ||
    message.includes('IntersectionObserver')
  )) {
    return
  }
  
  originalWarn(...args)
}

// ===== 测试钩子 =====

// 在每个测试前重置模拟
beforeEach(() => {
  resetAllMocks()
})

// 在每个测试后清理
afterEach(() => {
  vi.clearAllTimers()
})

console.log('✅ 测试环境设置完成')

export {
  localStorageMock,
  sessionStorageMock,
  MockWebSocket,
  createMockResponse
}

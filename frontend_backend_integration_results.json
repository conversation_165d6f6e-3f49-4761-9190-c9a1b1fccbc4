[{"success": true, "status_code": 200, "response_time": 2.91, "data": {"status": "ok", "version": "v1"}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "30", "content-type": "application/json", "x-process-time": "0.00024318695068359375", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "健康检查", "endpoint": "/health", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 11.65, "data": {"access_token": "<EMAIL>", "token_type": "bearer", "user": {"id": 1, "username": "<EMAIL>", "email": "<EMAIL>@example.com", "roles": ["user"]}}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "174", "content-type": "application/json", "x-process-time": "0.0005905628204345703", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "用户登录", "endpoint": "/auth/login", "method": "POST"}, {"success": true, "status_code": 200, "response_time": 14.93, "data": {"message": "User registered successfully", "user": {"id": 1, "username": "<EMAIL>", "email": "<EMAIL>"}}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "115", "content-type": "application/json", "x-process-time": "0.000835418701171875", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "用户注册", "endpoint": "/auth/register", "method": "POST"}, {"success": true, "status_code": 200, "response_time": 14.37, "data": {"message": "Logged out successfully"}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "37", "content-type": "application/json", "x-process-time": "0.0002281665802001953", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "用户登出", "endpoint": "/auth/logout", "method": "POST"}, {"success": true, "status_code": 200, "response_time": 16.05, "data": {"data": [{"code": "000001", "name": "平安银行", "market": "SZ", "price": 12.5, "change": 0.5}, {"code": "000002", "name": "万科A", "market": "SZ", "price": 15.3, "change": -0.3}, {"code": "600000", "name": "浦发银行", "market": "SH", "price": 8.9, "change": 0.2}, {"code": "600519", "name": "贵州茅台", "market": "SH", "price": 1680.0, "change": 20.0}], "total": 4}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "338", "content-type": "application/json", "x-process-time": "0.00138092041015625", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "股票列表", "endpoint": "/market/stocks", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 14.5, "data": {"data": [{"symbol": "000001", "name": "平安银行", "currentPrice": 12.88, "change": -0.28, "changePercent": -1.38, "volume": 48513042, "turnover": 323615734, "high": 13.3, "low": 12.58, "open": 12.06, "timestamp": "2025-08-11T14:36:07.700930"}, {"symbol": "600519", "name": "贵州茅台", "currentPrice": 1635.37, "change": 2.82, "changePercent": 0.04, "volume": 4519992, "turnover": 2458835086, "high": 1706.41, "low": 1655.72, "open": 1676.49, "timestamp": "2025-08-11T14:36:07.700942"}], "success": true}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "466", "content-type": "application/json", "x-process-time": "0.0003485679626464844", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "行情数据", "endpoint": "/market/quote", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 15.27, "data": {"data": [{"symbol": "000001", "name": "股票000001", "currentPrice": 90.59, "change": -0.7, "changePercent": 3.24, "volume": 39898915, "turnover": 600185940, "high": 94.58, "low": 89.41, "open": 86.43, "timestamp": "2025-08-11T14:36:07.716555"}, {"symbol": "600519", "name": "股票600519", "currentPrice": 142.37, "change": -0.15, "changePercent": -1.49, "volume": 29019610, "turnover": 750969487, "high": 150.73, "low": 140.05, "open": 144.61, "timestamp": "2025-08-11T14:36:07.716569"}], "success": true}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "463", "content-type": "application/json", "x-process-time": "0.0003917217254638672", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "行情数据(带参数)", "endpoint": "/market/quote", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 15.52, "data": {"success": true, "data": {"indices": [{"code": "000001", "name": "上证指数", "value": 3248.72, "change": -14.58, "change_percent": 0.45}, {"code": "399001", "name": "深证成指", "value": 10987.76, "change": 45.06, "change_percent": -0.57}, {"code": "399006", "name": "创业板指", "value": 2290.9, "change": -6.75, "change_percent": 1.36}], "statistics": {"total_stocks": 4800, "rising_stocks": 2657, "falling_stocks": 2164, "unchanged_stocks": 442, "total_volume": 636105799907, "total_turnover": 726235062242}, "hot_sectors": [{"name": "新能源", "change_percent": -0.54}, {"name": "半导体", "change_percent": 4.11}, {"name": "医药生物", "change_percent": 0.78}, {"name": "银行", "change_percent": 0.83}, {"name": "房地产", "change_percent": 0.9}], "market_status": "trading", "trading_day": "2025-08-11", "last_update": "2025-08-11T14:36:07.732526"}, "timestamp": "2025-08-11T14:36:07.732530"}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "845", "content-type": "application/json", "x-process-time": "0.0004208087921142578", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "市场概览", "endpoint": "/market/overview", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 15.51, "data": {"data": [{"timestamp": "2025-08-11T14:36:07.748253", "open": 101.19488100766485, "high": 102.21982117869902, "low": 97.52210177088175, "close": 98.67100603636108, "volume": 562682}, {"timestamp": "2025-08-11T14:36:07.748267", "open": 95.69578394201132, "high": 95.77672381705106, "low": 93.72046888378676, "close": 95.18416527969042, "volume": 821646}, {"timestamp": "2025-08-11T14:36:07.748271", "open": 99.32755268627781, "high": 105.16812592531217, "low": 98.68062438363792, "close": 103.85242364491565, "volume": 837848}, {"timestamp": "2025-08-11T14:36:07.748273", "open": 99.82899011464302, "high": 100.93692152780775, "low": 99.58156710422904, "close": 100.10154349209948, "volume": 337163}, {"timestamp": "2025-08-11T14:36:07.748276", "open": 102.48769545248558, "high": 104.58634310387167, "low": 102.09862670847302, "close": 104.13534661736341, "volume": 312558}, {"timestamp": "2025-08-11T14:36:07.748278", "open": 102.86748395362164, "high": 103.10706753515821, "low": 94.93058743119748, "close": 96.86005295690738, "volume": 517576}, {"timestamp": "2025-08-11T14:36:07.748282", "open": 96.59460712832157, "high": 100.88526167711707, "low": 96.31989329886837, "close": 99.77634518870236, "volume": 881539}, {"timestamp": "2025-08-11T14:36:07.748284", "open": 104.17822380538772, "high": 104.67547376345459, "low": 95.0773205932078, "close": 96.6803979907569, "volume": 243482}, {"timestamp": "2025-08-11T14:36:07.748288", "open": 99.98936122737128, "high": 100.19587672525584, "low": 96.52912224424776, "close": 98.35344833669039, "volume": 451857}, {"timestamp": "2025-08-11T14:36:07.748290", "open": 96.92018723858018, "high": 100.88394940452244, "low": 96.06154685434898, "close": 100.18190746190041, "volume": 623633}], "symbol": "000001", "period": "1d", "success": true}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "1663", "content-type": "application/json", "x-process-time": "0.0005409717559814453", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "K线数据", "endpoint": "/market/kline", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 14.79, "data": {"data": [{"symbol": "000001", "name": "平安银行", "market": "SZ", "pinyin": "payin<PERSON>"}], "success": true, "total": 1}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "112", "content-type": "application/json", "x-process-time": "0.00041866302490234375", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "股票搜索", "endpoint": "/market/search", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 15.51, "data": {"success": true, "data": [{"symbol": "000001", "name": "平安银行", "price": 12.11, "change": -0.39, "change_percent": -3.13, "volume": 31813653, "turnover": 142037705, "high": 13.71, "low": 10.78, "open": 11.95, "timestamp": "2025-08-11T14:36:07.779608"}, {"symbol": "600519", "name": "贵州茅台", "price": 1675.46, "change": -4.54, "change_percent": -0.27, "volume": 35542828, "turnover": 305232478, "high": 1677.14, "low": 1673.75, "open": 1679.0, "timestamp": "2025-08-11T14:36:07.779621"}], "count": 2, "timestamp": "2025-08-11T14:36:07.779624"}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "507", "content-type": "application/json", "x-process-time": "0.0005092620849609375", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "实时行情", "endpoint": "/market/quotes/realtime", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 17.18, "data": {"positions": [{"id": 1, "symbol": "000001", "name": "平安银行", "quantity": 1000, "avg_price": 12.0, "current_price": 12.5, "pnl": 500.0, "pnl_ratio": 0.042}], "total_value": 12500.0, "total_pnl": 500.0}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "188", "content-type": "application/json", "x-process-time": "0.0010120868682861328", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "持仓查询", "endpoint": "/trading/positions", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 3.41, "data": {"orders": [{"order_id": "ORD20250101120000", "symbol": "000001", "quantity": 1000, "price": 12.5, "side": "buy", "status": "filled", "timestamp": "2025-08-11T14:36:07.801528"}], "total": 1}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "174", "content-type": "application/json", "x-process-time": "0.0003123283386230469", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "订单查询", "endpoint": "/trading/orders", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 21.11, "data": {"order_id": "ORD20250811143607", "status": "submitted", "symbol": "000001", "quantity": 100, "price": 12.5, "side": "buy", "timestamp": "2025-08-11T14:36:07.822955"}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "153", "content-type": "application/json", "x-process-time": "0.0009045600891113281", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "下单", "endpoint": "/trading/orders", "method": "POST"}, {"success": true, "status_code": 200, "response_time": 2.72, "data": {"strategies": [{"id": 1, "name": "Moving Average Cross", "type": "trend_following", "status": "active", "performance": {"total_return": 0.15, "sharpe_ratio": 1.2, "max_drawdown": -0.08}}, {"id": 2, "name": "Mean Reversion", "type": "mean_reversion", "status": "inactive", "performance": {"total_return": 0.08, "sharpe_ratio": 0.9, "max_drawdown": -0.05}}], "total": 2}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "337", "content-type": "application/json", "x-process-time": "0.00029969215393066406", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "策略列表", "endpoint": "/strategies", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 3.79, "data": {"backtest_id": "test", "status": "completed", "metrics": {"total_return": 0.25, "annualized_return": 0.12, "sharpe_ratio": 1.5, "max_drawdown": -0.1, "win_rate": 0.65, "profit_factor": 1.8}, "trades": 150, "winning_trades": 98, "losing_trades": 52}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "227", "content-type": "application/json", "x-process-time": "0.0005562305450439453", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "回测结果", "endpoint": "/backtest/results/test", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 3.29, "data": {"portfolio_risk": {"var_95": -5000, "var_99": -8000, "expected_shortfall": -10000, "beta": 1.2, "correlation_with_market": 0.85}, "position_risks": [{"symbol": "000001", "var_95": -500, "concentration": 0.1, "liquidity_risk": "low"}]}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "216", "content-type": "application/json", "x-process-time": "0.0003266334533691406", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "风险指标", "endpoint": "/risk/metrics", "method": "GET"}, {"success": true, "status_code": 200, "response_time": 3.75, "data": {"limits": {"max_position_size": 0.2, "max_leverage": 2.0, "max_drawdown": 0.15, "daily_loss_limit": 0.05}, "current_usage": {"position_size": 0.1, "leverage": 1.0, "drawdown": 0.03, "daily_loss": 0.01}}, "headers": {"date": "Mon, 11 Aug 2025 06:36:07 GMT", "server": "u<PERSON><PERSON>", "content-length": "186", "content-type": "application/json", "x-process-time": "0.0003192424774169922", "connection": "keep-alive", "keep-alive": "timeout=60, max=1000"}, "name": "风险限制", "endpoint": "/risk/limits", "method": "GET"}]
/**
 * Comprehensive Frontend Diagnosis Script
 * Tests frontend functionality, console errors, network issues, and more
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function runDiagnosis() {
  let browser;
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    screenshots: [],
    errors: [],
    networkRequests: [],
    consoleMessages: []
  };

  try {
    console.log('🚀 Starting comprehensive frontend diagnosis...');

    // Launch browser
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-dev-shm-usage',
        '--no-sandbox'
      ]
    });

    const page = await browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });

    // Listen for console messages
    page.on('console', msg => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };
      results.consoleMessages.push(logEntry);
      console.log(`Console ${msg.type()}: ${msg.text()}`);
    });

    // Listen for JavaScript errors
    page.on('pageerror', err => {
      const errorEntry = {
        type: 'page_error',
        message: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString()
      };
      results.errors.push(errorEntry);
      console.error('Page error:', err.message);
    });

    // Listen for network requests
    page.on('request', request => {
      results.networkRequests.push({
        url: request.url(),
        method: request.method(),
        type: 'request',
        timestamp: new Date().toISOString()
      });
    });

    page.on('response', response => {
      results.networkRequests.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText(),
        type: 'response',
        timestamp: new Date().toISOString()
      });
      
      if (response.status() >= 400) {
        console.error(`Network error: ${response.status()} ${response.statusText()} - ${response.url()}`);
      }
    });

    // Test 1: Check if server is responding
    console.log('\n📡 Test 1: Checking server response...');
    try {
      const response = await page.goto('http://localhost:5173', {
        waitUntil: 'networkidle0',
        timeout: 10000
      });

      results.tests.serverResponse = {
        success: true,
        status: response.status(),
        statusText: response.statusText(),
        url: response.url()
      };
      console.log(`✅ Server responded: ${response.status()} ${response.statusText()}`);

      // Take screenshot after initial load
      const screenshotPath = path.join(__dirname, `screenshot_initial_${Date.now()}.png`);
      await page.screenshot({ path: screenshotPath, fullPage: true });
      results.screenshots.push(screenshotPath);
      console.log(`📸 Initial screenshot saved: ${screenshotPath}`);

    } catch (error) {
      results.tests.serverResponse = {
        success: false,
        error: error.message
      };
      console.error('❌ Server not responding:', error.message);
    }

    // Test 2: Check HTML structure
    console.log('\n🔍 Test 2: Checking HTML structure...');
    try {
      const appElement = await page.$('#app');
      const title = await page.title();
      const bodyContent = await page.evaluate(() => document.body.innerHTML.length);

      results.tests.htmlStructure = {
        success: true,
        hasAppElement: !!appElement,
        title: title,
        bodyContentLength: bodyContent
      };

      console.log(`✅ HTML structure check:`);
      console.log(`  - Title: ${title}`);
      console.log(`  - App element: ${appElement ? 'Found' : 'Not found'}`);
      console.log(`  - Body content length: ${bodyContent} characters`);

    } catch (error) {
      results.tests.htmlStructure = {
        success: false,
        error: error.message
      };
      console.error('❌ HTML structure check failed:', error.message);
    }

    // Test 3: Check JavaScript execution
    console.log('\n⚡ Test 3: Checking JavaScript execution...');
    try {
      // Wait for content to load
      await page.waitForTimeout(3000);

      const jsTest = await page.evaluate(() => {
        return {
          location: window.location.href,
          userAgent: navigator.userAgent,
          appElement: !!document.getElementById('app'),
          appContent: document.getElementById('app')?.innerHTML?.length || 0,
          vueApp: typeof window.__VUE_APP__ !== 'undefined',
          errors: window.__errors || []
        };
      });

      results.tests.javascript = {
        success: true,
        ...jsTest
      };

      console.log(`✅ JavaScript execution check:`);
      console.log(`  - App element content length: ${jsTest.appContent}`);
      console.log(`  - Vue app available: ${jsTest.vueApp}`);

    } catch (error) {
      results.tests.javascript = {
        success: false,
        error: error.message
      };
      console.error('❌ JavaScript execution check failed:', error.message);
    }

    // Test 4: Check for specific elements and content
    console.log('\n🎯 Test 4: Checking specific content...');
    try {
      // Check for specific text or elements that should be present
      const contentCheck = await page.evaluate(() => {
        const body = document.body.textContent || '';
        return {
          hasQuantPlatform: body.includes('量化投资平台') || body.includes('Quantum Investment'),
          hasDashboard: body.includes('仪表盘') || body.includes('Dashboard'),
          hasMarket: body.includes('市场行情') || body.includes('Market'),
          hasTrading: body.includes('智能交易') || body.includes('Trading'),
          hasStrategy: body.includes('策略研发') || body.includes('Strategy'),
          fullText: body.substring(0, 1000) // First 1000 characters
        };
      });

      results.tests.contentCheck = {
        success: true,
        ...contentCheck
      };

      console.log(`✅ Content check:`);
      console.log(`  - Has platform title: ${contentCheck.hasQuantPlatform}`);
      console.log(`  - Has dashboard: ${contentCheck.hasDashboard}`);
      console.log(`  - Has market section: ${contentCheck.hasMarket}`);
      console.log(`  - Has trading section: ${contentCheck.hasTrading}`);
      console.log(`  - Has strategy section: ${contentCheck.hasStrategy}`);

    } catch (error) {
      results.tests.contentCheck = {
        success: false,
        error: error.message
      };
      console.error('❌ Content check failed:', error.message);
    }

    // Test 5: Check CSS loading and styling
    console.log('\n🎨 Test 5: Checking CSS and styling...');
    try {
      const styleCheck = await page.evaluate(() => {
        const appElement = document.getElementById('app');
        if (!appElement) return { hasApp: false };

        const computedStyle = window.getComputedStyle(appElement);
        const allStyleSheets = Array.from(document.styleSheets);
        
        return {
          hasApp: true,
          appHeight: computedStyle.height,
          appWidth: computedStyle.width,
          backgroundColor: computedStyle.backgroundColor,
          fontFamily: computedStyle.fontFamily,
          styleSheetCount: allStyleSheets.length,
          hasInlineStyles: appElement.style.cssText.length > 0
        };
      });

      results.tests.styling = {
        success: true,
        ...styleCheck
      };

      console.log(`✅ Styling check:`);
      console.log(`  - Style sheets loaded: ${styleCheck.styleSheetCount}`);
      console.log(`  - App dimensions: ${styleCheck.appWidth} x ${styleCheck.appHeight}`);
      console.log(`  - Background color: ${styleCheck.backgroundColor}`);

    } catch (error) {
      results.tests.styling = {
        success: false,
        error: error.message
      };
      console.error('❌ Styling check failed:', error.message);
    }

    // Test 6: Check for any interactivity
    console.log('\n🖱️ Test 6: Checking interactivity...');
    try {
      // Look for buttons or interactive elements
      const buttonElement = await page.$('button');
      if (buttonElement) {
        console.log('✅ Found button element, testing click...');
        await buttonElement.click();
        await page.waitForTimeout(1000);
        
        // Take screenshot after interaction
        const interactionScreenshotPath = path.join(__dirname, `screenshot_after_click_${Date.now()}.png`);
        await page.screenshot({ path: interactionScreenshotPath, fullPage: true });
        results.screenshots.push(interactionScreenshotPath);
      }

      results.tests.interactivity = {
        success: true,
        hasButton: !!buttonElement
      };

      console.log(`✅ Interactivity check completed`);

    } catch (error) {
      results.tests.interactivity = {
        success: false,
        error: error.message
      };
      console.error('❌ Interactivity check failed:', error.message);
    }

    // Final screenshot
    const finalScreenshotPath = path.join(__dirname, `screenshot_final_${Date.now()}.png`);
    await page.screenshot({ path: finalScreenshotPath, fullPage: true });
    results.screenshots.push(finalScreenshotPath);

    console.log('\n📊 Diagnosis Summary:');
    console.log('='.repeat(50));
    
    Object.entries(results.tests).forEach(([testName, result]) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${testName}: ${result.success ? 'PASSED' : 'FAILED'}`);
      if (!result.success && result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });

    console.log(`\n📸 Screenshots captured: ${results.screenshots.length}`);
    console.log(`🗨️ Console messages: ${results.consoleMessages.length}`);
    console.log(`❌ Errors detected: ${results.errors.length}`);
    console.log(`🌐 Network requests: ${results.networkRequests.length}`);

    // Save detailed results
    const resultsPath = path.join(__dirname, `diagnosis_results_${Date.now()}.json`);
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 Detailed results saved to: ${resultsPath}`);

  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
    results.errors.push({
      type: 'diagnosis_error',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  return results;
}

// Run diagnosis if called directly
if (require.main === module) {
  runDiagnosis().then(() => {
    console.log('\n🏁 Frontend diagnosis completed');
  }).catch(error => {
    console.error('❌ Diagnosis script failed:', error);
    process.exit(1);
  });
}

module.exports = { runDiagnosis };
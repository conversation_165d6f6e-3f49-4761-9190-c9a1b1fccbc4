<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 简单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 3rem;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature {
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid var(--color);
        }
        
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .feature p {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .status {
            background: #e8f5e8;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin-bottom: 2rem;
        }
        
        .status p {
            color: #065f46;
            font-weight: 500;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        button {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-info {
            background: #6366f1;
            color: white;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .result {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            text-align: left;
        }
        
        .result.success {
            background: #e8f5e8;
            border-left: 4px solid #10b981;
            color: #065f46;
        }
        
        .result.error {
            background: #fee;
            border-left: 4px solid #f56c6c;
            color: #c53030;
        }
        
        .result.info {
            background: #f0f9ff;
            border-left: 4px solid #3b82f6;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 量化投资平台</h1>
        <p class="subtitle">前端应用测试页面</p>
        
        <div class="features">
            <div class="feature" style="--color: #3b82f6;">
                <h3>📊 仪表盘</h3>
                <p>投资数据概览</p>
            </div>
            <div class="feature" style="--color: #10b981;">
                <h3>📈 市场行情</h3>
                <p>实时市场数据</p>
            </div>
            <div class="feature" style="--color: #f59e0b;">
                <h3>💰 智能交易</h3>
                <p>自动化交易系统</p>
            </div>
            <div class="feature" style="--color: #8b5cf6;">
                <h3>🧠 策略研发</h3>
                <p>量化策略开发</p>
            </div>
        </div>

        <div class="status">
            <p>✅ 前端页面正常加载！</p>
            <p style="color: #047857; font-size: 0.9rem; margin-top: 0.5rem;">
                基础HTML页面工作正常，可以进行API测试
            </p>
        </div>

        <div class="buttons">
            <button class="btn-primary" onclick="testAPI()">测试API连接</button>
            <button class="btn-success" onclick="testAllAPIs()">测试所有API</button>
            <button class="btn-info" onclick="showSystemInfo()">系统信息</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        console.log('🚀 简单测试页面已加载')
        
        async function testAPI() {
            const resultDiv = document.getElementById('result')
            resultDiv.innerHTML = '<div class="result info"><p>正在测试API连接...</p></div>'
            
            try {
                const start = performance.now()
                const response = await fetch('http://127.0.0.1:8000/api/v1/health')
                const end = performance.now()
                const data = await response.json()
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ API连接成功！</h4>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>响应时间:</strong> ${Math.round(end - start)}ms</p>
                        <p><strong>响应数据:</strong> ${JSON.stringify(data, null, 2)}</p>
                    </div>
                `
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ API连接失败</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p><strong>建议:</strong> 请确保后端服务器正在运行</p>
                    </div>
                `
            }
        }
        
        async function testAllAPIs() {
            const resultDiv = document.getElementById('result')
            resultDiv.innerHTML = '<div class="result info"><p>正在测试所有API端点...</p></div>'
            
            const endpoints = [
                { name: '健康检查', url: '/health' },
                { name: '股票列表', url: '/market/stocks' },
                { name: '行情数据', url: '/market/quote' },
                { name: '市场概览', url: '/market/overview' },
                { name: '交易持仓', url: '/trading/positions' }
            ]
            
            let results = []
            
            for (const endpoint of endpoints) {
                try {
                    const start = performance.now()
                    const response = await fetch(`http://127.0.0.1:8000/api/v1${endpoint.url}`)
                    const end = performance.now()
                    
                    results.push({
                        name: endpoint.name,
                        status: response.status,
                        time: Math.round(end - start),
                        success: response.ok
                    })
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'Error',
                        time: 0,
                        success: false,
                        error: error.message
                    })
                }
            }
            
            const successCount = results.filter(r => r.success).length
            const avgTime = results.filter(r => r.success).reduce((sum, r) => sum + r.time, 0) / successCount || 0
            
            let html = `
                <div class="result ${successCount === results.length ? 'success' : 'error'}">
                    <h4>${successCount === results.length ? '✅' : '⚠️'} API测试完成</h4>
                    <p><strong>成功率:</strong> ${successCount}/${results.length} (${Math.round(successCount/results.length*100)}%)</p>
                    <p><strong>平均响应时间:</strong> ${Math.round(avgTime)}ms</p>
                    <br>
                    <h5>详细结果:</h5>
                    <ul style="margin-left: 1rem;">
            `
            
            results.forEach(result => {
                html += `
                    <li>
                        ${result.success ? '✅' : '❌'} ${result.name}: 
                        ${result.status} (${result.time}ms)
                        ${result.error ? ` - ${result.error}` : ''}
                    </li>
                `
            })
            
            html += '</ul></div>'
            resultDiv.innerHTML = html
        }
        
        function showSystemInfo() {
            const resultDiv = document.getElementById('result')
            resultDiv.innerHTML = `
                <div class="result info">
                    <h4>🔍 系统信息</h4>
                    <p><strong>前端地址:</strong> ${window.location.href}</p>
                    <p><strong>后端地址:</strong> http://127.0.0.1:8000</p>
                    <p><strong>用户代理:</strong> ${navigator.userAgent.substring(0, 80)}...</p>
                    <p><strong>屏幕分辨率:</strong> ${screen.width}x${screen.height}</p>
                    <p><strong>视口大小:</strong> ${window.innerWidth}x${window.innerHeight}</p>
                    <p><strong>时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>时区:</strong> ${Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
                </div>
            `
        }
        
        // 页面加载完成后自动测试API
        window.addEventListener('load', () => {
            console.log('✅ 页面加载完成，自动测试API连接')
            setTimeout(testAPI, 1000)
        })
    </script>
</body>
</html>

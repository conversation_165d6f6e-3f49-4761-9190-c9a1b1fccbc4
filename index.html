<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 48px;
            margin: 0;
            font-weight: 300;
        }
        
        .header p {
            font-size: 18px;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-ready {
            background: #e8f5e8;
            color: #52c41a;
        }
        
        .status-beta {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .status-dev {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .quick-actions h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .action-button {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .stats-section {
            margin-top: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            text-align: center;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 32px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>量化投资平台</h1>
                <p>专业的量化交易和投资管理解决方案</p>
            </div>
            
            <!-- 功能模块 -->
            <div class="features-grid">
                <div class="feature-card" @click="navigateTo('/trading/center')">
                    <div class="feature-icon">📈</div>
                    <div class="feature-title">交易中心</div>
                    <div class="feature-description">
                        集成交易终端、账户管理和数据中心，提供完整的交易解决方案
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-ready">已就绪</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
                
                <div class="feature-card" @click="navigateTo('trading-terminal.html')">
                    <div class="feature-icon">💹</div>
                    <div class="feature-title">交易终端</div>
                    <div class="feature-description">
                        专业的股票交易界面，支持实时行情、K线图表和快速下单
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-ready">已就绪</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
                
                <div class="feature-card" @click="navigateTo('/strategy/center')">
                    <div class="feature-icon">🧠</div>
                    <div class="feature-title">策略中心</div>
                    <div class="feature-description">
                        策略开发、回测和优化平台，支持多种量化策略模板
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-beta">测试版</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
                
                <div class="feature-card" @click="navigateTo('/market/realtime')">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">市场数据</div>
                    <div class="feature-description">
                        实时行情数据、历史数据分析和市场监控工具
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-ready">已就绪</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
                
                <div class="feature-card" @click="navigateTo('/portfolio')">
                    <div class="feature-icon">💼</div>
                    <div class="feature-title">投资组合</div>
                    <div class="feature-description">
                        投资组合管理、风险分析和收益追踪
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-beta">测试版</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
                
                <div class="feature-card" @click="navigateTo('monitoring-dashboard.html')">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">监控仪表板</div>
                    <div class="feature-description">
                        系统监控、性能分析和告警管理
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-ready">已就绪</span>
                        <el-button type="primary" size="small">进入</el-button>
                    </div>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="quick-actions">
                <h2>快速操作</h2>
                <div class="actions-grid">
                    <a href="/trading/simulated" class="action-button">模拟交易</a>
                    <a href="/backtest" class="action-button">策略回测</a>
                    <a href="/risk" class="action-button">风险管理</a>
                    <a href="/api/v1/docs" class="action-button">API文档</a>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.totalUsers }}</div>
                        <div class="stat-label">注册用户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.activeStrategies }}</div>
                        <div class="stat-label">活跃策略</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.totalTrades }}</div>
                        <div class="stat-label">总交易次数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.systemUptime }}</div>
                        <div class="stat-label">系统运行时间</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    stats: {
                        totalUsers: '1,247',
                        activeStrategies: '156',
                        totalTrades: '89,432',
                        systemUptime: '99.9%'
                    }
                }
            },
            methods: {
                navigateTo(path) {
                    if (path.startsWith('/')) {
                        // Vue Router 路径
                        window.location.href = `http://localhost:5173${path}`;
                    } else {
                        // 静态HTML页面
                        window.location.href = path;
                    }
                },
                
                async checkSystemStatus() {
                    try {
                        // 检查后端API状态
                        const response = await fetch('http://localhost:8000/health');
                        if (response.ok) {
                            ElMessage.success('系统运行正常');
                        }
                    } catch (error) {
                        console.log('后端服务未启动，使用模拟数据');
                    }
                }
            },
            
            mounted() {
                this.checkSystemStatus();
                
                // 定时更新统计信息
                setInterval(() => {
                    // 模拟数据更新
                    this.stats.totalUsers = (parseInt(this.stats.totalUsers.replace(',', '')) + Math.floor(Math.random() * 5)).toLocaleString();
                }, 30000);
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>

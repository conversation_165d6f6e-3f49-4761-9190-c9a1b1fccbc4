#!/usr/bin/env python3
"""
MCP文件系统深度分析工具
模拟真实用户对量化投资平台进行深度文件系统分析
"""

import os
import json
import time
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import subprocess

class MCPFileSystemAnalyzer:
    """MCP文件系统深度分析器"""
    
    def __init__(self):
        self.project_root = Path("C:/Users/<USER>/Desktop/quant013")
        self.analysis_results = {
            "analysis_time": datetime.now().isoformat(),
            "project_structure": {},
            "code_quality": {},
            "security_analysis": {},
            "performance_insights": {},
            "user_experience_issues": [],
            "recommendations": []
        }
        
    def analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        print("📁 分析项目结构...")
        
        structure = {
            "total_files": 0,
            "total_directories": 0,
            "file_types": {},
            "large_files": [],
            "empty_files": [],
            "duplicate_files": {},
            "directory_sizes": {}
        }
        
        file_hashes = {}
        
        for root, dirs, files in os.walk(self.project_root):
            # 跳过node_modules和.git等目录
            dirs[:] = [d for d in dirs if d not in ['.git', 'node_modules', '__pycache__', '.vscode']]
            
            structure["total_directories"] += len(dirs)
            
            for file in files:
                file_path = Path(root) / file
                try:
                    file_size = file_path.stat().st_size
                    structure["total_files"] += 1
                    
                    # 文件类型统计
                    ext = file_path.suffix.lower()
                    if ext:
                        structure["file_types"][ext] = structure["file_types"].get(ext, 0) + 1
                    
                    # 大文件检测 (>10MB)
                    if file_size > 10 * 1024 * 1024:
                        structure["large_files"].append({
                            "path": str(file_path.relative_to(self.project_root)),
                            "size_mb": round(file_size / (1024 * 1024), 2)
                        })
                    
                    # 空文件检测
                    if file_size == 0:
                        structure["empty_files"].append(str(file_path.relative_to(self.project_root)))
                    
                    # 重复文件检测
                    if file_size > 0 and file_size < 1024 * 1024:  # 只检查小于1MB的文件
                        try:
                            with open(file_path, 'rb') as f:
                                file_hash = hashlib.md5(f.read()).hexdigest()
                                if file_hash in file_hashes:
                                    if file_hash not in structure["duplicate_files"]:
                                        structure["duplicate_files"][file_hash] = []
                                    structure["duplicate_files"][file_hash].append(str(file_path.relative_to(self.project_root)))
                                else:
                                    file_hashes[file_hash] = str(file_path.relative_to(self.project_root))
                        except:
                            pass
                            
                except (OSError, PermissionError):
                    pass
        
        # 目录大小分析
        for dir_name in ['frontend', 'backend', 'docs', 'mcp', 'data']:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                size = self.get_directory_size(dir_path)
                structure["directory_sizes"][dir_name] = {
                    "size_mb": round(size / (1024 * 1024), 2),
                    "file_count": len(list(dir_path.rglob("*")))
                }
        
        self.analysis_results["project_structure"] = structure
        return structure
    
    def get_directory_size(self, path: Path) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    try:
                        total_size += file_path.stat().st_size
                    except (OSError, PermissionError):
                        pass
        except:
            pass
        return total_size
    
    def analyze_code_quality(self) -> Dict[str, Any]:
        """分析代码质量"""
        print("🔍 分析代码质量...")
        
        quality = {
            "python_files": [],
            "javascript_files": [],
            "vue_files": [],
            "config_files": [],
            "documentation_files": [],
            "potential_issues": []
        }
        
        # Python文件分析
        for py_file in self.project_root.rglob("*.py"):
            if 'node_modules' not in str(py_file) and '.git' not in str(py_file):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        
                    quality["python_files"].append({
                        "path": str(py_file.relative_to(self.project_root)),
                        "lines": len(lines),
                        "size_kb": round(len(content.encode('utf-8')) / 1024, 2),
                        "has_docstring": content.strip().startswith('"""') or content.strip().startswith("'''"),
                        "has_imports": any(line.strip().startswith(('import ', 'from ')) for line in lines[:20])
                    })
                except:
                    pass
        
        # JavaScript/TypeScript文件分析
        for js_file in self.project_root.rglob("*.js"):
            if 'node_modules' not in str(js_file):
                try:
                    with open(js_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    quality["javascript_files"].append({
                        "path": str(js_file.relative_to(self.project_root)),
                        "lines": len(lines),
                        "size_kb": round(len(content.encode('utf-8')) / 1024, 2),
                        "has_strict_mode": "'use strict'" in content or '"use strict"' in content,
                        "has_comments": any(line.strip().startswith('//') or '/*' in line for line in lines)
                    })
                except:
                    pass
        
        # Vue文件分析
        for vue_file in self.project_root.rglob("*.vue"):
            try:
                with open(vue_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                quality["vue_files"].append({
                    "path": str(vue_file.relative_to(self.project_root)),
                    "lines": len(lines),
                    "size_kb": round(len(content.encode('utf-8')) / 1024, 2),
                    "has_template": '<template>' in content,
                    "has_script": '<script>' in content,
                    "has_style": '<style>' in content
                })
            except:
                pass
        
        # 配置文件分析
        config_patterns = ['*.json', '*.yml', '*.yaml', '*.toml', '*.ini', '*.conf']
        for pattern in config_patterns:
            for config_file in self.project_root.rglob(pattern):
                if 'node_modules' not in str(config_file) and '.git' not in str(config_file):
                    try:
                        size = config_file.stat().st_size
                        quality["config_files"].append({
                            "path": str(config_file.relative_to(self.project_root)),
                            "type": config_file.suffix,
                            "size_kb": round(size / 1024, 2)
                        })
                    except:
                        pass
        
        # 文档文件分析
        for md_file in self.project_root.rglob("*.md"):
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                quality["documentation_files"].append({
                    "path": str(md_file.relative_to(self.project_root)),
                    "lines": len(content.split('\n')),
                    "size_kb": round(len(content.encode('utf-8')) / 1024, 2),
                    "has_toc": any(line.strip().startswith('- [') for line in content.split('\n')[:50])
                })
            except:
                pass
        
        self.analysis_results["code_quality"] = quality
        return quality
    
    def analyze_security_issues(self) -> Dict[str, Any]:
        """分析安全问题"""
        print("🔒 分析安全问题...")
        
        security = {
            "sensitive_files": [],
            "hardcoded_secrets": [],
            "insecure_patterns": [],
            "permission_issues": []
        }
        
        # 敏感文件检测
        sensitive_patterns = [
            '*.key', '*.pem', '*.p12', '*.pfx',
            '.env', '.env.*', 'secrets.*', 'password*',
            'id_rsa', 'id_dsa', 'id_ecdsa'
        ]
        
        for pattern in sensitive_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    security["sensitive_files"].append(str(file_path.relative_to(self.project_root)))
        
        # 硬编码密钥检测
        secret_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']',
            r'token\s*=\s*["\'][^"\']+["\']'
        ]
        
        import re
        for py_file in self.project_root.rglob("*.py"):
            if 'node_modules' not in str(py_file):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for pattern in secret_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            security["hardcoded_secrets"].append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "matches": matches
                            })
                except:
                    pass
        
        self.analysis_results["security_analysis"] = security
        return security
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("💡 生成改进建议...")
        
        recommendations = []
        
        # 基于项目结构的建议
        structure = self.analysis_results["project_structure"]
        
        if structure["large_files"]:
            recommendations.append({
                "category": "存储优化",
                "priority": "HIGH",
                "issue": f"发现{len(structure['large_files'])}个大文件",
                "suggestion": "考虑使用Git LFS管理大文件，或将其移至外部存储"
            })
        
        if structure["empty_files"]:
            recommendations.append({
                "category": "文件清理",
                "priority": "LOW",
                "issue": f"发现{len(structure['empty_files'])}个空文件",
                "suggestion": "删除不必要的空文件以保持项目整洁"
            })
        
        if structure["duplicate_files"]:
            recommendations.append({
                "category": "重复文件",
                "priority": "MEDIUM",
                "issue": f"发现{len(structure['duplicate_files'])}组重复文件",
                "suggestion": "删除重复文件或使用符号链接"
            })
        
        # 基于代码质量的建议
        quality = self.analysis_results["code_quality"]
        
        python_without_docstring = sum(1 for f in quality["python_files"] if not f["has_docstring"])
        if python_without_docstring > 0:
            recommendations.append({
                "category": "代码质量",
                "priority": "MEDIUM",
                "issue": f"{python_without_docstring}个Python文件缺少文档字符串",
                "suggestion": "为Python模块和函数添加文档字符串"
            })
        
        # 基于安全分析的建议
        security = self.analysis_results["security_analysis"]
        
        if security["sensitive_files"]:
            recommendations.append({
                "category": "安全",
                "priority": "CRITICAL",
                "issue": f"发现{len(security['sensitive_files'])}个敏感文件",
                "suggestion": "将敏感文件添加到.gitignore，避免提交到版本控制"
            })
        
        if security["hardcoded_secrets"]:
            recommendations.append({
                "category": "安全",
                "priority": "CRITICAL",
                "issue": f"发现{len(security['hardcoded_secrets'])}个硬编码密钥",
                "suggestion": "使用环境变量或配置文件管理敏感信息"
            })
        
        self.analysis_results["recommendations"] = recommendations
        return recommendations
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始MCP文件系统深度分析...")
        
        try:
            # 执行各项分析
            self.analyze_project_structure()
            self.analyze_code_quality()
            self.analyze_security_issues()
            self.generate_recommendations()
            
            # 生成报告
            self.generate_report()
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {e}")
    
    def generate_report(self):
        """生成分析报告"""
        timestamp = int(time.time())
        report_file = f"mcp_filesystem_analysis_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        # 生成摘要
        structure = self.analysis_results["project_structure"]
        quality = self.analysis_results["code_quality"]
        security = self.analysis_results["security_analysis"]
        
        print("\n" + "="*60)
        print("📊 MCP文件系统深度分析报告")
        print("="*60)
        print(f"📁 总文件数: {structure['total_files']}")
        print(f"📂 总目录数: {structure['total_directories']}")
        print(f"🐍 Python文件: {len(quality['python_files'])}")
        print(f"🌐 JavaScript文件: {len(quality['javascript_files'])}")
        print(f"🎨 Vue文件: {len(quality['vue_files'])}")
        print(f"📋 配置文件: {len(quality['config_files'])}")
        print(f"📖 文档文件: {len(quality['documentation_files'])}")
        print(f"🚨 安全问题: {len(security['sensitive_files']) + len(security['hardcoded_secrets'])}")
        print(f"💡 改进建议: {len(self.analysis_results['recommendations'])}")
        print(f"📄 详细报告: {report_file}")
        print("="*60)

if __name__ == "__main__":
    analyzer = MCPFileSystemAnalyzer()
    analyzer.run_full_analysis()

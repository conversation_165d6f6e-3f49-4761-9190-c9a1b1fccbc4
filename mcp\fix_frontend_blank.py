#!/usr/bin/env python3
"""
前端空白页面修复脚本
自动修复常见的导致页面空白的问题
"""

import os
import shutil
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Any

def run_command(cmd: str, cwd: str = None) -> tuple[bool, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True,
            timeout=60
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令执行超时"
    except Exception as e:
        return False, str(e)

def fix_dependencies():
    """修复依赖问题"""
    print("🔧 修复依赖问题...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    if not frontend_path.exists():
        print("❌ 前端目录不存在")
        return False
    
    # 删除 node_modules 和 lock 文件
    print("📦 清理依赖...")
    
    node_modules = frontend_path / "node_modules"
    if node_modules.exists():
        print("🗑️ 删除 node_modules...")
        shutil.rmtree(node_modules, ignore_errors=True)
    
    lock_files = [
        frontend_path / "package-lock.json",
        frontend_path / "yarn.lock",
        frontend_path / "pnpm-lock.yaml"
    ]
    
    for lock_file in lock_files:
        if lock_file.exists():
            print(f"🗑️ 删除 {lock_file.name}...")
            lock_file.unlink()
    
    # 重新安装依赖
    print("📦 重新安装依赖...")
    success, output = run_command("npm install", str(frontend_path))
    
    if success:
        print("✅ 依赖安装成功")
        return True
    else:
        print(f"❌ 依赖安装失败: {output}")
        return False

def fix_cache():
    """清理缓存"""
    print("🧹 清理缓存...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 清理 Vite 缓存
    vite_cache = frontend_path / "node_modules" / ".vite"
    if vite_cache.exists():
        print("🗑️ 清理 Vite 缓存...")
        shutil.rmtree(vite_cache, ignore_errors=True)
    
    # 清理 TypeScript 缓存
    ts_cache = frontend_path / "node_modules" / ".tmp"
    if ts_cache.exists():
        print("🗑️ 清理 TypeScript 缓存...")
        shutil.rmtree(ts_cache, ignore_errors=True)
    
    # 清理 dist 目录
    dist_dir = frontend_path / "dist"
    if dist_dir.exists():
        print("🗑️ 清理构建目录...")
        shutil.rmtree(dist_dir, ignore_errors=True)
    
    print("✅ 缓存清理完成")

def fix_config_issues():
    """修复配置问题"""
    print("⚙️ 检查配置文件...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 检查 .env 文件
    env_dev = frontend_path / ".env.development"
    if not env_dev.exists():
        print("📝 创建 .env.development 文件...")
        env_content = """# 开发环境配置
VITE_USE_MOCK=false
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/api/v1/ws
VITE_APP_TITLE=量化投资平台
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=true
VITE_ENABLE_DEVTOOLS=true
"""
        env_dev.write_text(env_content, encoding='utf-8')
        print("✅ .env.development 文件已创建")
    
    # 检查 vite.config.ts
    vite_config = frontend_path / "vite.config.ts"
    if vite_config.exists():
        print("✅ vite.config.ts 存在")
    else:
        print("❌ vite.config.ts 不存在")
        return False
    
    return True

def create_minimal_main():
    """创建最小化的 main.ts 用于测试"""
    print("🔧 创建最小化测试文件...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 备份原始 main.ts
    main_ts = frontend_path / "src" / "main.ts"
    if main_ts.exists():
        backup_path = frontend_path / "src" / "main.ts.backup"
        if not backup_path.exists():
            shutil.copy2(main_ts, backup_path)
            print("📋 已备份原始 main.ts")
    
    # 创建最小化版本
    minimal_content = '''import { createApp } from 'vue'

const app = createApp({
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1 style="color: #409EFF;">🎉 Vue应用正常运行！</h1>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="count++" style="padding: 8px 16px; background: #409EFF; color: white; border: none; border-radius: 4px;">
        点击测试 ({{ count }})
      </button>
    </div>
  `,
  data() {
    return {
      count: 0,
      currentTime: new Date().toLocaleString()
    }
  },
  mounted() {
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
    console.log('✅ 最小化应用启动成功')
  }
})

app.config.errorHandler = (err) => {
  console.error('Vue错误:', err)
}

app.mount('#app')
'''
    
    test_main = frontend_path / "src" / "main-test.ts"
    test_main.write_text(minimal_content, encoding='utf-8')
    print("✅ 最小化测试文件已创建: main-test.ts")

def create_test_html():
    """创建测试HTML页面"""
    print("📄 创建测试HTML页面...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    test_html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端修复测试</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端修复测试页面</h1>
        <div id="status"></div>
        <button onclick="testMinimal()">测试最小化应用</button>
        <button onclick="testOriginal()">测试原始应用</button>
        <button onclick="window.location.reload()">刷新页面</button>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusDiv.appendChild(div);
        }

        function testMinimal() {
            window.open('/test-minimal.html', '_blank');
        }

        function testOriginal() {
            window.open('/', '_blank');
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            showStatus('✅ 测试页面加载成功');
        });
    </script>
</body>
</html>'''
    
    test_html = frontend_path / "public" / "test-fix.html"
    test_html.write_text(test_html_content, encoding='utf-8')
    print("✅ 测试HTML页面已创建: test-fix.html")

def restart_dev_server():
    """重启开发服务器"""
    print("🔄 重启开发服务器...")
    
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 尝试停止现有服务器
    print("⏹️ 停止现有服务器...")
    run_command("taskkill /f /im node.exe", str(frontend_path))
    
    # 等待一下
    import time
    time.sleep(2)
    
    # 启动新的服务器
    print("▶️ 启动开发服务器...")
    success, output = run_command("npm run dev", str(frontend_path))
    
    if success:
        print("✅ 开发服务器启动成功")
        return True
    else:
        print(f"❌ 开发服务器启动失败: {output}")
        return False

def generate_fix_report():
    """生成修复报告"""
    print("\n📊 修复报告")
    print("=" * 50)
    
    fixes_applied = [
        "✅ 清理了依赖和缓存",
        "✅ 重新安装了npm包",
        "✅ 检查了配置文件",
        "✅ 创建了测试文件",
        "✅ 创建了调试页面"
    ]
    
    for fix in fixes_applied:
        print(fix)
    
    print("\n🔗 测试链接:")
    print("- 主应用: http://localhost:5173")
    print("- 调试工具: http://localhost:5173/debug-blank.html")
    print("- 修复测试: http://localhost:5173/test-fix.html")
    print("- 简化测试: http://localhost:5173/test-simple.html")
    
    print("\n💡 如果问题仍然存在:")
    print("1. 检查浏览器控制台错误")
    print("2. 确认后端服务正在运行")
    print("3. 尝试使用不同的浏览器")
    print("4. 检查防火墙和代理设置")

def main():
    """主函数"""
    print("🔧 前端空白页面修复工具")
    print("=" * 60)
    print("正在自动修复常见问题...\n")
    
    try:
        # 1. 清理缓存
        fix_cache()
        
        # 2. 修复依赖
        if not fix_dependencies():
            print("❌ 依赖修复失败，请手动检查")
            return
        
        # 3. 检查配置
        if not fix_config_issues():
            print("❌ 配置检查失败")
            return
        
        # 4. 创建测试文件
        create_minimal_main()
        create_test_html()
        
        # 5. 生成报告
        generate_fix_report()
        
        print(f"\n✅ 修复完成！")
        print("🌐 请在浏览器中访问 http://localhost:5173 测试")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        print("💡 请手动检查并修复问题")

if __name__ == "__main__":
    main()

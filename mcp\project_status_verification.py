#!/usr/bin/env python3
"""
量化投资平台项目状态验证
基于深度分析报告，验证关键问题是否已修复
"""

import asyncio
import subprocess
import sys
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from loguru import logger


class ProjectStatusVerifier:
    """项目状态验证器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.backend_path = self.project_root / "backend"
        self.frontend_path = self.project_root / "frontend"
        
        self.verification_results = {
            "timestamp": datetime.now().isoformat(),
            "p0_critical_issues": {},
            "p1_high_priority": {},
            "p2_medium_priority": {},
            "overall_status": {},
            "recommendations": []
        }
    
    def verify_p0_critical_issues(self):
        """验证P0致命问题"""
        logger.info("🔴 验证P0致命问题...")
        
        results = {}
        
        # 1. FastAPI类型系统检查
        results["fastapi_type_system"] = self._check_fastapi_types()
        
        # 2. 循环导入检查
        results["circular_imports"] = self._check_circular_imports()
        
        # 3. 数据库配置检查
        results["database_config"] = self._check_database_config()
        
        # 4. 应用启动测试
        results["app_startup"] = self._test_app_startup()
        
        self.verification_results["p0_critical_issues"] = results
        return results
    
    def _check_fastapi_types(self) -> Dict[str, Any]:
        """检查FastAPI类型系统问题"""
        logger.info("检查FastAPI类型系统...")
        
        try:
            # 检查关键API文件
            api_files = [
                self.backend_path / "app" / "api" / "v1" / "market.py",
                self.backend_path / "app" / "api" / "v1" / "trading.py",
                self.backend_path / "app" / "api" / "v1" / "auth.py"
            ]
            
            issues = []
            for file_path in api_files:
                if file_path.exists():
                    content = file_path.read_text(encoding='utf-8')
                    
                    # 检查AsyncSession类型问题
                    if "AsyncSession" in content and "Depends" in content:
                        if "db: AsyncSession = Depends" in content:
                            issues.append(f"{file_path.name}: AsyncSession类型可能导致Pydantic验证失败")
                    
                    # 检查其他类型问题
                    if "from sqlalchemy.ext.asyncio import AsyncSession" in content:
                        if "response_model" in content:
                            issues.append(f"{file_path.name}: 可能存在响应模型类型冲突")
            
            return {
                "status": "resolved" if not issues else "exists",
                "issues_found": len(issues),
                "details": issues,
                "files_checked": len([f for f in api_files if f.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "issues_found": -1
            }
    
    def _check_circular_imports(self) -> Dict[str, Any]:
        """检查循环导入问题"""
        logger.info("检查循环导入...")
        
        try:
            # 检查conftest.py和main.py
            conftest_path = self.project_root / "tests" / "conftest.py"
            main_path = self.backend_path / "app" / "main.py"
            
            issues = []
            
            if conftest_path.exists() and main_path.exists():
                conftest_content = conftest_path.read_text(encoding='utf-8')
                main_content = main_path.read_text(encoding='utf-8')
                
                # 检查相互导入
                if "from app.main" in conftest_content or "import app.main" in conftest_content:
                    if "from tests" in main_content or "import tests" in main_content:
                        issues.append("conftest.py ↔ main.py 循环导入")
                
                # 检查其他潜在循环导入
                if "from app" in conftest_content and "conftest" in main_content:
                    issues.append("可能存在测试配置循环导入")
            
            return {
                "status": "resolved" if not issues else "exists",
                "issues_found": len(issues),
                "details": issues,
                "conftest_exists": conftest_path.exists(),
                "main_exists": main_path.exists()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "issues_found": -1
            }
    
    def _check_database_config(self) -> Dict[str, Any]:
        """检查数据库配置"""
        logger.info("检查数据库配置...")
        
        try:
            db_config_path = self.backend_path / "app" / "core" / "database.py"
            
            if not db_config_path.exists():
                return {
                    "status": "missing",
                    "error": "database.py文件不存在"
                }
            
            content = db_config_path.read_text(encoding='utf-8')
            
            issues = []
            
            # 检查硬编码SQLite
            if "sqlite:///" in content and "settings.DATABASE_URL" not in content:
                issues.append("数据库URL被硬编码为SQLite")
            
            # 检查环境变量使用
            if "os.getenv" not in content and "settings." not in content:
                issues.append("未使用环境变量配置")
            
            # 检查配置灵活性
            if "postgresql" not in content.lower() and "mysql" not in content.lower():
                issues.append("可能不支持生产数据库")
            
            return {
                "status": "resolved" if not issues else "exists",
                "issues_found": len(issues),
                "details": issues,
                "uses_settings": "settings." in content,
                "supports_postgres": "postgresql" in content.lower()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "issues_found": -1
            }
    
    def _test_app_startup(self) -> Dict[str, Any]:
        """测试应用启动"""
        logger.info("测试应用启动...")
        
        try:
            # 检查主应用文件
            main_files = [
                self.backend_path / "app" / "main.py",
                self.backend_path / "app" / "main_stable.py"
            ]
            
            startup_issues = []
            
            for main_file in main_files:
                if main_file.exists():
                    try:
                        # 尝试语法检查
                        result = subprocess.run([
                            sys.executable, "-m", "py_compile", str(main_file)
                        ], capture_output=True, text=True, timeout=10)
                        
                        if result.returncode != 0:
                            startup_issues.append(f"{main_file.name}: 语法错误 - {result.stderr}")
                        
                    except subprocess.TimeoutExpired:
                        startup_issues.append(f"{main_file.name}: 编译超时")
                    except Exception as e:
                        startup_issues.append(f"{main_file.name}: 检查失败 - {str(e)}")
            
            return {
                "status": "healthy" if not startup_issues else "issues",
                "issues_found": len(startup_issues),
                "details": startup_issues,
                "main_files_found": len([f for f in main_files if f.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "issues_found": -1
            }
    
    def verify_p1_high_priority(self):
        """验证P1高优先级问题"""
        logger.info("🟡 验证P1高优先级问题...")
        
        results = {}
        
        # 1. 交易功能检查
        results["trading_functionality"] = self._check_trading_features()
        
        # 2. 市场数据可视化检查
        results["market_visualization"] = self._check_market_charts()
        
        # 3. 路由系统检查
        results["routing_system"] = self._check_routing_issues()
        
        self.verification_results["p1_high_priority"] = results
        return results
    
    def _check_trading_features(self) -> Dict[str, Any]:
        """检查交易功能"""
        logger.info("检查交易功能...")
        
        try:
            # 检查交易终端页面
            trading_pages = [
                self.frontend_path / "src" / "views" / "trading" / "TradingTerminal.vue",
                self.frontend_path / "src" / "views" / "TradingTerminal.vue",
                self.frontend_path / "public" / "trading-terminal.html"
            ]
            
            features_found = []
            issues = []
            
            for page_path in trading_pages:
                if page_path.exists():
                    content = page_path.read_text(encoding='utf-8')
                    
                    # 检查交易功能组件
                    if len(content) < 100:
                        issues.append(f"{page_path.name}: 内容过少 ({len(content)} 字符)")
                    
                    # 检查关键功能
                    if "买入" in content or "buy" in content.lower():
                        features_found.append("买入功能")
                    if "卖出" in content or "sell" in content.lower():
                        features_found.append("卖出功能")
                    if "价格" in content or "price" in content.lower():
                        features_found.append("价格输入")
                    if "数量" in content or "quantity" in content.lower():
                        features_found.append("数量输入")
                    if "搜索" in content or "search" in content.lower():
                        features_found.append("股票搜索")
            
            return {
                "status": "improved" if len(features_found) > 3 else "incomplete",
                "features_found": features_found,
                "issues": issues,
                "pages_checked": len([p for p in trading_pages if p.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_market_charts(self) -> Dict[str, Any]:
        """检查市场数据图表"""
        logger.info("检查市场数据图表...")
        
        try:
            # 检查图表相关文件
            chart_files = [
                self.frontend_path / "src" / "components" / "charts",
                self.frontend_path / "src" / "views" / "market",
                self.frontend_path / "public" / "market-data.html"
            ]
            
            chart_features = []
            issues = []
            
            for path in chart_files:
                if path.exists():
                    if path.is_dir():
                        # 检查目录中的图表组件
                        for file in path.rglob("*.vue"):
                            content = file.read_text(encoding='utf-8')
                            if "echarts" in content.lower():
                                chart_features.append(f"ECharts组件: {file.name}")
                            if "chart" in content.lower():
                                chart_features.append(f"图表组件: {file.name}")
                    else:
                        # 检查单个文件
                        content = path.read_text(encoding='utf-8')
                        if "echarts" in content.lower():
                            chart_features.append("ECharts集成")
                        if "k线" in content or "kline" in content.lower():
                            chart_features.append("K线图")
                        if "深度" in content or "depth" in content.lower():
                            chart_features.append("深度图")
            
            return {
                "status": "improved" if chart_features else "missing",
                "features_found": chart_features,
                "issues": issues,
                "paths_checked": len([p for p in chart_files if p.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_routing_issues(self) -> Dict[str, Any]:
        """检查路由系统问题"""
        logger.info("检查路由系统...")
        
        try:
            # 检查API路由文件
            api_dir = self.backend_path / "app" / "api" / "v1"
            
            if not api_dir.exists():
                return {
                    "status": "missing",
                    "error": "API目录不存在"
                }
            
            route_files = list(api_dir.glob("*.py"))
            fixed_files = list(api_dir.glob("*_fixed.py"))
            
            issues = []
            
            # 检查重复路由
            if fixed_files:
                issues.append(f"发现 {len(fixed_files)} 个 *_fixed.py 文件，可能存在重复路由")
            
            # 检查路由完整性
            expected_routes = ["auth.py", "market.py", "trading.py", "strategy.py"]
            missing_routes = []
            
            for route in expected_routes:
                if not (api_dir / route).exists():
                    missing_routes.append(route)
            
            if missing_routes:
                issues.append(f"缺少路由文件: {missing_routes}")
            
            return {
                "status": "clean" if not issues else "issues",
                "total_route_files": len(route_files),
                "fixed_files": len(fixed_files),
                "missing_routes": missing_routes,
                "issues": issues
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def verify_p2_medium_priority(self):
        """验证P2中优先级问题"""
        logger.info("🟢 验证P2中优先级问题...")
        
        results = {}
        
        # 1. 依赖版本冲突检查
        results["dependency_conflicts"] = self._check_dependencies()
        
        # 2. 监控系统检查
        results["monitoring_system"] = self._check_monitoring()
        
        # 3. 响应式设计检查
        results["responsive_design"] = self._check_responsive_design()
        
        self.verification_results["p2_medium_priority"] = results
        return results
    
    def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖版本冲突"""
        logger.info("检查依赖版本...")
        
        try:
            requirements_files = [
                self.backend_path / "requirements.txt",
                self.project_root / "requirements.txt",
                self.backend_path / "pyproject.toml"
            ]
            
            conflicts = []
            dependencies = {}
            
            for req_file in requirements_files:
                if req_file.exists():
                    content = req_file.read_text(encoding='utf-8')
                    
                    # 检查cryptography版本冲突
                    if "cryptography" in content:
                        lines = [line for line in content.split('\n') if 'cryptography' in line]
                        if len(lines) > 1:
                            conflicts.append(f"cryptography版本冲突: {lines}")
                    
                    # 检查vnpy兼容性
                    if "vnpy" in content and "pandas" in content:
                        dependencies["vnpy_with_pandas"] = True
            
            return {
                "status": "clean" if not conflicts else "conflicts",
                "conflicts_found": conflicts,
                "dependencies_checked": dependencies,
                "files_checked": len([f for f in requirements_files if f.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_monitoring(self) -> Dict[str, Any]:
        """检查监控系统"""
        logger.info("检查监控系统...")
        
        try:
            # 检查监控相关文件
            monitoring_files = [
                self.backend_path / "app" / "core" / "monitoring.py",
                self.backend_path / "app" / "services" / "storage_monitor.py",
                self.backend_path / "app" / "middleware" / "monitoring_middleware.py"
            ]
            
            features = []
            issues = []
            
            for file_path in monitoring_files:
                if file_path.exists():
                    content = file_path.read_text(encoding='utf-8')
                    
                    if "prometheus" in content.lower():
                        features.append("Prometheus集成")
                    if "health" in content.lower():
                        features.append("健康检查")
                    if "metrics" in content.lower():
                        features.append("指标收集")
                    if "alert" in content.lower():
                        features.append("告警系统")
                else:
                    issues.append(f"缺少监控文件: {file_path.name}")
            
            return {
                "status": "enhanced" if features else "missing",
                "features_found": features,
                "issues": issues,
                "files_checked": len([f for f in monitoring_files if f.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_responsive_design(self) -> Dict[str, Any]:
        """检查响应式设计"""
        logger.info("检查响应式设计...")
        
        try:
            # 检查CSS和Vue文件
            frontend_files = []
            
            if self.frontend_path.exists():
                frontend_files.extend(list(self.frontend_path.rglob("*.vue")))
                frontend_files.extend(list(self.frontend_path.rglob("*.css")))
            
            responsive_features = []
            issues = []
            
            for file_path in frontend_files[:20]:  # 限制检查文件数量
                try:
                    content = file_path.read_text(encoding='utf-8')
                    
                    if "@media" in content:
                        responsive_features.append(f"媒体查询: {file_path.name}")
                    if "responsive" in content.lower():
                        responsive_features.append(f"响应式类: {file_path.name}")
                    if "mobile" in content.lower():
                        responsive_features.append(f"移动端适配: {file_path.name}")
                    
                    # 检查可能的横向滚动问题
                    if "overflow-x" in content and "hidden" not in content:
                        issues.append(f"可能的横向滚动: {file_path.name}")
                        
                except Exception:
                    continue
            
            return {
                "status": "improved" if responsive_features else "basic",
                "features_found": responsive_features[:10],  # 限制显示数量
                "issues": issues,
                "files_checked": len(frontend_files)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def calculate_overall_status(self):
        """计算整体状态"""
        logger.info("计算整体项目状态...")
        
        p0_results = self.verification_results["p0_critical_issues"]
        p1_results = self.verification_results["p1_high_priority"]
        p2_results = self.verification_results["p2_medium_priority"]
        
        # 计算各级别问题解决率
        p0_resolved = sum(1 for r in p0_results.values() if r.get("status") in ["resolved", "healthy"])
        p0_total = len(p0_results)
        
        p1_improved = sum(1 for r in p1_results.values() if r.get("status") in ["improved", "clean"])
        p1_total = len(p1_results)
        
        p2_enhanced = sum(1 for r in p2_results.values() if r.get("status") in ["enhanced", "clean", "improved"])
        p2_total = len(p2_results)
        
        # 计算总体完成度
        total_score = (p0_resolved * 3 + p1_improved * 2 + p2_enhanced * 1)
        max_score = (p0_total * 3 + p1_total * 2 + p2_total * 1)
        
        completion_rate = (total_score / max_score * 100) if max_score > 0 else 0
        
        # 生成建议
        recommendations = []
        
        if p0_resolved < p0_total:
            recommendations.append("🔴 优先修复剩余的P0致命问题")
        
        if p1_improved < p1_total:
            recommendations.append("🟡 继续完善P1高优先级功能")
        
        if completion_rate > 80:
            recommendations.append("🎉 项目状态良好，可以考虑新功能开发")
        elif completion_rate > 60:
            recommendations.append("✅ 项目基本可用，建议继续优化")
        else:
            recommendations.append("⚠️ 项目需要更多修复工作")
        
        self.verification_results["overall_status"] = {
            "completion_rate": round(completion_rate, 1),
            "p0_resolution_rate": round(p0_resolved / p0_total * 100, 1) if p0_total > 0 else 100,
            "p1_improvement_rate": round(p1_improved / p1_total * 100, 1) if p1_total > 0 else 100,
            "p2_enhancement_rate": round(p2_enhanced / p2_total * 100, 1) if p2_total > 0 else 100,
            "project_health": "excellent" if completion_rate > 90 else
                            "good" if completion_rate > 75 else
                            "fair" if completion_rate > 50 else "poor"
        }
        
        self.verification_results["recommendations"] = recommendations
    
    def generate_report(self):
        """生成验证报告"""
        logger.info("生成项目状态验证报告...")
        
        report = f"""
# 量化投资平台项目状态验证报告

## 📊 验证概述
- **验证时间**: {self.verification_results['timestamp']}
- **项目完成度**: {self.verification_results['overall_status']['completion_rate']}%
- **项目健康度**: {self.verification_results['overall_status']['project_health']}

## 🔴 P0致命问题验证结果
"""
        
        for issue, result in self.verification_results["p0_critical_issues"].items():
            status_emoji = "✅" if result.get("status") in ["resolved", "healthy"] else "❌"
            report += f"- **{issue}**: {status_emoji} {result.get('status', 'unknown')}\n"
            if result.get("details"):
                for detail in result["details"][:3]:  # 限制显示数量
                    report += f"  - {detail}\n"
        
        report += f"""
## 🟡 P1高优先级问题验证结果
"""
        
        for issue, result in self.verification_results["p1_high_priority"].items():
            status_emoji = "✅" if result.get("status") in ["improved", "clean"] else "⚠️"
            report += f"- **{issue}**: {status_emoji} {result.get('status', 'unknown')}\n"
            if result.get("features_found"):
                report += f"  - 发现功能: {len(result['features_found'])} 项\n"
        
        report += f"""
## 🟢 P2中优先级问题验证结果
"""
        
        for issue, result in self.verification_results["p2_medium_priority"].items():
            status_emoji = "✅" if result.get("status") in ["enhanced", "clean", "improved"] else "⚠️"
            report += f"- **{issue}**: {status_emoji} {result.get('status', 'unknown')}\n"
        
        report += f"""
## 📈 整体状态评估
- **P0问题解决率**: {self.verification_results['overall_status']['p0_resolution_rate']}%
- **P1问题改善率**: {self.verification_results['overall_status']['p1_improvement_rate']}%
- **P2问题增强率**: {self.verification_results['overall_status']['p2_enhancement_rate']}%

## 💡 建议
"""
        
        for recommendation in self.verification_results["recommendations"]:
            report += f"- {recommendation}\n"
        
        return report
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🚀 开始项目状态验证...")
        
        # 验证各级别问题
        self.verify_p0_critical_issues()
        self.verify_p1_high_priority()
        self.verify_p2_medium_priority()
        
        # 计算整体状态
        self.calculate_overall_status()
        
        # 生成报告
        report = self.generate_report()
        
        # 保存报告
        report_file = f"project_status_verification_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.success(f"✅ 验证完成，报告已保存: {report_file}")
        
        return self.verification_results


def main():
    """主函数"""
    verifier = ProjectStatusVerifier()
    results = verifier.run_verification()
    
    # 输出关键结果
    overall = results["overall_status"]
    print(f"\n🎯 项目状态验证结果:")
    print(f"   完成度: {overall['completion_rate']}%")
    print(f"   健康度: {overall['project_health']}")
    print(f"   P0解决率: {overall['p0_resolution_rate']}%")
    print(f"   P1改善率: {overall['p1_improvement_rate']}%")
    print(f"   P2增强率: {overall['p2_enhancement_rate']}%")
    
    return results


if __name__ == "__main__":
    main()

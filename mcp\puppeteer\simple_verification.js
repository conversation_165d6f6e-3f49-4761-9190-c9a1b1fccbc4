/**
 * 简化的修复验证脚本
 */

const puppeteer = require('puppeteer');

async function verifyFixes() {
    console.log('🚀 开始验证修复效果...\n');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 }
    });
    
    const page = await browser.newPage();
    
    try {
        // 测试1: 前端页面加载
        console.log('📱 测试1: 前端页面加载...');
        const startTime = Date.now();
        await page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
        const loadTime = Date.now() - startTime;
        console.log(`✅ 页面加载时间: ${loadTime}ms`);
        
        // 测试2: 检查导航结构
        console.log('\n🧭 测试2: 检查导航结构...');
        
        // 等待侧边栏加载
        await page.waitForSelector('.layout-sidebar', { timeout: 10000 });
        
        // 检查菜单项
        const menuItems = await page.$$eval('.sidebar-nav .el-menu-item, .sidebar-nav .el-sub-menu', 
            elements => elements.length
        );
        console.log(`✅ 侧边栏菜单项数量: ${menuItems}`);
        
        // 检查快速导航
        const quickNavExists = await page.$('.quick-navigation-section') !== null;
        console.log(`${quickNavExists ? '✅' : '❌'} 快速导航区域: ${quickNavExists ? '存在' : '缺失'}`);
        
        if (quickNavExists) {
            const navItems = await page.$$eval('.nav-item', elements => elements.length);
            console.log(`✅ 快速导航项数量: ${navItems}`);
        }
        
        // 检查面包屑
        const breadcrumbExists = await page.$('.el-breadcrumb') !== null;
        console.log(`${breadcrumbExists ? '✅' : '❌'} 面包屑导航: ${breadcrumbExists ? '存在' : '缺失'}`);
        
        // 测试3: 检查后端连接
        console.log('\n📡 测试3: 检查后端API连接...');
        
        const apiTest = await page.evaluate(async () => {
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                return { success: true, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
        
        if (apiTest.success) {
            console.log(`✅ 后端健康检查: 状态码 ${apiTest.status}`);
            console.log(`✅ 响应数据: ${JSON.stringify(apiTest.data)}`);
        } else {
            console.log(`❌ 后端连接失败: ${apiTest.error}`);
        }
        
        // 测试4: 检查控制台错误
        console.log('\n🔍 测试4: 检查控制台错误...');
        
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });
        
        // 刷新页面收集错误
        await page.reload({ waitUntil: 'networkidle0' });
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log(`${consoleErrors.length === 0 ? '✅' : '⚠️'} 控制台错误数量: ${consoleErrors.length}`);
        if (consoleErrors.length > 0) {
            console.log('错误详情:', consoleErrors.slice(0, 3));
        }
        
        // 测试5: 交互性能测试
        console.log('\n⚡ 测试5: 交互性能测试...');
        
        const interactionStart = Date.now();
        await page.click('button', { timeout: 5000 });
        const interactionTime = Date.now() - interactionStart;
        console.log(`✅ 交互响应时间: ${interactionTime}ms`);
        
        // 生成总结
        console.log('\n📊 修复验证总结:');
        console.log('==================');
        
        const scores = {
            pageLoad: loadTime < 3000 ? 100 : Math.max(0, 100 - (loadTime - 3000) / 100),
            navigation: (menuItems >= 6 ? 25 : 0) + (quickNavExists ? 25 : 0) + (breadcrumbExists ? 25 : 0) + 25,
            backend: apiTest.success ? 100 : 0,
            console: consoleErrors.length === 0 ? 100 : Math.max(0, 100 - consoleErrors.length * 20),
            interaction: interactionTime < 200 ? 100 : Math.max(0, 100 - (interactionTime - 200) / 10)
        };
        
        const totalScore = Math.round(
            (scores.pageLoad * 0.2 + scores.navigation * 0.3 + scores.backend * 0.3 + scores.console * 0.1 + scores.interaction * 0.1)
        );
        
        console.log(`📱 页面加载性能: ${Math.round(scores.pageLoad)}/100`);
        console.log(`🧭 导航结构完整性: ${Math.round(scores.navigation)}/100`);
        console.log(`📡 后端服务可用性: ${Math.round(scores.backend)}/100`);
        console.log(`🔍 控制台错误控制: ${Math.round(scores.console)}/100`);
        console.log(`⚡ 交互响应性能: ${Math.round(scores.interaction)}/100`);
        console.log('==================');
        console.log(`🎯 总体评分: ${totalScore}/100`);
        
        if (totalScore >= 90) {
            console.log('🌟 优秀！所有问题都已成功修复！');
        } else if (totalScore >= 80) {
            console.log('✅ 良好！主要问题已修复，还有小幅优化空间。');
        } else if (totalScore >= 70) {
            console.log('⚠️ 一般，部分问题已修复，仍需进一步优化。');
        } else {
            console.log('❌ 需要改进，多个问题仍需修复。');
        }
        
        // 具体建议
        console.log('\n💡 改进建议:');
        if (scores.pageLoad < 80) console.log('- 优化页面加载性能');
        if (scores.navigation < 80) console.log('- 完善导航结构');
        if (scores.backend < 80) console.log('- 修复后端服务连接');
        if (scores.console < 80) console.log('- 解决控制台错误');
        if (scores.interaction < 80) console.log('- 优化交互响应速度');
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error.message);
    } finally {
        await browser.close();
    }
}

verifyFixes().catch(console.error);

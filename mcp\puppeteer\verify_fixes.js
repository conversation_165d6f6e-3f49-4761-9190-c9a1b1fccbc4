/**
 * 验证修复效果的测试脚本
 * 使用Puppeteer验证所有问题是否已解决
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const http = require('http');

class FixVerificationTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            timestamp: new Date().toISOString(),
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                score: 0
            }
        };
    }

    async init() {
        console.log('🚀 启动修复验证测试...');
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        
        // 监听控制台消息
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ 控制台错误:', msg.text());
            }
        });
    }

    async testBackendService() {
        console.log('\n📡 测试1: 验证后端服务是否正常运行...');

        try {
            // 使用页面来测试后端API
            const healthResponse = await this.page.evaluate(async () => {
                try {
                    const response = await fetch('http://localhost:8000/health');
                    const data = await response.json();
                    return { status: response.status, data };
                } catch (error) {
                    return { status: 0, error: error.message };
                }
            });

            const healthTest = {
                name: '后端健康检查',
                status: healthResponse.status === 200 ? 'PASS' : 'FAIL',
                details: `状态码: ${healthResponse.status}, 响应: ${JSON.stringify(healthResponse.data || healthResponse.error)}`
            };

            // 测试市场数据接口
            const marketResponse = await this.page.evaluate(async () => {
                try {
                    const response = await fetch('http://localhost:8000/api/v1/market/overview');
                    const data = await response.json();
                    return { status: response.status, data };
                } catch (error) {
                    return { status: 0, error: error.message };
                }
            });

            const marketTest = {
                name: '市场数据API',
                status: marketResponse.status === 200 && marketResponse.data?.success ? 'PASS' : 'FAIL',
                details: `状态码: ${marketResponse.status}, 数据完整性: ${marketResponse.data?.success ? '✅' : '❌'}`
            };

            this.results.tests.push(healthTest, marketTest);

            console.log(`✅ 后端健康检查: ${healthTest.status}`);
            console.log(`✅ 市场数据API: ${marketTest.status}`);

            return healthTest.status === 'PASS' && marketTest.status === 'PASS';

        } catch (error) {
            const errorTest = {
                name: '后端服务连接',
                status: 'FAIL',
                details: `连接错误: ${error.message}`
            };
            this.results.tests.push(errorTest);
            console.log('❌ 后端服务测试失败:', error.message);
            return false;
        }
    }

    async testNavigationStructure() {
        console.log('\n🧭 测试2: 验证导航结构是否完善...');
        
        try {
            await this.page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
            
            // 等待页面加载
            await this.page.waitForSelector('.layout-sidebar', { timeout: 10000 });
            
            // 检查侧边栏菜单项
            const menuItems = await this.page.$$eval('.sidebar-nav .el-menu-item, .sidebar-nav .el-sub-menu', 
                elements => elements.length
            );
            
            // 检查快速导航区域
            const quickNavExists = await this.page.$('.quick-navigation-section') !== null;
            
            // 检查面包屑导航
            const breadcrumbExists = await this.page.$('.el-breadcrumb') !== null;
            
            const navigationTest = {
                name: '导航结构完整性',
                status: menuItems >= 6 && quickNavExists && breadcrumbExists ? 'PASS' : 'FAIL',
                details: `菜单项: ${menuItems}, 快速导航: ${quickNavExists ? '✅' : '❌'}, 面包屑: ${breadcrumbExists ? '✅' : '❌'}`
            };
            
            this.results.tests.push(navigationTest);
            console.log(`✅ 导航结构测试: ${navigationTest.status}`);
            console.log(`   - 菜单项数量: ${menuItems}`);
            console.log(`   - 快速导航: ${quickNavExists ? '存在' : '缺失'}`);
            console.log(`   - 面包屑导航: ${breadcrumbExists ? '存在' : '缺失'}`);
            
            return navigationTest.status === 'PASS';
            
        } catch (error) {
            const errorTest = {
                name: '导航结构测试',
                status: 'FAIL',
                details: `测试错误: ${error.message}`
            };
            this.results.tests.push(errorTest);
            console.log('❌ 导航结构测试失败:', error.message);
            return false;
        }
    }

    async testQuickNavigation() {
        console.log('\n⚡ 测试3: 验证快速导航功能...');
        
        try {
            // 检查快速导航项目
            const navItems = await this.page.$$eval('.nav-item', 
                elements => elements.map(el => ({
                    title: el.querySelector('.nav-title')?.textContent || '',
                    description: el.querySelector('.nav-description')?.textContent || ''
                }))
            );
            
            // 测试点击快速导航
            if (navItems.length > 0) {
                await this.page.click('.nav-item:first-child');
                await this.page.waitForTimeout(1000);
            }
            
            const quickNavTest = {
                name: '快速导航功能',
                status: navItems.length >= 6 ? 'PASS' : 'FAIL',
                details: `导航项数量: ${navItems.length}, 项目: ${navItems.map(item => item.title).join(', ')}`
            };
            
            this.results.tests.push(quickNavTest);
            console.log(`✅ 快速导航测试: ${quickNavTest.status}`);
            console.log(`   - 导航项数量: ${navItems.length}`);
            
            return quickNavTest.status === 'PASS';
            
        } catch (error) {
            const errorTest = {
                name: '快速导航功能',
                status: 'FAIL',
                details: `测试错误: ${error.message}`
            };
            this.results.tests.push(errorTest);
            console.log('❌ 快速导航测试失败:', error.message);
            return false;
        }
    }

    async testPerformanceOptimization() {
        console.log('\n⚡ 测试4: 验证性能优化效果...');
        
        try {
            // 测试页面加载性能
            const startTime = Date.now();
            await this.page.reload({ waitUntil: 'networkidle0' });
            const loadTime = Date.now() - startTime;
            
            // 检查控制台错误数量
            const consoleErrors = [];
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    consoleErrors.push(msg.text());
                }
            });
            
            // 等待一段时间收集错误
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 测试交互响应
            const interactionStart = Date.now();
            await this.page.click('.header-actions button:first-child');
            const interactionTime = Date.now() - interactionStart;
            
            const performanceTest = {
                name: '性能优化效果',
                status: loadTime < 3000 && interactionTime < 200 && consoleErrors.length < 3 ? 'PASS' : 'FAIL',
                details: `加载时间: ${loadTime}ms, 交互时间: ${interactionTime}ms, 错误数: ${consoleErrors.length}`
            };
            
            this.results.tests.push(performanceTest);
            console.log(`✅ 性能优化测试: ${performanceTest.status}`);
            console.log(`   - 页面加载时间: ${loadTime}ms`);
            console.log(`   - 交互响应时间: ${interactionTime}ms`);
            console.log(`   - 控制台错误数: ${consoleErrors.length}`);
            
            return performanceTest.status === 'PASS';
            
        } catch (error) {
            const errorTest = {
                name: '性能优化测试',
                status: 'FAIL',
                details: `测试错误: ${error.message}`
            };
            this.results.tests.push(errorTest);
            console.log('❌ 性能优化测试失败:', error.message);
            return false;
        }
    }

    async generateReport() {
        console.log('\n📊 生成验证报告...');
        
        // 计算统计信息
        this.results.summary.total = this.results.tests.length;
        this.results.summary.passed = this.results.tests.filter(test => test.status === 'PASS').length;
        this.results.summary.failed = this.results.summary.total - this.results.summary.passed;
        this.results.summary.score = Math.round((this.results.summary.passed / this.results.summary.total) * 100);
        
        // 生成报告
        const report = `# 修复验证测试报告

## 测试概览
- **测试时间**: ${this.results.timestamp}
- **总测试数**: ${this.results.summary.total}
- **通过测试**: ${this.results.summary.passed}
- **失败测试**: ${this.results.summary.failed}
- **通过率**: ${this.results.summary.score}%

## 测试结果详情

${this.results.tests.map(test => `### ${test.name}
- **状态**: ${test.status === 'PASS' ? '✅ 通过' : '❌ 失败'}
- **详情**: ${test.details}
`).join('\n')}

## 总体评价

${this.results.summary.score >= 90 ? '🌟 优秀' : 
  this.results.summary.score >= 80 ? '✅ 良好' : 
  this.results.summary.score >= 70 ? '⚠️ 一般' : '❌ 需要改进'}

所有主要问题修复验证${this.results.summary.score >= 80 ? '成功' : '需要进一步优化'}！
`;

        // 保存报告
        const reportPath = path.join(__dirname, 'fix_verification_report.md');
        fs.writeFileSync(reportPath, report);
        
        console.log(`📄 报告已保存到: ${reportPath}`);
        console.log(`🎯 总体通过率: ${this.results.summary.score}%`);
        
        return this.results.summary.score;
    }

    async runAllTests() {
        try {
            await this.init();
            
            console.log('🔍 开始执行修复验证测试...\n');
            
            const test1 = await this.testBackendService();
            const test2 = await this.testNavigationStructure();
            const test3 = await this.testQuickNavigation();
            const test4 = await this.testPerformanceOptimization();
            
            const score = await this.generateReport();
            
            console.log('\n🎉 修复验证测试完成！');
            console.log(`📊 最终评分: ${score}/100`);
            
            if (score >= 90) {
                console.log('🌟 优秀！所有问题都已成功修复！');
            } else if (score >= 80) {
                console.log('✅ 良好！主要问题已修复，还有小幅优化空间。');
            } else {
                console.log('⚠️ 部分问题仍需进一步修复。');
            }
            
        } catch (error) {
            console.error('❌ 测试执行失败:', error);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }
}

// 运行测试
const tester = new FixVerificationTester();
tester.runAllTests().catch(console.error);

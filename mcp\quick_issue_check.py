#!/usr/bin/env python3
"""
快速问题检查
直接检查深度分析报告中提到的关键问题
"""

import re
from pathlib import Path

def check_database_hardcode():
    """检查数据库硬编码问题"""
    print("🔍 检查数据库硬编码问题...")
    
    db_file = Path("../backend/app/core/database.py")
    if not db_file.exists():
        print("❌ database.py文件不存在")
        return False
    
    content = db_file.read_text(encoding='utf-8')
    
    # 检查硬编码
    hardcode_patterns = [
        r'db_url\s*=\s*["\']sqlite\+aiosqlite:///\./quant_dev\.db["\']',
        r'强制使用.*SQLite.*绕过.*配置'
    ]
    
    hardcode_found = False
    for pattern in hardcode_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"❌ 发现硬编码: {pattern}")
            hardcode_found = True
    
    # 检查配置使用
    if "settings.DATABASE_URL" in content:
        print("✅ 使用配置系统: settings.DATABASE_URL")
    elif "database_url" in content and "settings" in content:
        print("✅ 使用配置系统: 检测到配置相关代码")
    else:
        print("⚠️ 未检测到配置系统使用")
    
    if not hardcode_found:
        print("✅ 数据库硬编码问题已修复")
        return True
    else:
        print("❌ 数据库硬编码问题仍存在")
        return False

def check_trading_service():
    """检查交易服务问题"""
    print("\n🔍 检查交易服务实现...")
    
    trading_file = Path("../backend/app/services/trading_service.py")
    if not trading_file.exists():
        print("❌ trading_service.py文件不存在")
        return False
    
    content = trading_file.read_text(encoding='utf-8')
    
    # 检查order_id未定义问题
    lines = content.split('\n')
    order_id_issues = []
    
    for i, line in enumerate(lines):
        if "order_id" in line and "logger.info" in line:
            # 检查是否使用了正确的order.order_id格式
            if "order.order_id" not in line:
                # 检查前面10行是否有order_id定义
                preceding_lines = lines[max(0, i-10):i]
                order_id_defined = any("order_id" in pline and "=" in pline for pline in preceding_lines)

                if not order_id_defined:
                    order_id_issues.append(f"第{i+1}行: {line.strip()}")
    
    if order_id_issues:
        print("❌ 发现order_id未定义问题:")
        for issue in order_id_issues:
            print(f"   {issue}")
        return False
    else:
        print("✅ 未发现order_id未定义问题")
    
    # 检查基本方法实现
    methods = ["create_order", "get_orders", "cancel_order", "get_positions"]
    missing_methods = []
    
    for method in methods:
        if f"def {method}" not in content:
            missing_methods.append(method)
    
    if missing_methods:
        print(f"⚠️ 缺少方法实现: {missing_methods}")
    else:
        print("✅ 基本方法实现完整")
    
    return len(order_id_issues) == 0

def check_route_duplicates():
    """检查路由重复问题"""
    print("\n🔍 检查路由重复定义...")
    
    api_dir = Path("../backend/app/api/v1")
    if not api_dir.exists():
        print("❌ API v1目录不存在")
        return False
    
    # 查找重复文件
    route_files = list(api_dir.glob("*.py"))
    fixed_files = [f for f in route_files if f.name.endswith("_fixed.py")]
    original_files = [f for f in route_files if not f.name.endswith("_fixed.py") and f.name != "__init__.py"]
    
    duplicates = []
    for fixed_file in fixed_files:
        original_name = fixed_file.name.replace("_fixed.py", ".py")
        original_file = api_dir / original_name
        
        if original_file.exists():
            duplicates.append((original_name, fixed_file.name))
    
    print(f"📊 路由文件统计:")
    print(f"   总文件数: {len(route_files)}")
    print(f"   Fixed文件数: {len(fixed_files)}")
    print(f"   原始文件数: {len(original_files)}")
    print(f"   重复文件对: {len(duplicates)}")
    
    if duplicates:
        print("❌ 发现重复路由文件:")
        for original, fixed in duplicates:
            print(f"   {original} ↔ {fixed}")
        return False
    else:
        print("✅ 未发现重复路由文件")
        return True

def check_dependency_conflicts():
    """检查依赖版本冲突"""
    print("\n🔍 检查依赖版本冲突...")
    
    req_files = [
        Path("../backend/requirements.txt"),
        Path("../requirements.txt")
    ]
    
    all_deps = {}
    conflicts = []
    
    for req_file in req_files:
        if req_file.exists():
            content = req_file.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if '==' in line and not line.startswith('#'):
                    try:
                        package, version = line.split('==')
                        package = package.strip()
                        version = version.strip()
                        
                        if package in all_deps:
                            if all_deps[package] != version:
                                conflicts.append((package, all_deps[package], version))
                        else:
                            all_deps[package] = version
                    except:
                        continue
    
    # 特别检查cryptography
    crypto_versions = []
    for req_file in req_files:
        if req_file.exists():
            content = req_file.read_text(encoding='utf-8')
            crypto_matches = re.findall(r'cryptography==([0-9.]+)', content)
            crypto_versions.extend(crypto_matches)
    
    print(f"📊 依赖统计:")
    print(f"   总依赖数: {len(all_deps)}")
    print(f"   版本冲突: {len(conflicts)}")
    
    if conflicts:
        print("❌ 发现版本冲突:")
        for package, v1, v2 in conflicts:
            print(f"   {package}: {v1} vs {v2}")
    
    crypto_conflict = len(set(crypto_versions)) > 1
    if crypto_conflict:
        print(f"❌ Cryptography版本冲突: {set(crypto_versions)}")
    else:
        print("✅ Cryptography版本一致")
    
    return len(conflicts) == 0 and not crypto_conflict

def check_real_data_integration():
    """检查真实数据源集成"""
    print("\n🔍 检查真实数据源集成...")
    
    services_dir = Path("../backend/app/services")
    if not services_dir.exists():
        print("❌ services目录不存在")
        return False
    
    real_data_files = []
    mock_usage = 0
    real_integration = 0
    
    for service_file in services_dir.glob("*.py"):
        try:
            content = service_file.read_text(encoding='utf-8')
            
            # 检查真实数据源
            if any(keyword in content.lower() for keyword in ['tushare', 'akshare', 'real_data']):
                real_data_files.append(service_file.name)
                real_integration += 1
            
            # 检查Mock使用
            if "MockMarketService" in content:
                mock_usage += 1
                
        except:
            continue
    
    print(f"📊 数据源统计:")
    print(f"   真实数据源文件: {len(real_data_files)}")
    print(f"   Mock使用次数: {mock_usage}")
    print(f"   真实集成次数: {real_integration}")
    
    if real_data_files:
        print("✅ 发现真实数据源集成:")
        for file in real_data_files:
            print(f"   {file}")
        return True
    else:
        print("❌ 未发现真实数据源集成")
        return False

def main():
    """主函数"""
    print("🚀 快速问题检查开始...\n")
    
    results = {}
    
    # 检查各项问题
    results["database_hardcode"] = check_database_hardcode()
    results["trading_service"] = check_trading_service()
    results["route_duplicates"] = check_route_duplicates()
    results["dependency_conflicts"] = check_dependency_conflicts()
    results["real_data_integration"] = check_real_data_integration()
    
    # 统计结果
    fixed_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    fix_rate = (fixed_count / total_count * 100) if total_count > 0 else 0
    
    print(f"\n📊 检查结果汇总:")
    print(f"   总检查项: {total_count}")
    print(f"   已修复项: {fixed_count}")
    print(f"   修复率: {fix_rate:.1f}%")
    
    print(f"\n🎯 详细结果:")
    for issue, fixed in results.items():
        status = "✅ 已修复" if fixed else "❌ 未修复"
        print(f"   {issue}: {status}")
    
    if fix_rate >= 80:
        print(f"\n🎉 修复效果优秀！大部分关键问题已解决。")
    elif fix_rate >= 60:
        print(f"\n✅ 修复效果良好，主要问题已改善。")
    else:
        print(f"\n⚠️ 仍需继续修复，部分关键问题待解决。")

if __name__ == "__main__":
    main()

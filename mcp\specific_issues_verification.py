#!/usr/bin/env python3
"""
具体问题验证脚本
验证深度分析报告中提到的具体问题是否已修复
"""

import asyncio
import subprocess
import sys
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from loguru import logger


class SpecificIssuesVerifier:
    """具体问题验证器"""
    
    def __init__(self):
        self.project_root = Path.cwd().parent
        self.backend_path = self.project_root / "backend"
        
        self.verification_results = {
            "timestamp": datetime.now().isoformat(),
            "p0_critical_issues": {},
            "p1_high_priority": {},
            "p2_architecture": {},
            "overall_status": {}
        }
    
    def verify_p0_critical_issues(self):
        """验证P0致命问题"""
        logger.info("🔴 验证P0致命问题...")
        
        results = {}
        
        # 1. 数据库配置硬编码问题
        results["database_hardcode"] = self._check_database_hardcode()
        
        # 2. FastAPI类型系统崩溃
        results["fastapi_type_system"] = self._check_fastapi_types()
        
        # 3. 循环导入死锁
        results["circular_imports"] = self._check_circular_imports()
        
        self.verification_results["p0_critical_issues"] = results
        return results
    
    def _check_database_hardcode(self) -> Dict[str, Any]:
        """检查数据库配置硬编码问题"""
        logger.info("检查数据库配置硬编码...")
        
        try:
            db_file = self.backend_path / "app" / "core" / "database.py"
            
            if not db_file.exists():
                return {
                    "status": "missing",
                    "error": "database.py文件不存在"
                }
            
            content = db_file.read_text(encoding='utf-8')
            
            # 检查是否还有硬编码
            hardcode_patterns = [
                r'db_url\s*=\s*["\']sqlite\+aiosqlite:///\./quant_dev\.db["\']',
                r'sqlite\+aiosqlite:///\./quant_dev\.db',
                r'强制使用.*SQLite.*绕过.*配置'
            ]
            
            hardcode_found = []
            for pattern in hardcode_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    hardcode_found.extend(matches)
            
            # 检查是否使用了配置系统
            config_usage = [
                "settings.DATABASE_URL" in content,
                "database_url" in content and "settings" in content,
                "环境变量" in content or "environment" in content.lower()
            ]
            
            uses_config = any(config_usage)
            
            return {
                "status": "fixed" if not hardcode_found and uses_config else "exists",
                "hardcode_found": hardcode_found,
                "uses_config_system": uses_config,
                "config_indicators": config_usage,
                "file_size": len(content)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_fastapi_types(self) -> Dict[str, Any]:
        """检查FastAPI类型系统问题"""
        logger.info("检查FastAPI类型系统...")
        
        try:
            # 检查主要API文件
            api_files = [
                self.backend_path / "app" / "api" / "v1" / "auth.py",
                self.backend_path / "app" / "api" / "v1" / "market.py",
                self.backend_path / "app" / "api" / "v1" / "trading.py",
                self.backend_path / "app" / "api" / "v1" / "auth_fixed.py",
                self.backend_path / "app" / "api" / "v1" / "market_fixed.py"
            ]
            
            type_issues = []
            files_checked = 0
            
            for api_file in api_files:
                if api_file.exists():
                    files_checked += 1
                    content = api_file.read_text(encoding='utf-8')
                    
                    # 检查AsyncSession类型问题
                    if "AsyncSession" in content:
                        # 检查是否有问题的用法
                        problematic_patterns = [
                            r'db:\s*AsyncSession\s*=\s*Depends.*response_model',
                            r'AsyncSession.*Pydantic',
                            r'AsyncSession.*Field'
                        ]
                        
                        for pattern in problematic_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                type_issues.append(f"{api_file.name}: AsyncSession类型问题")
                    
                    # 检查其他类型问题
                    if "from sqlalchemy.ext.asyncio import AsyncSession" in content:
                        if "response_model" in content and "AsyncSession" in content:
                            # 进一步检查是否正确处理
                            if "Depends(get_db)" not in content:
                                type_issues.append(f"{api_file.name}: 可能的依赖注入问题")
            
            return {
                "status": "fixed" if not type_issues else "exists",
                "files_checked": files_checked,
                "type_issues": type_issues,
                "total_issues": len(type_issues)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_circular_imports(self) -> Dict[str, Any]:
        """检查循环导入问题"""
        logger.info("检查循环导入...")
        
        try:
            conftest_path = self.project_root / "tests" / "conftest.py"
            main_path = self.backend_path / "app" / "main.py"
            main_stable_path = self.backend_path / "app" / "main_stable.py"
            
            circular_issues = []
            
            # 检查conftest.py
            if conftest_path.exists():
                conftest_content = conftest_path.read_text(encoding='utf-8')
                
                # 检查是否导入了main模块
                main_imports = [
                    "from app.main" in conftest_content,
                    "import app.main" in conftest_content,
                    "from app import main" in conftest_content
                ]
                
                if any(main_imports):
                    circular_issues.append("conftest.py导入main模块")
            
            # 检查main.py是否导入测试相关
            for main_file in [main_path, main_stable_path]:
                if main_file.exists():
                    main_content = main_file.read_text(encoding='utf-8')
                    
                    test_imports = [
                        "from tests" in main_content,
                        "import tests" in main_content,
                        "conftest" in main_content
                    ]
                    
                    if any(test_imports):
                        circular_issues.append(f"{main_file.name}导入测试模块")
            
            return {
                "status": "fixed" if not circular_issues else "exists",
                "circular_issues": circular_issues,
                "conftest_exists": conftest_path.exists(),
                "main_exists": main_path.exists(),
                "main_stable_exists": main_stable_path.exists()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def verify_p1_high_priority(self):
        """验证P1高优先级问题"""
        logger.info("🟡 验证P1高优先级问题...")
        
        results = {}
        
        # 1. 交易服务实现问题
        results["trading_service"] = self._check_trading_service()
        
        # 2. 市场数据服务依赖Mock
        results["market_data_service"] = self._check_market_data_service()
        
        # 3. 前端状态管理
        results["frontend_state"] = self._check_frontend_state()
        
        # 4. 路由重复定义问题
        results["route_duplicates"] = self._check_route_duplicates()
        
        self.verification_results["p1_high_priority"] = results
        return results
    
    def _check_trading_service(self) -> Dict[str, Any]:
        """检查交易服务实现"""
        logger.info("检查交易服务实现...")
        
        try:
            trading_service_path = self.backend_path / "app" / "services" / "trading_service.py"
            
            if not trading_service_path.exists():
                return {
                    "status": "missing",
                    "error": "trading_service.py文件不存在"
                }
            
            content = trading_service_path.read_text(encoding='utf-8')
            
            # 检查具体的问题：order_id变量未定义
            issues = []
            
            # 检查order_id未定义问题
            if "order_id" in content:
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if "order_id" in line and "logger.info" in line:
                        # 检查前面是否有order_id的定义
                        preceding_lines = lines[max(0, i-10):i]
                        order_id_defined = any("order_id" in pline and "=" in pline for pline in preceding_lines)
                        
                        if not order_id_defined:
                            issues.append(f"第{i+1}行: order_id变量可能未定义")
            
            # 检查其他实现问题
            implementation_checks = {
                "create_order方法": "def create_order" in content,
                "get_orders方法": "def get_orders" in content,
                "cancel_order方法": "def cancel_order" in content,
                "get_positions方法": "def get_positions" in content,
                "错误处理": "try:" in content and "except:" in content
            }
            
            missing_implementations = [name for name, exists in implementation_checks.items() if not exists]
            
            return {
                "status": "improved" if not issues and not missing_implementations else "issues",
                "variable_issues": issues,
                "missing_implementations": missing_implementations,
                "implementation_checks": implementation_checks,
                "file_size": len(content)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_market_data_service(self) -> Dict[str, Any]:
        """检查市场数据服务"""
        logger.info("检查市场数据服务...")
        
        try:
            services_dir = self.backend_path / "app" / "services"
            
            # 查找市场数据相关服务
            market_services = []
            real_data_services = []
            
            if services_dir.exists():
                for service_file in services_dir.glob("*market*.py"):
                    market_services.append(service_file.name)
                
                for service_file in services_dir.glob("*data*.py"):
                    content = service_file.read_text(encoding='utf-8')
                    if "tushare" in content.lower() or "akshare" in content.lower() or "real" in content.lower():
                        real_data_services.append(service_file.name)
            
            # 检查Mock服务依赖
            mock_dependency = 0
            real_integration = 0
            
            for service_file in services_dir.glob("*.py"):
                try:
                    content = service_file.read_text(encoding='utf-8')
                    if "MockMarketService" in content:
                        mock_dependency += 1
                    if "tushare" in content.lower() or "akshare" in content.lower():
                        real_integration += 1
                except:
                    continue
            
            return {
                "status": "improved" if real_data_services else "mock_dependent",
                "market_services": market_services,
                "real_data_services": real_data_services,
                "mock_dependency_count": mock_dependency,
                "real_integration_count": real_integration,
                "has_real_data_integration": len(real_data_services) > 0
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_frontend_state(self) -> Dict[str, Any]:
        """检查前端状态管理"""
        logger.info("检查前端状态管理...")
        
        try:
            frontend_path = self.project_root / "frontend"
            
            state_issues = []
            config_issues = []
            
            # 检查前端文件
            if frontend_path.exists():
                # 检查状态管理文件
                for state_file in frontend_path.rglob("*store*.js"):
                    content = state_file.read_text(encoding='utf-8')
                    if "localStorage" in content and "JWT" in content:
                        state_issues.append(f"{state_file.name}: localStorage与JWT同步问题")
                
                # 检查API配置
                for config_file in frontend_path.rglob("*.js"):
                    try:
                        content = config_file.read_text(encoding='utf-8')
                        if "localhost:8000" in content:
                            config_issues.append(f"{config_file.name}: 硬编码localhost配置")
                    except:
                        continue
            
            # 检查public目录的HTML文件
            public_path = frontend_path / "public"
            if public_path.exists():
                for html_file in public_path.glob("*.html"):
                    try:
                        content = html_file.read_text(encoding='utf-8')
                        if "localhost:8000" in content:
                            config_issues.append(f"{html_file.name}: 硬编码API地址")
                    except:
                        continue
            
            return {
                "status": "improved" if not state_issues else "issues",
                "state_issues": state_issues,
                "config_issues": config_issues,
                "total_issues": len(state_issues) + len(config_issues)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_route_duplicates(self) -> Dict[str, Any]:
        """检查路由重复定义问题"""
        logger.info("检查路由重复定义...")
        
        try:
            api_v1_path = self.backend_path / "app" / "api" / "v1"
            
            if not api_v1_path.exists():
                return {
                    "status": "missing",
                    "error": "API v1目录不存在"
                }
            
            # 查找所有路由文件
            route_files = list(api_v1_path.glob("*.py"))
            fixed_files = list(api_v1_path.glob("*_fixed.py"))
            original_files = [f for f in route_files if not f.name.endswith("_fixed.py") and f.name != "__init__.py"]
            
            # 分析重复情况
            duplicates = []
            for fixed_file in fixed_files:
                original_name = fixed_file.name.replace("_fixed.py", ".py")
                original_file = api_v1_path / original_name
                
                if original_file.exists():
                    duplicates.append({
                        "original": original_name,
                        "fixed": fixed_file.name,
                        "both_exist": True
                    })
            
            # 检查__init__.py中的注释情况
            init_file = api_v1_path / "__init__.py"
            commented_routes = []
            
            if init_file.exists():
                content = init_file.read_text(encoding='utf-8')
                lines = content.split('\n')
                
                for line in lines:
                    if line.strip().startswith('#') and 'router' in line:
                        commented_routes.append(line.strip())
            
            return {
                "status": "cleaned" if not duplicates else "duplicates_exist",
                "total_route_files": len(route_files),
                "fixed_files": len(fixed_files),
                "original_files": len(original_files),
                "duplicates": duplicates,
                "commented_routes": commented_routes,
                "duplicate_count": len(duplicates)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def verify_p2_architecture(self):
        """验证P2架构级问题"""
        logger.info("🟢 验证P2架构级问题...")
        
        results = {}
        
        # 1. 测试框架复杂度
        results["test_framework"] = self._check_test_framework()
        
        # 2. 依赖版本管理
        results["dependency_versions"] = self._check_dependency_versions()
        
        # 3. WebSocket架构
        results["websocket_architecture"] = self._check_websocket_architecture()
        
        self.verification_results["p2_architecture"] = results
        return results
    
    def _check_test_framework(self) -> Dict[str, Any]:
        """检查测试框架复杂度"""
        logger.info("检查测试框架复杂度...")
        
        try:
            tests_path = self.project_root / "tests"
            
            if not tests_path.exists():
                return {
                    "status": "simplified",
                    "message": "测试目录不存在，可能已简化"
                }
            
            # 统计测试文件
            test_files = list(tests_path.rglob("test_*.py"))
            test_functions = 0
            total_test_size = 0
            
            for test_file in test_files:
                try:
                    content = test_file.read_text(encoding='utf-8')
                    total_test_size += len(content)
                    
                    # 统计测试函数
                    test_functions += len(re.findall(r'def test_', content))
                except:
                    continue
            
            # 判断复杂度
            complexity_indicators = {
                "file_count": len(test_files),
                "function_count": test_functions,
                "total_size_kb": round(total_test_size / 1024, 1),
                "avg_functions_per_file": round(test_functions / len(test_files), 1) if test_files else 0
            }
            
            is_complex = (
                len(test_files) > 20 or 
                test_functions > 500 or 
                total_test_size > 400 * 1024  # 400KB
            )
            
            return {
                "status": "simplified" if not is_complex else "complex",
                "complexity_indicators": complexity_indicators,
                "is_over_engineered": is_complex,
                "recommendations": ["简化测试结构", "减少测试文件数量"] if is_complex else ["测试框架合理"]
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_dependency_versions(self) -> Dict[str, Any]:
        """检查依赖版本管理"""
        logger.info("检查依赖版本管理...")
        
        try:
            requirements_files = [
                self.backend_path / "requirements.txt",
                self.project_root / "requirements.txt",
                self.backend_path / "pyproject.toml"
            ]
            
            version_conflicts = []
            all_dependencies = {}
            
            for req_file in requirements_files:
                if req_file.exists():
                    content = req_file.read_text(encoding='utf-8')
                    
                    # 查找版本冲突
                    lines = content.split('\n')
                    for line in lines:
                        line = line.strip()
                        if '==' in line and not line.startswith('#'):
                            package_name = line.split('==')[0].strip()
                            version = line.split('==')[1].strip()
                            
                            if package_name in all_dependencies:
                                if all_dependencies[package_name] != version:
                                    version_conflicts.append({
                                        "package": package_name,
                                        "versions": [all_dependencies[package_name], version],
                                        "files": ["previous", req_file.name]
                                    })
                            else:
                                all_dependencies[package_name] = version
            
            # 特别检查cryptography冲突
            crypto_versions = []
            for req_file in requirements_files:
                if req_file.exists():
                    content = req_file.read_text(encoding='utf-8')
                    crypto_matches = re.findall(r'cryptography==([0-9.]+)', content)
                    crypto_versions.extend(crypto_matches)
            
            crypto_conflict = len(set(crypto_versions)) > 1
            
            return {
                "status": "clean" if not version_conflicts and not crypto_conflict else "conflicts",
                "version_conflicts": version_conflicts,
                "crypto_conflict": crypto_conflict,
                "crypto_versions": list(set(crypto_versions)),
                "total_dependencies": len(all_dependencies),
                "files_checked": len([f for f in requirements_files if f.exists()])
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_websocket_architecture(self) -> Dict[str, Any]:
        """检查WebSocket架构"""
        logger.info("检查WebSocket架构...")
        
        try:
            # 查找WebSocket相关文件
            websocket_files = []
            websocket_implementations = []
            
            for py_file in self.backend_path.rglob("*.py"):
                try:
                    content = py_file.read_text(encoding='utf-8')
                    if "websocket" in content.lower() or "WebSocket" in content:
                        websocket_files.append(py_file.name)
                        
                        # 检查实现方式
                        if "fastapi" in content.lower() and "websocket" in content.lower():
                            websocket_implementations.append("FastAPI WebSocket")
                        if "socketio" in content.lower():
                            websocket_implementations.append("Socket.IO")
                        if "ws://" in content or "wss://" in content:
                            websocket_implementations.append("Raw WebSocket")
                except:
                    continue
            
            # 检查是否过度复杂
            unique_implementations = list(set(websocket_implementations))
            is_complex = len(unique_implementations) > 2 or len(websocket_files) > 5
            
            return {
                "status": "simplified" if not is_complex else "complex",
                "websocket_files": websocket_files,
                "implementations": unique_implementations,
                "file_count": len(websocket_files),
                "implementation_count": len(unique_implementations),
                "is_over_engineered": is_complex
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def generate_verification_report(self):
        """生成验证报告"""
        logger.info("生成具体问题验证报告...")
        
        # 计算各级别修复率
        p0_results = self.verification_results["p0_critical_issues"]
        p0_fixed = sum(1 for r in p0_results.values() if r.get("status") in ["fixed", "improved"])
        p0_total = len(p0_results)
        p0_rate = (p0_fixed / p0_total * 100) if p0_total > 0 else 0
        
        p1_results = self.verification_results["p1_high_priority"]
        p1_improved = sum(1 for r in p1_results.values() if r.get("status") in ["improved", "cleaned"])
        p1_total = len(p1_results)
        p1_rate = (p1_improved / p1_total * 100) if p1_total > 0 else 0
        
        p2_results = self.verification_results["p2_architecture"]
        p2_simplified = sum(1 for r in p2_results.values() if r.get("status") in ["simplified", "clean"])
        p2_total = len(p2_results)
        p2_rate = (p2_simplified / p2_total * 100) if p2_total > 0 else 0
        
        # 计算总体修复率
        total_score = (p0_rate * 0.5 + p1_rate * 0.3 + p2_rate * 0.2)
        
        self.verification_results["overall_status"] = {
            "p0_fix_rate": round(p0_rate, 1),
            "p1_improvement_rate": round(p1_rate, 1),
            "p2_simplification_rate": round(p2_rate, 1),
            "total_score": round(total_score, 1),
            "overall_grade": "excellent" if total_score >= 90 else
                           "good" if total_score >= 75 else
                           "fair" if total_score >= 60 else "poor"
        }
        
        # 生成报告
        report = f"""
# 具体问题验证报告

## 📊 验证概述
- **验证时间**: {self.verification_results['timestamp']}
- **总体修复率**: {total_score:.1f}%
- **整体等级**: {self.verification_results['overall_status']['overall_grade']}

## 🔴 P0致命问题验证 ({p0_rate:.1f}%)

### 1. 数据库配置硬编码问题
"""
        
        db_result = p0_results.get("database_hardcode", {})
        status_emoji = "✅" if db_result.get("status") == "fixed" else "❌"
        report += f"**状态**: {status_emoji} {db_result.get('status', 'unknown')}\n"
        if db_result.get("hardcode_found"):
            report += f"- 发现硬编码: {len(db_result['hardcode_found'])} 处\n"
        if db_result.get("uses_config_system"):
            report += f"- 使用配置系统: ✅\n"
        
        report += f"""
### 2. FastAPI类型系统崩溃
"""
        
        type_result = p0_results.get("fastapi_type_system", {})
        status_emoji = "✅" if type_result.get("status") == "fixed" else "❌"
        report += f"**状态**: {status_emoji} {type_result.get('status', 'unknown')}\n"
        report += f"- 检查文件数: {type_result.get('files_checked', 0)}\n"
        report += f"- 类型问题数: {type_result.get('total_issues', 0)}\n"
        
        report += f"""
### 3. 循环导入死锁
"""
        
        import_result = p0_results.get("circular_imports", {})
        status_emoji = "✅" if import_result.get("status") == "fixed" else "❌"
        report += f"**状态**: {status_emoji} {import_result.get('status', 'unknown')}\n"
        report += f"- 循环导入问题: {len(import_result.get('circular_issues', []))}\n"
        
        report += f"""
## 🟡 P1高优先级问题验证 ({p1_rate:.1f}%)

### 1. 交易服务实现问题
"""
        
        trading_result = p1_results.get("trading_service", {})
        status_emoji = "✅" if trading_result.get("status") == "improved" else "⚠️"
        report += f"**状态**: {status_emoji} {trading_result.get('status', 'unknown')}\n"
        report += f"- 变量问题: {len(trading_result.get('variable_issues', []))}\n"
        report += f"- 缺失实现: {len(trading_result.get('missing_implementations', []))}\n"
        
        report += f"""
### 2. 市场数据服务Mock依赖
"""
        
        market_result = p1_results.get("market_data_service", {})
        status_emoji = "✅" if market_result.get("status") == "improved" else "⚠️"
        report += f"**状态**: {status_emoji} {market_result.get('status', 'unknown')}\n"
        report += f"- 真实数据服务: {len(market_result.get('real_data_services', []))}\n"
        report += f"- Mock依赖数: {market_result.get('mock_dependency_count', 0)}\n"
        
        report += f"""
### 3. 路由重复定义问题
"""
        
        route_result = p1_results.get("route_duplicates", {})
        status_emoji = "✅" if route_result.get("status") == "cleaned" else "⚠️"
        report += f"**状态**: {status_emoji} {route_result.get('status', 'unknown')}\n"
        report += f"- 重复路由数: {route_result.get('duplicate_count', 0)}\n"
        report += f"- Fixed文件数: {route_result.get('fixed_files', 0)}\n"
        
        report += f"""
## 🟢 P2架构级问题验证 ({p2_rate:.1f}%)

### 1. 测试框架复杂度
"""
        
        test_result = p2_results.get("test_framework", {})
        status_emoji = "✅" if test_result.get("status") == "simplified" else "⚠️"
        report += f"**状态**: {status_emoji} {test_result.get('status', 'unknown')}\n"
        if test_result.get("complexity_indicators"):
            indicators = test_result["complexity_indicators"]
            report += f"- 测试文件数: {indicators.get('file_count', 0)}\n"
            report += f"- 测试函数数: {indicators.get('function_count', 0)}\n"
            report += f"- 总大小: {indicators.get('total_size_kb', 0)}KB\n"
        
        report += f"""
### 2. 依赖版本管理
"""
        
        dep_result = p2_results.get("dependency_versions", {})
        status_emoji = "✅" if dep_result.get("status") == "clean" else "⚠️"
        report += f"**状态**: {status_emoji} {dep_result.get('status', 'unknown')}\n"
        report += f"- 版本冲突数: {len(dep_result.get('version_conflicts', []))}\n"
        report += f"- Cryptography冲突: {'是' if dep_result.get('crypto_conflict') else '否'}\n"
        
        report += f"""
## 🎯 总结

### 修复进度
- **P0致命问题**: {p0_rate:.1f}% ({p0_fixed}/{p0_total})
- **P1高优先级**: {p1_rate:.1f}% ({p1_improved}/{p1_total})  
- **P2架构级**: {p2_rate:.1f}% ({p2_simplified}/{p2_total})

### 整体评价
**总体修复率**: {total_score:.1f}% ({self.verification_results['overall_status']['overall_grade']})

"""
        
        if total_score >= 80:
            report += "🎉 **修复效果优秀**，大部分关键问题已解决！"
        elif total_score >= 60:
            report += "✅ **修复效果良好**，主要问题已改善。"
        else:
            report += "⚠️ **仍需继续修复**，部分关键问题待解决。"
        
        return report
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🔍 开始具体问题验证...")
        
        # 验证各级别问题
        self.verify_p0_critical_issues()
        self.verify_p1_high_priority()
        self.verify_p2_architecture()
        
        # 生成报告
        report = self.generate_verification_report()
        
        # 保存报告
        report_file = f"specific_issues_verification_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.success(f"✅ 具体问题验证完成，报告已保存: {report_file}")
        
        return self.verification_results


def main():
    """主函数"""
    verifier = SpecificIssuesVerifier()
    results = verifier.run_verification()
    
    # 输出关键结果
    overall = results["overall_status"]
    print(f"\n🎯 具体问题验证结果:")
    print(f"   总体修复率: {overall['total_score']}%")
    print(f"   整体等级: {overall['overall_grade']}")
    print(f"   P0修复率: {overall['p0_fix_rate']}%")
    print(f"   P1改善率: {overall['p1_improvement_rate']}%")
    print(f"   P2简化率: {overall['p2_simplification_rate']}%")
    
    return results


if __name__ == "__main__":
    import time
    main()

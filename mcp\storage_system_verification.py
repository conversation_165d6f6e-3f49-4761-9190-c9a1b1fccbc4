#!/usr/bin/env python3
"""
数据存储系统验证测试
验证基于pythonstock的数据存储优化是否正常工作
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List

import aiohttp
import requests
from loguru import logger

class StorageSystemVerification:
    """数据存储系统验证器"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.api_base = f"{self.base_url}/api/v1"
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "errors": []
            }
        }
    
    def log_test_result(self, test_name: str, success: bool, details: Dict[str, Any] = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        
        self.test_results["tests"].append(result)
        self.test_results["summary"]["total"] += 1
        
        if success:
            self.test_results["summary"]["passed"] += 1
            logger.success(f"✅ {test_name} - 通过")
        else:
            self.test_results["summary"]["failed"] += 1
            error_msg = details.get("error", "未知错误") if details else "测试失败"
            self.test_results["summary"]["errors"].append(f"{test_name}: {error_msg}")
            logger.error(f"❌ {test_name} - 失败: {error_msg}")
    
    def test_backend_health(self) -> bool:
        """测试后端健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            success = response.status_code == 200
            
            details = {
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "content": response.text[:200] if response.text else ""
            }
            
            self.log_test_result("后端健康检查", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("后端健康检查", False, {"error": str(e)})
            return False
    
    def test_storage_stats_api(self) -> bool:
        """测试存储统计API"""
        try:
            response = requests.get(f"{self.api_base}/storage/stats", timeout=15)
            success = response.status_code == 200
            
            details = {
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if success:
                data = response.json()
                details.update({
                    "has_data": bool(data.get("data")),
                    "cache_hit_rate": data.get("data", {}).get("cache_hit_rate", "N/A"),
                    "service_stats": data.get("data", {}).get("service_stats", {})
                })
            
            self.log_test_result("存储统计API", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("存储统计API", False, {"error": str(e)})
            return False
    
    def test_enhanced_quote_api(self) -> bool:
        """测试增强行情API"""
        test_symbols = ["000001.SZ", "600519.SH", "000858.SZ"]
        
        for symbol in test_symbols:
            try:
                response = requests.get(f"{self.api_base}/market/enhanced/quote/{symbol}", timeout=15)
                success = response.status_code == 200
                
                details = {
                    "symbol": symbol,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if success:
                    data = response.json()
                    details.update({
                        "has_data": bool(data.get("data")),
                        "cache_info": data.get("cache_info", ""),
                        "data_fields": list(data.get("data", {}).keys()) if data.get("data") else []
                    })
                
                self.log_test_result(f"增强行情API-{symbol}", success, details)
                
                if not success:
                    return False
                    
                # 小延迟避免API调用过于频繁
                time.sleep(0.5)
                
            except Exception as e:
                self.log_test_result(f"增强行情API-{symbol}", False, {"error": str(e)})
                return False
        
        return True
    
    def test_enhanced_kline_api(self) -> bool:
        """测试增强K线API"""
        try:
            symbol = "600519.SH"
            response = requests.get(
                f"{self.api_base}/market/enhanced/kline/{symbol}?period=daily&limit=10", 
                timeout=20
            )
            success = response.status_code == 200
            
            details = {
                "symbol": symbol,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if success:
                data = response.json()
                details.update({
                    "data_count": data.get("count", 0),
                    "cache_info": data.get("cache_info", ""),
                    "has_kline_data": bool(data.get("data"))
                })
            
            self.log_test_result("增强K线API", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("增强K线API", False, {"error": str(e)})
            return False
    
    def test_enhanced_market_overview(self) -> bool:
        """测试增强市场概览API"""
        try:
            response = requests.get(f"{self.api_base}/market/enhanced/overview", timeout=15)
            success = response.status_code == 200
            
            details = {
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if success:
                data = response.json()
                overview_data = data.get("data", {})
                details.update({
                    "cache_info": data.get("cache_info", ""),
                    "indices_count": len(overview_data.get("indices", [])),
                    "has_market_stats": bool(overview_data.get("market_stats")),
                    "timestamp": overview_data.get("timestamp", "")
                })
            
            self.log_test_result("增强市场概览API", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("增强市场概览API", False, {"error": str(e)})
            return False
    
    def test_cache_warm_up(self) -> bool:
        """测试缓存预热功能"""
        try:
            response = requests.post(f"{self.api_base}/storage/warm-up", timeout=30)
            success = response.status_code == 200
            
            details = {
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if success:
                data = response.json()
                details.update({
                    "message": data.get("message", ""),
                    "timestamp": data.get("timestamp", "")
                })
            
            self.log_test_result("缓存预热功能", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("缓存预热功能", False, {"error": str(e)})
            return False
    
    def test_original_apis_compatibility(self) -> bool:
        """测试原有API的兼容性"""
        original_apis = [
            "/api/v1/market/overview",
            "/api/v1/market/stocks",
            "/api/v1/trading/orders"
        ]
        
        for api_path in original_apis:
            try:
                response = requests.get(f"{self.base_url}{api_path}", timeout=15)
                success = response.status_code == 200
                
                details = {
                    "api_path": api_path,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
                
                self.log_test_result(f"原有API兼容性-{api_path}", success, details)
                
                if not success:
                    return False
                    
                time.sleep(0.3)
                
            except Exception as e:
                self.log_test_result(f"原有API兼容性-{api_path}", False, {"error": str(e)})
                return False
        
        return True
    
    def test_performance_comparison(self) -> bool:
        """测试性能对比"""
        try:
            # 测试原有API性能
            start_time = time.time()
            response1 = requests.get(f"{self.api_base}/market/overview", timeout=10)
            original_time = time.time() - start_time
            
            # 测试增强API性能
            start_time = time.time()
            response2 = requests.get(f"{self.api_base}/market/enhanced/overview", timeout=10)
            enhanced_time = time.time() - start_time
            
            success = response1.status_code == 200 and response2.status_code == 200
            
            details = {
                "original_api_time": round(original_time * 1000, 2),  # 毫秒
                "enhanced_api_time": round(enhanced_time * 1000, 2),  # 毫秒
                "performance_improvement": round((original_time - enhanced_time) / original_time * 100, 2) if original_time > 0 else 0,
                "both_apis_working": success
            }
            
            self.log_test_result("性能对比测试", success, details)
            return success
            
        except Exception as e:
            self.log_test_result("性能对比测试", False, {"error": str(e)})
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始数据存储系统验证测试...")
        
        # 测试顺序很重要
        tests = [
            ("后端健康检查", self.test_backend_health),
            ("存储统计API", self.test_storage_stats_api),
            ("增强行情API", self.test_enhanced_quote_api),
            ("增强K线API", self.test_enhanced_kline_api),
            ("增强市场概览API", self.test_enhanced_market_overview),
            ("缓存预热功能", self.test_cache_warm_up),
            ("原有API兼容性", self.test_original_apis_compatibility),
            ("性能对比测试", self.test_performance_comparison)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🧪 执行测试: {test_name}")
            try:
                test_func()
            except Exception as e:
                logger.error(f"测试执行异常 {test_name}: {e}")
                self.log_test_result(test_name, False, {"error": f"执行异常: {str(e)}"})
            
            time.sleep(1)  # 测试间隔
        
        # 生成测试报告
        self.generate_report()
        return self.test_results
    
    def generate_report(self):
        """生成测试报告"""
        summary = self.test_results["summary"]
        success_rate = (summary["passed"] / summary["total"] * 100) if summary["total"] > 0 else 0
        
        logger.info("=" * 60)
        logger.info("📊 数据存储系统验证测试报告")
        logger.info("=" * 60)
        logger.info(f"总测试数: {summary['total']}")
        logger.info(f"通过数: {summary['passed']}")
        logger.info(f"失败数: {summary['failed']}")
        logger.info(f"成功率: {success_rate:.1f}%")
        
        if summary["errors"]:
            logger.error("❌ 失败的测试:")
            for error in summary["errors"]:
                logger.error(f"  - {error}")
        
        # 保存详细报告
        report_file = f"storage_verification_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 详细报告已保存: {report_file}")
        
        if success_rate >= 80:
            logger.success("🎉 数据存储系统验证通过！")
        else:
            logger.warning("⚠️ 数据存储系统存在问题，需要进一步检查")


def main():
    """主函数"""
    verifier = StorageSystemVerification()
    results = verifier.run_all_tests()
    
    # 返回结果供其他程序使用
    return results


if __name__ == "__main__":
    main()

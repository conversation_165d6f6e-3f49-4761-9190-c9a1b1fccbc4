#!/usr/bin/env python3
"""
交易中心验证脚本
检查交易中心的实际完成状态
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Any

def analyze_vue_component(file_path: Path) -> Dict[str, Any]:
    """分析Vue组件的完成度"""
    if not file_path.exists():
        return {"exists": False, "completion": 0}
    
    try:
        content = file_path.read_text(encoding='utf-8')
        
        # 检查占位符
        placeholders = [
            "开发中", "正在开发", "Coming Soon", "TODO", "待实现",
            "开发中...", "正在开发中...", "功能开发中"
        ]
        
        has_placeholder = any(placeholder in content for placeholder in placeholders)
        
        # 检查组件复杂度指标
        complexity_indicators = {
            "template_size": len(re.findall(r'<template.*?</template>', content, re.DOTALL)),
            "script_size": len(re.findall(r'<script.*?</script>', content, re.DOTALL)),
            "style_size": len(re.findall(r'<style.*?</style>', content, re.DOTALL)),
            "methods_count": len(re.findall(r'const \w+ = \(.*?\) => {', content)),
            "reactive_data": len(re.findall(r'const \w+ = ref\(|const \w+ = reactive\(', content)),
            "imports": len(re.findall(r'import .* from', content)),
            "components": len(re.findall(r'<[A-Z]\w+', content))
        }
        
        # 计算完成度
        file_size_kb = len(content) / 1024
        
        if has_placeholder:
            completion = 30  # 有占位符说明还在开发
        else:
            # 基于文件大小和复杂度计算
            size_score = min(100, file_size_kb * 5)  # 每KB得5分
            complexity_score = min(100, sum(complexity_indicators.values()) * 2)  # 复杂度得分
            completion = int((size_score + complexity_score) / 2)
        
        return {
            "exists": True,
            "completion": completion,
            "size_kb": round(file_size_kb, 1),
            "has_placeholder": has_placeholder,
            "complexity": complexity_indicators,
            "lines": len(content.split('\n'))
        }
        
    except Exception as e:
        return {
            "exists": True,
            "error": str(e),
            "completion": 0
        }

def check_trading_center_status():
    """检查交易中心状态"""
    project_root = Path.cwd().parent
    frontend_path = project_root / "frontend"
    
    # 检查主要文件
    files_to_check = {
        "TradingCenter.vue": frontend_path / "src" / "views" / "Trading" / "TradingCenter.vue",
        "TradingTerminalModule.vue": frontend_path / "src" / "views" / "Trading" / "modules" / "TradingTerminalModule.vue",
        "AccountManagementModule.vue": frontend_path / "src" / "views" / "Trading" / "modules" / "AccountManagementModule.vue",
        "DataCenterModule.vue": frontend_path / "src" / "views" / "Trading" / "modules" / "DataCenterModule.vue"
    }
    
    results = {}
    
    for name, path in files_to_check.items():
        results[name] = analyze_vue_component(path)
    
    # 生成报告
    print("📊 交易中心组件状态检查报告")
    print("=" * 50)
    
    total_completion = 0
    valid_files = 0
    
    for name, result in results.items():
        if result["exists"]:
            status = "✅" if result["completion"] >= 70 else "⚠️" if result["completion"] >= 40 else "❌"
            placeholder_status = "❌ 有占位符" if result.get("has_placeholder") else "✅ 无占位符"
            
            print(f"\n{status} **{name}**:")
            print(f"   完成度: {result['completion']}%")
            print(f"   文件大小: {result.get('size_kb', 0)}KB")
            print(f"   代码行数: {result.get('lines', 0)}")
            print(f"   占位符状态: {placeholder_status}")
            
            if result.get("complexity"):
                comp = result["complexity"]
                print(f"   复杂度指标: 方法{comp.get('methods_count', 0)}个, 组件{comp.get('components', 0)}个, 导入{comp.get('imports', 0)}个")
            
            if result["completion"] > 0:
                total_completion += result["completion"]
                valid_files += 1
        else:
            print(f"\n❌ **{name}**: 文件不存在")
    
    # 计算总体完成度
    overall_completion = total_completion / valid_files if valid_files > 0 else 0
    
    print(f"\n📈 **总体评估**:")
    print(f"   整体完成度: {overall_completion:.1f}%")
    print(f"   有效文件数: {valid_files}/{len(files_to_check)}")
    
    # 给出建议
    print(f"\n💡 **改进建议**:")
    
    for name, result in results.items():
        if result.get("has_placeholder"):
            print(f"   - {name}: 移除占位符，实现实际功能")
        elif result.get("completion", 0) < 70:
            print(f"   - {name}: 完善功能实现，当前{result.get('completion', 0)}%")
    
    if overall_completion >= 80:
        print(f"\n🎉 **结论**: 交易中心基本完成，可以投入使用！")
    elif overall_completion >= 60:
        print(f"\n✅ **结论**: 交易中心大部分功能完成，需要少量完善。")
    else:
        print(f"\n⚠️ **结论**: 交易中心需要重点完善。")
    
    return results

def check_specific_issues():
    """检查具体问题"""
    project_root = Path.cwd().parent
    trading_center_path = project_root / "frontend" / "src" / "views" / "Trading" / "TradingCenter.vue"
    
    if not trading_center_path.exists():
        print("❌ TradingCenter.vue 文件不存在")
        return
    
    content = trading_center_path.read_text(encoding='utf-8')
    
    print("\n🔍 **具体问题检查**:")
    
    # 检查是否有注释掉的导入
    commented_imports = re.findall(r'//\s*import.*Module.*vue', content)
    if commented_imports:
        print(f"   ❌ 发现注释掉的导入: {len(commented_imports)}个")
        for imp in commented_imports:
            print(f"      {imp}")
    else:
        print(f"   ✅ 导入语句正常")
    
    # 检查组件注册
    components_in_template = re.findall(r'<([A-Z]\w+Module)', content)
    components_imported = re.findall(r'import (\w+Module) from', content)
    
    print(f"   模板中使用的组件: {components_in_template}")
    print(f"   导入的组件: {components_imported}")
    
    if set(components_in_template) == set(components_imported):
        print(f"   ✅ 组件导入和使用匹配")
    else:
        print(f"   ❌ 组件导入和使用不匹配")
    
    # 检查占位符
    placeholders_found = []
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if any(p in line for p in ["开发中", "正在开发", "Coming Soon", "TODO"]):
            placeholders_found.append(f"第{i+1}行: {line.strip()}")
    
    if placeholders_found:
        print(f"   ❌ 发现占位符 ({len(placeholders_found)}处):")
        for placeholder in placeholders_found[:5]:  # 只显示前5个
            print(f"      {placeholder}")
    else:
        print(f"   ✅ 无占位符内容")

if __name__ == "__main__":
    print("🚀 开始检查交易中心状态...\n")
    
    # 检查组件状态
    results = check_trading_center_status()
    
    # 检查具体问题
    check_specific_issues()
    
    print(f"\n✅ 检查完成！")

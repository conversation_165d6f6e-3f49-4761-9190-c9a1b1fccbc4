#!/usr/bin/env python3
"""
交易系统状态检查
验证各个交易模块的实际完成度和可用性
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import importlib.util

from loguru import logger


class TradingSystemStatusChecker:
    """交易系统状态检查器"""
    
    def __init__(self):
        self.project_root = Path.cwd().parent
        self.backend_path = self.project_root / "backend"
        self.frontend_path = self.project_root / "frontend"
        
        self.status_report = {
            "timestamp": "2025-08-05",
            "modules": {},
            "overall_assessment": {}
        }
    
    def check_backend_services(self) -> Dict[str, Any]:
        """检查后端服务完成度"""
        logger.info("🔍 检查后端服务...")
        
        services_path = self.backend_path / "app" / "services"
        api_path = self.backend_path / "app" / "api" / "v1"
        
        # 关键服务文件检查
        key_services = {
            "trading_service.py": "交易服务",
            "simulated_trading_engine.py": "模拟交易引擎", 
            "miniqmt_service.py": "MiniQMT服务",
            "risk_service.py": "风险管理服务",
            "websocket_manager.py": "WebSocket管理",
            "market_data_service.py": "市场数据服务",
            "strategy_execution_engine.py": "策略执行引擎",
            "technical_indicators.py": "技术指标服务"
        }
        
        # API文件检查
        key_apis = {
            "trading.py": "交易API",
            "simulated_trading.py": "模拟交易API",
            "trading_terminal.py": "交易终端API",
            "risk.py": "风险管理API",
            "websocket.py": "WebSocket API"
        }
        
        results = {
            "services": {},
            "apis": {},
            "service_count": 0,
            "api_count": 0
        }
        
        # 检查服务文件
        for filename, description in key_services.items():
            file_path = services_path / filename
            if file_path.exists():
                file_size = file_path.stat().st_size
                results["services"][filename] = {
                    "exists": True,
                    "description": description,
                    "size_kb": round(file_size / 1024, 1),
                    "estimated_completion": self._estimate_completion_by_size(file_size)
                }
                results["service_count"] += 1
            else:
                results["services"][filename] = {
                    "exists": False,
                    "description": description
                }
        
        # 检查API文件
        for filename, description in key_apis.items():
            file_path = api_path / filename
            if file_path.exists():
                file_size = file_path.stat().st_size
                results["apis"][filename] = {
                    "exists": True,
                    "description": description,
                    "size_kb": round(file_size / 1024, 1),
                    "estimated_completion": self._estimate_completion_by_size(file_size)
                }
                results["api_count"] += 1
            else:
                results["apis"][filename] = {
                    "exists": False,
                    "description": description
                }
        
        # 统计所有服务文件
        if services_path.exists():
            all_services = list(services_path.glob("*.py"))
            results["total_service_files"] = len(all_services)
        
        return results
    
    def check_frontend_components(self) -> Dict[str, Any]:
        """检查前端组件完成度"""
        logger.info("🎨 检查前端组件...")
        
        # 关键前端组件
        key_components = {
            "src/views/Trading/TradingCenter.vue": "交易中心",
            "src/views/Trading/SimulatedTrading.vue": "模拟交易",
            "src/views/Trading/MiniQMTTrading.vue": "MiniQMT交易",
            "src/views/Trading/TradingTerminal.vue": "交易终端",
            "src/components/Trading/OrderPanel.vue": "订单面板",
            "src/components/Trading/PositionPanel.vue": "持仓面板",
            "src/components/Risk/RiskMonitor.vue": "风险监控"
        }
        
        results = {
            "components": {},
            "component_count": 0
        }
        
        for filepath, description in key_components.items():
            full_path = self.frontend_path / filepath
            if full_path.exists():
                file_size = full_path.stat().st_size
                
                # 检查是否包含"开发中"等占位符
                try:
                    content = full_path.read_text(encoding='utf-8')
                    has_placeholder = any(keyword in content for keyword in [
                        "开发中", "正在开发", "Coming Soon", "TODO", "待实现"
                    ])
                    
                    # 检查组件复杂度
                    component_complexity = self._analyze_vue_component(content)
                    
                    results["components"][filepath] = {
                        "exists": True,
                        "description": description,
                        "size_kb": round(file_size / 1024, 1),
                        "has_placeholder": has_placeholder,
                        "complexity": component_complexity,
                        "estimated_completion": self._estimate_vue_completion(content, file_size)
                    }
                    results["component_count"] += 1
                    
                except Exception as e:
                    results["components"][filepath] = {
                        "exists": True,
                        "description": description,
                        "error": str(e)
                    }
            else:
                results["components"][filepath] = {
                    "exists": False,
                    "description": description
                }
        
        return results
    
    def check_miniqmt_integration(self) -> Dict[str, Any]:
        """检查MiniQMT集成状态"""
        logger.info("🔌 检查MiniQMT集成...")
        
        results = {
            "config_exists": False,
            "service_exists": False,
            "api_exists": False,
            "frontend_exists": False,
            "integration_level": "none"
        }
        
        # 检查配置文件
        config_path = self.backend_path / "app" / "core" / "miniqmt_config.py"
        if config_path.exists():
            results["config_exists"] = True
            results["config_size_kb"] = round(config_path.stat().st_size / 1024, 1)
        
        # 检查服务文件
        service_path = self.backend_path / "app" / "services" / "miniqmt_service.py"
        if service_path.exists():
            results["service_exists"] = True
            results["service_size_kb"] = round(service_path.stat().st_size / 1024, 1)
            
            # 分析服务实现程度
            try:
                content = service_path.read_text(encoding='utf-8')
                results["has_real_api_calls"] = "httpx.AsyncClient" in content
                results["has_mock_fallback"] = "模拟数据" in content or "mock" in content.lower()
                results["error_handling"] = "MiniQMTError" in content
            except:
                pass
        
        # 检查API文件
        api_path = self.backend_path / "app" / "api" / "v1" / "miniqmt.py"
        if api_path.exists():
            results["api_exists"] = True
        
        # 检查前端组件
        frontend_path = self.frontend_path / "src" / "views" / "Trading" / "MiniQMTTrading.vue"
        if frontend_path.exists():
            results["frontend_exists"] = True
            results["frontend_size_kb"] = round(frontend_path.stat().st_size / 1024, 1)
        
        # 评估集成程度
        if all([results["config_exists"], results["service_exists"], results["frontend_exists"]]):
            if results.get("has_real_api_calls"):
                results["integration_level"] = "advanced"
            else:
                results["integration_level"] = "basic"
        elif any([results["config_exists"], results["service_exists"]]):
            results["integration_level"] = "partial"
        
        return results
    
    def check_risk_management(self) -> Dict[str, Any]:
        """检查风险管理系统"""
        logger.info("⚠️ 检查风险管理系统...")
        
        risk_files = {
            "backend/app/services/risk_service.py": "风险服务",
            "backend/app/services/realtime_risk_control.py": "实时风控",
            "backend/app/services/risk_control_service.py": "风控服务",
            "backend/app/api/v1/risk.py": "风险API"
        }
        
        results = {
            "files": {},
            "file_count": 0,
            "estimated_completion": 0
        }
        
        total_size = 0
        existing_files = 0
        
        for filepath, description in risk_files.items():
            full_path = self.project_root / filepath
            if full_path.exists():
                file_size = full_path.stat().st_size
                results["files"][filepath] = {
                    "exists": True,
                    "description": description,
                    "size_kb": round(file_size / 1024, 1)
                }
                total_size += file_size
                existing_files += 1
                results["file_count"] += 1
            else:
                results["files"][filepath] = {
                    "exists": False,
                    "description": description
                }
        
        # 估算风险管理系统完成度
        if existing_files > 0:
            avg_size = total_size / existing_files
            results["estimated_completion"] = min(90, max(10, (avg_size / 1024) * 20))  # 基于文件大小估算
        
        return results
    
    def _estimate_completion_by_size(self, file_size: int) -> int:
        """根据文件大小估算完成度"""
        size_kb = file_size / 1024
        
        if size_kb < 1:
            return 10
        elif size_kb < 5:
            return 30
        elif size_kb < 10:
            return 50
        elif size_kb < 20:
            return 70
        elif size_kb < 30:
            return 85
        else:
            return 95
    
    def _analyze_vue_component(self, content: str) -> str:
        """分析Vue组件复杂度"""
        if len(content) < 1000:
            return "simple"
        elif len(content) < 5000:
            return "medium"
        elif len(content) < 15000:
            return "complex"
        else:
            return "very_complex"
    
    def _estimate_vue_completion(self, content: str, file_size: int) -> int:
        """估算Vue组件完成度"""
        # 检查占位符
        placeholders = ["开发中", "正在开发", "Coming Soon", "TODO", "待实现"]
        has_placeholder = any(p in content for p in placeholders)
        
        if has_placeholder:
            return 30  # 有占位符说明还在开发中
        
        # 基于文件大小和内容复杂度估算
        size_score = self._estimate_completion_by_size(file_size)
        
        # 检查功能完整性指标
        functionality_indicators = [
            "methods:", "computed:", "watch:", "mounted:", "created:",
            "async", "await", "axios", "fetch", "emit", "props:"
        ]
        
        functionality_score = sum(1 for indicator in functionality_indicators if indicator in content)
        functionality_percentage = min(100, functionality_score * 10)
        
        # 综合评分
        return int((size_score + functionality_percentage) / 2)
    
    def generate_status_report(self) -> str:
        """生成状态报告"""
        logger.info("📊 生成状态报告...")
        
        # 收集所有检查结果
        backend_results = self.check_backend_services()
        frontend_results = self.check_frontend_components()
        miniqmt_results = self.check_miniqmt_integration()
        risk_results = self.check_risk_management()
        
        # 计算整体完成度
        backend_completion = self._calculate_backend_completion(backend_results)
        frontend_completion = self._calculate_frontend_completion(frontend_results)
        miniqmt_completion = self._calculate_miniqmt_completion(miniqmt_results)
        risk_completion = risk_results["estimated_completion"]
        
        overall_completion = (
            backend_completion * 0.4 +
            frontend_completion * 0.3 +
            miniqmt_completion * 0.2 +
            risk_completion * 0.1
        )
        
        # 生成报告
        report = f"""
# 📊 交易系统状态检查报告

## 🎯 整体评估
- **整体完成度**: {overall_completion:.1f}%
- **检查时间**: 2025年8月5日
- **系统状态**: {'🟢 良好' if overall_completion >= 70 else '🟡 待完善' if overall_completion >= 50 else '🔴 需改进'}

## 📋 各模块详细状态

### 🔧 后端服务 ({backend_completion:.1f}%)
- **服务文件**: {backend_results['service_count']}/{len(backend_results['services'])} 个存在
- **API文件**: {backend_results['api_count']}/{len(backend_results['apis'])} 个存在
- **总服务文件**: {backend_results.get('total_service_files', 0)} 个

#### 关键服务状态:
"""
        
        for filename, info in backend_results['services'].items():
            if info['exists']:
                status = "✅"
                completion = info.get('estimated_completion', 0)
                report += f"- {status} **{info['description']}**: {completion}% ({info['size_kb']}KB)\n"
            else:
                report += f"- ❌ **{info['description']}**: 缺失\n"
        
        report += f"""
### 🎨 前端组件 ({frontend_completion:.1f}%)
- **组件文件**: {frontend_results['component_count']}/{len(frontend_results['components'])} 个存在

#### 关键组件状态:
"""
        
        for filepath, info in frontend_results['components'].items():
            if info['exists']:
                status = "✅" if not info.get('has_placeholder', False) else "⚠️"
                completion = info.get('estimated_completion', 0)
                complexity = info.get('complexity', 'unknown')
                report += f"- {status} **{info['description']}**: {completion}% ({complexity}, {info['size_kb']}KB)\n"
            else:
                report += f"- ❌ **{info['description']}**: 缺失\n"
        
        report += f"""
### 🔌 MiniQMT集成 ({miniqmt_completion:.1f}%)
- **集成程度**: {miniqmt_results['integration_level']}
- **配置文件**: {'✅' if miniqmt_results['config_exists'] else '❌'}
- **服务文件**: {'✅' if miniqmt_results['service_exists'] else '❌'}
- **前端组件**: {'✅' if miniqmt_results['frontend_exists'] else '❌'}
- **真实API调用**: {'✅' if miniqmt_results.get('has_real_api_calls') else '❌'}
- **模拟数据回退**: {'✅' if miniqmt_results.get('has_mock_fallback') else '❌'}

### ⚠️ 风险管理 ({risk_completion:.1f}%)
- **风险文件**: {risk_results['file_count']}/{len(risk_results['files'])} 个存在

## 🎯 改进建议

### 🔴 高优先级
"""
        
        if frontend_completion < 70:
            report += "- **完善前端组件**: 特别是交易中心核心功能\n"
        
        if miniqmt_completion < 60:
            report += "- **完善MiniQMT集成**: 实现真实API调用\n"
        
        if risk_completion < 50:
            report += "- **加强风险管理**: 完善风控系统核心功能\n"
        
        report += f"""
### 🟡 中优先级
- **优化WebSocket通信**: 提升实时数据推送效率
- **完善错误处理**: 统一错误处理机制
- **添加单元测试**: 提高代码质量

## 📈 总结
当前系统{'基础扎实，部分模块表现突出' if overall_completion >= 60 else '需要重点完善核心功能'}。
建议优先{'完善前端交易中心和MiniQMT集成' if overall_completion >= 60 else '从基础功能开始逐步完善'}。
"""
        
        return report
    
    def _calculate_backend_completion(self, results: Dict) -> float:
        """计算后端完成度"""
        service_completions = []
        for info in results['services'].values():
            if info['exists']:
                service_completions.append(info.get('estimated_completion', 0))
        
        api_completions = []
        for info in results['apis'].values():
            if info['exists']:
                api_completions.append(info.get('estimated_completion', 0))
        
        if service_completions and api_completions:
            return (sum(service_completions) / len(service_completions) + 
                   sum(api_completions) / len(api_completions)) / 2
        elif service_completions:
            return sum(service_completions) / len(service_completions)
        else:
            return 0
    
    def _calculate_frontend_completion(self, results: Dict) -> float:
        """计算前端完成度"""
        completions = []
        for info in results['components'].values():
            if info['exists']:
                completions.append(info.get('estimated_completion', 0))
        
        return sum(completions) / len(completions) if completions else 0
    
    def _calculate_miniqmt_completion(self, results: Dict) -> float:
        """计算MiniQMT完成度"""
        level_scores = {
            "none": 0,
            "partial": 30,
            "basic": 60,
            "advanced": 85
        }
        
        return level_scores.get(results['integration_level'], 0)


def main():
    """主函数"""
    checker = TradingSystemStatusChecker()
    
    try:
        report = checker.generate_status_report()
        
        # 保存报告
        report_file = f"trading_system_status_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"\n📄 报告已保存: {report_file}")
        
    except Exception as e:
        logger.error(f"状态检查失败: {e}")


if __name__ == "__main__":
    import time
    main()

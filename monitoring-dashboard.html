<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控仪表板 - 量化投资平台</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
        }
        
        .dashboard {
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        
        .metric-change {
            font-size: 12px;
            margin-top: 5px;
        }
        
        .positive {
            color: #67c23a;
        }
        
        .negative {
            color: #f56c6c;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .chart-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            height: 300px;
        }
        
        .alerts-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .alert-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .alert-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-healthy {
            background: #67c23a;
        }
        
        .status-warning {
            background: #e6a23c;
        }
        
        .status-error {
            background: #f56c6c;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard">
            <!-- 头部 -->
            <div class="header">
                <div>
                    <h2>监控仪表板</h2>
                    <p style="margin: 0; color: #666;">实时系统监控和性能指标</p>
                </div>
                <div>
                    <el-button @click="refreshData" :loading="loading">刷新数据</el-button>
                    <el-button @click="goBack">返回主页</el-button>
                </div>
            </div>
            
            <!-- 关键指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">系统状态</div>
                    <div class="metric-value" :style="{color: systemStatus.color}">
                        {{ systemStatus.text }}
                    </div>
                    <div class="metric-change">
                        运行时间: {{ uptime }}
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">API响应时间</div>
                    <div class="metric-value">{{ apiResponseTime }}ms</div>
                    <div class="metric-change" :class="apiChangeClass">
                        {{ apiChange > 0 ? '+' : '' }}{{ apiChange }}ms
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">活跃连接</div>
                    <div class="metric-value">{{ activeConnections }}</div>
                    <div class="metric-change positive">
                        +{{ newConnections }} 新连接
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">内存使用率</div>
                    <div class="metric-value">{{ memoryUsage }}%</div>
                    <div class="metric-change" :class="memoryChangeClass">
                        {{ memoryChange > 0 ? '+' : '' }}{{ memoryChange }}%
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">CPU使用率</div>
                    <div class="metric-value">{{ cpuUsage }}%</div>
                    <div class="metric-change" :class="cpuChangeClass">
                        {{ cpuChange > 0 ? '+' : '' }}{{ cpuChange }}%
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">错误率</div>
                    <div class="metric-value">{{ errorRate }}%</div>
                    <div class="metric-change" :class="errorChangeClass">
                        {{ errorChange > 0 ? '+' : '' }}{{ errorChange }}%
                    </div>
                </div>
            </div>
            
            <!-- 图表 -->
            <div class="charts-grid">
                <div class="chart-card">
                    <h3>系统性能趋势</h3>
                    <div id="performance-chart" class="chart-container"></div>
                </div>
                
                <div class="chart-card">
                    <h3>API请求统计</h3>
                    <div id="api-chart" class="chart-container"></div>
                </div>
                
                <div class="chart-card">
                    <h3>资源使用情况</h3>
                    <div id="resource-chart" class="chart-container"></div>
                </div>
                
                <div class="chart-card">
                    <h3>错误分布</h3>
                    <div id="error-chart" class="chart-container"></div>
                </div>
            </div>
            
            <!-- 告警和状态 -->
            <div class="alerts-section">
                <h3>系统状态和告警</h3>
                
                <div class="alert-item" v-for="alert in alerts" :key="alert.id">
                    <div style="display: flex; align-items: center;">
                        <div class="status-indicator" :class="'status-' + alert.level"></div>
                        <div>
                            <div style="font-weight: bold;">{{ alert.title }}</div>
                            <div style="font-size: 12px; color: #666;">{{ alert.description }}</div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 12px; color: #666;">{{ alert.time }}</div>
                        <el-tag :type="alert.level === 'error' ? 'danger' : alert.level === 'warning' ? 'warning' : 'success'" size="small">
                            {{ alert.level }}
                        </el-tag>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    systemStatus: { text: '正常', color: '#67c23a' },
                    uptime: '2天 14小时 32分钟',
                    apiResponseTime: 125,
                    apiChange: -15,
                    activeConnections: 1247,
                    newConnections: 23,
                    memoryUsage: 68,
                    memoryChange: 2,
                    cpuUsage: 45,
                    cpuChange: -5,
                    errorRate: 0.12,
                    errorChange: -0.03,
                    alerts: [
                        {
                            id: 1,
                            level: 'healthy',
                            title: '系统运行正常',
                            description: '所有服务运行正常，无异常',
                            time: '2分钟前'
                        },
                        {
                            id: 2,
                            level: 'warning',
                            title: '内存使用率较高',
                            description: '内存使用率达到68%，建议关注',
                            time: '5分钟前'
                        },
                        {
                            id: 3,
                            level: 'healthy',
                            title: 'API响应时间优化',
                            description: '平均响应时间降低至125ms',
                            time: '10分钟前'
                        },
                        {
                            id: 4,
                            level: 'error',
                            title: '数据库连接异常',
                            description: '检测到3次数据库连接超时',
                            time: '1小时前'
                        }
                    ]
                }
            },
            computed: {
                apiChangeClass() {
                    return this.apiChange > 0 ? 'negative' : 'positive';
                },
                memoryChangeClass() {
                    return this.memoryChange > 5 ? 'negative' : this.memoryChange > 0 ? 'neutral' : 'positive';
                },
                cpuChangeClass() {
                    return this.cpuChange > 10 ? 'negative' : this.cpuChange > 0 ? 'neutral' : 'positive';
                },
                errorChangeClass() {
                    return this.errorChange > 0 ? 'negative' : 'positive';
                }
            },
            methods: {
                refreshData() {
                    this.loading = true;
                    
                    // 模拟数据刷新
                    setTimeout(() => {
                        this.loading = false;
                        this.updateMetrics();
                        this.updateCharts();
                        ElMessage.success('数据刷新成功');
                    }, 1000);
                },
                
                updateMetrics() {
                    // 模拟指标更新
                    this.apiResponseTime = Math.floor(Math.random() * 50 + 100);
                    this.activeConnections = Math.floor(Math.random() * 200 + 1200);
                    this.memoryUsage = Math.floor(Math.random() * 20 + 60);
                    this.cpuUsage = Math.floor(Math.random() * 30 + 30);
                    this.errorRate = (Math.random() * 0.5).toFixed(2);
                },
                
                initCharts() {
                    this.initPerformanceChart();
                    this.initApiChart();
                    this.initResourceChart();
                    this.initErrorChart();
                },
                
                initPerformanceChart() {
                    const chart = echarts.init(document.getElementById('performance-chart'));
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['响应时间', 'CPU使用率', '内存使用率']
                        },
                        xAxis: {
                            type: 'category',
                            data: Array.from({length: 24}, (_, i) => i + 'h')
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '响应时间',
                                type: 'line',
                                data: Array.from({length: 24}, () => Math.floor(Math.random() * 50 + 100))
                            },
                            {
                                name: 'CPU使用率',
                                type: 'line',
                                data: Array.from({length: 24}, () => Math.floor(Math.random() * 30 + 30))
                            },
                            {
                                name: '内存使用率',
                                type: 'line',
                                data: Array.from({length: 24}, () => Math.floor(Math.random() * 20 + 60))
                            }
                        ]
                    };
                    
                    chart.setOption(option);
                },
                
                initApiChart() {
                    const chart = echarts.init(document.getElementById('api-chart'));
                    
                    const option = {
                        tooltip: {
                            trigger: 'item'
                        },
                        series: [{
                            type: 'pie',
                            radius: '50%',
                            data: [
                                { value: 1048, name: '成功请求' },
                                { value: 735, name: '缓存命中' },
                                { value: 580, name: '重定向' },
                                { value: 484, name: '客户端错误' },
                                { value: 300, name: '服务器错误' }
                            ]
                        }]
                    };
                    
                    chart.setOption(option);
                },
                
                initResourceChart() {
                    const chart = echarts.init(document.getElementById('resource-chart'));
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: ['CPU', '内存', '磁盘', '网络']
                        },
                        yAxis: {
                            type: 'value',
                            max: 100
                        },
                        series: [{
                            type: 'bar',
                            data: [45, 68, 23, 12],
                            itemStyle: {
                                color: function(params) {
                                    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666'];
                                    return colors[params.dataIndex];
                                }
                            }
                        }]
                    };
                    
                    chart.setOption(option);
                },
                
                initErrorChart() {
                    const chart = echarts.init(document.getElementById('error-chart'));
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: Array.from({length: 7}, (_, i) => {
                                const date = new Date();
                                date.setDate(date.getDate() - 6 + i);
                                return date.toLocaleDateString();
                            })
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [{
                            name: '错误数量',
                            type: 'bar',
                            data: [12, 8, 15, 6, 9, 4, 2],
                            itemStyle: {
                                color: '#f56c6c'
                            }
                        }]
                    };
                    
                    chart.setOption(option);
                },
                
                updateCharts() {
                    // 重新初始化图表以更新数据
                    this.initCharts();
                },
                
                goBack() {
                    window.location.href = '/';
                }
            },
            
            mounted() {
                this.initCharts();
                
                // 定时刷新数据
                setInterval(() => {
                    this.updateMetrics();
                }, 30000); // 每30秒更新一次
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>

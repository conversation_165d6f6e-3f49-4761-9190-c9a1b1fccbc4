{"openapi": "3.1.0", "info": {"title": "Quantum Investment Platform API", "description": "High-performance quantitative trading platform backend", "version": "1.0.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "Root endpoint", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health": {"get": {"summary": "Api Health", "description": "API v1 health check", "operationId": "api_health_api_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/login": {"post": {"summary": "<PERSON><PERSON>", "description": "Login endpoint", "operationId": "login_api_v1_auth_login_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/register": {"post": {"summary": "Register", "description": "Register endpoint", "operationId": "register_api_v1_auth_register_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/logout": {"post": {"summary": "Logout", "description": "Logout endpoint", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/market/stocks": {"get": {"summary": "Get Stocks", "description": "Get stock list", "operationId": "get_stocks_api_v1_market_stocks_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/market/realtime/{symbol}": {"get": {"summary": "Get Realtime Data", "description": "Get realtime market data", "operationId": "get_realtime_data_api_v1_market_realtime__symbol__get", "parameters": [{"name": "symbol", "in": "path", "required": true, "schema": {"type": "string", "title": "Symbol"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/market/kline/{symbol}": {"get": {"summary": "Get Kline Data", "description": "Get K-line data", "operationId": "get_kline_data_api_v1_market_kline__symbol__get", "parameters": [{"name": "symbol", "in": "path", "required": true, "schema": {"type": "string", "title": "Symbol"}}, {"name": "period", "in": "query", "required": false, "schema": {"type": "string", "default": "1d", "title": "Period"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/trading/positions": {"get": {"summary": "Get Positions", "description": "Get trading positions", "operationId": "get_positions_api_v1_trading_positions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/trading/orders": {"get": {"summary": "Get Orders", "description": "Get trading orders", "operationId": "get_orders_api_v1_trading_orders_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "post": {"summary": "Create Order", "description": "Create trading order", "operationId": "create_order_api_v1_trading_orders_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/strategies": {"get": {"summary": "Get Strategies", "description": "Get strategy list", "operationId": "get_strategies_api_v1_strategies_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "post": {"summary": "Create Strategy", "description": "Create new strategy", "operationId": "create_strategy_api_v1_strategies_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/backtest/run": {"post": {"summary": "Run Backtest", "description": "Run strategy backtest", "operationId": "run_backtest_api_v1_backtest_run_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/backtest/results/{backtest_id}": {"get": {"summary": "Get Backtest Results", "description": "Get backtest results", "operationId": "get_backtest_results_api_v1_backtest_results__backtest_id__get", "parameters": [{"name": "backtest_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Backtest Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/risk/metrics": {"get": {"summary": "Get Risk Metrics", "description": "Get risk metrics", "operationId": "get_risk_metrics_api_v1_risk_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/risk/limits": {"get": {"summary": "Get Risk Limits", "description": "Get risk limits", "operationId": "get_risk_limits_api_v1_risk_limits_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}
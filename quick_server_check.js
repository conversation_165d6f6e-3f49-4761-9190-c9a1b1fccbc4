/**
 * Quick Server Status Check
 * Checks if the trading center website is accessible before running full tests
 */

const puppeteer = require('puppeteer');

async function quickServerCheck() {
    console.log('🔍 Quick Server Status Check...');
    
    let browser = null;
    try {
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        console.log('📡 Checking http://localhost:5173...');
        
        try {
            const response = await page.goto('http://localhost:5173', {
                waitUntil: 'networkidle2',
                timeout: 10000
            });
            
            const status = response ? response.status() : 0;
            const url = page.url();
            const title = await page.title();
            
            console.log(`✅ Server Response: ${status}`);
            console.log(`🌐 Final URL: ${url}`);
            console.log(`📄 Page Title: "${title}"`);
            
            if (status === 200 || (status >= 200 && status < 400)) {
                console.log('✅ Server is accessible - ready for comprehensive testing!');
                return true;
            } else {
                console.log(`⚠️  Server returned status ${status} - may have issues`);
                return false;
            }
            
        } catch (error) {
            console.log('❌ Server check failed:', error.message);
            return false;
        }
        
    } catch (error) {
        console.error('❌ Browser launch failed:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Check if we're running this directly
if (require.main === module) {
    quickServerCheck().then(success => {
        if (success) {
            console.log('\n🚀 Server check passed! You can now run the comprehensive test:');
            console.log('node comprehensive_trading_center_test.js');
        } else {
            console.log('\n⚠️  Server check failed. Please ensure:');
            console.log('1. The frontend server is running (npm run dev in frontend folder)');
            console.log('2. The server is accessible at http://localhost:5173');
            console.log('3. There are no firewall or network issues');
        }
        process.exit(success ? 0 : 1);
    }).catch(console.error);
}

module.exports = quickServerCheck;
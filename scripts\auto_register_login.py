#!/usr/bin/env python3
"""
自动化注册和登录脚本
使用MCP Puppeteer服务器完成量化平台的注册和登录流程
"""

import asyncio
import json
import logging
import random
import string
import time
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuantPlatformAutomation:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.browser = None
        self.page = None
        self.user_data = {
            "username": f"testuser_{random.randint(1000, 9999)}",
            "email": f"test_{random.randint(1000, 9999)}@example.com",
            "password": "Test123456!"
        }
        
    async def start_browser(self):
        """启动浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        logger.info("浏览器启动成功")
        
    async def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            logger.info("浏览器已关闭")
            
    async def navigate_to_register(self):
        """导航到登录页面并切换到注册模式"""
        login_url = f"{self.base_url}/login"
        await self.page.goto(login_url)
        await self.page.wait_for_load_state('networkidle')
        logger.info(f"已导航到登录页面: {login_url}")

        # 等待页面加载完成
        await asyncio.sleep(2)

        # 查找并点击切换到注册模式的按钮
        try:
            # 尝试多种可能的选择器
            toggle_selectors = [
                'text=注册',
                'text=立即注册',
                'text=免费注册',
                '.toggle-mode',
                '[data-testid="toggle-register"]',
                'button:has-text("注册")'
            ]

            for selector in toggle_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        logger.info(f"已点击注册切换按钮: {selector}")
                        await asyncio.sleep(1)
                        break
                except:
                    continue
            else:
                logger.warning("未找到注册切换按钮，可能已经在注册模式")

        except Exception as e:
            logger.warning(f"切换到注册模式时出错: {e}")

    async def fill_registration_form(self):
        """填写注册表单"""
        logger.info("开始填写注册表单")

        # 等待表单加载
        await asyncio.sleep(2)

        # 填写用户名 - 尝试多种选择器
        username_selectors = [
            'input[placeholder="用户名"]',
            'input[placeholder*="用户名"]',
            'input[name="username"]',
            '.el-input__inner[placeholder*="用户名"]'
        ]

        for selector in username_selectors:
            try:
                await self.page.fill(selector, self.user_data["username"])
                logger.info(f"已填写用户名: {self.user_data['username']} (使用选择器: {selector})")
                break
            except:
                continue
        else:
            logger.error("无法找到用户名输入框")

        # 填写邮箱
        email_selectors = [
            'input[placeholder="邮箱地址"]',
            'input[placeholder*="邮箱"]',
            'input[name="email"]',
            'input[type="email"]',
            '.el-input__inner[placeholder*="邮箱"]'
        ]

        for selector in email_selectors:
            try:
                await self.page.fill(selector, self.user_data["email"])
                logger.info(f"已填写邮箱: {self.user_data['email']} (使用选择器: {selector})")
                break
            except:
                continue
        else:
            logger.error("无法找到邮箱输入框")

        # 填写密码
        password_selectors = [
            'input[placeholder="请输入密码"]',
            'input[placeholder*="密码"]:not([placeholder*="确认"])',
            'input[name="password"]',
            'input[type="password"]:first-of-type',
            '.el-input__inner[placeholder*="密码"]:not([placeholder*="确认"])'
        ]

        for selector in password_selectors:
            try:
                await self.page.fill(selector, self.user_data["password"])
                logger.info("已填写密码")
                break
            except:
                continue
        else:
            logger.error("无法找到密码输入框")

        # 填写确认密码
        confirm_password_selectors = [
            'input[placeholder="确认密码"]',
            'input[placeholder*="确认密码"]',
            'input[name="confirmPassword"]',
            'input[type="password"]:last-of-type',
            '.el-input__inner[placeholder*="确认"]'
        ]

        for selector in confirm_password_selectors:
            try:
                await self.page.fill(selector, self.user_data["password"])
                logger.info("已填写确认密码")
                break
            except:
                continue
        else:
            logger.warning("无法找到确认密码输入框，可能不需要")

        # 勾选用户协议（如果存在）
        try:
            agreement_checkbox = await self.page.query_selector('input[type="checkbox"]')
            if agreement_checkbox:
                await agreement_checkbox.check()
                logger.info("已勾选用户协议")
        except:
            logger.warning("未找到用户协议复选框")
        
    async def solve_slider_captcha(self):
        """解决滑块验证码"""
        logger.info("开始解决滑块验证码")

        try:
            # 等待滑块验证码加载 - 尝试多种选择器
            captcha_selectors = [
                '.slider-captcha',
                '.captcha-container',
                '.slider-verify',
                '[class*="slider"]',
                '[class*="captcha"]'
            ]

            captcha_element = None
            for selector in captcha_selectors:
                try:
                    captcha_element = await self.page.wait_for_selector(selector, timeout=5000)
                    if captcha_element:
                        logger.info(f"滑块验证码已加载: {selector}")
                        break
                except:
                    continue

            if not captcha_element:
                logger.warning("未找到滑块验证码，可能不需要验证")
                return True

            # 获取滑块和轨道元素 - 尝试多种选择器
            handle_selectors = [
                '.slider-handle',
                '.slider-btn',
                '.drag-btn',
                '[class*="handle"]',
                '[class*="slider"] button',
                '.slider-captcha .el-button'
            ]

            track_selectors = [
                '.slider-track',
                '.slider-bar',
                '.drag-track',
                '[class*="track"]',
                '.slider-captcha .slider-container'
            ]

            slider_handle = None
            slider_track = None

            for selector in handle_selectors:
                try:
                    slider_handle = await self.page.query_selector(selector)
                    if slider_handle:
                        logger.info(f"找到滑块手柄: {selector}")
                        break
                except:
                    continue

            for selector in track_selectors:
                try:
                    slider_track = await self.page.query_selector(selector)
                    if slider_track:
                        logger.info(f"找到滑块轨道: {selector}")
                        break
                except:
                    continue

            if not slider_handle:
                logger.error("未找到滑块手柄元素")
                return False

            if not slider_track:
                logger.warning("未找到滑块轨道，使用手柄元素计算")
                slider_track = slider_handle

            # 获取滑块和轨道的位置信息
            handle_box = await slider_handle.bounding_box()
            track_box = await slider_track.bounding_box()

            if not handle_box:
                logger.error("无法获取滑块手柄位置信息")
                return False

            if not track_box:
                logger.error("无法获取滑块轨道位置信息")
                return False

            # 计算需要拖拽的距离
            # 使用轨道宽度的80%作为目标位置
            if slider_track == slider_handle:
                # 如果没有找到轨道，假设需要向右拖拽200像素
                drag_distance = 200
            else:
                drag_distance = track_box['width'] * 0.8

            logger.info(f"开始拖拽滑块，距离: {drag_distance}px")

            # 执行拖拽操作
            start_x = handle_box['x'] + handle_box['width'] / 2
            start_y = handle_box['y'] + handle_box['height'] / 2

            await self.page.mouse.move(start_x, start_y)
            await self.page.mouse.down()

            # 模拟人类拖拽行为：分段移动
            steps = 15
            step_distance = drag_distance / steps

            for i in range(steps):
                target_x = start_x + (i + 1) * step_distance
                await self.page.mouse.move(target_x, start_y, steps=3)
                await asyncio.sleep(0.03)  # 短暂延迟

            await self.page.mouse.up()
            logger.info("滑块拖拽完成")

            # 等待验证结果
            await asyncio.sleep(3)

            # 检查是否验证成功 - 尝试多种成功指示器
            success_selectors = [
                '.captcha-success',
                '.verify-success',
                '.success',
                '[class*="success"]',
                '.el-message--success'
            ]

            for selector in success_selectors:
                try:
                    success_element = await self.page.query_selector(selector)
                    if success_element:
                        logger.info("✅ 滑块验证码验证成功")
                        return True
                except:
                    continue

            logger.info("滑块验证码验证完成，继续流程")
            return True  # 继续流程，让后续步骤处理

        except Exception as e:
            logger.error(f"解决滑块验证码时出错: {e}")
            return False
            
    async def submit_registration(self):
        """提交注册表单"""
        logger.info("提交注册表单")
        
        # 点击注册按钮
        register_button = await self.page.query_selector('button[type="submit"]')
        if register_button:
            await register_button.click()
            logger.info("已点击注册按钮")
        else:
            # 尝试其他可能的注册按钮选择器
            await self.page.click('text=注册')
            logger.info("已点击注册按钮（备用选择器）")
            
        # 等待注册结果
        await asyncio.sleep(3)
        
    async def check_registration_result(self):
        """检查注册结果"""
        logger.info("检查注册结果")
        
        # 检查是否有成功消息
        try:
            success_message = await self.page.wait_for_selector('.el-message--success', timeout=5000)
            if success_message:
                message_text = await success_message.text_content()
                logger.info(f"✅ 注册成功: {message_text}")
                return True
        except:
            pass
            
        # 检查是否跳转到登录页面或其他成功页面
        current_url = self.page.url
        if 'login' in current_url or 'dashboard' in current_url:
            logger.info("✅ 注册成功，已跳转到其他页面")
            return True
            
        # 检查是否有错误消息
        try:
            error_message = await self.page.query_selector('.el-message--error')
            if error_message:
                message_text = await error_message.text_content()
                logger.error(f"❌ 注册失败: {message_text}")
                return False
        except:
            pass
            
        logger.warning("注册结果不明确")
        return False
        
    async def navigate_to_login(self):
        """导航到登录页面"""
        login_url = f"{self.base_url}/login"
        await self.page.goto(login_url)
        await self.page.wait_for_load_state('networkidle')
        logger.info(f"已导航到登录页面: {login_url}")
        
    async def perform_login(self):
        """执行登录"""
        logger.info("开始登录流程")
        
        # 填写用户名/邮箱
        await self.page.fill('input[placeholder*="用户名"], input[placeholder*="邮箱"]', self.user_data["username"])
        logger.info(f"已填写登录用户名: {self.user_data['username']}")
        
        # 填写密码
        await self.page.fill('input[type="password"]', self.user_data["password"])
        logger.info("已填写登录密码")
        
        # 如果有滑块验证码，解决它
        slider_exists = await self.page.query_selector('.slider-captcha')
        if slider_exists:
            await self.solve_slider_captcha()
            
        # 点击登录按钮
        login_button = await self.page.query_selector('button[type="submit"]')
        if login_button:
            await login_button.click()
            logger.info("已点击登录按钮")
        else:
            await self.page.click('text=登录')
            logger.info("已点击登录按钮（备用选择器）")
            
        # 等待登录结果
        await asyncio.sleep(3)
        
    async def check_login_result(self):
        """检查登录结果"""
        logger.info("检查登录结果")

        # 等待更长时间让页面响应
        await asyncio.sleep(5)

        # 检查当前URL
        current_url = self.page.url
        logger.info(f"当前URL: {current_url}")

        # 检查是否跳转到拼图验证页面
        if 'puzzle-verify' in current_url:
            logger.info(f"✅ 登录成功，已跳转到拼图验证页面: {current_url}")
            return True

        # 检查是否跳转到仪表板或主页
        success_urls = ['dashboard', 'main', 'home', '/', 'index']
        for url_part in success_urls:
            if url_part in current_url and 'login' not in current_url:
                logger.info(f"✅ 登录成功，已跳转到: {current_url}")
                return True

        # 检查页面标题
        try:
            page_title = await self.page.title()
            logger.info(f"页面标题: {page_title}")
            if any(keyword in page_title.lower() for keyword in ['dashboard', 'main', 'home', '量化', 'platform']):
                logger.info("✅ 登录成功，页面标题显示已登录")
                return True
        except:
            pass

        # 检查是否有成功消息
        success_message_selectors = [
            '.el-message--success',
            '.message-success',
            '.success-message',
            '[class*="success"]'
        ]

        for selector in success_message_selectors:
            try:
                success_message = await self.page.wait_for_selector(selector, timeout=3000)
                if success_message:
                    message_text = await success_message.text_content()
                    logger.info(f"✅ 登录成功消息: {message_text}")
                    return True
            except:
                continue

        # 检查是否有用户信息显示（表示已登录）
        user_info_selectors = [
            '.user-info',
            '.user-avatar',
            '.user-name',
            '[class*="user"]',
            '.header-user',
            '.nav-user'
        ]

        for selector in user_info_selectors:
            try:
                user_element = await self.page.query_selector(selector)
                if user_element:
                    logger.info(f"✅ 登录成功，找到用户信息元素: {selector}")
                    return True
            except:
                continue

        # 检查是否还在登录页面
        if 'login' in current_url:
            # 检查是否有错误消息
            error_message_selectors = [
                '.el-message--error',
                '.message-error',
                '.error-message',
                '[class*="error"]'
            ]

            for selector in error_message_selectors:
                try:
                    error_message = await self.page.query_selector(selector)
                    if error_message:
                        message_text = await error_message.text_content()
                        logger.error(f"❌ 登录失败: {message_text}")
                        return False
                except:
                    continue

            logger.warning("仍在登录页面，但未找到明确的错误消息")
            return False
        else:
            # 不在登录页面，可能登录成功了
            logger.info("✅ 已离开登录页面，可能登录成功")
            return True

    async def solve_puzzle_verification(self):
        """解决拼图验证"""
        logger.info("开始拼图验证")

        try:
            # 等待拼图页面加载
            await asyncio.sleep(3)

            # 查找拼图画布
            puzzle_canvas = await self.page.query_selector('.puzzle-canvas')
            if not puzzle_canvas:
                logger.error("未找到拼图画布")
                return False

            # 查找滑块按钮
            slider_btn = await self.page.query_selector('.slider-btn')
            if not slider_btn:
                logger.error("未找到滑块按钮")
                return False

            logger.info("找到拼图元素，开始拖拽")

            # 获取滑块按钮的位置
            btn_box = await slider_btn.bounding_box()
            if not btn_box:
                logger.error("无法获取滑块按钮位置")
                return False

            # 计算拖拽距离 - 拼图验证通常需要拖拽到特定位置
            # 这里使用一个估算值，实际应用中可能需要图像识别
            drag_distance = 200  # 根据拼图的实际位置调整

            # 执行拖拽操作
            start_x = btn_box['x'] + btn_box['width'] / 2
            start_y = btn_box['y'] + btn_box['height'] / 2

            await self.page.mouse.move(start_x, start_y)
            await self.page.mouse.down()

            # 模拟人类拖拽：分段移动
            steps = 20
            step_distance = drag_distance / steps

            for i in range(steps):
                target_x = start_x + (i + 1) * step_distance
                await self.page.mouse.move(target_x, start_y, steps=2)
                await asyncio.sleep(0.02)

            await self.page.mouse.up()
            logger.info("拼图拖拽完成")

            # 等待验证结果
            await asyncio.sleep(3)

            # 检查验证结果
            success_selectors = [
                '.result-message.success',
                '.verify-result .success',
                'text=验证成功',
                '.slider-btn-success'
            ]

            for selector in success_selectors:
                try:
                    success_element = await self.page.query_selector(selector)
                    if success_element:
                        logger.info("✅ 拼图验证成功")

                        # 点击继续访问按钮
                        continue_btn = await self.page.query_selector('button:has-text("继续访问")')
                        if continue_btn:
                            await continue_btn.click()
                            logger.info("已点击继续访问按钮")
                            await asyncio.sleep(2)

                        return True
                except:
                    continue

            # 检查是否有错误消息
            error_element = await self.page.query_selector('.result-message.error')
            if error_element:
                error_text = await error_element.text_content()
                logger.warning(f"拼图验证失败: {error_text}")

                # 尝试刷新拼图
                refresh_btn = await self.page.query_selector('.refresh-btn')
                if refresh_btn:
                    await refresh_btn.click()
                    logger.info("已刷新拼图，重新尝试")
                    await asyncio.sleep(2)
                    return await self.solve_puzzle_verification()  # 递归重试

            logger.warning("拼图验证结果不明确")
            return False

        except Exception as e:
            logger.error(f"拼图验证过程出错: {e}")
            return False
        
    async def run_automation(self):
        """运行完整的自动化流程"""
        try:
            logger.info("🚀 开始自动化注册和登录流程")
            
            # 启动浏览器
            await self.start_browser()
            
            # 注册流程
            logger.info("=" * 50)
            logger.info("📝 开始注册流程")
            logger.info("=" * 50)
            
            await self.navigate_to_register()
            await self.fill_registration_form()
            await self.solve_slider_captcha()
            await self.submit_registration()
            registration_success = await self.check_registration_result()
            
            if registration_success:
                logger.info("✅ 注册流程完成")
            else:
                logger.warning("⚠️ 注册流程可能有问题，继续尝试登录")
                
            # 登录流程
            logger.info("=" * 50)
            logger.info("🔐 开始登录流程")
            logger.info("=" * 50)
            
            await self.navigate_to_login()
            await self.perform_login()
            login_success = await self.check_login_result()
            
            if login_success:
                logger.info("✅ 登录流程完成")

                # 检查是否需要拼图验证
                current_url = self.page.url
                if 'puzzle-verify' in current_url:
                    logger.info("=" * 50)
                    logger.info("🧩 开始拼图验证流程")
                    logger.info("=" * 50)

                    puzzle_success = await self.solve_puzzle_verification()
                    if puzzle_success:
                        logger.info("✅ 拼图验证完成")
                    else:
                        logger.error("❌ 拼图验证失败")
            else:
                logger.error("❌ 登录流程失败")

            # 保持浏览器打开一段时间以便观察结果
            logger.info("保持浏览器打开10秒以便观察结果...")
            await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"自动化流程出错: {e}")
        finally:
            await self.close_browser()
            
        logger.info("🎉 自动化流程完成")

async def main():
    """主函数"""
    automation = QuantPlatformAutomation()
    await automation.run_automation()

if __name__ == "__main__":
    asyncio.run(main())

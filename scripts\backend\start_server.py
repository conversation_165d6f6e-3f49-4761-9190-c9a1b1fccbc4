#!/usr/bin/env python3
"""
简单的服务器启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import uvicorn
    # 使用统一的主入口点
    try:
        from app.main import app
        app_module = "app.main:app"
        print("使用完整版后端服务...")
    except ImportError:
        from app.main_simple import app
        app_module = "app.main_simple:app"
        print("使用简化版后端服务...")

    if __name__ == "__main__":
        print("启动量化投资平台后端服务...")
        print("访问地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")

        uvicorn.run(
            app_module,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请安装依赖: pip install fastapi uvicorn")
    sys.exit(1)
except Exception as e:
    print(f"启动错误: {e}")
    sys.exit(1)

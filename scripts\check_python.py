#!/usr/bin/env python3
"""
Python 版本兼容性检查脚本
确保运行环境使用兼容的 Python 版本
"""
import sys
import logging
from pathlib import Path

# 版本要求配置
MIN_VERSION = (3, 10)
MAX_VERSION = (3, 11, 99)  # 允许 3.11.x 的所有补丁版本
RECOMMENDED_VERSION = "3.10.13"

# 关键依赖兼容性映射
CRITICAL_DEPENDENCIES = {
    "vnpy": {
        "max_python": (3, 11),
        "risk_level": "HIGH",
        "impact": "交易核心功能可能崩溃"
    },
    "TA-Lib": {
        "max_python": (3, 11),
        "risk_level": "HIGH", 
        "impact": "技术指标计算异常"
    },
    "SQLAlchemy": {
        "max_python": (3, 12),
        "risk_level": "MEDIUM",
        "impact": "数据库操作偶发失败"
    }
}

def check_python_version():
    """检查当前 Python 版本是否兼容"""
    current_version = sys.version_info[:3]
    
    print(f"🐍 当前 Python 版本: {'.'.join(map(str, current_version))}")
    print(f"📋 要求版本范围: {'.'.join(map(str, MIN_VERSION))} ~ {'.'.join(map(str, MAX_VERSION[:2]))}.x")
    print(f"✅ 推荐版本: {RECOMMENDED_VERSION}")
    
    # 版本范围检查
    if current_version[:2] < MIN_VERSION:
        print(f"❌ 错误: Python 版本过低")
        print(f"   当前: {sys.version}")
        print(f"   最低要求: {'.'.join(map(str, MIN_VERSION))}")
        return False
        
    if current_version[:2] > MAX_VERSION[:2]:
        print(f"⚠️  警告: Python 版本过高，可能存在兼容性问题")
        print(f"   当前: {sys.version}")
        print(f"   最高支持: {'.'.join(map(str, MAX_VERSION[:2]))}.x")
        
        # 检查关键依赖兼容性
        print("\n🔍 关键依赖兼容性分析:")
        for dep, info in CRITICAL_DEPENDENCIES.items():
            if current_version[:2] > info["max_python"]:
                risk_emoji = "🚨" if info["risk_level"] == "HIGH" else "⚠️"
                print(f"   {risk_emoji} {dep}: {info['impact']}")
        
        return False
    
    print("✅ Python 版本兼容性检查通过")
    return True

def check_environment():
    """检查运行环境配置"""
    print("\n🔧 环境配置检查:")
    
    # 检查 .python-version 文件
    python_version_file = Path(".python-version")
    if python_version_file.exists():
        specified_version = python_version_file.read_text().strip()
        print(f"   📄 .python-version: {specified_version}")
        
        current_version_str = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        if not current_version_str.startswith(specified_version[:4]):  # 比较主版本号
            print(f"   ⚠️  版本不匹配: 当前 {current_version_str} vs 指定 {specified_version}")
    else:
        print("   ❌ 缺少 .python-version 文件")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("   ✅ 运行在虚拟环境中")
    else:
        print("   ⚠️  未使用虚拟环境")

def generate_compatibility_report():
    """生成兼容性报告"""
    report_path = Path("reports/python_compatibility_check.md")
    report_path.parent.mkdir(exist_ok=True)
    
    current_version = sys.version_info[:3]
    
    report_content = f"""# Python 版本兼容性检查报告

## 检查时间
{sys.version}

## 版本信息
- **当前版本**: {'.'.join(map(str, current_version))}
- **要求范围**: {'.'.join(map(str, MIN_VERSION))} ~ {'.'.join(map(str, MAX_VERSION[:2]))}.x
- **推荐版本**: {RECOMMENDED_VERSION}

## 兼容性状态
"""
    
    if MIN_VERSION <= current_version[:2] <= MAX_VERSION[:2]:
        report_content += "✅ **兼容** - 当前版本在支持范围内\n\n"
    else:
        report_content += "❌ **不兼容** - 需要版本调整\n\n"
        
    report_content += "## 关键依赖分析\n\n"
    for dep, info in CRITICAL_DEPENDENCIES.items():
        status = "✅" if current_version[:2] <= info["max_python"] else "❌"
        report_content += f"- **{dep}**: {status} {info['impact']}\n"
    
    report_content += f"""
## 建议操作
1. 使用 pyenv 锁定到推荐版本: `pyenv install {RECOMMENDED_VERSION} && pyenv local {RECOMMENDED_VERSION}`
2. 重建虚拟环境: `python -m venv venv && source venv/bin/activate`
3. 重新安装依赖: `pip install -r requirements.txt`
4. 运行测试验证: `pytest tests/`
"""
    
    report_path.write_text(report_content, encoding='utf-8')
    print(f"\n📊 兼容性报告已生成: {report_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Python 版本兼容性检查")
    print("=" * 60)
    
    # 版本检查
    version_ok = check_python_version()
    
    # 环境检查
    check_environment()
    
    # 生成报告
    generate_compatibility_report()
    
    print("\n" + "=" * 60)
    
    if not version_ok:
        print("❌ 检查失败: Python 版本不兼容")
        print(f"🔧 建议执行: pyenv install {RECOMMENDED_VERSION} && pyenv local {RECOMMENDED_VERSION}")
        sys.exit(1)
    else:
        print("✅ 检查通过: Python 版本兼容")
        sys.exit(0)

if __name__ == "__main__":
    main()

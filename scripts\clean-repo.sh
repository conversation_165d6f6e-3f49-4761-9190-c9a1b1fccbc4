#!/bin/bash

# 仓库清理脚本 - 自动清理构建产物和大文件
# 使用方法: ./scripts/clean-repo.sh [--dry-run]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
DRY_RUN=false
if [[ "$1" == "--dry-run" ]]; then
    DRY_RUN=true
    log_info "运行在干跑模式，不会实际删除文件"
fi

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "开始清理仓库: $PROJECT_ROOT"

# 创建清理报告
REPORT_FILE="cleanup_report_$(date +%Y%m%d_%H%M%S).log"

{
    echo "仓库清理报告"
    echo "============="
    echo "时间: $(date)"
    echo "项目: $PROJECT_ROOT"
    echo "模式: $([ "$DRY_RUN" = true ] && echo "干跑模式" || echo "实际清理")"
    echo ""
} > "$REPORT_FILE"

# 清理函数
cleanup_files() {
    local pattern="$1"
    local description="$2"
    
    log_info "清理 $description..."
    
    local files_found=()
    while IFS= read -r -d '' file; do
        files_found+=("$file")
    done < <(find . -name "$pattern" -print0 2>/dev/null)
    
    if [ ${#files_found[@]} -eq 0 ]; then
        log_info "未找到 $description"
        echo "未找到: $description" >> "$REPORT_FILE"
        return 0
    fi
    
    echo "清理 $description: ${#files_found[@]} 个项目" >> "$REPORT_FILE"
    
    for file in "${files_found[@]}"; do
        if [ "$DRY_RUN" = true ]; then
            echo "  [DRY-RUN] 将删除: $file" >> "$REPORT_FILE"
            log_info "将删除: $file"
        else
            echo "  已删除: $file" >> "$REPORT_FILE"
            rm -rf "$file"
            log_success "已删除: $file"
        fi
    done
}

cleanup_directories() {
    local dir_name="$1"
    local description="$2"
    
    log_info "清理 $description 目录..."
    
    local dirs_found=()
    while IFS= read -r -d '' dir; do
        dirs_found+=("$dir")
    done < <(find . -name "$dir_name" -type d -print0 2>/dev/null)
    
    if [ ${#dirs_found[@]} -eq 0 ]; then
        log_info "未找到 $description 目录"
        echo "未找到: $description 目录" >> "$REPORT_FILE"
        return 0
    fi
    
    echo "清理 $description 目录: ${#dirs_found[@]} 个" >> "$REPORT_FILE"
    
    for dir in "${dirs_found[@]}"; do
        if [ "$DRY_RUN" = true ]; then
            echo "  [DRY-RUN] 将删除目录: $dir" >> "$REPORT_FILE"
            log_info "将删除目录: $dir"
        else
            echo "  已删除目录: $dir" >> "$REPORT_FILE"
            rm -rf "$dir"
            log_success "已删除目录: $dir"
        fi
    done
}

# 计算清理前大小
log_info "计算清理前仓库大小..."
if command -v du > /dev/null; then
    SIZE_BEFORE=$(du -sh . 2>/dev/null | cut -f1 || echo "未知")
    echo "清理前大小: $SIZE_BEFORE" >> "$REPORT_FILE"
    log_info "清理前大小: $SIZE_BEFORE"
fi

# 开始清理
echo "" >> "$REPORT_FILE"
echo "清理详情:" >> "$REPORT_FILE"
echo "--------" >> "$REPORT_FILE"

# 1. 清理前端构建产物和依赖
log_info "=== 清理前端构建产物 ==="
cleanup_directories "node_modules" "Node.js依赖"
cleanup_directories "dist" "构建输出"
cleanup_directories ".vite" "Vite缓存"
cleanup_directories ".turbo" "Turbo缓存"
cleanup_directories ".next" "Next.js构建"
cleanup_directories ".nuxt" "Nuxt.js构建"
cleanup_files "package-lock.json" "NPM锁文件"
cleanup_files "pnpm-lock.yaml" "PNPM锁文件"
cleanup_files "yarn.lock" "Yarn锁文件"

# 2. 清理Python构建产物
log_info "=== 清理Python构建产物 ==="
cleanup_directories "__pycache__" "Python缓存"
cleanup_directories ".pytest_cache" "Pytest缓存"
cleanup_directories "venv" "Python虚拟环境"
cleanup_directories ".venv" "Python虚拟环境"
cleanup_directories "env" "Python环境"
cleanup_files "*.pyc" "Python字节码"
cleanup_files "*.pyo" "Python优化字节码"
cleanup_files "*.pyd" "Python扩展模块"

# 3. 清理数据库文件
log_info "=== 清理数据库文件 ==="
cleanup_files "*.db" "SQLite数据库"
cleanup_files "*.sqlite" "SQLite数据库"
cleanup_files "*.sqlite3" "SQLite3数据库"

# 4. 清理日志文件
log_info "=== 清理日志文件 ==="
cleanup_files "*.log" "日志文件"
cleanup_directories "logs" "日志目录"

# 5. 清理临时文件
log_info "=== 清理临时文件 ==="
cleanup_files "*.tmp" "临时文件"
cleanup_files "*.swp" "Vim交换文件"
cleanup_files "*.swo" "Vim交换文件"
cleanup_files ".DS_Store" "macOS系统文件"
cleanup_files "Thumbs.db" "Windows缩略图"

# 6. 清理测试产物
log_info "=== 清理测试产物 ==="
cleanup_directories "coverage" "测试覆盖率"
cleanup_directories ".nyc_output" "NYC覆盖率"
cleanup_directories "test-results" "测试结果"
cleanup_files "*_test_*.png" "测试截图"
cleanup_files "*_test_*.json" "测试报告"
cleanup_files "debug_*.png" "调试截图"

# 7. 清理缓存目录
log_info "=== 清理缓存目录 ==="
cleanup_directories "cache" "缓存目录"
cleanup_directories ".cache" "隐藏缓存"

# 8. 清理压缩文件
log_info "=== 清理压缩文件 ==="
cleanup_files "*.tar.gz" "压缩包"
cleanup_files "*.zip" "ZIP文件"
cleanup_files "*.rar" "RAR文件"

# 9. 清理项目特定大文件
log_info "=== 清理项目特定文件 ==="
cleanup_files "*_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]*" "时间戳文件"
cleanup_files "comprehensive_*.py" "综合测试文件"
cleanup_files "final_*.py" "最终测试文件"
cleanup_files "*_report_*.json" "JSON报告"
cleanup_files "*_report_*.md" "Markdown报告"

# 计算清理后大小
if command -v du > /dev/null; then
    SIZE_AFTER=$(du -sh . 2>/dev/null | cut -f1 || echo "未知")
    echo "" >> "$REPORT_FILE"
    echo "清理后大小: $SIZE_AFTER" >> "$REPORT_FILE"
    log_info "清理后大小: $SIZE_AFTER"
fi

# 计算节省空间
echo "" >> "$REPORT_FILE"
echo "清理完成时间: $(date)" >> "$REPORT_FILE"

log_success "仓库清理完成！"
log_info "清理报告已保存到: $REPORT_FILE"

# 显示清理统计
echo ""
log_info "=== 清理统计 ==="
if [ "$DRY_RUN" = false ]; then
    log_success "清理模式: 实际删除"
else
    log_warning "清理模式: 干跑测试"
fi

if command -v du > /dev/null && [ "$DRY_RUN" = false ]; then
    log_info "清理前: $SIZE_BEFORE"
    log_info "清理后: $SIZE_AFTER"
    
    # 尝试计算节省的空间（简化版本）
    if command -v numfmt > /dev/null; then
        SIZE_BEFORE_BYTES=$(echo "$SIZE_BEFORE" | numfmt --from=iec 2>/dev/null || echo "0")
        SIZE_AFTER_BYTES=$(echo "$SIZE_AFTER" | numfmt --from=iec 2>/dev/null || echo "0")
        
        if [[ "$SIZE_BEFORE_BYTES" -gt "$SIZE_AFTER_BYTES" && "$SIZE_BEFORE_BYTES" -gt 0 ]]; then
            SAVED_BYTES=$((SIZE_BEFORE_BYTES - SIZE_AFTER_BYTES))
            SAVED_HUMAN=$(echo "$SAVED_BYTES" | numfmt --to=iec)
            SAVED_PERCENT=$(( (SAVED_BYTES * 100) / SIZE_BEFORE_BYTES ))
            log_success "节省空间: $SAVED_HUMAN ($SAVED_PERCENT%)"
        fi
    fi
fi

# 提供下一步建议
echo ""
log_info "=== 下一步建议 ==="
log_info "1. 检查清理报告: cat $REPORT_FILE"
log_info "2. 重新安装依赖: cd frontend && npm install"
log_info "3. 验证项目功能: npm run dev"
log_info "4. 提交清理结果: git add . && git commit -m 'chore: cleanup build artifacts and dependencies'"

if [ "$DRY_RUN" = true ]; then
    echo ""
    log_warning "这是干跑模式，没有实际删除文件"
    log_info "要实际执行清理，请运行: $0"
fi
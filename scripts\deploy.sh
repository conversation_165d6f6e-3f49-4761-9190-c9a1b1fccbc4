#!/bin/bash

# ===== 量化交易平台自动化部署脚本 =====
#
# 功能：
# - 自动化部署到不同环境
# - 数据库迁移和初始化
# - 服务健康检查
# - 回滚支持
# - 日志收集
#
# 使用方法：
# ./scripts/deploy.sh [环境] [选项]
#
# 环境：
# - dev: 开发环境
# - staging: 测试环境  
# - prod: 生产环境
#
# 选项：
# --build: 重新构建镜像
# --migrate: 执行数据库迁移
# --rollback: 回滚到上一版本
# --health-check: 仅执行健康检查
#
# 示例：
# ./scripts/deploy.sh prod --build --migrate
#
# 作者: 量化交易平台团队
# 更新时间: 2025-07-27
# 版本: 1.0.0

set -euo pipefail  # 严格模式：遇到错误立即退出

# ===== 全局变量 =====
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-dev}"
BUILD_FLAG=false
MIGRATE_FLAG=false
ROLLBACK_FLAG=false
HEALTH_CHECK_ONLY=false
BACKUP_FLAG=true
TIMEOUT=300

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ===== 工具函数 =====

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 信息消息
info() {
    print_message "$BLUE" "ℹ️  $1"
}

# 成功消息
success() {
    print_message "$GREEN" "✅ $1"
}

# 警告消息
warning() {
    print_message "$YELLOW" "⚠️  $1"
}

# 错误消息
error() {
    print_message "$RED" "❌ $1"
    exit 1
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "命令 '$1' 未找到，请先安装"
    fi
}

# 检查文件是否存在
check_file() {
    if [[ ! -f "$1" ]]; then
        error "文件 '$1' 不存在"
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1
    
    info "等待服务 $service_name 启动..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            success "服务 $service_name 启动成功"
            return 0
        fi
        
        info "尝试 $attempt/$max_attempts: 服务 $service_name 尚未就绪，等待5秒..."
        sleep 5
        ((attempt++))
    done
    
    error "服务 $service_name 启动超时"
}

# 执行数据库备份
backup_database() {
    if [[ "$BACKUP_FLAG" == true && "$ENVIRONMENT" == "prod" ]]; then
        info "执行数据库备份..."
        
        local backup_dir="$PROJECT_ROOT/backups"
        local backup_file="$backup_dir/backup_$(date +%Y%m%d_%H%M%S).sql"
        
        mkdir -p "$backup_dir"
        
        docker-compose exec -T postgres pg_dump \
            -U "${POSTGRES_USER:-quant_user}" \
            -d "${POSTGRES_DB:-quant_platform}" \
            > "$backup_file"
        
        gzip "$backup_file"
        success "数据库备份完成: ${backup_file}.gz"
        
        # 清理旧备份（保留最近7天）
        find "$backup_dir" -name "backup_*.sql.gz" -mtime +7 -delete
    fi
}

# 执行数据库迁移
migrate_database() {
    if [[ "$MIGRATE_FLAG" == true ]]; then
        info "执行数据库迁移..."
        
        docker-compose exec backend alembic upgrade head
        
        if [[ $? -eq 0 ]]; then
            success "数据库迁移完成"
        else
            error "数据库迁移失败"
        fi
    fi
}

# 健康检查
health_check() {
    info "执行健康检查..."
    
    # 检查后端服务
    wait_for_service "Backend API" "http://localhost:8000/health"
    
    # 检查前端服务
    wait_for_service "Frontend" "http://localhost:3000"
    
    # 检查数据库连接
    if docker-compose exec postgres pg_isready -U "${POSTGRES_USER:-quant_user}" > /dev/null 2>&1; then
        success "数据库连接正常"
    else
        error "数据库连接失败"
    fi
    
    # 检查Redis连接
    if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
        success "Redis连接正常"
    else
        error "Redis连接失败"
    fi
    
    success "所有服务健康检查通过"
}

# 回滚部署
rollback_deployment() {
    warning "开始回滚部署..."
    
    # 停止当前服务
    docker-compose down
    
    # 恢复上一个版本的镜像
    if [[ -f "$PROJECT_ROOT/.last_deploy_tag" ]]; then
        local last_tag=$(cat "$PROJECT_ROOT/.last_deploy_tag")
        info "回滚到版本: $last_tag"
        
        # 这里应该实现具体的回滚逻辑
        # 例如：切换到上一个Git标签，重新构建镜像等
        
        success "回滚完成"
    else
        error "未找到上一个部署版本信息"
    fi
}

# ===== 参数解析 =====
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build)
                BUILD_FLAG=true
                shift
                ;;
            --migrate)
                MIGRATE_FLAG=true
                shift
                ;;
            --rollback)
                ROLLBACK_FLAG=true
                shift
                ;;
            --health-check)
                HEALTH_CHECK_ONLY=true
                shift
                ;;
            --no-backup)
                BACKUP_FLAG=false
                shift
                ;;
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                if [[ -z "${ENVIRONMENT:-}" ]]; then
                    ENVIRONMENT="$1"
                fi
                shift
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
量化交易平台部署脚本

使用方法:
    $0 [环境] [选项]

环境:
    dev         开发环境 (默认)
    staging     测试环境
    prod        生产环境

选项:
    --build             重新构建Docker镜像
    --migrate           执行数据库迁移
    --rollback          回滚到上一版本
    --health-check      仅执行健康检查
    --no-backup         跳过数据库备份
    --timeout SECONDS   设置超时时间 (默认: 300秒)
    -h, --help          显示此帮助信息

示例:
    $0 dev --build --migrate
    $0 prod --migrate --no-backup
    $0 staging --health-check

EOF
}

# ===== 环境验证 =====
validate_environment() {
    info "验证部署环境: $ENVIRONMENT"
    
    # 检查必需的命令
    check_command "docker"
    check_command "docker-compose"
    check_command "curl"
    
    # 检查必需的文件
    check_file "$PROJECT_ROOT/docker-compose.yml"
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        check_file "$PROJECT_ROOT/docker-compose.prod.yml"
    fi
    
    # 检查环境变量文件
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    if [[ -f "$env_file" ]]; then
        info "使用环境配置文件: $env_file"
        export $(grep -v '^#' "$env_file" | xargs)
    else
        warning "环境配置文件 $env_file 不存在，使用默认配置"
    fi
    
    success "环境验证通过"
}

# ===== 主部署流程 =====
main_deploy() {
    info "开始部署到 $ENVIRONMENT 环境"
    
    cd "$PROJECT_ROOT"
    
    # 记录当前版本
    if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
        local current_commit=$(git rev-parse HEAD)
        echo "$current_commit" > .last_deploy_tag
        info "当前版本: $current_commit"
    fi
    
    # 构建镜像
    if [[ "$BUILD_FLAG" == true ]]; then
        info "构建Docker镜像..."
        
        if [[ "$ENVIRONMENT" == "prod" ]]; then
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache
        else
            docker-compose build --no-cache
        fi
        
        success "镜像构建完成"
    fi
    
    # 执行备份
    backup_database
    
    # 停止旧服务
    info "停止现有服务..."
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    # 启动新服务
    info "启动服务..."
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    # 等待服务启动
    sleep 10
    
    # 执行数据库迁移
    migrate_database
    
    # 健康检查
    health_check
    
    success "部署完成！"
    
    # 显示服务状态
    info "服务状态:"
    docker-compose ps
}

# ===== 主函数 =====
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 显示部署信息
    info "量化交易平台部署脚本 v1.0.0"
    info "目标环境: $ENVIRONMENT"
    info "项目根目录: $PROJECT_ROOT"
    
    # 处理特殊操作
    if [[ "$ROLLBACK_FLAG" == true ]]; then
        rollback_deployment
        exit 0
    fi
    
    if [[ "$HEALTH_CHECK_ONLY" == true ]]; then
        health_check
        exit 0
    fi
    
    # 验证环境
    validate_environment
    
    # 执行部署
    main_deploy
    
    success "🎉 部署成功完成！"
    info "前端地址: http://localhost:3000"
    info "后端API: http://localhost:8000"
    info "API文档: http://localhost:8000/docs"
}

# ===== 错误处理 =====
trap 'error "部署过程中发生错误，请检查日志"' ERR

# ===== 脚本入口 =====
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

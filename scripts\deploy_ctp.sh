#!/bin/bash

# CTP证券公司对接系统部署脚本
# 专门用于CTP交易系统的部署和管理

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "CTP证券公司对接系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动CTP交易系统"
    echo "  stop      停止CTP交易系统"
    echo "  restart   重启CTP交易系统"
    echo "  status    查看系统状态"
    echo "  logs      查看系统日志"
    echo "  test      测试CTP连接"
    echo "  backup    备份数据"
    echo "  help      显示帮助信息"
    echo ""
    echo "选项:"
    echo "  --env     指定环境 (dev|prod) 默认: dev"
    echo "  --build   重新构建镜像"
    echo "  --force   强制执行"
    echo ""
    echo "示例:"
    echo "  $0 start --env prod"
    echo "  $0 restart --build"
    echo "  $0 test"
    echo ""
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3未安装，某些功能可能不可用"
    fi
    
    log_success "环境检查通过"
}

# 启动系统
start_system() {
    log_info "启动CTP交易系统..."
    
    local build_flag=""
    if [ "$BUILD" = "true" ]; then
        build_flag="--build"
        log_info "将重新构建镜像..."
    fi
    
    # 启动服务
    if [ "$ENV" = "prod" ]; then
        log_info "启动生产环境..."
        if [ -f "docker-compose.production.yml" ]; then
            docker-compose -f docker-compose.production.yml up -d $build_flag
        else
            log_error "生产环境配置文件不存在"
            exit 1
        fi
    else
        log_info "启动开发环境..."
        docker-compose up -d $build_flag
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services
    
    log_success "CTP交易系统启动完成"
    show_access_info
}

# 停止系统
stop_system() {
    log_info "停止CTP交易系统..."
    
    if [ "$ENV" = "prod" ]; then
        docker-compose -f docker-compose.production.yml down
    else
        docker-compose down
    fi
    
    log_success "CTP交易系统已停止"
}

# 重启系统
restart_system() {
    log_info "重启CTP交易系统..."
    stop_system
    sleep 5
    start_system
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查后端API
    local backend_url="http://localhost:8001"
    if curl -f "$backend_url/health" > /dev/null 2>&1; then
        log_success "后端API服务正常"
    else
        log_warning "后端API服务异常"
    fi
    
    # 检查前端
    local frontend_url="http://localhost:5173"
    if curl -f "$frontend_url" > /dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_warning "前端服务异常"
    fi
    
    # 检查CTP状态
    if curl -f "$backend_url/api/v1/ctp/status" > /dev/null 2>&1; then
        log_success "CTP服务正常"
    else
        log_warning "CTP服务异常"
    fi
}

# 查看系统状态
show_status() {
    log_info "系统状态:"
    
    if [ "$ENV" = "prod" ]; then
        docker-compose -f docker-compose.production.yml ps
    else
        docker-compose ps
    fi
    
    echo ""
    check_services
}

# 查看日志
show_logs() {
    log_info "显示系统日志..."
    
    if [ "$ENV" = "prod" ]; then
        docker-compose -f docker-compose.production.yml logs -f --tail=100
    else
        docker-compose logs -f --tail=100
    fi
}

# 测试CTP连接
test_ctp() {
    log_info "测试CTP连接..."
    
    # 检查后端是否运行
    if ! curl -f "http://localhost:8001/health" > /dev/null 2>&1; then
        log_error "后端服务未运行，请先启动系统"
        exit 1
    fi
    
    # 测试CTP状态
    log_info "检查CTP状态..."
    local ctp_status=$(curl -s "http://localhost:8001/api/v1/ctp/status" | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['connected'])" 2>/dev/null || echo "false")
    
    if [ "$ctp_status" = "true" ]; then
        log_success "CTP已连接"
    else
        log_warning "CTP未连接，尝试初始化..."
        
        # 尝试初始化CTP
        local init_result=$(curl -s -X POST "http://localhost:8001/api/v1/ctp/initialize" | python3 -c "import sys, json; print(json.load(sys.stdin)['success'])" 2>/dev/null || echo "false")
        
        if [ "$init_result" = "true" ]; then
            log_success "CTP初始化成功"
        else
            log_error "CTP初始化失败，请检查配置"
        fi
    fi
    
    # 测试账户查询
    log_info "测试账户查询..."
    if curl -f "http://localhost:8001/api/v1/ctp/account" > /dev/null 2>&1; then
        log_success "账户查询正常"
    else
        log_warning "账户查询异常"
    fi
    
    # 运行Python测试脚本
    if [ -f "backend/tests/test_ctp_connection.py" ]; then
        log_info "运行CTP连接测试..."
        cd backend && python tests/test_ctp_connection.py
        cd ..
    fi
}

# 备份数据
backup_data() {
    log_info "备份系统数据..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份日志
    if [ -d "logs" ]; then
        cp -r logs "$backup_dir/"
        log_info "日志备份完成"
    fi
    
    # 备份数据
    if [ -d "data" ]; then
        cp -r data "$backup_dir/"
        log_info "数据备份完成"
    fi
    
    # 备份配置
    if [ -f ".env" ]; then
        cp .env "$backup_dir/"
    fi
    if [ -f ".env.production" ]; then
        cp .env.production "$backup_dir/"
    fi
    
    log_success "数据备份完成: $backup_dir"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 CTP证券公司对接系统已启动"
    echo ""
    echo "📊 访问地址:"
    echo "  - 交易终端: http://localhost:5173/trading/terminal"
    echo "  - 系统首页: http://localhost:5173"
    echo "  - API文档: http://localhost:8001/docs"
    echo "  - CTP状态: http://localhost:8001/api/v1/ctp/status"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看状态: $0 status"
    echo "  - 查看日志: $0 logs"
    echo "  - 测试CTP: $0 test"
    echo "  - 重启系统: $0 restart"
    echo ""
    echo "⚠️  重要提醒:"
    echo "  - 首次使用请先测试CTP连接"
    echo "  - 建议在仿真环境充分测试"
    echo "  - 定期备份重要数据"
    echo ""
}

# 解析命令行参数
parse_args() {
    ENV="dev"
    BUILD="false"
    FORCE="false"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --env)
                ENV="$2"
                shift 2
                ;;
            --build)
                BUILD="true"
                shift
                ;;
            --force)
                FORCE="true"
                shift
                ;;
            *)
                break
                ;;
        esac
    done
}

# 主函数
main() {
    # 解析参数
    local command="$1"
    shift
    parse_args "$@"
    
    # 检查环境
    check_environment
    
    # 执行命令
    case "$command" in
        start)
            start_system
            ;;
        stop)
            stop_system
            ;;
        restart)
            restart_system
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            test_ctp
            ;;
        backup)
            backup_data
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

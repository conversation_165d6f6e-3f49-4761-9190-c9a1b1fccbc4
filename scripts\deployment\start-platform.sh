#!/bin/bash

# 量化投资平台一键启动脚本
# 自动启动前端和后端服务

set -e

echo "🚀 量化投资平台启动脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 清理端口
cleanup_port() {
    local port=$1
    if check_port $port; then
        log_warning "端口 $port 已被占用，正在清理..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查 Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python3"
        exit 1
    fi
    
    # 检查 pip3
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装 pip3"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "检查前端依赖..."
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        pnpm install --frozen-lockfile
    else
        log_success "前端依赖已存在"
    fi
    
    cd ..
}

# 安装后端依赖
install_backend_deps() {
    log_info "检查后端依赖..."
    cd backend
    
    # 检查基础依赖
    if ! python3 -c "import fastapi" 2>/dev/null; then
        log_info "安装后端基础依赖..."
        python3 -m pip install fastapi uvicorn --break-system-packages 2>/dev/null || {
            log_warning "系统级安装失败，尝试用户级安装..."
            python3 -m pip install fastapi uvicorn --user
        }
    else
        log_success "后端依赖已存在"
    fi
    
    cd ..
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务 (端口: 8000)..."
    cd backend
    
    cleanup_port 8000
    
    # 启动简化版后端
    python3 -m uvicorn minimal_backend:app --host 0.0.0.0 --port 8000 --reload > ../backend.log 2>&1 &
    BACKEND_PID=$!
    
    # 等待后端启动
    sleep 3
    
    # 检查后端是否启动成功
    if check_port 8000; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > ../backend.pid
    else
        log_error "后端服务启动失败，请检查日志: backend.log"
        exit 1
    fi
    
    cd ..
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务 (端口: 5174)..."
    cd frontend
    
    cleanup_port 5174
    
    # 启动前端开发服务器
    pnpm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # 等待前端启动
    sleep 5
    
    # 检查前端是否启动成功
    if check_port 5174; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > ../frontend.pid
    else
        log_error "前端服务启动失败，请检查日志: frontend.log"
        exit 1
    fi
    
    cd ..
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端API
    if curl -s http://localhost:8000/api/v1/health > /dev/null; then
        log_success "后端API健康检查通过"
    else
        log_warning "后端API健康检查失败"
    fi
    
    # 检查前端服务
    if curl -s http://localhost:5174 > /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务健康检查失败"
    fi
}

# 显示服务信息
show_info() {
    echo ""
    echo "🎉 量化投资平台启动完成！"
    echo "================================"
    echo "前端服务: http://localhost:5174"
    echo "后端API:  http://localhost:8000"
    echo "API文档:  http://localhost:8000/docs"
    echo ""
    echo "默认登录信息:"
    echo "用户名: admin"
    echo "密码:   admin123"
    echo ""
    echo "日志文件:"
    echo "前端日志: frontend.log"
    echo "后端日志: backend.log"
    echo ""
    echo "停止服务: ./stop-platform.sh"
    echo "或使用 Ctrl+C 停止脚本"
    echo "================================"
}

# 停止服务函数
stop_services() {
    log_info "正在停止服务..."
    
    if [ -f backend.pid ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_success "后端服务已停止"
        fi
        rm -f backend.pid
    fi
    
    if [ -f frontend.pid ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_success "前端服务已停止"
        fi
        rm -f frontend.pid
    fi
    
    # 清理端口
    cleanup_port 8000
    cleanup_port 5174
    
    log_success "所有服务已停止"
    exit 0
}

# 信号处理
trap stop_services SIGINT SIGTERM

# 主执行流程
main() {
    # 检查是否在项目根目录
    if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 执行检查和启动
    check_dependencies
    install_frontend_deps
    install_backend_deps
    start_backend
    start_frontend
    health_check
    show_info
    
    # 保持脚本运行
    log_info "服务运行中，按 Ctrl+C 停止..."
    while true; do
        sleep 30
        
        # 检查服务是否还在运行
        if ! check_port 8000; then
            log_error "后端服务异常停止"
            break
        fi
        
        if ! check_port 5174; then
            log_error "前端服务异常停止"
            break
        fi
    done
    
    stop_services
}

# 执行主函数
main "$@"
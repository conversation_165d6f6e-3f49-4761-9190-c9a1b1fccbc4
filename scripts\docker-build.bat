@echo off
REM 量化投资平台 Docker 构建脚本 (Windows)
REM 演示正确的构建上下文和命令

setlocal enabledelayedexpansion

REM 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker 未安装或不在 PATH 中
    exit /b 1
)

echo %BLUE%[INFO]%NC% Docker 已安装

REM 获取参数
set "ACTION=%1"
if "%ACTION%"=="" set "ACTION=help"

REM 根据参数执行相应操作
if "%ACTION%"=="frontend-dev" goto :build_frontend_dev
if "%ACTION%"=="frontend-prod" goto :build_frontend_prod
if "%ACTION%"=="backend" goto :build_backend
if "%ACTION%"=="all" goto :build_all
if "%ACTION%"=="clean" goto :clean_images
if "%ACTION%"=="help" goto :show_help
goto :unknown_option

:build_frontend_dev
echo %BLUE%[INFO]%NC% 构建前端开发镜像...
echo %BLUE%[INFO]%NC% 构建上下文: frontend/
echo %BLUE%[INFO]%NC% Dockerfile: frontend/Dockerfile
docker build ^
    -f frontend/Dockerfile ^
    --target development ^
    --build-arg BUILD_ENV=development ^
    -t quant-platform-frontend:dev ^
    frontend/
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 前端开发镜像构建失败
    echo %YELLOW%[提示]%NC% 请检查 frontend/ 目录下的 package.json 和 pnpm-lock.yaml
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% 前端开发镜像构建完成
echo %GREEN%[INFO]%NC% 镜像标签: quant-platform-frontend:dev
goto :end

:build_frontend_prod
echo %BLUE%[INFO]%NC% 构建前端生产镜像...
echo %BLUE%[INFO]%NC% 构建上下文: frontend/
echo %BLUE%[INFO]%NC% Dockerfile: frontend/Dockerfile.prod
echo %BLUE%[INFO]%NC% 使用多阶段构建优化镜像体积...
docker build ^
    -f frontend/Dockerfile.prod ^
    --build-arg VITE_API_BASE_URL=/api/v1 ^
    --build-arg VITE_WS_URL=/ws ^
    --build-arg VITE_APP_TITLE=量化投资平台 ^
    --build-arg VITE_APP_VERSION=1.0.0 ^
    -t quant-platform-frontend:prod ^
    frontend/
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 前端生产镜像构建失败
    echo %YELLOW%[提示]%NC% 请检查 Node.js 版本和依赖安装
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% 前端生产镜像构建完成
echo %GREEN%[INFO]%NC% 镜像标签: quant-platform-frontend:prod
goto :end

:build_backend
echo %BLUE%[INFO]%NC% 构建后端镜像...
echo %BLUE%[INFO]%NC% 构建上下文: . (项目根目录)
echo %BLUE%[INFO]%NC% Dockerfile: backend/Dockerfile
echo %BLUE%[INFO]%NC% 包含 TA-Lib 和 Python 依赖安装...
docker build ^
    -f backend/Dockerfile ^
    --target development ^
    -t quant-platform-backend:dev ^
    .
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 后端镜像构建失败
    echo %YELLOW%[提示]%NC% 请检查 Python 版本和 requirements.txt
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% 后端镜像构建完成
echo %GREEN%[INFO]%NC% 镜像标签: quant-platform-backend:dev
goto :end

:build_all
echo %BLUE%[INFO]%NC% 开始构建所有镜像...
call :build_frontend_dev
call :build_frontend_prod
call :build_backend
echo %GREEN%[SUCCESS]%NC% 所有镜像构建完成
echo %BLUE%[INFO]%NC% 构建的镜像列表:
docker images | findstr quant-platform
goto :end

:clean_images
echo %YELLOW%[WARNING]%NC% 清理旧的镜像...
docker image prune -f
echo %GREEN%[SUCCESS]%NC% 镜像清理完成
goto :end

:show_help
echo 量化投资平台 Docker 构建脚本
echo.
echo 用法: %0 [选项]
echo.
echo 选项:
echo   frontend-dev    构建前端开发镜像
echo   frontend-prod   构建前端生产镜像
echo   backend         构建后端镜像
echo   all             构建所有镜像
echo   clean           清理镜像
echo   help            显示此帮助信息
echo.
echo 示例:
echo   %0 frontend-dev     # 构建前端开发镜像
echo   %0 all              # 构建所有镜像
echo   %0 clean            # 清理镜像
echo.
echo 重要说明:
echo   - 前端构建使用 frontend/ 作为上下文目录
echo   - 后端构建使用根目录作为上下文目录
echo   - 确保在项目根目录执行此脚本
goto :end

:unknown_option
echo %RED%[ERROR]%NC% 未知选项: %ACTION%
call :show_help
exit /b 1

:end
endlocal

#!/bin/bash

# 量化投资平台 Docker 构建脚本
# 演示正确的构建上下文和命令

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    log_info "Docker 版本: $(docker --version)"
}

# 构建前端开发镜像
build_frontend_dev() {
    log_info "构建前端开发镜像..."
    
    # 正确的构建命令：上下文为 frontend/ 目录
    docker build \
        -f frontend/Dockerfile \
        --target development \
        --build-arg BUILD_ENV=development \
        -t quant-platform-frontend:dev \
        frontend/
    
    log_success "前端开发镜像构建完成"
}

# 构建前端生产镜像
build_frontend_prod() {
    log_info "构建前端生产镜像..."
    
    # 使用专门的生产 Dockerfile
    docker build \
        -f frontend/Dockerfile.prod \
        --build-arg VITE_API_BASE_URL=/api/v1 \
        --build-arg VITE_WS_URL=/ws \
        --build-arg VITE_APP_TITLE="量化投资平台" \
        --build-arg VITE_APP_VERSION="1.0.0" \
        -t quant-platform-frontend:prod \
        frontend/
    
    log_success "前端生产镜像构建完成"
}

# 构建后端镜像
build_backend() {
    log_info "构建后端镜像..."
    
    # 后端构建（上下文为根目录，因为需要访问多个目录）
    docker build \
        -f backend/Dockerfile \
        -t quant-platform-backend:latest \
        .
    
    log_success "后端镜像构建完成"
}

# 构建所有镜像
build_all() {
    log_info "开始构建所有镜像..."
    
    build_frontend_dev
    build_frontend_prod
    build_backend
    
    log_success "所有镜像构建完成"
    
    # 显示构建的镜像
    log_info "构建的镜像列表："
    docker images | grep quant-platform
}

# 清理镜像
clean_images() {
    log_warning "清理旧的镜像..."
    
    # 删除 dangling 镜像
    docker image prune -f
    
    # 可选：删除项目相关镜像
    read -p "是否删除所有 quant-platform 镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker images | grep quant-platform | awk '{print $3}' | xargs -r docker rmi -f
        log_success "项目镜像已清理"
    fi
}

# 测试镜像
test_images() {
    log_info "测试构建的镜像..."
    
    # 测试前端开发镜像
    if docker images | grep -q "quant-platform-frontend.*dev"; then
        log_info "测试前端开发镜像..."
        docker run --rm -d --name test-frontend-dev -p 5173:5173 quant-platform-frontend:dev
        sleep 5
        if curl -f http://localhost:5173 > /dev/null 2>&1; then
            log_success "前端开发镜像测试通过"
        else
            log_warning "前端开发镜像测试失败"
        fi
        docker stop test-frontend-dev
    fi
    
    # 测试前端生产镜像
    if docker images | grep -q "quant-platform-frontend.*prod"; then
        log_info "测试前端生产镜像..."
        docker run --rm -d --name test-frontend-prod -p 8080:80 quant-platform-frontend:prod
        sleep 5
        if curl -f http://localhost:8080/health > /dev/null 2>&1; then
            log_success "前端生产镜像测试通过"
        else
            log_warning "前端生产镜像测试失败"
        fi
        docker stop test-frontend-prod
    fi
}

# 显示帮助信息
show_help() {
    echo "量化投资平台 Docker 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  frontend-dev    构建前端开发镜像"
    echo "  frontend-prod   构建前端生产镜像"
    echo "  backend         构建后端镜像"
    echo "  all             构建所有镜像"
    echo "  clean           清理镜像"
    echo "  test            测试镜像"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 frontend-dev     # 构建前端开发镜像"
    echo "  $0 all              # 构建所有镜像"
    echo "  $0 clean            # 清理镜像"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        frontend-dev)
            build_frontend_dev
            ;;
        frontend-prod)
            build_frontend_prod
            ;;
        backend)
            build_backend
            ;;
        all)
            build_all
            ;;
        clean)
            clean_images
            ;;
        test)
            test_images
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

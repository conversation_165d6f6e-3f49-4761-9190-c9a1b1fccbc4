#!/bin/bash

# Docker交互式演示和教程脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 等待用户输入
wait_for_user() {
    echo -e "${CYAN}按 Enter 继续...${NC}"
    read
}

# 显示标题
show_title() {
    clear
    echo -e "${BLUE}
╔══════════════════════════════════════════════╗
║           🚀 量化投资平台演示                  ║
║         Interactive Demo & Tutorial         ║
╚══════════════════════════════════════════════╝
${NC}"
}

# 欢迎界面
welcome() {
    show_title
    echo -e "${GREEN}欢迎使用量化投资平台！${NC}"
    echo ""
    echo "这个演示将帮助您："
    echo "  1. 🏗️  了解平台架构"
    echo "  2. 🐳 学习Docker部署"
    echo "  3. 🔧 掌握基本操作"
    echo "  4. 🚀 快速上手使用"
    echo ""
    wait_for_user
}

# 架构介绍
show_architecture() {
    show_title
    echo -e "${YELLOW}📐 平台架构介绍${NC}"
    echo "==============================================="
    echo ""
    echo -e "${BLUE}前端层:${NC}"
    echo "  • Vue3 + TypeScript + Vite"
    echo "  • Element Plus UI组件库"
    echo "  • ECharts专业图表库"
    echo "  • WebSocket实时通信"
    echo ""
    echo -e "${BLUE}后端层:${NC}"
    echo "  • FastAPI异步Web框架"
    echo "  • SQLAlchemy数据库ORM"
    echo "  • Celery异步任务队列"
    echo "  • Redis缓存系统"
    echo ""
    echo -e "${BLUE}数据层:${NC}"
    echo "  • PostgreSQL关系型数据库"
    echo "  • TimescaleDB时序数据"
    echo "  • Redis内存缓存"
    echo ""
    wait_for_user
}

# Docker介绍
show_docker_intro() {
    show_title
    echo -e "${YELLOW}🐳 Docker部署方案${NC}"
    echo "==============================================="
    echo ""
    echo -e "${BLUE}我们提供两种部署模式:${NC}"
    echo ""
    echo -e "${GREEN}1. 简单模式 (Simple Mode)${NC}"
    echo "  • 前端 + 后端"
    echo "  • 适合快速体验"
    echo "  • 端口: 3000(前端), 8000(后端)"
    echo ""
    echo -e "${GREEN}2. 完整模式 (Full Mode)${NC}"
    echo "  • 前端 + 后端 + 数据库 + 缓存 + 管理工具"
    echo "  • 适合开发和生产环境"
    echo "  • 包含PostgreSQL、Redis、pgAdmin、RedisInsight"
    echo ""
    wait_for_user
}

# 演示启动过程
demo_startup() {
    show_title
    echo -e "${YELLOW}🚀 演示启动过程${NC}"
    echo "==============================================="
    echo ""
    echo "让我们演示如何启动平台："
    echo ""
    echo -e "${CYAN}步骤 1: 检查Docker环境${NC}"
    if command -v docker &> /dev/null; then
        echo -e "  ✅ Docker: ${GREEN}已安装${NC}"
    else
        echo -e "  ❌ Docker: ${RED}未安装${NC}"
    fi
    
    if command -v docker-compose &> /dev/null; then
        echo -e "  ✅ Docker Compose: ${GREEN}已安装${NC}"
    else
        echo -e "  ❌ Docker Compose: ${RED}未安装${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}步骤 2: 查看可用脚本${NC}"
    echo "  • quick-docker.sh     - 一键快速启动"
    echo "  • docker-start.sh     - 完整启动脚本"
    echo "  • docker-health-check.sh - 健康检查"
    echo ""
    wait_for_user
}

# 演示命令使用
demo_commands() {
    show_title
    echo -e "${YELLOW}🔧 命令使用演示${NC}"
    echo "==============================================="
    echo ""
    echo -e "${BLUE}基本命令:${NC}"
    echo ""
    echo -e "${GREEN}# 一键快速启动${NC}"
    echo -e "${CYAN}./quick-docker.sh${NC}"
    echo ""
    echo -e "${GREEN}# 启动简单模式${NC}"
    echo -e "${CYAN}./docker-start.sh simple${NC}"
    echo ""
    echo -e "${GREEN}# 启动完整模式${NC}"
    echo -e "${CYAN}./docker-start.sh full${NC}"
    echo ""
    echo -e "${GREEN}# 停止服务${NC}"
    echo -e "${CYAN}./docker-start.sh stop full${NC}"
    echo ""
    echo -e "${GREEN}# 查看日志${NC}"
    echo -e "${CYAN}./docker-start.sh logs full${NC}"
    echo ""
    echo -e "${GREEN}# 健康检查${NC}"
    echo -e "${CYAN}./docker-health-check.sh${NC}"
    echo ""
    wait_for_user
}

# 功能特性演示
demo_features() {
    show_title
    echo -e "${YELLOW}✨ 平台功能特性${NC}"
    echo "==============================================="
    echo ""
    echo -e "${BLUE}核心功能:${NC}"
    echo "  📊 实时行情数据"
    echo "  📈 专业金融图表"
    echo "  💹 智能交易终端"
    echo "  📉 策略回测分析"
    echo "  ⚠️  风险管理系统"
    echo "  📱 全端响应式设计"
    echo ""
    echo -e "${BLUE}技术特色:${NC}"
    echo "  ⚡ 毫秒级实时数据"
    echo "  🔒 金融级安全防护"
    echo "  🚀 高性能异步处理"
    echo "  📊 专业监控运维"
    echo ""
    wait_for_user
}

# 访问地址说明
show_access_info() {
    show_title
    echo -e "${YELLOW}🌐 服务访问地址${NC}"
    echo "==============================================="
    echo ""
    echo -e "${GREEN}启动成功后，您可以访问:${NC}"
    echo ""
    echo -e "${BLUE}📊 前端应用:${NC}"
    echo -e "   ${CYAN}http://localhost:3000${NC}"
    echo -e "   现代化的Vue3交易界面"
    echo ""
    echo -e "${BLUE}🔗 后端API:${NC}"
    echo -e "   ${CYAN}http://localhost:8000${NC}"
    echo -e "   FastAPI高性能接口"
    echo ""
    echo -e "${BLUE}📚 API文档:${NC}"
    echo -e "   ${CYAN}http://localhost:8000/docs${NC}"
    echo -e "   Swagger交互式文档"
    echo ""
    echo -e "${BLUE}🗄️  数据库管理:${NC}"
    echo -e "   ${CYAN}http://localhost:5050${NC}"
    echo -e "   pgAdmin数据库管理界面"
    echo ""
    echo -e "${BLUE}📈 Redis管理:${NC}"
    echo -e "   ${CYAN}http://localhost:8081${NC}"
    echo -e "   RedisInsight缓存管理"
    echo ""
    wait_for_user
}

# 故障排除
show_troubleshooting() {
    show_title
    echo -e "${YELLOW}🔧 故障排除指南${NC}"
    echo "==============================================="
    echo ""
    echo -e "${RED}常见问题及解决方案:${NC}"
    echo ""
    echo -e "${BLUE}问题 1: 端口被占用${NC}"
    echo "  解决: 检查并停止占用端口的进程"
    echo -e "  命令: ${CYAN}lsof -i :3000${NC} 或 ${CYAN}lsof -i :8000${NC}"
    echo ""
    echo -e "${BLUE}问题 2: 容器启动失败${NC}"
    echo "  解决: 查看详细日志"
    echo -e "  命令: ${CYAN}./docker-start.sh logs full${NC}"
    echo ""
    echo -e "${BLUE}问题 3: 服务无响应${NC}"
    echo "  解决: 重启服务"
    echo -e "  命令: ${CYAN}./docker-start.sh stop full && ./docker-start.sh full${NC}"
    echo ""
    echo -e "${BLUE}问题 4: 数据库连接失败${NC}"
    echo "  解决: 检查数据库容器状态"
    echo -e "  命令: ${CYAN}docker-compose ps${NC}"
    echo ""
    wait_for_user
}

# 最佳实践
show_best_practices() {
    show_title
    echo -e "${YELLOW}💡 最佳实践建议${NC}"
    echo "==============================================="
    echo ""
    echo -e "${GREEN}开发环境:${NC}"
    echo "  • 使用简单模式快速开发调试"
    echo "  • 定期运行健康检查"
    echo "  • 及时查看和清理日志"
    echo ""
    echo -e "${GREEN}生产环境:${NC}"
    echo "  • 使用完整模式部署"
    echo "  • 配置环境变量和密钥"
    echo "  • 设置监控和告警"
    echo "  • 定期备份数据"
    echo ""
    echo -e "${GREEN}安全建议:${NC}"
    echo "  • 修改默认密码"
    echo "  • 使用HTTPS/WSS"
    echo "  • 限制网络访问"
    echo "  • 定期更新镜像"
    echo ""
    wait_for_user
}

# 结束演示
finish_demo() {
    show_title
    echo -e "${GREEN}🎉 演示完成！${NC}"
    echo "==============================================="
    echo ""
    echo "现在您已经了解了："
    echo "  ✅ 平台架构和功能"
    echo "  ✅ Docker部署方案"
    echo "  ✅ 基本操作命令"
    echo "  ✅ 故障排除方法"
    echo ""
    echo -e "${BLUE}下一步建议:${NC}"
    echo "  1. 运行 ./quick-docker.sh 快速体验"
    echo "  2. 访问 http://localhost:3000 查看界面"
    echo "  3. 查看 http://localhost:8000/docs API文档"
    echo "  4. 探索平台各项功能"
    echo ""
    echo -e "${YELLOW}需要帮助?${NC}"
    echo "  • 查看README.md详细文档"
    echo "  • 运行 ./docker-health-check.sh 检查状态"
    echo "  • 联系技术支持团队"
    echo ""
    echo -e "${CYAN}感谢使用量化投资平台！${NC}"
    echo ""
}

# 交互式选择
interactive_menu() {
    while true; do
        show_title
        echo -e "${YELLOW}📋 演示菜单${NC}"
        echo "==============================================="
        echo ""
        echo "请选择您想了解的内容："
        echo ""
        echo "  1. 📐 平台架构介绍"
        echo "  2. 🐳 Docker部署方案"
        echo "  3. 🚀 启动过程演示"
        echo "  4. 🔧 命令使用演示"
        echo "  5. ✨ 功能特性展示"
        echo "  6. 🌐 访问地址说明"
        echo "  7. 🔧 故障排除指南"
        echo "  8. 💡 最佳实践建议"
        echo "  9. 🎯 完整演示流程"
        echo "  0. 🚪 退出演示"
        echo ""
        echo -n -e "${CYAN}请输入选项 (0-9): ${NC}"
        read choice
        
        case $choice in
            1) show_architecture ;;
            2) show_docker_intro ;;
            3) demo_startup ;;
            4) demo_commands ;;
            5) demo_features ;;
            6) show_access_info ;;
            7) show_troubleshooting ;;
            8) show_best_practices ;;
            9) full_demo ;;
            0) finish_demo; exit 0 ;;
            *) echo -e "${RED}无效选项，请重新选择${NC}"; sleep 2 ;;
        esac
    done
}

# 完整演示流程
full_demo() {
    welcome
    show_architecture
    show_docker_intro
    demo_startup
    demo_commands
    demo_features
    show_access_info
    show_troubleshooting
    show_best_practices
    finish_demo
}

# 主函数
main() {
    if [ "$1" = "--full" ]; then
        full_demo
    else
        interactive_menu
    fi
}

# 执行主函数
main "$@"
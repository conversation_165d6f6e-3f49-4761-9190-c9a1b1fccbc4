#!/bin/bash

# Docker健康检查和监控脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 量化投资平台健康检查${NC}"
echo "==============================================="

# 检查Docker服务状态
check_docker_services() {
    echo -e "${YELLOW}📊 检查Docker服务状态...${NC}"
    cd docker
    
    if ! docker-compose ps &> /dev/null; then
        echo -e "${RED}❌ Docker Compose服务未运行${NC}"
        cd ..
        return 1
    fi
    
    # 获取服务状态
    local services=$(docker-compose ps --services)
    local running_count=0
    local total_count=0
    
    for service in $services; do
        total_count=$((total_count + 1))
        local status=$(docker-compose ps -q $service | xargs docker inspect --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
        
        if [ "$status" = "running" ]; then
            echo -e "  ✅ $service: ${GREEN}运行中${NC}"
            running_count=$((running_count + 1))
        elif [ "$status" = "not_found" ]; then
            echo -e "  ❌ $service: ${RED}未找到${NC}"
        else
            echo -e "  ⚠️  $service: ${YELLOW}$status${NC}"
        fi
    done
    
    echo -e "📈 服务状态: ${running_count}/${total_count} 运行中"
    cd ..
}

# 检查服务端点
check_endpoints() {
    echo -e "${YELLOW}🌐 检查服务端点...${NC}"
    
    # 检查前端
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo -e "  ✅ 前端应用: ${GREEN}可访问${NC} (http://localhost:3000)"
    else
        echo -e "  ❌ 前端应用: ${RED}不可访问${NC}"
    fi
    
    # 检查后端API
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "  ✅ 后端API: ${GREEN}可访问${NC} (http://localhost:8000)"
    else
        echo -e "  ❌ 后端API: ${RED}不可访问${NC}"
    fi
    
    # 检查API文档
    if curl -s http://localhost:8000/docs > /dev/null 2>&1; then
        echo -e "  ✅ API文档: ${GREEN}可访问${NC} (http://localhost:8000/docs)"
    else
        echo -e "  ❌ API文档: ${RED}不可访问${NC}"
    fi
    
    # 检查数据库管理
    if curl -s http://localhost:5050 > /dev/null 2>&1; then
        echo -e "  ✅ 数据库管理: ${GREEN}可访问${NC} (http://localhost:5050)"
    else
        echo -e "  ⚠️  数据库管理: ${YELLOW}不可访问${NC} (可能未启动完整模式)"
    fi
    
    # 检查Redis管理
    if curl -s http://localhost:8081 > /dev/null 2>&1; then
        echo -e "  ✅ Redis管理: ${GREEN}可访问${NC} (http://localhost:8081)"
    else
        echo -e "  ⚠️  Redis管理: ${YELLOW}不可访问${NC} (可能未启动完整模式)"
    fi
}

# 检查资源使用情况
check_resources() {
    echo -e "${YELLOW}💻 检查资源使用情况...${NC}"
    
    if command -v docker &> /dev/null; then
        echo "Docker容器资源使用:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null | head -20
    fi
}

# 检查日志错误
check_logs() {
    echo -e "${YELLOW}📋 检查最近的错误日志...${NC}"
    
    cd docker
    
    # 检查后端错误
    local backend_errors=$(docker-compose logs backend 2>/dev/null | grep -i "error\|exception\|traceback" | tail -5)
    if [ -n "$backend_errors" ]; then
        echo -e "${RED}⚠️  后端错误日志:${NC}"
        echo "$backend_errors"
    else
        echo -e "  ✅ 后端: ${GREEN}无明显错误${NC}"
    fi
    
    # 检查前端错误
    local frontend_errors=$(docker-compose logs frontend 2>/dev/null | grep -i "error\|failed" | tail -5)
    if [ -n "$frontend_errors" ]; then
        echo -e "${RED}⚠️  前端错误日志:${NC}"
        echo "$frontend_errors"
    else
        echo -e "  ✅ 前端: ${GREEN}无明显错误${NC}"
    fi
    
    cd ..
}

# 生成健康报告
generate_report() {
    echo -e "${BLUE}📄 生成健康检查报告...${NC}"
    
    local report_file="health-check-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "量化投资平台健康检查报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "Docker服务状态:"
        cd docker && docker-compose ps 2>/dev/null && cd ..
        echo ""
        
        echo "系统资源使用:"
        docker stats --no-stream 2>/dev/null || echo "无法获取资源信息"
        echo ""
        
        echo "磁盘使用情况:"
        df -h . 2>/dev/null || echo "无法获取磁盘信息"
        echo ""
        
    } > "$report_file"
    
    echo -e "✅ 报告已保存到: ${GREEN}$report_file${NC}"
}

# 主函数
main() {
    check_docker_services
    echo ""
    
    check_endpoints
    echo ""
    
    check_resources
    echo ""
    
    check_logs
    echo ""
    
    generate_report
    echo ""
    
    echo -e "${GREEN}🎉 健康检查完成！${NC}"
    echo "==============================================="
    echo -e "💡 如果发现问题，可以尝试:"
    echo -e "  - 重启服务: ${YELLOW}./docker-start.sh stop && ./docker-start.sh full${NC}"
    echo -e "  - 查看详细日志: ${YELLOW}./docker-start.sh logs${NC}"
    echo -e "  - 清理重建: ${YELLOW}./docker-start.sh cleanup && ./docker-start.sh full${NC}"
}

# 执行主函数
main "$@"
#!/bin/bash

# 环境切换脚本
# 用于在不同环境之间切换 Docker Compose 配置

set -e

# 设置脚本执行权限
chmod +x "$0" 2>/dev/null || true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
环境切换脚本 - 量化投资平台

用法: $0 [环境] [操作] [选项]

环境:
  local       本地开发环境
  staging     测试环境
  production  生产环境

操作:
  up          启动服务 (默认)
  down        停止服务
  restart     重启服务
  logs        查看日志
  ps          查看服务状态
  build       构建镜像
  pull        拉取镜像

选项:
  -d, --detach    后台运行
  -f, --force     强制操作
  --build         启动时重新构建
  --no-cache      构建时不使用缓存
  -h, --help      显示帮助信息

示例:
  $0 local up -d              # 后台启动本地开发环境
  $0 production down          # 停止生产环境
  $0 staging restart          # 重启测试环境
  $0 local logs backend       # 查看本地环境后端日志

EOF
}

# 验证环境
validate_environment() {
    local env=$1
    case $env in
        local|staging|production)
            return 0
            ;;
        *)
            log_error "未知环境: $env"
            log_info "支持的环境: local, staging, production"
            return 1
            ;;
    esac
}

# 获取配置路径
get_config_path() {
    local env=$1
    echo "docker/compose/$env/docker-compose.yml"
}

# 获取环境变量文件
get_env_file() {
    local env=$1
    case $env in
        local)
            echo "docker/compose/local/.env"
            ;;
        staging)
            echo "docker/compose/staging/.env.staging"
            ;;
        production)
            echo "docker/compose/production/.env.prod"
            ;;
    esac
}

# 检查配置文件
check_config_files() {
    local env=$1
    local config_file=$(get_config_path $env)
    local env_file=$(get_env_file $env)
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境变量文件不存在: $env_file"
        log_info "请复制对应的示例文件并配置:"
        case $env in
            local)
                log_info "cp docker/compose/local/.env.local docker/compose/local/.env"
                ;;
            staging)
                log_info "cp docker/compose/staging/.env.staging.example docker/compose/staging/.env.staging"
                ;;
            production)
                log_info "cp docker/compose/production/.env.prod.example docker/compose/production/.env.prod"
                ;;
        esac
        return 1
    fi
    
    return 0
}

# 构建 Docker Compose 命令
build_compose_command() {
    local env=$1
    local config_file=$(get_config_path $env)
    local env_file=$(get_env_file $env)
    
    echo "docker compose -f $config_file --env-file $env_file"
}

# 启动服务
start_services() {
    local env=$1
    local detach=$2
    local build=$3
    local compose_cmd=$(build_compose_command $env)
    
    log_info "启动 $env 环境服务..."
    
    local cmd="$compose_cmd up"
    
    if [[ "$detach" == "true" ]]; then
        cmd="$cmd -d"
    fi
    
    if [[ "$build" == "true" ]]; then
        cmd="$cmd --build"
    fi
    
    log_info "执行命令: $cmd"
    eval $cmd
    
    if [[ $? -eq 0 ]]; then
        log_success "$env 环境启动成功"
        
        # 显示访问信息
        case $env in
            local)
                log_info "前端访问地址: http://localhost:5173"
                log_info "后端 API: http://localhost:8000"
                log_info "API 文档: http://localhost:8000/docs"
                ;;
            staging)
                log_info "测试环境访问地址: https://staging.yourdomain.com"
                ;;
            production)
                log_info "生产环境访问地址: https://yourdomain.com"
                ;;
        esac
    else
        log_error "$env 环境启动失败"
        return 1
    fi
}

# 停止服务
stop_services() {
    local env=$1
    local compose_cmd=$(build_compose_command $env)
    
    log_info "停止 $env 环境服务..."
    
    eval "$compose_cmd down"
    
    if [[ $? -eq 0 ]]; then
        log_success "$env 环境停止成功"
    else
        log_error "$env 环境停止失败"
        return 1
    fi
}

# 重启服务
restart_services() {
    local env=$1
    
    log_info "重启 $env 环境服务..."
    
    stop_services $env
    start_services $env true false
}

# 查看日志
show_logs() {
    local env=$1
    local service=$2
    local compose_cmd=$(build_compose_command $env)
    
    log_info "查看 $env 环境日志..."
    
    if [[ -n "$service" ]]; then
        eval "$compose_cmd logs -f $service"
    else
        eval "$compose_cmd logs -f"
    fi
}

# 查看服务状态
show_status() {
    local env=$1
    local compose_cmd=$(build_compose_command $env)
    
    log_info "$env 环境服务状态:"
    eval "$compose_cmd ps"
}

# 构建镜像
build_images() {
    local env=$1
    local no_cache=$2
    local compose_cmd=$(build_compose_command $env)
    
    log_info "构建 $env 环境镜像..."
    
    local cmd="$compose_cmd build"
    
    if [[ "$no_cache" == "true" ]]; then
        cmd="$cmd --no-cache"
    fi
    
    eval $cmd
    
    if [[ $? -eq 0 ]]; then
        log_success "$env 环境镜像构建成功"
    else
        log_error "$env 环境镜像构建失败"
        return 1
    fi
}

# 拉取镜像
pull_images() {
    local env=$1
    local compose_cmd=$(build_compose_command $env)
    
    log_info "拉取 $env 环境镜像..."
    
    eval "$compose_cmd pull"
    
    if [[ $? -eq 0 ]]; then
        log_success "$env 环境镜像拉取成功"
    else
        log_error "$env 环境镜像拉取失败"
        return 1
    fi
}

# 主函数
main() {
    local env=${1:-local}
    local action=${2:-up}
    local detach=false
    local force=false
    local build=false
    local no_cache=false
    local service=""
    
    # 解析参数
    shift 2 2>/dev/null || shift $# 2>/dev/null
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--detach)
                detach=true
                shift
                ;;
            -f|--force)
                force=true
                shift
                ;;
            --build)
                build=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                service=$1
                shift
                ;;
        esac
    done
    
    # 验证环境
    if ! validate_environment $env; then
        exit 1
    fi
    
    # 检查配置文件
    if ! check_config_files $env; then
        exit 1
    fi
    
    # 执行操作
    case $action in
        up|start)
            start_services $env $detach $build
            ;;
        down|stop)
            stop_services $env
            ;;
        restart)
            restart_services $env
            ;;
        logs)
            show_logs $env $service
            ;;
        ps|status)
            show_status $env
            ;;
        build)
            build_images $env $no_cache
            ;;
        pull)
            pull_images $env
            ;;
        *)
            log_error "未知操作: $action"
            log_info "支持的操作: up, down, restart, logs, ps, build, pull"
            exit 1
            ;;
    esac
}

# 检查是否显示帮助
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 执行主函数
main "$@"

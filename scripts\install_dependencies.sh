#!/bin/bash

# 安装市场数据服务依赖脚本

set -e  # 遇到错误立即退出

echo "开始安装市场数据服务依赖..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "项目目录: $PROJECT_DIR"

# 检查Python版本
echo "检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "Python版本检查通过: $python_version"

# 创建虚拟环境（如果不存在）
VENV_DIR="$PROJECT_DIR/venv"
if [ ! -d "$VENV_DIR" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv "$VENV_DIR"
    echo "虚拟环境创建完成: $VENV_DIR"
else
    echo "虚拟环境已存在: $VENV_DIR"
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source "$VENV_DIR/bin/activate"

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装基础依赖
echo "安装基础依赖..."
pip install wheel setuptools

# 安装数据处理依赖
echo "安装数据处理依赖..."
pip install pandas numpy

# 安装数据源依赖
echo "安装数据源依赖..."

# 安装Tushare
echo "安装Tushare..."
pip install tushare

# 安装AKShare
echo "安装AKShare..."
pip install akshare

# 安装异步HTTP客户端
echo "安装异步HTTP依赖..."
pip install aiohttp aiofiles

# 安装定时任务依赖
echo "安装定时任务依赖..."
pip install apscheduler

# 安装时区依赖
echo "安装时区依赖..."
pip install pytz

# 安装配置管理依赖
echo "安装配置管理依赖..."
pip install pydantic[dotenv]

# 安装日志依赖
echo "安装日志依赖..."
pip install loguru

# 安装数据库依赖（如果需要）
echo "安装数据库依赖..."
pip install sqlalchemy asyncpg psycopg2-binary

# 安装FastAPI相关依赖
echo "安装FastAPI依赖..."
pip install fastapi uvicorn

# 安装测试依赖
echo "安装测试依赖..."
pip install pytest pytest-asyncio pytest-cov

# 创建requirements.txt
echo "生成requirements.txt..."
pip freeze > "$PROJECT_DIR/requirements_market.txt"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p "$PROJECT_DIR/data/cache/tushare"
mkdir -p "$PROJECT_DIR/data/cache/akshare"
mkdir -p "$PROJECT_DIR/data/processed"
mkdir -p "$PROJECT_DIR/data/analysis"
mkdir -p "$PROJECT_DIR/logs/crawler"
mkdir -p "$PROJECT_DIR/logs/scheduler"
mkdir -p "$PROJECT_DIR/logs/cron"

# 设置目录权限
echo "设置目录权限..."
chmod -R 755 "$PROJECT_DIR/data"
chmod -R 755 "$PROJECT_DIR/logs"
chmod +x "$PROJECT_DIR/scripts"/*.py 2>/dev/null || true
chmod +x "$PROJECT_DIR/scripts"/*.sh 2>/dev/null || true

# 创建环境配置文件模板
ENV_FILE="$PROJECT_DIR/.env.market"
if [ ! -f "$ENV_FILE" ]; then
    echo "创建环境配置文件模板..."
    cat > "$ENV_FILE" << 'EOF'
# 市场数据服务配置

# Tushare配置
MARKET_TUSHARE_TOKEN=f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400
MARKET_TUSHARE_BASE_URL=http://api.tushare.pro
MARKET_TUSHARE_MAX_CALLS_PER_MINUTE=200

# 数据源配置
MARKET_USE_REAL_DATA=true
MARKET_PRIMARY_DATA_SOURCE=tushare
MARKET_FALLBACK_DATA_SOURCE=akshare

# 缓存配置
MARKET_CACHE_ENABLED=true
MARKET_CACHE_TTL_REALTIME=30
MARKET_CACHE_TTL_DAILY=300
MARKET_CACHE_TTL_BASIC=3600

# 数据抓取配置
MARKET_CRAWL_ENABLED=true
MARKET_CRAWL_BATCH_SIZE=50
MARKET_CRAWL_DELAY_SECONDS=2
MARKET_CRAWL_HISTORY_DAYS=300

# 定时任务配置
MARKET_SCHEDULER_ENABLED=true
MARKET_DAILY_CRAWL_HOUR=18
MARKET_DAILY_CRAWL_MINUTE=0

# 监控配置
MARKET_MONITORING_ENABLED=true
MARKET_HEALTH_CHECK_INTERVAL=60
EOF
    echo "环境配置文件已创建: $ENV_FILE"
else
    echo "环境配置文件已存在: $ENV_FILE"
fi

# 创建启动脚本
START_SCRIPT="$PROJECT_DIR/start_market_services.sh"
cat > "$START_SCRIPT" << EOF
#!/bin/bash

# 市场数据服务启动脚本

cd "$PROJECT_DIR"

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

# 设置环境变量
export PYTHONPATH="$PROJECT_DIR:\$PYTHONPATH"

# 加载环境配置
if [ -f ".env.market" ]; then
    export \$(cat .env.market | grep -v '^#' | xargs)
fi

# 启动服务
echo "启动市场数据服务..."
python scripts/start_market_services.py
EOF

chmod +x "$START_SCRIPT"
echo "启动脚本已创建: $START_SCRIPT"

# 测试安装
echo "测试安装..."
python3 -c "
import pandas as pd
import numpy as np
import aiohttp
import asyncio
print('✓ 基础依赖测试通过')

try:
    import tushare as ts
    print('✓ Tushare安装成功')
except ImportError as e:
    print(f'✗ Tushare安装失败: {e}')

try:
    import akshare as ak
    print('✓ AKShare安装成功')
except ImportError as e:
    print(f'✗ AKShare安装失败: {e}')

try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    print('✓ APScheduler安装成功')
except ImportError as e:
    print(f'✗ APScheduler安装失败: {e}')

print('依赖安装测试完成')
"

echo ""
echo "=========================================="
echo "市场数据服务依赖安装完成！"
echo "=========================================="
echo ""
echo "安装内容："
echo "- Python虚拟环境: $VENV_DIR"
echo "- 数据目录: $PROJECT_DIR/data"
echo "- 日志目录: $PROJECT_DIR/logs"
echo "- 环境配置: $PROJECT_DIR/.env.market"
echo "- 启动脚本: $START_SCRIPT"
echo ""
echo "下一步操作："
echo "1. 检查并修改环境配置文件: .env.market"
echo "2. 设置Cron定时任务: bash scripts/setup_cron.sh"
echo "3. 启动服务: bash start_market_services.sh"
echo "4. 或手动启动: python scripts/start_market_services.py"
echo ""
echo "测试数据抓取："
echo "python -c \"
import asyncio
import sys
sys.path.append('$PROJECT_DIR')
from backend.app.services.data_crawler_service import run_daily_data_crawl
asyncio.run(run_daily_data_crawl(days=5))
\""
echo ""
echo "查看服务状态："
echo "python -c \"
import asyncio
import sys
sys.path.append('$PROJECT_DIR')
from backend.app.services.scheduler_service import get_scheduler_status
print(asyncio.run(get_scheduler_status()))
\""
echo ""

deactivate 2>/dev/null || true
echo "安装脚本执行完成！"

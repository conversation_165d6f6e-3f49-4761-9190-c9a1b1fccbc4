#!/usr/bin/env python3
"""
Docker 配置迁移脚本
将旧的 docker-compose 和 nginx 配置迁移到新的标准化结构
"""

import os
import shutil
import sys
from pathlib import Path
from typing import List, Dict

class DockerConfigMigrator:
    """Docker 配置迁移器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / "archive" / "old_configs"
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def migrate_all(self) -> bool:
        """执行完整迁移"""
        print("🚀 开始 Docker 配置迁移...")
        print("=" * 50)
        
        success = True
        
        # 创建备份目录
        success &= self.create_backup_directory()
        
        # 备份旧配置
        success &= self.backup_old_configs()
        
        # 清理冗余文件
        success &= self.cleanup_redundant_files()
        
        # 验证新配置
        success &= self.validate_new_structure()
        
        # 输出结果
        self.print_results()
        
        return success
    
    def create_backup_directory(self) -> bool:
        """创建备份目录"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            self.info.append(f"✅ 创建备份目录: {self.backup_dir}")
            return True
        except Exception as e:
            self.errors.append(f"❌ 创建备份目录失败: {e}")
            return False
    
    def backup_old_configs(self) -> bool:
        """备份旧配置文件"""
        print("🔍 备份旧配置文件...")
        
        # 需要备份的文件
        files_to_backup = [
            "docker-compose.yml",
            "docker-compose.prod.yml", 
            "docker-compose.override.yml",
            "nginx/nginx.conf",
            "nginx/nginx.simple.conf",
            "frontend/nginx.conf",
            "frontend/nginx.simple.conf",
            "deployment/docker-compose.yml"
        ]
        
        backed_up_files = []
        
        for file_path in files_to_backup:
            source = self.project_root / file_path
            if source.exists():
                try:
                    # 创建目标目录
                    target = self.backup_dir / file_path
                    target.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(source, target)
                    backed_up_files.append(file_path)
                    self.info.append(f"✅ 备份文件: {file_path}")
                    
                except Exception as e:
                    self.warnings.append(f"⚠️ 备份文件失败 {file_path}: {e}")
        
        if backed_up_files:
            self.info.append(f"✅ 成功备份 {len(backed_up_files)} 个文件")
        else:
            self.warnings.append("⚠️ 没有找到需要备份的旧配置文件")
        
        return True
    
    def cleanup_redundant_files(self) -> bool:
        """清理冗余文件"""
        print("🧹 清理冗余配置文件...")
        
        # 需要删除的文件
        files_to_remove = [
            "docker-compose.yml",
            "docker-compose.prod.yml",
            "docker-compose.override.yml",
            "frontend/nginx.conf",
            "frontend/nginx.simple.conf"
        ]
        
        # 需要删除的目录
        dirs_to_remove = [
            "nginx",
            "deployment"
        ]
        
        removed_files = []
        removed_dirs = []
        
        # 删除文件
        for file_path in files_to_remove:
            file_full_path = self.project_root / file_path
            if file_full_path.exists():
                try:
                    file_full_path.unlink()
                    removed_files.append(file_path)
                    self.info.append(f"✅ 删除冗余文件: {file_path}")
                except Exception as e:
                    self.warnings.append(f"⚠️ 删除文件失败 {file_path}: {e}")
        
        # 删除目录
        for dir_path in dirs_to_remove:
            dir_full_path = self.project_root / dir_path
            if dir_full_path.exists() and dir_full_path.is_dir():
                try:
                    shutil.rmtree(dir_full_path)
                    removed_dirs.append(dir_path)
                    self.info.append(f"✅ 删除冗余目录: {dir_path}")
                except Exception as e:
                    self.warnings.append(f"⚠️ 删除目录失败 {dir_path}: {e}")
        
        if removed_files or removed_dirs:
            self.info.append(f"✅ 清理完成: {len(removed_files)} 个文件, {len(removed_dirs)} 个目录")
        else:
            self.info.append("✅ 没有需要清理的冗余文件")
        
        return True
    
    def validate_new_structure(self) -> bool:
        """验证新配置结构"""
        print("🔍 验证新配置结构...")
        
        # 检查必需的目录
        required_dirs = [
            "docker/compose/local",
            "docker/compose/staging",
            "docker/compose/production",
            "docker/nginx/templates",
            "docker/nginx/local",
            "docker/nginx/staging",
            "docker/nginx/production"
        ]
        
        # 检查必需的文件
        required_files = [
            "docker/compose/local/docker-compose.yml",
            "docker/compose/staging/docker-compose.yml",
            "docker/compose/production/docker-compose.yml",
            "docker/nginx/templates/frontend.conf.j2",
            "docker/nginx/local/default.conf",
            "docker/nginx/staging/default.conf",
            "docker/nginx/production/default.conf"
        ]
        
        missing_dirs = []
        missing_files = []
        
        # 检查目录
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
        
        # 检查文件
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_dirs:
            self.errors.append(f"❌ 缺失目录: {missing_dirs}")
            return False
        
        if missing_files:
            self.errors.append(f"❌ 缺失文件: {missing_files}")
            return False
        
        self.info.append("✅ 新配置结构验证通过")
        return True
    
    def print_results(self):
        """输出迁移结果"""
        print("\n" + "=" * 50)
        print("📊 迁移结果:")
        
        if self.info:
            print(f"\n✅ 成功信息 ({len(self.info)}):")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print(f"\n⚠️ 警告信息 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print(f"\n❌ 错误信息 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  {error}")
        
        # 总结
        total_issues = len(self.warnings) + len(self.errors)
        if total_issues == 0:
            print(f"\n🎉 配置迁移完全成功！")
            print("\n📋 下一步操作:")
            print("  1. 运行 'python scripts/validate-config.py' 验证配置")
            print("  2. 使用新的环境管理脚本启动服务")
            print("  3. 查看 docs/deployment/STANDARD_SETUP.md 了解使用方法")
        else:
            print(f"\n📈 迁移完成，发现 {total_issues} 个问题 (警告: {len(self.warnings)}, 错误: {len(self.errors)})")

def main():
    """主函数"""
    migrator = DockerConfigMigrator()
    success = migrator.migrate_all()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

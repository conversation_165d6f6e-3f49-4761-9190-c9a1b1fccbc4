#!/bin/bash

# 预提交钩子：检查模型结构规范
# 使用方法：将此脚本复制到 .git/hooks/pre-commit 并设置执行权限

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 运行预提交模型结构检查...${NC}"

# 检查是否有备份文件被提交
echo -e "${BLUE}检查备份文件...${NC}"
if git diff --cached --name-only | grep -q '\.backup$'; then
    echo -e "${RED}❌ 错误：检测到备份文件提交！${NC}"
    echo -e "${YELLOW}以下备份文件不应被提交：${NC}"
    git diff --cached --name-only | grep '\.backup$' | sed 's/^/  /'
    echo -e "${YELLOW}请移除这些文件后重新提交。${NC}"
    exit 1
fi

# 检查是否有旧的模型导入路径
echo -e "${BLUE}检查模型导入路径...${NC}"
if git diff --cached --name-only | grep '\.py$' | xargs grep -l "from app\.models\.[^.]*\s\+import" 2>/dev/null; then
    echo -e "${RED}❌ 错误：检测到旧的模型导入路径！${NC}"
    echo -e "${YELLOW}以下文件包含旧的导入路径：${NC}"
    git diff --cached --name-only | grep '\.py$' | xargs grep -l "from app\.models\.[^.]*\s\+import" 2>/dev/null | sed 's/^/  /'
    echo -e "${YELLOW}请使用统一的导入路径：from app.db.models import ...${NC}"
    exit 1
fi

# 检查是否在模型目录中添加了新的 Python 文件（除了 __init__.py）
echo -e "${BLUE}检查模型目录结构...${NC}"
if git diff --cached --name-only | grep -q "^backend/app/models/.*\.py$" | grep -v "__init__.py$"; then
    echo -e "${RED}❌ 错误：在遗留模型目录中添加了新文件！${NC}"
    echo -e "${YELLOW}请将模型文件添加到 backend/app/db/models/ 目录${NC}"
    git diff --cached --name-only | grep "^backend/app/models/.*\.py$" | grep -v "__init__.py$" | sed 's/^/  /'
    exit 1
fi

# 运行模型结构验证脚本
echo -e "${BLUE}运行模型结构验证...${NC}"
if command -v python3 &> /dev/null; then
    if ! python3 scripts/validate_model_structure.py; then
        echo -e "${RED}❌ 模型结构验证失败！${NC}"
        echo -e "${YELLOW}请修复上述问题后重新提交。${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️ 警告：未找到 python3，跳过模型结构验证${NC}"
fi

echo -e "${GREEN}✅ 预提交检查通过！${NC}"
exit 0

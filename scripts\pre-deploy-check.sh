#!/bin/bash

# Pre-deployment Security Check Script
# Usage: ./scripts/pre-deploy-check.sh

echo "🔍 Starting Pre-deployment Security Check..."
echo "============================================"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check counters
ERRORS=0
WARNINGS=0

# Function to check for sensitive patterns
check_sensitive_patterns() {
    echo -e "\n📋 Checking for sensitive information..."
    
    # Check for hardcoded secrets (exclude node_modules and venv)
    if find . -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" \) \
        -not -path "*/node_modules/*" -not -path "*/venv/*" -not -path "*/.venv/*" \
        -not -path "*/venv12/*" -not -path "*/dist/*" -not -path "*/build/*" \
        -exec grep -l "SECRET_KEY\s*=\s*[\"'][^\"']*[\"']" {} \; 2>/dev/null | \
        grep -v "example\|\.env\|test\|mock"; then
        echo -e "${RED}❌ Found hardcoded SECRET_KEY${NC}"
        ((ERRORS++))
    fi
    
    # Check for default passwords (exclude dependencies)
    if find . -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" \) \
        -not -path "*/node_modules/*" -not -path "*/venv/*" -not -path "*/.venv/*" \
        -not -path "*/venv12/*" -not -path "*/dist/*" -not -path "*/build/*" \
        -exec grep -l "password.*=.*[\"']\(admin\|demo\|test\|12345\)" {} \; 2>/dev/null | \
        grep -v "example\|\.env\|test\|mock"; then
        echo -e "${YELLOW}⚠️  Found default passwords${NC}"
        ((WARNINGS++))
    fi
    
    # Check for API keys (exclude dependencies)
    if find . -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" \) \
        -not -path "*/node_modules/*" -not -path "*/venv/*" -not -path "*/.venv/*" \
        -not -path "*/venv12/*" -not -path "*/dist/*" -not -path "*/build/*" \
        -exec grep -E "(API_KEY|api_key).*[a-zA-Z0-9]{32,}" {} \; 2>/dev/null | \
        grep -v "example\|\.env\|test\|mock"; then
        echo -e "${RED}❌ Found potential API keys${NC}"
        ((ERRORS++))
    fi
}

# Function to check environment files
check_env_files() {
    echo -e "\n📋 Checking environment files..."
    
    # Check if production env files exist
    if [ ! -f "backend/.env.production" ]; then
        echo -e "${RED}❌ Missing backend/.env.production${NC}"
        ((ERRORS++))
    fi
    
    if [ ! -f "frontend/.env.production" ]; then
        echo -e "${RED}❌ Missing frontend/.env.production${NC}"
        ((ERRORS++))
    fi
    
    # Check if .env files are in .gitignore
    if ! grep -q "\.env" .gitignore 2>/dev/null; then
        echo -e "${RED}❌ .env files not in .gitignore${NC}"
        ((ERRORS++))
    fi
}

# Function to check dependencies
check_dependencies() {
    echo -e "\n📋 Checking dependencies..."
    
    # Check for known vulnerable packages
    if [ -f "frontend/package.json" ]; then
        cd frontend
        if command -v npm &> /dev/null; then
            npm audit --audit-level=high 2>/dev/null
            if [ $? -ne 0 ]; then
                echo -e "${YELLOW}⚠️  Found high severity vulnerabilities in frontend${NC}"
                ((WARNINGS++))
            fi
        fi
        cd ..
    fi
}

# Function to check Docker configuration
check_docker() {
    echo -e "\n📋 Checking Docker configuration..."
    
    # Check if Dockerfile exists
    if [ ! -f "frontend/Dockerfile" ]; then
        echo -e "${YELLOW}⚠️  Missing frontend/Dockerfile${NC}"
        ((WARNINGS++))
    fi
    
    if [ ! -f "backend/Dockerfile" ]; then
        echo -e "${YELLOW}⚠️  Missing backend/Dockerfile${NC}"
        ((WARNINGS++))
    fi
    
    # Check for root user in Dockerfile
    if grep -q "USER root" frontend/Dockerfile backend/Dockerfile 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Docker containers running as root${NC}"
        ((WARNINGS++))
    fi
}

# Function to check HTTPS configuration
check_https() {
    echo -e "\n📋 Checking HTTPS configuration..."
    
    # Check frontend API URL
    if [ -f "frontend/.env.production" ]; then
        if ! grep -q "https://" frontend/.env.production; then
            echo -e "${RED}❌ Frontend not configured for HTTPS${NC}"
            ((ERRORS++))
        fi
    fi
}

# Function to check rate limiting
check_rate_limiting() {
    echo -e "\n📋 Checking rate limiting..."
    
    # Check if rate limiting is mentioned in backend
    if ! grep -r "rate.*limit\|RateLimit\|throttle" backend/ --include="*.py" 2>/dev/null | grep -v test > /dev/null; then
        echo -e "${YELLOW}⚠️  No rate limiting found in backend${NC}"
        ((WARNINGS++))
    fi
}

# Run all checks
check_sensitive_patterns
check_env_files
check_dependencies
check_docker
check_https
check_rate_limiting

# Summary
echo -e "\n============================================"
echo -e "📊 Security Check Summary:"
echo -e "   Errors: ${RED}${ERRORS}${NC}"
echo -e "   Warnings: ${YELLOW}${WARNINGS}${NC}"

if [ $ERRORS -gt 0 ]; then
    echo -e "\n${RED}❌ Pre-deployment check FAILED${NC}"
    echo -e "Please fix all errors before deploying to production."
    exit 1
elif [ $WARNINGS -gt 0 ]; then
    echo -e "\n${YELLOW}⚠️  Pre-deployment check passed with warnings${NC}"
    echo -e "Consider addressing warnings for better security."
    exit 0
else
    echo -e "\n${GREEN}✅ Pre-deployment check PASSED${NC}"
    echo -e "Project is ready for deployment!"
    exit 0
fi
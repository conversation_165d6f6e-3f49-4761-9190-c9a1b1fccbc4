#!/bin/bash

# 快速Docker启动脚本
# 用于快速体验量化投资平台

set -e

echo "🚀 启动量化投资平台..."
echo "==============================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的环境文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cat > .env << EOF
# 数据库配置
POSTGRES_PASSWORD=quant_pass_2024
REDIS_PASSWORD=redis_pass_2024

# 应用配置
SECRET_KEY=your-super-secret-key-change-in-production
GRAFANA_PASSWORD=admin123

# 开发模式
ENVIRONMENT=development
EOF
    echo "✅ 环境配置文件已创建"
fi

# 创建数据目录
mkdir -p data/{historical,realtime,reports,uploads}
mkdir -p logs

echo "🐳 启动Docker容器..."
cd docker
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 量化投资平台启动成功！"
echo "==============================================="
echo "📊 前端应用: http://localhost:3000"
echo "🔗 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo "🗄️  数据库管理: http://localhost:5050"
echo "📈 Redis管理: http://localhost:8081"
echo "==============================================="
echo ""
echo "💡 使用说明:"
echo "  - 停止服务: ./docker-stop.sh"
echo "  - 查看日志: ./docker-logs.sh"
echo "  - 健康检查: ./docker-health-check.sh"
echo ""
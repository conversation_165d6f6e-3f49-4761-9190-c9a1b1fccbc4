#!/usr/bin/env python3
"""
仓库大小检查工具
用于监控仓库大小，识别大文件，并提供清理建议
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple
import argparse

class Colors:
    """终端颜色定义"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def log_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def log_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def log_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def get_dir_size(path: str) -> int:
    """获取目录大小（字节）"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except (OSError, IOError):
        pass
    return total

def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def find_large_files(root_path: str, min_size_mb: int = 1) -> List[Tuple[str, int]]:
    """查找大文件"""
    large_files = []
    min_size_bytes = min_size_mb * 1024 * 1024
    
    try:
        for root, dirs, files in os.walk(root_path):
            # 跳过常见的大目录
            dirs[:] = [d for d in dirs if d not in {
                'node_modules', '.git', 'dist', 'build', '__pycache__', 
                '.venv', 'venv', 'env', 'cache', '.cache'
            }]
            
            for file in files:
                filepath = os.path.join(root, file)
                try:
                    size = os.path.getsize(filepath)
                    if size >= min_size_bytes:
                        rel_path = os.path.relpath(filepath, root_path)
                        large_files.append((rel_path, size))
                except (OSError, IOError):
                    continue
    except Exception as e:
        log_error(f"扫描文件时出错: {e}")
    
    return sorted(large_files, key=lambda x: x[1], reverse=True)

def analyze_directory_sizes(root_path: str) -> List[Tuple[str, int]]:
    """分析各目录大小"""
    dir_sizes = []
    
    try:
        for item in os.listdir(root_path):
            item_path = os.path.join(root_path, item)
            if os.path.isdir(item_path) and not item.startswith('.git'):
                size = get_dir_size(item_path)
                if size > 0:
                    dir_sizes.append((item, size))
    except Exception as e:
        log_error(f"分析目录大小时出错: {e}")
    
    return sorted(dir_sizes, key=lambda x: x[1], reverse=True)

def get_git_repo_size() -> Dict[str, str]:
    """获取Git仓库大小信息"""
    sizes = {}
    
    try:
        # 获取.git目录大小
        if os.path.exists('.git'):
            git_size = get_dir_size('.git')
            sizes['git_dir'] = format_size(git_size)
        
        # 获取工作目录大小（不包括.git）
        working_size = 0
        for root, dirs, files in os.walk('.'):
            if '.git' in dirs:
                dirs.remove('.git')
            for file in files:
                filepath = os.path.join(root, file)
                try:
                    working_size += os.path.getsize(filepath)
                except (OSError, IOError):
                    continue
        sizes['working_dir'] = format_size(working_size)
        
        # 总大小
        total_size = git_size + working_size if '.git' in sizes else working_size
        sizes['total'] = format_size(total_size)
        
    except Exception as e:
        log_error(f"获取Git仓库大小时出错: {e}")
        sizes = {'total': '未知'}
    
    return sizes

def check_problematic_files(root_path: str) -> Dict[str, List[str]]:
    """检查有问题的文件类型"""
    problems = {
        'databases': [],
        'logs': [],
        'node_modules': [],
        'build_artifacts': [],
        'temp_files': [],
        'media_files': []
    }
    
    problematic_patterns = {
        'databases': ['.db', '.sqlite', '.sqlite3'],
        'logs': ['.log'],
        'temp_files': ['.tmp', '.swp', '.swo'],
        'media_files': ['.jpg', '.jpeg', '.png', '.gif', '.mp4', '.avi', '.mov'],
    }
    
    try:
        for root, dirs, files in os.walk(root_path):
            # 检查node_modules目录
            if 'node_modules' in dirs:
                rel_path = os.path.relpath(os.path.join(root, 'node_modules'), root_path)
                problems['node_modules'].append(rel_path)
            
            # 检查构建产物目录
            for build_dir in ['dist', 'build', '__pycache__']:
                if build_dir in dirs:
                    rel_path = os.path.relpath(os.path.join(root, build_dir), root_path)
                    problems['build_artifacts'].append(rel_path)
            
            # 检查文件扩展名
            for file in files:
                file_lower = file.lower()
                file_path = os.path.relpath(os.path.join(root, file), root_path)
                
                for category, extensions in problematic_patterns.items():
                    if any(file_lower.endswith(ext) for ext in extensions):
                        problems[category].append(file_path)
                        break
    
    except Exception as e:
        log_error(f"检查问题文件时出错: {e}")
    
    return problems

def generate_report(analysis_data: Dict) -> str:
    """生成详细报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"repo_size_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        return report_file
    except Exception as e:
        log_error(f"生成报告失败: {e}")
        return ""

def provide_cleanup_suggestions(problems: Dict[str, List[str]]) -> List[str]:
    """提供清理建议"""
    suggestions = []
    
    if problems['node_modules']:
        suggestions.append("🗂️  删除 node_modules 目录: find . -name 'node_modules' -type d -prune -exec rm -rf {} +")
    
    if problems['build_artifacts']:
        suggestions.append("🔨 清理构建产物: find . -name 'dist' -o -name 'build' -o -name '__pycache__' -type d -prune -exec rm -rf {} +")
    
    if problems['databases']:
        suggestions.append("🗄️  移除数据库文件: find . -name '*.db' -o -name '*.sqlite*' -type f -delete")
    
    if problems['logs']:
        suggestions.append("📝 清理日志文件: find . -name '*.log' -type f -delete")
    
    if problems['temp_files']:
        suggestions.append("🗑️  删除临时文件: find . -name '*.tmp' -o -name '*.swp' -type f -delete")
    
    if problems['media_files']:
        suggestions.append("🖼️  检查媒体文件是否必要，考虑移至外部存储")
    
    suggestions.extend([
        "📋 运行自动清理脚本: ./scripts/clean-repo.sh",
        "⚙️  更新 .gitignore 文件以防止将来提交这些文件",
        "🔄 重新安装前端依赖: cd frontend && npm install",
    ])
    
    return suggestions

def main():
    parser = argparse.ArgumentParser(description='仓库大小检查和分析工具')
    parser.add_argument('--min-size', type=int, default=1, help='大文件阈值(MB，默认1)')
    parser.add_argument('--report', action='store_true', help='生成详细JSON报告')
    parser.add_argument('--suggest', action='store_true', help='提供清理建议')
    
    args = parser.parse_args()
    
    # 检查是否在Git仓库中
    if not os.path.exists('.git'):
        log_warning("当前目录不是Git仓库")
    
    log_info("开始分析仓库大小...")
    
    # 获取仓库大小信息
    repo_sizes = get_git_repo_size()
    
    print(f"\n{Colors.WHITE}=== 仓库大小概况 ==={Colors.NC}")
    for key, size in repo_sizes.items():
        label = {
            'total': '总大小',
            'git_dir': 'Git目录(.git)',
            'working_dir': '工作目录'
        }.get(key, key)
        print(f"{label}: {Colors.CYAN}{size}{Colors.NC}")
    
    # 分析目录大小
    log_info("分析各目录大小...")
    dir_sizes = analyze_directory_sizes('.')
    
    if dir_sizes:
        print(f"\n{Colors.WHITE}=== 最大的目录 ==={Colors.NC}")
        for dir_name, size in dir_sizes[:10]:  # 显示前10个最大目录
            size_str = format_size(size)
            if size > 100 * 1024 * 1024:  # 100MB以上用红色
                color = Colors.RED
            elif size > 10 * 1024 * 1024:  # 10MB以上用黄色
                color = Colors.YELLOW
            else:
                color = Colors.GREEN
            print(f"{dir_name}: {color}{size_str}{Colors.NC}")
    
    # 查找大文件
    log_info(f"查找大于{args.min_size}MB的文件...")
    large_files = find_large_files('.', args.min_size)
    
    if large_files:
        print(f"\n{Colors.WHITE}=== 大文件列表 ==={Colors.NC}")
        for file_path, size in large_files[:20]:  # 显示前20个大文件
            size_str = format_size(size)
            if size > 50 * 1024 * 1024:  # 50MB以上用红色
                color = Colors.RED
            elif size > 5 * 1024 * 1024:  # 5MB以上用黄色
                color = Colors.YELLOW
            else:
                color = Colors.GREEN
            print(f"{file_path}: {color}{size_str}{Colors.NC}")
    
    # 检查问题文件
    log_info("检查有问题的文件类型...")
    problems = check_problematic_files('.')
    
    has_problems = any(files for files in problems.values())
    if has_problems:
        print(f"\n{Colors.WHITE}=== 发现的问题 ==={Colors.NC}")
        
        problem_labels = {
            'databases': '数据库文件',
            'logs': '日志文件',
            'node_modules': 'Node.js依赖目录',
            'build_artifacts': '构建产物',
            'temp_files': '临时文件',
            'media_files': '媒体文件'
        }
        
        for category, files in problems.items():
            if files:
                label = problem_labels.get(category, category)
                print(f"\n{Colors.RED}🔍 {label} ({len(files)}个):{Colors.NC}")
                for file_path in files[:5]:  # 只显示前5个
                    print(f"  • {file_path}")
                if len(files) > 5:
                    print(f"  ... 还有 {len(files) - 5} 个")
    
    # 提供清理建议
    if args.suggest and has_problems:
        suggestions = provide_cleanup_suggestions(problems)
        print(f"\n{Colors.WHITE}=== 清理建议 ==={Colors.NC}")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{Colors.GREEN}{i}.{Colors.NC} {suggestion}")
    
    # 生成报告
    if args.report:
        analysis_data = {
            'timestamp': datetime.now().isoformat(),
            'repo_sizes': repo_sizes,
            'directory_sizes': [{'name': name, 'size': size, 'size_formatted': format_size(size)} 
                              for name, size in dir_sizes],
            'large_files': [{'path': path, 'size': size, 'size_formatted': format_size(size)} 
                           for path, size in large_files],
            'problems': {k: v for k, v in problems.items() if v},
            'total_large_files': len(large_files),
            'total_problems': sum(len(files) for files in problems.values())
        }
        
        report_file = generate_report(analysis_data)
        if report_file:
            log_success(f"详细报告已保存至: {report_file}")
    
    # 总结
    print(f"\n{Colors.WHITE}=== 总结 ==={Colors.NC}")
    print(f"仓库总大小: {Colors.CYAN}{repo_sizes.get('total', '未知')}{Colors.NC}")
    print(f"发现大文件: {Colors.YELLOW}{len(large_files)}个{Colors.NC}")
    problem_count = sum(len(files) for files in problems.values())
    print(f"问题文件: {Colors.RED if problem_count > 0 else Colors.GREEN}{problem_count}个{Colors.NC}")
    
    if problem_count > 0:
        print(f"\n{Colors.YELLOW}💡 建议运行清理脚本: ./scripts/clean-repo.sh{Colors.NC}")
    else:
        print(f"\n{Colors.GREEN}✅ 仓库状态良好！{Colors.NC}")

if __name__ == '__main__':
    main()
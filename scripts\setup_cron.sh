#!/bin/bash

# 设置Cron定时任务脚本
# 每天18点执行数据抓取任务

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志目录
LOG_DIR="$PROJECT_DIR/logs/cron"
mkdir -p "$LOG_DIR"

# Python环境路径（根据实际情况修改）
PYTHON_ENV="$PROJECT_DIR/venv/bin/python"
if [ ! -f "$PYTHON_ENV" ]; then
    PYTHON_ENV="python"
fi

# 创建数据抓取脚本
cat > "$SCRIPT_DIR/run_daily_crawl.py" << 'EOF'
#!/usr/bin/env python3
"""
每日数据抓取脚本
通过Cron调用，执行股票数据抓取任务
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
log_dir = project_root / "logs" / "cron"
log_dir.mkdir(parents=True, exist_ok=True)

log_file = log_dir / f"daily_crawl_{datetime.now().strftime('%Y%m%d')}.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """主函数"""
    try:
        logger.info("开始执行Cron数据抓取任务")
        
        # 导入服务
        from backend.app.services.data_crawler_service import run_daily_data_crawl
        
        # 执行数据抓取（300天数据）
        await run_daily_data_crawl(days=300)
        
        logger.info("Cron数据抓取任务完成")
        
    except Exception as e:
        logger.error(f"Cron数据抓取任务失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
EOF

# 使脚本可执行
chmod +x "$SCRIPT_DIR/run_daily_crawl.py"

# 创建Cron任务包装脚本
cat > "$SCRIPT_DIR/cron_wrapper.sh" << EOF
#!/bin/bash

# Cron任务包装脚本
# 设置环境变量并执行Python脚本

# 设置工作目录
cd "$PROJECT_DIR"

# 设置环境变量
export PYTHONPATH="$PROJECT_DIR:\$PYTHONPATH"
export PATH="$PROJECT_DIR/venv/bin:\$PATH"

# 日志文件
LOG_FILE="$LOG_DIR/cron_wrapper_\$(date +%Y%m%d_%H%M%S).log"

# 执行Python脚本
echo "\$(date): 开始执行数据抓取任务" >> "\$LOG_FILE"
$PYTHON_ENV "$SCRIPT_DIR/run_daily_crawl.py" >> "\$LOG_FILE" 2>&1
EXIT_CODE=\$?

if [ \$EXIT_CODE -eq 0 ]; then
    echo "\$(date): 数据抓取任务成功完成" >> "\$LOG_FILE"
else
    echo "\$(date): 数据抓取任务失败，退出码: \$EXIT_CODE" >> "\$LOG_FILE"
fi

# 清理旧日志（保留30天）
find "$LOG_DIR" -name "*.log" -mtime +30 -delete 2>/dev/null || true

exit \$EXIT_CODE
EOF

# 使包装脚本可执行
chmod +x "$SCRIPT_DIR/cron_wrapper.sh"

# 创建Cron配置
CRON_JOB="0 18 * * * $SCRIPT_DIR/cron_wrapper.sh"

echo "正在设置Cron任务..."
echo "任务内容: $CRON_JOB"

# 检查是否已存在相同的任务
if crontab -l 2>/dev/null | grep -q "cron_wrapper.sh"; then
    echo "Cron任务已存在，正在更新..."
    # 移除旧任务
    crontab -l 2>/dev/null | grep -v "cron_wrapper.sh" | crontab -
fi

# 添加新任务
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "Cron任务设置完成！"
echo ""
echo "任务详情:"
echo "- 执行时间: 每天18:00"
echo "- 执行脚本: $SCRIPT_DIR/cron_wrapper.sh"
echo "- 日志目录: $LOG_DIR"
echo "- 数据目录: $PROJECT_DIR/data"
echo ""
echo "查看当前Cron任务:"
crontab -l
echo ""
echo "手动测试执行:"
echo "$SCRIPT_DIR/cron_wrapper.sh"
EOF

# 使设置脚本可执行
chmod +x "$SCRIPT_DIR/setup_cron.sh"

echo "Cron设置脚本已创建: $SCRIPT_DIR/setup_cron.sh"
echo "运行以下命令来设置定时任务:"
echo "bash $SCRIPT_DIR/setup_cron.sh"

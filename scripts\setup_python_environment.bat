@echo off
REM Python 环境设置脚本 (Windows)
REM 确保使用兼容的 Python 版本并设置开发环境

setlocal enabledelayedexpansion

REM 配置
set RECOMMENDED_VERSION=3.10.13
set MIN_VERSION=3.10
set MAX_VERSION=3.11

echo 🐍 Python 环境设置脚本 (Windows)
echo ==================================

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请从 https://python.org 下载安装
    echo 建议安装版本: %RECOMMENDED_VERSION%
    pause
    exit /b 1
)

REM 获取当前 Python 版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set CURRENT_VERSION=%%i
echo ✅ 当前 Python 版本: %CURRENT_VERSION%

REM 检查版本兼容性
echo 🔍 检查版本兼容性...
python scripts\check_python.py
if errorlevel 1 (
    echo ❌ Python 版本不兼容
    echo 建议安装 Python %RECOMMENDED_VERSION%
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "backend\venv" (
    echo 📦 创建虚拟环境...
    cd backend
    python -m venv venv
    cd ..
) else (
    echo ✅ 虚拟环境已存在
)

REM 激活虚拟环境
echo 📦 激活虚拟环境并安装依赖...
call backend\venv\Scripts\activate.bat

REM 升级 pip
python -m pip install --upgrade pip

REM 安装依赖
pip install -r backend\requirements.txt

REM 验证依赖兼容性
echo 🔍 验证依赖兼容性...
python scripts\verify_python_compatibility.py
if errorlevel 1 (
    echo ⚠️ 依赖兼容性检查发现问题，请查看报告
)

echo 🎉 Python 环境设置完成！
echo.
echo 下一步：
echo 1. 激活虚拟环境:
echo    backend\venv\Scripts\activate.bat
echo 2. 启动开发服务器:
echo    cd backend ^&^& python -m app.main
echo 3. 运行测试:
echo    pytest backend\app\tests\

pause

#!/bin/bash
# Python 环境设置脚本
# 确保使用兼容的 Python 版本并设置开发环境

set -e  # 遇到错误立即退出

# 配置
RECOMMENDED_VERSION="3.10.13"
MIN_VERSION="3.10"
MAX_VERSION="3.11"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐍 Python 环境设置脚本${NC}"
echo "=================================="

# 检查 pyenv 是否安装
if ! command -v pyenv &> /dev/null; then
    echo -e "${YELLOW}⚠️  pyenv 未安装，尝试安装...${NC}"
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install pyenv
        else
            echo -e "${RED}❌ 请先安装 Homebrew: https://brew.sh${NC}"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl https://pyenv.run | bash
        echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
        echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
        echo 'eval "$(pyenv init -)"' >> ~/.bashrc
        source ~/.bashrc
    else
        echo -e "${RED}❌ 不支持的操作系统，请手动安装 pyenv${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ pyenv 已安装${NC}"

# 检查推荐版本是否已安装
if ! pyenv versions | grep -q "$RECOMMENDED_VERSION"; then
    echo -e "${YELLOW}📦 安装 Python $RECOMMENDED_VERSION...${NC}"
    
    # 安装构建依赖（Linux）
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y make build-essential libssl-dev zlib1g-dev \
                libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm \
                libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev \
                libffi-dev liblzma-dev
        elif command -v yum &> /dev/null; then
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y zlib-devel bzip2 bzip2-devel readline-devel \
                sqlite sqlite-devel openssl-devel tk-devel libffi-devel xz-devel
        fi
    fi
    
    # 安装 Python
    pyenv install "$RECOMMENDED_VERSION"
else
    echo -e "${GREEN}✅ Python $RECOMMENDED_VERSION 已安装${NC}"
fi

# 设置本地版本
echo -e "${BLUE}🔧 设置项目 Python 版本为 $RECOMMENDED_VERSION${NC}"
pyenv local "$RECOMMENDED_VERSION"

# 验证版本
CURRENT_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
echo -e "${GREEN}✅ 当前 Python 版本: $CURRENT_VERSION${NC}"

# 运行兼容性检查
echo -e "${BLUE}🔍 运行兼容性检查...${NC}"
python scripts/check_python.py

# 创建虚拟环境
VENV_PATH="backend/venv"
if [ ! -d "$VENV_PATH" ]; then
    echo -e "${BLUE}📦 创建虚拟环境...${NC}"
    cd backend
    python -m venv venv
    cd ..
else
    echo -e "${GREEN}✅ 虚拟环境已存在${NC}"
fi

# 激活虚拟环境并安装依赖
echo -e "${BLUE}📦 安装 Python 依赖...${NC}"
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    source backend/venv/Scripts/activate
else
    # Unix-like
    source backend/venv/bin/activate
fi

# 升级 pip
pip install --upgrade pip

# 安装依赖
pip install -r backend/requirements.txt

# 运行依赖兼容性验证
echo -e "${BLUE}🔍 验证依赖兼容性...${NC}"
python scripts/verify_python_compatibility.py

echo -e "${GREEN}🎉 Python 环境设置完成！${NC}"
echo ""
echo "下一步："
echo "1. 激活虚拟环境:"
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "   source backend/venv/Scripts/activate"
else
    echo "   source backend/venv/bin/activate"
fi
echo "2. 启动开发服务器:"
echo "   cd backend && python -m app.main"
echo "3. 运行测试:"
echo "   pytest backend/app/tests/"

#!/usr/bin/env python3
"""
简单的自动化注册和登录脚本
"""
import time
import subprocess
import sys

def run_automation():
    print("🚀 开始自动化流程...")
    
    # 使用AppleScript在macOS上自动化浏览器操作
    applescript = '''
    tell application "Google Chrome"
        activate
        open location "http://localhost:5174/register"
        delay 2
        
        -- 这里可以添加更多的自动化操作
        -- 但由于网页交互的复杂性，建议手动完成
    end tell
    '''
    
    try:
        subprocess.run(['osascript', '-e', applescript], check=True)
        print("✅ 已打开注册页面")
        print("📝 请手动完成以下步骤：")
        print("   1. 填写用户名：testuser123")
        print("   2. 填写邮箱：<EMAIL>")
        print("   3. 填写密码：password123")
        print("   4. 确认密码：password123")
        print("   5. 拖动滑块验证码")
        print("   6. 点击注册按钮")
        print("   7. 注册成功后，使用相同信息登录")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ AppleScript执行失败: {e}")
        print("请手动打开浏览器并访问 http://localhost:5174/register")
    except FileNotFoundError:
        print("❌ 未找到osascript命令")
        print("请手动打开浏览器并访问 http://localhost:5174/register")

if __name__ == "__main__":
    run_automation()

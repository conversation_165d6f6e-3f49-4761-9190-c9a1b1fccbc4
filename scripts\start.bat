@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 量化投资平台一键启动脚本 (Windows版本)
echo 🚀 启动量化投资平台...
echo.

:: 进入项目根目录
cd /d "%~dp0\.."

:: 创建日志目录
if not exist "logs" mkdir logs

:: 检查依赖
echo 🔍 检查运行环境...

:: 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    python3 --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python 未安装，请安装 Python 3.9+
        echo 📥 下载地址: https://www.python.org/
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

:: 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请安装 Node.js 16+
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ 环境检查通过

:: 停止现有服务
echo 🛑 停止现有服务...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im python3.exe >nul 2>&1
timeout /t 2 >nul

:: 启动后端
echo 📡 启动后端服务...
cd backend

:: 使用新的启动脚本
if exist "start_backend.py" (
    set BACKEND_FILE=start_backend.py
    echo 📝 使用后端启动脚本: !BACKEND_FILE!
) else if exist "app\main_simple.py" (
    set BACKEND_FILE=app\main_simple.py
    echo 📝 使用简化后端: !BACKEND_FILE!
) else (
    echo ❌ 未找到后端启动文件
    pause
    exit /b 1
)

:: 启动后端服务
start /b "" %PYTHON_CMD% %BACKEND_FILE% > ..\logs\backend.log 2>&1

:: 等待后端启动
echo ⏳ 等待后端服务启动...
set /a count=0
:wait_backend
timeout /t 2 >nul
set /a count+=1
curl -f http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    if !count! geq 15 (
        echo ❌ 后端服务启动失败
        echo 📄 后端日志:
        type ..\logs\backend.log
        pause
        exit /b 1
    )
    echo .
    goto wait_backend
)
echo ✅ 后端服务启动成功

:: 返回项目根目录
cd ..

:: 启动前端
echo 🎨 启动前端服务...
cd frontend

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo 📝 前端依赖已存在
)

:: 启动前端开发服务器
start /b "" npm run dev > ..\logs\frontend.log 2>&1

:: 等待前端启动
echo ⏳ 等待前端服务启动...
set /a count=0
:wait_frontend
timeout /t 3 >nul
set /a count+=1
curl -f http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    if !count! geq 20 (
        echo ❌ 前端服务启动失败
        echo 📄 前端日志:
        type ..\logs\frontend.log
        pause
        exit /b 1
    )
    echo .
    goto wait_frontend
)
echo ✅ 前端服务启动成功

:: 返回项目根目录
cd ..

:: 显示启动完成信息
echo.
echo ✅ 🎉 量化投资平台启动完成！
echo.
echo 📊 服务地址:
echo    前端应用: http://localhost:5173
echo    后端API: http://localhost:8000
echo    API文档: http://localhost:8000/docs
echo.
echo 📄 日志文件:
echo    后端日志: logs\backend.log
echo    前端日志: logs\frontend.log
echo.
echo 🔧 管理命令:
echo    停止服务: scripts\stop.bat
echo    查看状态: scripts\status.bat
echo    重启服务: scripts\restart.bat
echo.
echo 💡 提示: 首次启动可能需要较长时间，请耐心等待
echo 💡 如遇问题，请查看日志文件或运行 scripts\status.bat
echo.
echo 🌐 浏览器将自动打开前端页面...

:: 等待3秒后自动打开浏览器
timeout /t 3 >nul
start http://localhost:5173

echo.
echo 按任意键关闭此窗口...
pause >nul

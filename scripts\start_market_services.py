#!/usr/bin/env python3
"""
市场数据服务启动脚本
启动所有相关的数据服务，包括调度器、数据抓取等
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.market_config import get_market_config
from backend.app.services.scheduler_service import start_scheduler, stop_scheduler
from backend.app.services.data_crawler_service import data_crawler
from backend.app.services.tushare_data_service import tushare_service
from backend.app.services.akshare_data_service import akshare_service

# 配置日志
config = get_market_config()
log_dir = Path(config.LOG_DIR)
log_dir.mkdir(parents=True, exist_ok=True)

log_file = log_dir / f"market_services_{datetime.now().strftime('%Y%m%d')}.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class MarketServicesManager:
    """市场数据服务管理器"""
    
    def __init__(self):
        self.config = get_market_config()
        self.services_running = False
        
    async def start_all_services(self):
        """启动所有服务"""
        logger.info("开始启动市场数据服务...")
        
        try:
            # 1. 检查配置
            await self._check_configuration()
            
            # 2. 初始化数据源
            await self._initialize_data_sources()
            
            # 3. 启动调度器
            if self.config.SCHEDULER_ENABLED:
                await self._start_scheduler()
            
            # 4. 执行初始数据检查
            await self._initial_data_check()
            
            # 5. 启动监控
            if self.config.MONITORING_ENABLED:
                await self._start_monitoring()
            
            self.services_running = True
            logger.info("所有市场数据服务启动完成")
            
        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            await self.stop_all_services()
            raise
    
    async def stop_all_services(self):
        """停止所有服务"""
        logger.info("开始停止市场数据服务...")
        
        try:
            # 停止调度器
            await stop_scheduler()
            
            # 清理资源
            await self._cleanup_resources()
            
            self.services_running = False
            logger.info("所有市场数据服务已停止")
            
        except Exception as e:
            logger.error(f"停止服务失败: {e}")
    
    async def _check_configuration(self):
        """检查配置"""
        logger.info("检查配置...")
        
        # 检查Tushare Token
        if not self.config.TUSHARE_TOKEN or self.config.TUSHARE_TOKEN == "your_token_here":
            logger.warning("Tushare Token未配置，将使用模拟数据")
        
        # 检查目录
        paths = self.config.get_data_paths()
        for name, path in paths.items():
            if not path.exists():
                logger.info(f"创建目录: {path}")
                path.mkdir(parents=True, exist_ok=True)
        
        logger.info("配置检查完成")
    
    async def _initialize_data_sources(self):
        """初始化数据源"""
        logger.info("初始化数据源...")
        
        try:
            # 测试Tushare连接
            if self.config.USE_REAL_DATA:
                async with tushare_service:
                    test_data = await tushare_service.get_stock_basic()
                    if test_data:
                        logger.info(f"Tushare连接成功，获取到{len(test_data)}只股票")
                    else:
                        logger.warning("Tushare连接失败，将使用备用数据源")
            
            logger.info("数据源初始化完成")
            
        except Exception as e:
            logger.error(f"数据源初始化失败: {e}")
            raise
    
    async def _start_scheduler(self):
        """启动调度器"""
        logger.info("启动定时任务调度器...")
        
        try:
            await start_scheduler()
            logger.info("调度器启动成功")
            
        except Exception as e:
            logger.error(f"调度器启动失败: {e}")
            raise
    
    async def _initial_data_check(self):
        """初始数据检查"""
        logger.info("执行初始数据检查...")
        
        try:
            # 检查今日是否已有数据
            today = datetime.now().strftime('%Y%m%d')
            processed_dir = Path(self.config.PROCESSED_DIR)
            
            today_files = list(processed_dir.glob(f"*_{today}.json"))
            
            if today_files:
                logger.info(f"发现今日数据文件{len(today_files)}个")
            else:
                logger.info("未发现今日数据，建议执行数据抓取")
                
                # 如果是交易时间且启用了抓取，可以考虑自动执行
                if self.config.is_trading_time() and self.config.CRAWL_ENABLED:
                    logger.info("当前为交易时间，考虑启动数据抓取...")
            
            logger.info("初始数据检查完成")
            
        except Exception as e:
            logger.error(f"初始数据检查失败: {e}")
    
    async def _start_monitoring(self):
        """启动监控"""
        logger.info("启动服务监控...")
        
        # 这里可以添加监控逻辑
        # 例如：定期检查服务状态、磁盘空间、内存使用等
        
        logger.info("监控启动完成")
    
    async def _cleanup_resources(self):
        """清理资源"""
        logger.info("清理资源...")
        
        try:
            # 清理缓存
            if hasattr(tushare_service, 'cleanup_old_cache'):
                async with tushare_service:
                    await tushare_service.cleanup_old_cache()
            
            if hasattr(akshare_service, 'cleanup_old_cache'):
                await akshare_service.cleanup_old_cache()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    async def get_services_status(self):
        """获取服务状态"""
        try:
            from backend.app.services.scheduler_service import get_scheduler_status
            from backend.app.services.data_crawler_service import get_crawler_status
            
            scheduler_status = await get_scheduler_status()
            crawler_status = await get_crawler_status()
            
            return {
                "services_running": self.services_running,
                "scheduler": scheduler_status,
                "crawler": crawler_status,
                "config": {
                    "use_real_data": self.config.USE_REAL_DATA,
                    "scheduler_enabled": self.config.SCHEDULER_ENABLED,
                    "crawl_enabled": self.config.CRAWL_ENABLED,
                    "monitoring_enabled": self.config.MONITORING_ENABLED
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return {"error": str(e)}

async def main():
    """主函数"""
    manager = MarketServicesManager()
    
    try:
        # 启动服务
        await manager.start_all_services()
        
        # 显示状态
        status = await manager.get_services_status()
        logger.info(f"服务状态: {status}")
        
        # 保持运行
        logger.info("服务正在运行，按Ctrl+C停止...")
        
        try:
            while True:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                # 可以在这里添加定期检查逻辑
                if manager.config.MONITORING_ENABLED:
                    status = await manager.get_services_status()
                    logger.debug(f"服务状态检查: {status.get('services_running', False)}")
                
        except KeyboardInterrupt:
            logger.info("收到停止信号...")
        
    except Exception as e:
        logger.error(f"服务运行失败: {e}")
        sys.exit(1)
    
    finally:
        # 停止服务
        await manager.stop_all_services()
        logger.info("服务已停止")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)

#!/bin/bash

# 量化投资平台状态检查脚本
# 支持 Linux、macOS、Windows (Git Bash)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 检查量化投资平台状态..."
echo "=========================="

# 进入项目根目录
cd "$(dirname "$0")/.."

echo ""
log_info "检查后端服务状态..."
if curl -s --connect-timeout 3 http://localhost:8000/health > /dev/null 2>&1; then
    log_success "后端服务正常运行"
    echo "   🔌 地址: http://localhost:8000"
    echo "   📚 API文档: http://localhost:8000/docs"

    # 检查后端进程
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo "   📋 进程ID: $BACKEND_PID"
        else
            log_warning "PID文件存在但进程已停止"
        fi
    fi

    # 检查API端点
    echo "   API端点检查:"
    if curl -s --connect-timeout 2 "http://localhost:8000/api/v1/market/stocks" > /dev/null 2>&1; then
        echo "     ✅ 市场数据API"
    else
        echo "     ❌ 市场数据API"
    fi
else
    log_error "后端服务未响应"
    # 检查端口占用
    if command -v lsof &> /dev/null && lsof -i:8000 > /dev/null 2>&1; then
        log_warning "端口8000被占用但服务无响应"
    else
        log_warning "端口8000未被监听"
    fi
fi

echo ""
log_info "检查前端服务状态..."
if curl -s --connect-timeout 3 http://localhost:5173 > /dev/null 2>&1; then
    log_success "前端服务正常运行"
    echo "   🌐 地址: http://localhost:5173"

    # 检查前端进程
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "   📋 进程ID: $FRONTEND_PID"
        else
            log_warning "PID文件存在但进程已停止"
        fi
    fi
else
    log_error "前端服务未响应"
    # 检查端口占用
    if command -v lsof &> /dev/null && lsof -i:5173 > /dev/null 2>&1; then
        log_warning "端口5173被占用但服务无响应"
    else
        log_warning "端口5173未被监听"
    fi
fi

echo ""
echo "🔌 端口占用情况:"
echo "  端口 8000 (后端):"
if lsof -i:8000 > /dev/null 2>&1; then
    lsof -i:8000 | grep LISTEN
else
    echo "    无进程监听"
fi

echo "  端口 3000 (前端):"
if lsof -i:3000 > /dev/null 2>&1; then
    lsof -i:3000 | grep LISTEN
else
    echo "    无进程监听"
fi

echo ""
echo "📄 日志文件状态:"
if [ -f backend/backend.log ]; then
    BACKEND_LOG_SIZE=$(stat -f%z backend/backend.log 2>/dev/null || stat -c%s backend/backend.log 2>/dev/null)
    echo "  后端日志: backend/backend.log (${BACKEND_LOG_SIZE} bytes)"
else
    echo "  后端日志: 不存在"
fi

if [ -f frontend/frontend.log ]; then
    FRONTEND_LOG_SIZE=$(stat -f%z frontend/frontend.log 2>/dev/null || stat -c%s frontend/frontend.log 2>/dev/null)
    echo "  前端日志: frontend/frontend.log (${FRONTEND_LOG_SIZE} bytes)"
else
    echo "  前端日志: 不存在"
fi

echo ""
echo "🔧 系统信息:"
echo "  Python: $(python3 --version 2>&1 || echo '未安装')"
echo "  Node.js: $(node --version 2>&1 || echo '未安装')"
echo "  操作系统: $(uname -s)"
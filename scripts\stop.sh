#!/bin/bash

# 量化投资平台停止脚本
# 支持 Linux、macOS、Windows (Git Bash)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🛑 停止量化投资平台..."

# 进入项目根目录
cd "$(dirname "$0")/.."

# 停止后端服务
log_info "停止后端服务..."
BACKEND_STOPPED=false

if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        log_info "停止后端服务 (PID: $BACKEND_PID)"
        kill $BACKEND_PID
        BACKEND_STOPPED=true
    else
        log_warning "后端进程已停止"
        BACKEND_STOPPED=true
    fi
    rm -f .backend.pid
fi

# 按端口停止后端
if ! $BACKEND_STOPPED; then
    log_info "按端口停止后端服务 (8000)"
    if command -v lsof &> /dev/null; then
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    fi
fi

# 停止前端服务
log_info "停止前端服务..."
FRONTEND_STOPPED=false

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        log_info "停止前端服务 (PID: $FRONTEND_PID)"
        kill $FRONTEND_PID
        FRONTEND_STOPPED=true
    else
        log_warning "前端进程已停止"
        FRONTEND_STOPPED=true
    fi
    rm -f .frontend.pid
fi

# 按端口停止前端
if ! $FRONTEND_STOPPED; then
    log_info "按端口停止前端服务 (5173)"
    if command -v lsof &> /dev/null; then
        lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    fi
fi

# 清理相关进程
log_info "清理相关进程..."
pkill -f "main_minimal\|main_simple\|main\.py" 2>/dev/null || true
pkill -f "vite\|npm.*dev" 2>/dev/null || true
pkill -f "uvicorn\|fastapi" 2>/dev/null || true

# 等待进程完全停止
sleep 2

log_success "量化投资平台已停止"
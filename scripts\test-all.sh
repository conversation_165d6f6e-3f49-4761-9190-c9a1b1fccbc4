#!/bin/bash

# 量化投资平台全面测试脚本
echo "🧪 开始全面测试量化投资平台..."

# 进入项目根目录
cd "$(dirname "$0")/.."

# 测试结果文件
TEST_REPORT="/Users/<USER>/Desktop/quant-platf/final_test_report.md"

# 创建测试报告
cat > "$TEST_REPORT" << 'EOF'
# 量化投资平台测试报告

测试时间: $(date '+%Y-%m-%d %H:%M:%S')

## 测试环境
- 操作系统: $(uname -s)
- Python版本: $(python3 --version 2>&1 || echo "未安装")
- Node.js版本: $(node --version 2>&1 || echo "未安装")

## 测试结果

EOF

echo "📊 开始后端API测试..."

# 测试后端API
echo "### 后端API测试" >> "$TEST_REPORT"
echo "" >> "$TEST_REPORT"

# 测试健康检查
echo "#### 1. 健康检查端点" >> "$TEST_REPORT"
if curl -f http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 健康检查：通过" >> "$TEST_REPORT"
    echo "✅ 健康检查端点正常"
else
    echo "❌ 健康检查：失败" >> "$TEST_REPORT"
    echo "❌ 健康检查端点异常"
fi

# 测试策略API
echo "" >> "$TEST_REPORT"
echo "#### 2. 策略API端点" >> "$TEST_REPORT"
STRATEGY_RESPONSE=$(curl -s http://localhost:8000/api/v1/strategies)
if echo "$STRATEGY_RESPONSE" | grep -q "success"; then
    echo "✅ 策略API：通过" >> "$TEST_REPORT"
    echo "✅ 策略API正常"
else
    echo "❌ 策略API：失败" >> "$TEST_REPORT"
    echo "❌ 策略API异常"
fi

# 测试市场数据API
echo "" >> "$TEST_REPORT"
echo "#### 3. 市场数据API端点" >> "$TEST_REPORT"
MARKET_RESPONSE=$(curl -s http://localhost:8000/api/v1/market/stocks)
if echo "$MARKET_RESPONSE" | grep -q "success"; then
    echo "✅ 市场数据API：通过" >> "$TEST_REPORT"
    echo "✅ 市场数据API正常"
else
    echo "❌ 市场数据API：失败" >> "$TEST_REPORT"
    echo "❌ 市场数据API异常"
fi

# 测试认证API
echo "" >> "$TEST_REPORT"
echo "#### 4. 认证API端点" >> "$TEST_REPORT"
AUTH_RESPONSE=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')
if echo "$AUTH_RESPONSE" | grep -q "access_token"; then
    echo "✅ 认证API：通过" >> "$TEST_REPORT"
    echo "✅ 认证API正常"
else
    echo "❌ 认证API：失败" >> "$TEST_REPORT"
    echo "❌ 认证API异常"
fi

# 测试监控端点
echo "" >> "$TEST_REPORT"
echo "#### 5. 监控端点" >> "$TEST_REPORT"
STATUS_RESPONSE=$(curl -s http://localhost:8000/api/v1/status)
if echo "$STATUS_RESPONSE" | grep -q "healthy"; then
    echo "✅ 系统状态API：通过" >> "$TEST_REPORT"
    echo "✅ 监控端点正常"
else
    echo "❌ 系统状态API：失败" >> "$TEST_REPORT"
    echo "❌ 监控端点异常"
fi

echo "" >> "$TEST_REPORT"
echo "📊 开始前端服务测试..."

# 测试前端服务
echo "### 前端服务测试" >> "$TEST_REPORT"
echo "" >> "$TEST_REPORT"

if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 前端服务：正常运行" >> "$TEST_REPORT"
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务：无响应" >> "$TEST_REPORT"
    echo "❌ 前端服务异常"
fi

echo "" >> "$TEST_REPORT"
echo "🔧 检查项目配置..."

# 检查项目配置
echo "### 项目配置检查" >> "$TEST_REPORT"
echo "" >> "$TEST_REPORT"

# 检查Docker配置
if [ -f "docker-compose.yml" ]; then
    echo "✅ Docker配置：存在" >> "$TEST_REPORT"
    echo "✅ Docker配置文件存在"
else
    echo "❌ Docker配置：缺失" >> "$TEST_REPORT"
    echo "❌ Docker配置文件缺失"
fi

# 检查启动脚本
if [ -f "scripts/start.sh" ]; then
    echo "✅ 启动脚本：存在" >> "$TEST_REPORT"
    echo "✅ 启动脚本存在"
else
    echo "❌ 启动脚本：缺失" >> "$TEST_REPORT"
    echo "❌ 启动脚本缺失"
fi

# 检查验证码组件
if [ -f "frontend/src/components/common/SliderCaptcha/index.vue" ]; then
    echo "✅ 验证码组件：存在" >> "$TEST_REPORT"
    echo "✅ 验证码组件存在"
else
    echo "❌ 验证码组件：缺失" >> "$TEST_REPORT"
    echo "❌ 验证码组件缺失"
fi

echo "" >> "$TEST_REPORT"
echo "📋 测试完成时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEST_REPORT"

echo ""
echo "🎉 测试完成！"
echo "📄 测试报告已生成: $TEST_REPORT"
echo ""
echo "📊 快速查看报告："
cat "$TEST_REPORT"
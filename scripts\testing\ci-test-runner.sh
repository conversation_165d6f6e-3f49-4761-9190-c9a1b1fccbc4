#!/bin/bash

# CI 测试运行器脚本
# 用于在 CI 环境中运行各种类型的测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker/compose/ci/docker-compose.yml"
ENV_FILE="docker/compose/ci/.env.ci"
RESULTS_DIR="test-results"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建结果目录
setup_results_dir() {
    mkdir -p "$RESULTS_DIR"/{backend,frontend,e2e,integration,performance}
    log_info "创建测试结果目录: $RESULTS_DIR"
}

# 启动测试环境
start_test_environment() {
    log_info "启动 CI 测试环境..."
    
    # 清理可能存在的旧容器
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --remove-orphans 2>/dev/null || true
    
    # 启动服务
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d --build
    
    # 等待服务就绪
    log_info "等待服务启动..."
    sleep 30
    
    # 使用等待脚本
    if ! bash scripts/testing/wait-for-services.sh ci; then
        log_error "服务启动失败"
        return 1
    fi
    
    log_success "测试环境启动成功"
}

# 停止测试环境
stop_test_environment() {
    log_info "停止 CI 测试环境..."
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --remove-orphans
    log_success "测试环境已停止"
}

# 运行后端单元测试
run_backend_unit_tests() {
    log_info "运行后端单元测试..."
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        pytest tests/unit/ \
        -v \
        --cov=app \
        --cov-report=xml \
        --cov-report=html \
        --junitxml="$RESULTS_DIR/backend/unit-test-results.xml" \
        --html="$RESULTS_DIR/backend/unit-test-report.html" \
        --self-contained-html
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "后端单元测试通过"
    else
        log_error "后端单元测试失败"
    fi
    
    return $exit_code
}

# 运行后端集成测试
run_backend_integration_tests() {
    log_info "运行后端集成测试..."
    
    # 确保数据库迁移已完成
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        alembic upgrade head
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        pytest tests/integration/ \
        -v \
        --cov=app \
        --cov-append \
        --cov-report=xml \
        --junitxml="$RESULTS_DIR/integration/integration-test-results.xml" \
        --html="$RESULTS_DIR/integration/integration-test-report.html" \
        --self-contained-html
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "后端集成测试通过"
    else
        log_error "后端集成测试失败"
    fi
    
    return $exit_code
}

# 运行 API 测试
run_api_tests() {
    log_info "运行 API 测试..."
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        pytest tests/api/ \
        -v \
        --junitxml="$RESULTS_DIR/backend/api-test-results.xml" \
        --html="$RESULTS_DIR/backend/api-test-report.html" \
        --self-contained-html
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "API 测试通过"
    else
        log_error "API 测试失败"
    fi
    
    return $exit_code
}

# 运行前端测试
run_frontend_tests() {
    log_info "运行前端测试..."
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T frontend \
        pnpm run test:unit \
        --reporter=junit \
        --outputFile="$RESULTS_DIR/frontend/unit-test-results.xml"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "前端测试通过"
    else
        log_error "前端测试失败"
    fi
    
    return $exit_code
}

# 运行 E2E 测试
run_e2e_tests() {
    log_info "运行 E2E 测试..."
    
    # 确保 Nginx 代理正常工作
    if ! curl -f http://localhost:80/health >/dev/null 2>&1; then
        log_error "Nginx 代理未就绪"
        return 1
    fi
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T frontend \
        pnpm run test:e2e \
        --reporter=junit \
        --outputFile="$RESULTS_DIR/e2e/e2e-test-results.xml"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "E2E 测试通过"
    else
        log_error "E2E 测试失败"
    fi
    
    return $exit_code
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    # 使用 locust 或其他性能测试工具
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" run --rm test-runner \
        python -m locust \
        --headless \
        --users 10 \
        --spawn-rate 2 \
        --run-time 60s \
        --host http://backend:8000 \
        --html "$RESULTS_DIR/performance/performance-report.html"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "性能测试完成"
    else
        log_error "性能测试失败"
    fi
    
    return $exit_code
}

# 收集测试结果
collect_test_results() {
    log_info "收集测试结果..."
    
    # 复制覆盖率报告
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        cp coverage.xml /app/results/backend/ 2>/dev/null || true
    
    # 复制日志文件
    mkdir -p "$RESULTS_DIR/logs"
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs backend > "$RESULTS_DIR/logs/backend.log" 2>&1 || true
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs frontend > "$RESULTS_DIR/logs/frontend.log" 2>&1 || true
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs postgres > "$RESULTS_DIR/logs/postgres.log" 2>&1 || true
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs redis > "$RESULTS_DIR/logs/redis.log" 2>&1 || true
    
    log_success "测试结果收集完成"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    cat > "$RESULTS_DIR/test-summary.md" << EOF
# CI 测试报告

## 测试环境
- 环境: CI
- 时间: $(date)
- 提交: ${GITHUB_SHA:-unknown}
- 分支: ${GITHUB_REF_NAME:-unknown}

## 测试结果
EOF
    
    # 检查各个测试结果文件
    for test_type in backend/unit backend/api integration frontend e2e; do
        result_file="$RESULTS_DIR/$test_type-test-results.xml"
        if [ -f "$result_file" ]; then
            echo "- $test_type: ✅ 通过" >> "$RESULTS_DIR/test-summary.md"
        else
            echo "- $test_type: ❌ 失败或未运行" >> "$RESULTS_DIR/test-summary.md"
        fi
    done
    
    log_success "测试报告生成完成: $RESULTS_DIR/test-summary.md"
}

# 主函数
main() {
    local test_type="${1:-all}"
    local exit_code=0
    
    setup_results_dir
    
    case "$test_type" in
        "unit")
            start_test_environment
            run_backend_unit_tests || exit_code=1
            ;;
        "integration")
            start_test_environment
            run_backend_integration_tests || exit_code=1
            ;;
        "api")
            start_test_environment
            run_api_tests || exit_code=1
            ;;
        "frontend")
            start_test_environment
            run_frontend_tests || exit_code=1
            ;;
        "e2e")
            start_test_environment
            run_e2e_tests || exit_code=1
            ;;
        "performance")
            start_test_environment
            run_performance_tests || exit_code=1
            ;;
        "all")
            start_test_environment
            run_backend_unit_tests || exit_code=1
            run_backend_integration_tests || exit_code=1
            run_api_tests || exit_code=1
            run_frontend_tests || exit_code=1
            run_e2e_tests || exit_code=1
            ;;
        "stop")
            stop_test_environment
            return 0
            ;;
        *)
            echo "用法: $0 [unit|integration|api|frontend|e2e|performance|all|stop]"
            exit 1
            ;;
    esac
    
    collect_test_results
    generate_test_report
    
    if [ "$test_type" != "stop" ]; then
        stop_test_environment
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_success "所有测试完成"
    else
        log_error "部分测试失败"
    fi
    
    return $exit_code
}

# 清理函数
cleanup() {
    log_warning "收到中断信号，清理测试环境..."
    stop_test_environment
    exit 1
}

# 设置信号处理
trap cleanup INT TERM

# 执行主函数
main "$@"

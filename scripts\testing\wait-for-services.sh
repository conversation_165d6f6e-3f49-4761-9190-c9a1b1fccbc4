#!/bin/bash

# 等待服务就绪脚本
# 用于 CI 环境中等待各种服务启动完成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待 PostgreSQL 就绪
wait_for_postgres() {
    local host=${1:-localhost}
    local port=${2:-5432}
    local user=${3:-test_user}
    local db=${4:-test_db}
    local timeout=${5:-60}
    
    log_info "等待 PostgreSQL 就绪 ($host:$port)..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        if pg_isready -h $host -p $port -U $user -d $db >/dev/null 2>&1; then
            log_success "PostgreSQL 已就绪"
            return 0
        fi
        
        sleep 2
        count=$((count + 2))
        
        if [ $((count % 10)) -eq 0 ]; then
            log_info "等待 PostgreSQL... (${count}s/${timeout}s)"
        fi
    done
    
    log_error "PostgreSQL 启动超时"
    return 1
}

# 等待 Redis 就绪
wait_for_redis() {
    local host=${1:-localhost}
    local port=${2:-6379}
    local timeout=${3:-30}
    
    log_info "等待 Redis 就绪 ($host:$port)..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        if redis-cli -h $host -p $port ping >/dev/null 2>&1; then
            log_success "Redis 已就绪"
            return 0
        fi
        
        sleep 1
        count=$((count + 1))
        
        if [ $((count % 5)) -eq 0 ]; then
            log_info "等待 Redis... (${count}s/${timeout}s)"
        fi
    done
    
    log_error "Redis 启动超时"
    return 1
}

# 等待 HTTP 服务就绪
wait_for_http() {
    local url=$1
    local timeout=${2:-60}
    local expected_status=${3:-200}
    
    log_info "等待 HTTP 服务就绪 ($url)..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        local status=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        
        if [ "$status" = "$expected_status" ]; then
            log_success "HTTP 服务已就绪 ($url)"
            return 0
        fi
        
        sleep 2
        count=$((count + 2))
        
        if [ $((count % 10)) -eq 0 ]; then
            log_info "等待 HTTP 服务... (${count}s/${timeout}s) [状态: $status]"
        fi
    done
    
    log_error "HTTP 服务启动超时 ($url)"
    return 1
}

# 等待 Docker 容器健康
wait_for_container_health() {
    local container_name=$1
    local timeout=${2:-120}
    
    log_info "等待容器健康检查通过 ($container_name)..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        
        case $health in
            "healthy")
                log_success "容器健康检查通过 ($container_name)"
                return 0
                ;;
            "unhealthy")
                log_error "容器健康检查失败 ($container_name)"
                return 1
                ;;
            "starting"|"unknown")
                # 继续等待
                ;;
        esac
        
        sleep 3
        count=$((count + 3))
        
        if [ $((count % 15)) -eq 0 ]; then
            log_info "等待容器健康检查... (${count}s/${timeout}s) [状态: $health]"
        fi
    done
    
    log_error "容器健康检查超时 ($container_name)"
    return 1
}

# 等待所有 CI 服务就绪
wait_for_ci_services() {
    log_info "开始等待 CI 测试环境服务就绪..."
    
    # 等待数据库
    if ! wait_for_postgres localhost 5432 test_user test_db 90; then
        return 1
    fi
    
    # 等待 Redis
    if ! wait_for_redis localhost 6379 30; then
        return 1
    fi
    
    # 等待后端服务
    if ! wait_for_http "http://localhost:8000/health" 120; then
        return 1
    fi
    
    # 等待前端服务
    if ! wait_for_http "http://localhost:5173" 90; then
        return 1
    fi
    
    # 等待 Nginx 代理
    if ! wait_for_http "http://localhost:80/health" 60; then
        return 1
    fi
    
    log_success "所有 CI 服务已就绪"
    return 0
}

# 主函数
main() {
    case "${1:-all}" in
        "postgres")
            wait_for_postgres "${2:-localhost}" "${3:-5432}" "${4:-test_user}" "${5:-test_db}" "${6:-60}"
            ;;
        "redis")
            wait_for_redis "${2:-localhost}" "${3:-6379}" "${4:-30}"
            ;;
        "http")
            wait_for_http "$2" "${3:-60}" "${4:-200}"
            ;;
        "container")
            wait_for_container_health "$2" "${3:-120}"
            ;;
        "all"|"ci")
            wait_for_ci_services
            ;;
        *)
            echo "用法: $0 [postgres|redis|http|container|all|ci] [参数...]"
            echo ""
            echo "示例:"
            echo "  $0 postgres localhost 5432 test_user test_db 60"
            echo "  $0 redis localhost 6379 30"
            echo "  $0 http http://localhost:8000/health 60 200"
            echo "  $0 container ci-backend 120"
            echo "  $0 all  # 等待所有 CI 服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

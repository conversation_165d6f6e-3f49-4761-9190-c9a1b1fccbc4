#!/usr/bin/env python3
"""
CI 配置验证脚本
验证 CI 测试环境配置是否正确
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Set, Any

class CIConfigValidator:
    """CI 配置验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def validate_all(self) -> bool:
        """验证所有 CI 配置"""
        print("🔍 开始验证 CI 测试环境配置...")
        print("=" * 60)
        
        success = True
        
        # 验证 CI Docker Compose 配置
        success &= self.validate_ci_compose_config()
        
        # 验证 CI 环境变量
        success &= self.validate_ci_env_config()
        
        # 验证 GitHub Actions 配置
        success &= self.validate_github_actions_config()
        
        # 验证测试脚本
        success &= self.validate_test_scripts()
        
        # 验证 Dockerfile
        success &= self.validate_dockerfiles()
        
        # 输出结果
        self.print_results()
        
        return success
    
    def validate_ci_compose_config(self) -> bool:
        """验证 CI Docker Compose 配置"""
        print("🔍 验证 CI Docker Compose 配置...")
        
        compose_file = self.project_root / "docker/compose/ci/docker-compose.yml"
        
        if not compose_file.exists():
            self.errors.append("❌ CI Docker Compose 文件不存在")
            return False
        
        try:
            with open(compose_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证必需的服务
            required_services = ["postgres", "redis", "backend", "frontend"]
            services = config.get("services", {})
            
            missing_services = []
            for service in required_services:
                if service not in services:
                    missing_services.append(service)
            
            if missing_services:
                self.errors.append(f"❌ CI 配置缺少必需服务: {missing_services}")
                return False
            
            # 验证服务配置
            for service_name, service_config in services.items():
                if not self.validate_ci_service_config(service_config, service_name):
                    return False
            
            # 验证网络配置
            if "networks" not in config:
                self.warnings.append("⚠️ CI 配置缺少网络定义")
            
            # 验证卷配置
            if "volumes" not in config:
                self.warnings.append("⚠️ CI 配置缺少卷定义")
            
            self.info.append("✅ CI Docker Compose 配置验证通过")
            return True
            
        except yaml.YAMLError as e:
            self.errors.append(f"❌ CI Docker Compose 配置解析失败: {e}")
            return False
        except Exception as e:
            self.errors.append(f"❌ CI Docker Compose 配置验证失败: {e}")
            return False
    
    def validate_ci_service_config(self, service: Dict, name: str) -> bool:
        """验证 CI 服务配置"""
        # 验证健康检查
        if name in ["postgres", "redis", "backend", "frontend"] and "healthcheck" not in service:
            self.warnings.append(f"⚠️ CI 服务 {name} 缺少健康检查配置")
        
        # 验证环境变量
        if name == "backend" and "environment" not in service:
            self.errors.append(f"❌ CI 服务 {name} 缺少环境变量配置")
            return False
        
        # 验证依赖关系
        if name in ["backend", "frontend"] and "depends_on" not in service:
            self.warnings.append(f"⚠️ CI 服务 {name} 缺少依赖关系配置")
        
        return True
    
    def validate_ci_env_config(self) -> bool:
        """验证 CI 环境变量配置"""
        print("🔍 验证 CI 环境变量配置...")
        
        env_file = self.project_root / "docker/compose/ci/.env.ci"
        
        if not env_file.exists():
            self.errors.append("❌ CI 环境变量文件不存在")
            return False
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 验证必需的环境变量
            required_vars = [
                "DATABASE_URL",
                "REDIS_URL",
                "SECRET_KEY",
                "JWT_SECRET_KEY",
                "TESTING",
                "CI"
            ]
            
            missing_vars = []
            for var in required_vars:
                if f"{var}=" not in content:
                    missing_vars.append(var)
            
            if missing_vars:
                self.errors.append(f"❌ CI 环境变量缺少必需变量: {missing_vars}")
                return False
            
            # 验证测试环境特定配置
            if "TESTING=true" not in content:
                self.warnings.append("⚠️ CI 环境变量未设置 TESTING=true")
            
            if "CI=true" not in content:
                self.warnings.append("⚠️ CI 环境变量未设置 CI=true")
            
            # 验证安全性
            if "CHANGE_ME" in content:
                self.warnings.append("⚠️ CI 环境变量包含未更改的默认值")
            
            self.info.append("✅ CI 环境变量配置验证通过")
            return True
            
        except Exception as e:
            self.errors.append(f"❌ CI 环境变量配置验证失败: {e}")
            return False
    
    def validate_github_actions_config(self) -> bool:
        """验证 GitHub Actions 配置"""
        print("🔍 验证 GitHub Actions 配置...")
        
        workflow_file = self.project_root / ".github/workflows/main.yml"
        
        if not workflow_file.exists():
            self.errors.append("❌ GitHub Actions 工作流文件不存在")
            return False
        
        try:
            with open(workflow_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证作业配置
            jobs = config.get("jobs", {})
            
            # 检查是否有集成测试作业
            if "integration-test" not in jobs:
                self.warnings.append("⚠️ GitHub Actions 缺少集成测试作业")
            
            # 检查是否有 E2E 测试作业
            if "e2e-test" not in jobs:
                self.warnings.append("⚠️ GitHub Actions 缺少 E2E 测试作业")
            
            # 验证服务配置
            for job_name, job_config in jobs.items():
                if "test" in job_name.lower():
                    steps = job_config.get("steps", [])
                    
                    # 检查是否使用了正确的 Docker Compose 文件
                    compose_steps = [step for step in steps if "docker compose" in str(step.get("run", ""))]
                    
                    for step in compose_steps:
                        run_command = step.get("run", "")
                        if "docker/compose/ci/docker-compose.yml" not in run_command:
                            self.warnings.append(f"⚠️ 作业 {job_name} 可能未使用 CI 专用配置")
            
            self.info.append("✅ GitHub Actions 配置验证通过")
            return True
            
        except yaml.YAMLError as e:
            self.errors.append(f"❌ GitHub Actions 配置解析失败: {e}")
            return False
        except Exception as e:
            self.errors.append(f"❌ GitHub Actions 配置验证失败: {e}")
            return False
    
    def validate_test_scripts(self) -> bool:
        """验证测试脚本"""
        print("🔍 验证测试脚本...")
        
        required_scripts = [
            "scripts/testing/wait-for-services.sh",
            "scripts/testing/ci-test-runner.sh"
        ]
        
        missing_scripts = []
        for script_path in required_scripts:
            full_path = self.project_root / script_path
            if not full_path.exists():
                missing_scripts.append(script_path)
        
        if missing_scripts:
            self.errors.append(f"❌ 缺少测试脚本: {missing_scripts}")
            return False
        
        self.info.append("✅ 测试脚本验证通过")
        return True
    
    def validate_dockerfiles(self) -> bool:
        """验证 Dockerfile"""
        print("🔍 验证 CI Dockerfile...")
        
        required_dockerfiles = [
            "docker/backend/Dockerfile.ci",
            "docker/frontend/Dockerfile.ci",
            "docker/test/Dockerfile.test-runner"
        ]
        
        missing_dockerfiles = []
        for dockerfile_path in required_dockerfiles:
            full_path = self.project_root / dockerfile_path
            if not full_path.exists():
                missing_dockerfiles.append(dockerfile_path)
        
        if missing_dockerfiles:
            self.errors.append(f"❌ 缺少 CI Dockerfile: {missing_dockerfiles}")
            return False
        
        # 验证 Dockerfile 内容
        for dockerfile_path in required_dockerfiles:
            full_path = self.project_root / dockerfile_path
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查健康检查
                if "HEALTHCHECK" not in content and "backend" in dockerfile_path:
                    self.warnings.append(f"⚠️ {dockerfile_path} 缺少健康检查")
                
                # 检查测试依赖
                if "pytest" not in content and "backend" in dockerfile_path:
                    self.warnings.append(f"⚠️ {dockerfile_path} 可能缺少测试依赖")
                
            except Exception as e:
                self.warnings.append(f"⚠️ 无法验证 {dockerfile_path}: {e}")
        
        self.info.append("✅ CI Dockerfile 验证通过")
        return True
    
    def print_results(self):
        """输出验证结果"""
        print("\n" + "=" * 60)
        print("📊 CI 配置验证结果:")
        
        if self.info:
            print(f"\n✅ 成功信息 ({len(self.info)}):")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print(f"\n⚠️ 警告信息 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print(f"\n❌ 错误信息 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  {error}")
        
        # 总结
        total_issues = len(self.warnings) + len(self.errors)
        if total_issues == 0:
            print(f"\n🎉 CI 配置验证完全通过！")
            print("\n📋 下一步操作:")
            print("  1. 运行 'scripts/testing/ci-test-runner.sh all' 测试完整流程")
            print("  2. 在 GitHub Actions 中验证 CI 流程")
            print("  3. 查看测试结果和覆盖率报告")
        else:
            print(f"\n📈 发现 {total_issues} 个问题 (警告: {len(self.warnings)}, 错误: {len(self.errors)})")

def main():
    """主函数"""
    validator = CIConfigValidator()
    success = validator.validate_all()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

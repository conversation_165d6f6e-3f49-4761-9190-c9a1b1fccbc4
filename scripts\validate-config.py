#!/usr/bin/env python3
"""
Docker Compose 和 Nginx 配置验证脚本
验证所有环境的配置文件是否正确
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Set, Any
import re

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def validate_all(self) -> bool:
        """验证所有配置"""
        print("🔍 开始验证 Docker Compose 和 Nginx 配置...")
        print("=" * 60)
        
        success = True
        
        # 验证目录结构
        success &= self.validate_directory_structure()
        
        # 验证 Docker Compose 配置
        success &= self.validate_compose_configs()
        
        # 验证 Nginx 配置
        success &= self.validate_nginx_configs()
        
        # 验证环境变量文件
        success &= self.validate_env_files()
        
        # 验证端口冲突
        success &= self.validate_port_conflicts()
        
        # 输出结果
        self.print_results()
        
        return success
    
    def validate_directory_structure(self) -> bool:
        """验证目录结构"""
        print("🔍 验证目录结构...")
        
        required_dirs = [
            "docker/compose/local",
            "docker/compose/staging", 
            "docker/compose/production",
            "docker/nginx/templates",
            "docker/nginx/local",
            "docker/nginx/staging",
            "docker/nginx/production"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            self.errors.append(f"缺失目录: {missing_dirs}")
            return False
        
        self.info.append("✅ 目录结构验证通过")
        return True
    
    def validate_compose_configs(self) -> bool:
        """验证 Docker Compose 配置"""
        print("🔍 验证 Docker Compose 配置...")
        
        environments = ["local", "staging", "production"]
        success = True
        
        for env in environments:
            compose_file = self.project_root / f"docker/compose/{env}/docker-compose.yml"
            
            if not compose_file.exists():
                self.errors.append(f"Docker Compose 文件不存在: {compose_file}")
                success = False
                continue
            
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                # 验证基本结构
                if not self.validate_compose_structure(config, env):
                    success = False
                
                # 验证服务配置
                if not self.validate_compose_services(config, env):
                    success = False
                
                self.info.append(f"✅ {env} 环境 Docker Compose 配置验证通过")
                
            except yaml.YAMLError as e:
                self.errors.append(f"{env} 环境 Docker Compose 配置解析失败: {e}")
                success = False
            except Exception as e:
                self.errors.append(f"{env} 环境 Docker Compose 配置验证失败: {e}")
                success = False
        
        return success
    
    def validate_compose_structure(self, config: Dict, env: str) -> bool:
        """验证 Docker Compose 结构"""
        required_keys = ["version", "services"]
        
        for key in required_keys:
            if key not in config:
                self.errors.append(f"{env} 环境缺少必需的键: {key}")
                return False
        
        # 验证版本
        version = config.get("version", "")
        if not version.startswith("3."):
            self.warnings.append(f"{env} 环境使用的 Docker Compose 版本可能过旧: {version}")
        
        return True
    
    def validate_compose_services(self, config: Dict, env: str) -> bool:
        """验证 Docker Compose 服务配置"""
        services = config.get("services", {})
        
        # 验证必需服务
        required_services = ["backend", "postgres", "redis"]
        if env != "local":
            required_services.extend(["frontend", "nginx"])
        
        missing_services = []
        for service in required_services:
            if service not in services:
                missing_services.append(service)
        
        if missing_services:
            self.errors.append(f"{env} 环境缺少必需服务: {missing_services}")
            return False
        
        # 验证服务配置
        for service_name, service_config in services.items():
            if not self.validate_service_config(service_config, service_name, env):
                return False
        
        return True
    
    def validate_service_config(self, service: Dict, name: str, env: str) -> bool:
        """验证单个服务配置"""
        # 验证健康检查
        if name in ["backend", "postgres", "redis"] and "healthcheck" not in service:
            self.warnings.append(f"{env} 环境 {name} 服务缺少健康检查配置")
        
        # 验证重启策略
        if "restart" not in service and env != "local":
            self.warnings.append(f"{env} 环境 {name} 服务缺少重启策略")
        
        # 验证环境变量
        if name == "backend" and "environment" not in service:
            self.errors.append(f"{env} 环境 {name} 服务缺少环境变量配置")
            return False
        
        return True
    
    def validate_nginx_configs(self) -> bool:
        """验证 Nginx 配置"""
        print("🔍 验证 Nginx 配置...")
        
        environments = ["local", "staging", "production"]
        success = True
        
        for env in environments:
            nginx_file = self.project_root / f"docker/nginx/{env}/default.conf"
            
            if not nginx_file.exists():
                self.errors.append(f"Nginx 配置文件不存在: {nginx_file}")
                success = False
                continue
            
            try:
                with open(nginx_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not self.validate_nginx_content(content, env):
                    success = False
                
                self.info.append(f"✅ {env} 环境 Nginx 配置验证通过")
                
            except Exception as e:
                self.errors.append(f"{env} 环境 Nginx 配置验证失败: {e}")
                success = False
        
        return success
    
    def validate_nginx_content(self, content: str, env: str) -> bool:
        """验证 Nginx 配置内容"""
        # 验证必需的配置块
        required_blocks = ["upstream backend", "server {"]
        
        for block in required_blocks:
            if block not in content:
                self.errors.append(f"{env} 环境 Nginx 配置缺少: {block}")
                return False
        
        # 验证安全配置
        if env == "production":
            security_headers = [
                "add_header Strict-Transport-Security",
                "add_header X-Frame-Options",
                "add_header X-Content-Type-Options"
            ]
            
            for header in security_headers:
                if header not in content:
                    self.warnings.append(f"{env} 环境 Nginx 配置缺少安全头: {header}")
        
        # 验证 SSL 配置
        if env in ["staging", "production"]:
            ssl_configs = ["ssl_certificate", "ssl_certificate_key"]
            
            for ssl_config in ssl_configs:
                if ssl_config not in content:
                    self.warnings.append(f"{env} 环境 Nginx 配置缺少 SSL 配置: {ssl_config}")
        
        return True
    
    def validate_env_files(self) -> bool:
        """验证环境变量文件"""
        print("🔍 验证环境变量文件...")
        
        env_files = {
            "local": "docker/compose/local/.env.local",
            "staging": "docker/compose/staging/.env.staging.example", 
            "production": "docker/compose/production/.env.prod.example"
        }
        
        success = True
        
        for env, file_path in env_files.items():
            full_path = self.project_root / file_path
            
            if not full_path.exists():
                self.warnings.append(f"{env} 环境变量文件不存在: {file_path}")
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not self.validate_env_content(content, env):
                    success = False
                
                self.info.append(f"✅ {env} 环境变量文件验证通过")
                
            except Exception as e:
                self.errors.append(f"{env} 环境变量文件验证失败: {e}")
                success = False
        
        return success
    
    def validate_env_content(self, content: str, env: str) -> bool:
        """验证环境变量内容"""
        required_vars = [
            "DATABASE_URL",
            "REDIS_URL", 
            "SECRET_KEY",
            "JWT_SECRET_KEY"
        ]
        
        missing_vars = []
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
        
        if missing_vars:
            self.errors.append(f"{env} 环境缺少必需的环境变量: {missing_vars}")
            return False
        
        # 验证生产环境安全性
        if env == "production":
            if "CHANGE_ME" in content:
                self.warnings.append(f"{env} 环境包含未更改的默认值")
        
        return True
    
    def validate_port_conflicts(self) -> bool:
        """验证端口冲突"""
        print("🔍 验证端口冲突...")
        
        port_usage: Dict[str, List[str]] = {}
        
        environments = ["local", "staging", "production"]
        
        for env in environments:
            compose_file = self.project_root / f"docker/compose/{env}/docker-compose.yml"
            
            if not compose_file.exists():
                continue
            
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                services = config.get("services", {})
                
                for service_name, service_config in services.items():
                    ports = service_config.get("ports", [])
                    
                    for port_mapping in ports:
                        if isinstance(port_mapping, str):
                            host_port = port_mapping.split(":")[0]
                        else:
                            host_port = str(port_mapping)
                        
                        if host_port not in port_usage:
                            port_usage[host_port] = []
                        
                        port_usage[host_port].append(f"{env}:{service_name}")
                
            except Exception as e:
                self.warnings.append(f"无法检查 {env} 环境的端口配置: {e}")
        
        # 检查冲突
        conflicts = []
        for port, usages in port_usage.items():
            if len(usages) > 1:
                conflicts.append(f"端口 {port} 被多个服务使用: {usages}")
        
        if conflicts:
            self.warnings.extend(conflicts)
        else:
            self.info.append("✅ 端口配置验证通过，无冲突")
        
        return True
    
    def print_results(self):
        """输出验证结果"""
        print("\n" + "=" * 60)
        print("📊 验证结果:")
        
        if self.info:
            print(f"\n✅ 成功信息 ({len(self.info)}):")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print(f"\n⚠️ 警告信息 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print(f"\n❌ 错误信息 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  {error}")
        
        # 总结
        total_issues = len(self.warnings) + len(self.errors)
        if total_issues == 0:
            print(f"\n🎉 配置验证完全通过！")
        else:
            print(f"\n📈 发现 {total_issues} 个问题 (警告: {len(self.warnings)}, 错误: {len(self.errors)})")

def main():
    """主函数"""
    validator = ConfigValidator()
    success = validator.validate_all()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

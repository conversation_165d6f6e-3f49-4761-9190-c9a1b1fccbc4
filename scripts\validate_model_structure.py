#!/usr/bin/env python3
"""
数据模型目录结构验证脚本
确保模型文件结构清晰，避免导入混淆
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Set
import ast
import re

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ModelStructureValidator:
    """模型结构验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backend_dir = self.project_root / "backend"
        self.models_dir = self.backend_dir / "app" / "db" / "models"
        self.legacy_models_dir = self.backend_dir / "app" / "models"
        
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def validate_directory_structure(self) -> bool:
        """验证目录结构"""
        print("🔍 验证模型目录结构...")
        
        # 检查主模型目录是否存在
        if not self.models_dir.exists():
            self.errors.append(f"❌ 主模型目录不存在: {self.models_dir}")
            return False
        
        self.info.append(f"✅ 主模型目录存在: {self.models_dir}")
        
        # 检查统一导入文件是否存在
        init_file = self.models_dir / "__init__.py"
        if not init_file.exists():
            self.errors.append(f"❌ 统一导入文件不存在: {init_file}")
            return False
        
        self.info.append(f"✅ 统一导入文件存在: {init_file}")
        
        # 检查是否有备份文件
        backup_files = list(self.models_dir.glob("*.backup"))
        if backup_files:
            self.errors.append(f"❌ 模型目录包含备份文件: {backup_files}")
            return False
        
        self.info.append("✅ 模型目录无备份文件")
        
        # 检查遗留模型目录
        if self.legacy_models_dir.exists():
            legacy_files = [f for f in self.legacy_models_dir.iterdir() 
                          if f.is_file() and f.suffix == '.py' and f.name != '__init__.py']
            if legacy_files:
                self.warnings.append(f"⚠️ 遗留模型目录包含Python文件: {legacy_files}")
            else:
                self.info.append("✅ 遗留模型目录只包含统一导入文件")
        
        return True
    
    def validate_import_consistency(self) -> bool:
        """验证导入一致性"""
        print("🔍 验证导入一致性...")
        
        # 检查所有Python文件中的模型导入
        inconsistent_imports = []
        
        for py_file in self.backend_dir.rglob("*.py"):
            if self._should_skip_file(py_file):
                continue
                
            try:
                content = py_file.read_text(encoding='utf-8')
                
                # 检查是否有直接从 app.models.* 导入（除了统一导入）
                if py_file != self.legacy_models_dir / "__init__.py":
                    old_import_patterns = [
                        r'from app\.models\.[^.]+\s+import',
                        r'import app\.models\.[^.]+',
                    ]
                    
                    for pattern in old_import_patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            inconsistent_imports.append(f"{py_file}: {matches}")
                
            except Exception as e:
                self.warnings.append(f"⚠️ 无法读取文件 {py_file}: {e}")
        
        if inconsistent_imports:
            self.errors.append("❌ 发现不一致的模型导入:")
            for imp in inconsistent_imports:
                self.errors.append(f"   {imp}")
            return False
        
        self.info.append("✅ 模型导入一致性检查通过")
        return True
    
    def validate_model_exports(self) -> bool:
        """验证模型导出"""
        print("🔍 验证模型导出...")
        
        try:
            # 检查主模型目录的 __init__.py
            init_file = self.models_dir / "__init__.py"
            content = init_file.read_text(encoding='utf-8')
            
            # 解析 AST 获取导出的模型
            tree = ast.parse(content)
            
            exported_models = set()
            imported_models = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ImportFrom):
                    if node.names:
                        for alias in node.names:
                            imported_models.add(alias.name)
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id == "__all__":
                            if isinstance(node.value, ast.List):
                                for elt in node.value.elts:
                                    if isinstance(elt, ast.Str):
                                        exported_models.add(elt.s)
                                    elif isinstance(elt, ast.Constant):
                                        exported_models.add(elt.value)
            
            # 检查导入和导出是否一致
            missing_exports = imported_models - exported_models
            extra_exports = exported_models - imported_models
            
            if missing_exports:
                self.warnings.append(f"⚠️ 导入但未导出的模型: {missing_exports}")
            
            if extra_exports:
                self.warnings.append(f"⚠️ 导出但未导入的模型: {extra_exports}")
            
            if not missing_exports and not extra_exports:
                self.info.append("✅ 模型导入导出一致")
            
            return True
            
        except Exception as e:
            self.errors.append(f"❌ 验证模型导出时出错: {e}")
            return False
    
    def validate_model_files(self) -> bool:
        """验证模型文件"""
        print("🔍 验证模型文件...")
        
        model_files = [f for f in self.models_dir.iterdir() 
                      if f.is_file() and f.suffix == '.py' and f.name != '__init__.py']
        
        if not model_files:
            self.warnings.append("⚠️ 模型目录中没有模型文件")
            return True
        
        for model_file in model_files:
            try:
                content = model_file.read_text(encoding='utf-8')
                
                # 检查是否包含 SQLAlchemy 模型
                if 'class ' in content and 'Base' in content:
                    self.info.append(f"✅ 发现模型文件: {model_file.name}")
                else:
                    self.warnings.append(f"⚠️ 文件可能不包含模型: {model_file.name}")
                    
            except Exception as e:
                self.warnings.append(f"⚠️ 无法读取模型文件 {model_file}: {e}")
        
        return True
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            "__pycache__",
            ".git",
            "venv",
            "env",
            "node_modules",
            ".pytest_cache",
            "migrations",
            "alembic",
        ]
        
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("🚀 开始模型结构验证...")
        print("=" * 50)
        
        success = True
        
        # 运行各项验证
        success &= self.validate_directory_structure()
        success &= self.validate_import_consistency()
        success &= self.validate_model_exports()
        success &= self.validate_model_files()
        
        # 输出结果
        print("\n" + "=" * 50)
        print("📊 验证结果:")
        
        if self.info:
            print("\n✅ 成功信息:")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print("\n⚠️ 警告信息:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print("\n❌ 错误信息:")
            for error in self.errors:
                print(f"  {error}")
        
        print(f"\n🎯 验证结果: {'✅ 通过' if success else '❌ 失败'}")
        
        return success

def main():
    """主函数"""
    validator = ModelStructureValidator()
    success = validator.run_validation()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

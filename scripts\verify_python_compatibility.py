#!/usr/bin/env python3
"""
Python 兼容性验证脚本
验证关键依赖在当前 Python 版本下的兼容性
"""
import sys
import subprocess
import importlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# 关键依赖兼容性测试
CRITICAL_DEPENDENCIES = {
    "vnpy": {
        "import_test": "import vnpy",
        "version_check": "vnpy.__version__",
        "compatibility_test": """
try:
    from vnpy.trader.engine import MainEngine
    from vnpy.trader.ui import MainWindow
    print("✅ vnpy 核心模块导入成功")
except Exception as e:
    print(f"❌ vnpy 导入失败: {e}")
    raise
""",
        "max_python": (3, 11)
    },
    "talib": {
        "import_test": "import talib",
        "version_check": "talib.__version__",
        "compatibility_test": """
try:
    import talib
    import numpy as np
    # 测试基本技术指标计算
    close_prices = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], dtype=float)
    sma = talib.SMA(close_prices, timeperiod=5)
    rsi = talib.RSI(close_prices, timeperiod=5)
    print(f"✅ TA-Lib 计算测试成功: SMA={sma[-1]:.2f}, RSI={rsi[-1]:.2f}")
except Exception as e:
    print(f"❌ TA-Lib 计算失败: {e}")
    raise
""",
        "max_python": (3, 11)
    },
    "sqlalchemy": {
        "import_test": "import sqlalchemy",
        "version_check": "sqlalchemy.__version__",
        "compatibility_test": """
try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.ext.asyncio import create_async_engine
    # 测试异步引擎创建
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    print("✅ SQLAlchemy 异步引擎创建成功")
except Exception as e:
    print(f"❌ SQLAlchemy 异步功能失败: {e}")
    raise
""",
        "max_python": (3, 12)
    },
    "fastapi": {
        "import_test": "import fastapi",
        "version_check": "fastapi.__version__",
        "compatibility_test": """
try:
    from fastapi import FastAPI, APIRouter
    from fastapi.middleware.cors import CORSMiddleware
    app = FastAPI()
    router = APIRouter()
    print("✅ FastAPI 应用创建成功")
except Exception as e:
    print(f"❌ FastAPI 创建失败: {e}")
    raise
""",
        "max_python": (3, 12)
    },
    "redis": {
        "import_test": "import redis",
        "version_check": "redis.__version__",
        "compatibility_test": """
try:
    import redis.asyncio as redis_async
    # 测试异步 Redis 客户端创建
    client = redis_async.Redis.from_url("redis://localhost:6379", decode_responses=True)
    print("✅ Redis 异步客户端创建成功")
except Exception as e:
    print(f"❌ Redis 异步功能失败: {e}")
    raise
""",
        "max_python": (3, 12)
    }
}

def check_python_version() -> bool:
    """检查 Python 版本"""
    current_version = sys.version_info[:2]
    min_version = (3, 10)
    max_version = (3, 11)
    
    print(f"🐍 当前 Python 版本: {sys.version}")
    
    if current_version < min_version:
        logger.error(f"Python 版本过低: {current_version} < {min_version}")
        return False
    elif current_version > max_version:
        logger.warning(f"Python 版本过高: {current_version} > {max_version}")
        return False
    else:
        logger.info(f"✅ Python 版本兼容: {current_version}")
        return True

def test_dependency_import(dep_name: str, dep_info: Dict) -> Tuple[bool, Optional[str]]:
    """测试依赖导入"""
    try:
        exec(dep_info["import_test"])
        
        # 获取版本信息
        try:
            version = eval(dep_info["version_check"])
            logger.info(f"✅ {dep_name} 导入成功，版本: {version}")
        except:
            logger.info(f"✅ {dep_name} 导入成功")
        
        return True, None
    except ImportError as e:
        error_msg = f"❌ {dep_name} 导入失败: {e}"
        logger.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"❌ {dep_name} 测试异常: {e}"
        logger.error(error_msg)
        return False, error_msg

def test_dependency_functionality(dep_name: str, dep_info: Dict) -> Tuple[bool, Optional[str]]:
    """测试依赖功能"""
    try:
        exec(dep_info["compatibility_test"])
        logger.info(f"✅ {dep_name} 功能测试通过")
        return True, None
    except Exception as e:
        error_msg = f"❌ {dep_name} 功能测试失败: {e}"
        logger.error(error_msg)
        return False, error_msg

def check_version_compatibility(dep_name: str, dep_info: Dict) -> bool:
    """检查版本兼容性"""
    current_version = sys.version_info[:2]
    max_version = dep_info["max_python"]
    
    if current_version > max_version:
        logger.warning(f"⚠️ {dep_name} 可能不兼容 Python {current_version} (最大支持: {max_version})")
        return False
    return True

def generate_compatibility_report(results: Dict) -> None:
    """生成兼容性报告"""
    report_path = Path("reports/dependency_compatibility_report.md")
    report_path.parent.mkdir(exist_ok=True)
    
    current_version = sys.version_info[:3]
    
    report_content = f"""# 依赖兼容性验证报告

## 测试环境
- **Python 版本**: {'.'.join(map(str, current_version))}
- **测试时间**: {subprocess.check_output(['date'], text=True).strip()}

## 兼容性测试结果

| 依赖 | 导入测试 | 功能测试 | 版本兼容性 | 状态 |
|------|----------|----------|------------|------|
"""
    
    overall_status = "✅ 通过"
    
    for dep_name, result in results.items():
        import_status = "✅" if result["import_success"] else "❌"
        func_status = "✅" if result["function_success"] else "❌"
        version_status = "✅" if result["version_compatible"] else "⚠️"
        
        if not (result["import_success"] and result["function_success"]):
            overall_status = "❌ 失败"
        elif not result["version_compatible"]:
            overall_status = "⚠️ 警告"
        
        status = "正常" if result["import_success"] and result["function_success"] else "异常"
        
        report_content += f"| {dep_name} | {import_status} | {func_status} | {version_status} | {status} |\n"
    
    report_content += f"""
## 总体状态: {overall_status}

## 建议操作
"""
    
    if overall_status == "❌ 失败":
        report_content += """
1. **立即降级 Python 版本到 3.10.13**
2. 重新安装所有依赖
3. 运行完整测试套件验证
"""
    elif overall_status == "⚠️ 警告":
        report_content += """
1. 监控生产环境运行状况
2. 准备降级方案
3. 关注依赖更新进展
"""
    else:
        report_content += """
1. 当前配置最优，继续使用
2. 定期检查依赖更新
3. 监控新版本兼容性
"""
    
    report_path.write_text(report_content, encoding='utf-8')
    logger.info(f"📊 兼容性报告已生成: {report_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Python 依赖兼容性验证")
    print("=" * 60)
    
    # 检查 Python 版本
    python_ok = check_python_version()
    
    # 测试关键依赖
    results = {}
    all_passed = True
    
    for dep_name, dep_info in CRITICAL_DEPENDENCIES.items():
        print(f"\n📦 测试 {dep_name}...")
        
        # 版本兼容性检查
        version_compatible = check_version_compatibility(dep_name, dep_info)
        
        # 导入测试
        import_success, import_error = test_dependency_import(dep_name, dep_info)
        
        # 功能测试
        function_success = False
        function_error = None
        if import_success:
            function_success, function_error = test_dependency_functionality(dep_name, dep_info)
        
        results[dep_name] = {
            "version_compatible": version_compatible,
            "import_success": import_success,
            "function_success": function_success,
            "import_error": import_error,
            "function_error": function_error
        }
        
        if not (import_success and function_success):
            all_passed = False
    
    # 生成报告
    generate_compatibility_report(results)
    
    print("\n" + "=" * 60)
    
    if not python_ok or not all_passed:
        print("❌ 兼容性验证失败")
        print("🔧 建议执行: pyenv install 3.10.13 && pyenv local 3.10.13")
        sys.exit(1)
    else:
        print("✅ 兼容性验证通过")
        sys.exit(0)

if __name__ == "__main__":
    main()

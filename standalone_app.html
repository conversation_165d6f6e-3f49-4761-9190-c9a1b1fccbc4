<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 独立版</title>
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "Microsoft YaHei", Arial, sans-serif;
        }
        
        #app {
            min-height: 100vh;
            background: #f5f7fa;
        }
        
        .header-title {
            padding: 20px;
            color: white;
            font-size: 18px;
            text-align: center;
            background-color: #545c64;
        }
        
        .metric-card {
            margin-bottom: 20px;
        }
        
        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 侧边栏 -->
            <el-aside width="200px" style="background-color: #545c64;">
                <div class="header-title">量化投资平台</div>
                <el-menu
                    :default-active="currentPage"
                    background-color="#545c64"
                    text-color="#fff"
                    active-text-color="#ffd04b"
                    @select="handleMenuSelect"
                >
                    <el-menu-item index="dashboard">
                        <el-icon><data-analysis /></el-icon>
                        <span>仪表盘</span>
                    </el-menu-item>
                    <el-menu-item index="market">
                        <el-icon><trend-charts /></el-icon>
                        <span>市场行情</span>
                    </el-menu-item>
                    <el-menu-item index="trading">
                        <el-icon><coin /></el-icon>
                        <span>交易中心</span>
                    </el-menu-item>
                    <el-menu-item index="strategy">
                        <el-icon><cpu /></el-icon>
                        <span>策略中心</span>
                    </el-menu-item>
                    <el-menu-item index="risk">
                        <el-icon><warning /></el-icon>
                        <span>风险管理</span>
                    </el-menu-item>
                    <el-menu-item index="portfolio">
                        <el-icon><pie-chart /></el-icon>
                        <span>投资组合</span>
                    </el-menu-item>
                </el-menu>
            </el-aside>
            
            <!-- 主内容区 -->
            <el-container>
                <!-- 顶部导航栏 -->
                <el-header style="background-color: white; display: flex; align-items: center; justify-content: space-between; border-bottom: 1px solid #e4e7ed; height: 60px;">
                    <div style="font-size: 20px; font-weight: bold;">{{ pageTitle }}</div>
                    <div style="display: flex; align-items: center; gap: 20px;">
                        <el-badge :value="notifications">
                            <el-icon :size="20"><bell /></el-icon>
                        </el-badge>
                        <el-dropdown>
                            <span style="cursor: pointer; display: flex; align-items: center; gap: 10px;">
                                <el-avatar :size="32">U</el-avatar>
                                <span>管理员</span>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>个人中心</el-dropdown-item>
                                    <el-dropdown-item>系统设置</el-dropdown-item>
                                    <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>
                
                <!-- 主要内容 -->
                <el-main style="padding: 20px;">
                    <!-- 仪表盘页面 -->
                    <div v-if="currentPage === 'dashboard'">
                        <el-row :gutter="20">
                            <el-col :span="6" v-for="stat in dashboardStats" :key="stat.title">
                                <el-card class="metric-card">
                                    <el-statistic 
                                        :title="stat.title" 
                                        :value="stat.value" 
                                        :prefix="stat.prefix"
                                        :suffix="stat.suffix"
                                        :value-style="{ color: stat.color }"
                                    />
                                </el-card>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20" style="margin-top: 20px;">
                            <el-col :span="12">
                                <el-card>
                                    <template #header>
                                        <div>收益曲线</div>
                                    </template>
                                    <div class="chart-placeholder">收益曲线图表</div>
                                </el-card>
                            </el-col>
                            <el-col :span="12">
                                <el-card>
                                    <template #header>
                                        <div>持仓分布</div>
                                    </template>
                                    <div class="chart-placeholder">持仓分布饼图</div>
                                </el-card>
                            </el-col>
                        </el-row>
                        
                        <el-card style="margin-top: 20px;">
                            <template #header>
                                <div>最新交易记录</div>
                            </template>
                            <el-table :data="recentTrades" style="width: 100%">
                                <el-table-column prop="time" label="时间" width="180" />
                                <el-table-column prop="code" label="代码" width="100" />
                                <el-table-column prop="name" label="名称" />
                                <el-table-column prop="type" label="类型" width="80">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.type === '买入' ? 'success' : 'danger'" size="small">
                                            {{ scope.row.type }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="price" label="价格" align="right" />
                                <el-table-column prop="quantity" label="数量" align="right" />
                                <el-table-column prop="amount" label="金额" align="right" />
                                <el-table-column prop="profit" label="盈亏" align="right">
                                    <template #default="scope">
                                        <span :style="{ color: scope.row.profit > 0 ? '#67c23a' : '#f56c6c' }">
                                            {{ scope.row.profit > 0 ? '+' : '' }}{{ scope.row.profit }}
                                        </span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                    
                    <!-- 市场行情页面 -->
                    <div v-else-if="currentPage === 'market'">
                        <el-card>
                            <template #header>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>实时行情</span>
                                    <el-input v-model="searchStock" placeholder="搜索股票" style="width: 200px;" clearable />
                                </div>
                            </template>
                            <el-table :data="filteredStocks" style="width: 100%">
                                <el-table-column prop="code" label="代码" width="100" sortable />
                                <el-table-column prop="name" label="名称" width="120" />
                                <el-table-column prop="price" label="现价" align="right" sortable />
                                <el-table-column prop="change" label="涨跌" align="right">
                                    <template #default="scope">
                                        <span :style="{ color: scope.row.change > 0 ? '#f56c6c' : '#67c23a' }">
                                            {{ scope.row.change > 0 ? '+' : '' }}{{ scope.row.change }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="changePercent" label="涨跌幅" align="right">
                                    <template #default="scope">
                                        <span :style="{ color: scope.row.changePercent > 0 ? '#f56c6c' : '#67c23a' }">
                                            {{ scope.row.changePercent > 0 ? '+' : '' }}{{ scope.row.changePercent }}%
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="volume" label="成交量" align="right" />
                                <el-table-column prop="amount" label="成交额" align="right" />
                                <el-table-column label="操作" width="150">
                                    <template #default="scope">
                                        <el-button type="primary" size="small" @click="handleBuy(scope.row)">买入</el-button>
                                        <el-button type="danger" size="small" @click="handleSell(scope.row)">卖出</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                    
                    <!-- 交易中心页面 -->
                    <div v-else-if="currentPage === 'trading'">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-card>
                                    <template #header>下单交易</template>
                                    <el-form :model="orderForm" label-width="100px">
                                        <el-form-item label="股票代码">
                                            <el-input v-model="orderForm.code" placeholder="请输入股票代码" />
                                        </el-form-item>
                                        <el-form-item label="股票名称">
                                            <el-input v-model="orderForm.name" disabled />
                                        </el-form-item>
                                        <el-form-item label="交易类型">
                                            <el-radio-group v-model="orderForm.type">
                                                <el-radio-button label="buy">买入</el-radio-button>
                                                <el-radio-button label="sell">卖出</el-radio-button>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item label="委托价格">
                                            <el-input-number v-model="orderForm.price" :precision="2" :step="0.01" :min="0" />
                                        </el-form-item>
                                        <el-form-item label="委托数量">
                                            <el-input-number v-model="orderForm.quantity" :step="100" :min="100" />
                                        </el-form-item>
                                        <el-form-item label="预计金额">
                                            <el-input :value="orderAmount" disabled />
                                        </el-form-item>
                                        <el-form-item>
                                            <el-button type="primary" @click="submitOrder">提交订单</el-button>
                                            <el-button @click="resetOrder">重置</el-button>
                                        </el-form-item>
                                    </el-form>
                                </el-card>
                            </el-col>
                            <el-col :span="12">
                                <el-card>
                                    <template #header>当前持仓</template>
                                    <el-table :data="positions" style="width: 100%">
                                        <el-table-column prop="code" label="代码" width="80" />
                                        <el-table-column prop="name" label="名称" />
                                        <el-table-column prop="quantity" label="数量" align="right" />
                                        <el-table-column prop="cost" label="成本" align="right" />
                                        <el-table-column prop="current" label="现价" align="right" />
                                        <el-table-column prop="profit" label="盈亏" align="right">
                                            <template #default="scope">
                                                <span :style="{ color: scope.row.profit > 0 ? '#67c23a' : '#f56c6c' }">
                                                    {{ scope.row.profit > 0 ? '+' : '' }}{{ scope.row.profit }}%
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-card>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <!-- 其他页面 -->
                    <div v-else>
                        <el-card>
                            <el-empty :description="`${pageTitle}功能开发中...`" />
                        </el-card>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    currentPage: 'dashboard',
                    pageTitle: '仪表盘',
                    notifications: 3,
                    searchStock: '',
                    
                    // 仪表盘数据
                    dashboardStats: [
                        { title: '总资产', value: 1234567.89, prefix: '¥', color: '#409EFF' },
                        { title: '今日盈亏', value: 12345.67, prefix: '¥', color: '#67c23a' },
                        { title: '持仓数量', value: 15, suffix: '只' },
                        { title: '胜率', value: 68.5, suffix: '%' }
                    ],
                    
                    // 最近交易
                    recentTrades: [
                        { time: '2025-01-11 09:30:00', code: '000001', name: '平安银行', type: '买入', price: 10.50, quantity: 1000, amount: 10500, profit: 230 },
                        { time: '2025-01-11 09:35:00', code: '000002', name: '万科A', type: '卖出', price: 15.20, quantity: 500, amount: 7600, profit: -150 },
                        { time: '2025-01-11 10:00:00', code: '000858', name: '五粮液', type: '买入', price: 168.50, quantity: 200, amount: 33700, profit: 1200 }
                    ],
                    
                    // 股票列表
                    stockList: [
                        { code: '000001', name: '平安银行', price: 10.50, change: 0.23, changePercent: 2.3, volume: '1.2亿', amount: '12.6亿' },
                        { code: '000002', name: '万科A', price: 15.20, change: -0.22, changePercent: -1.5, volume: '8500万', amount: '12.9亿' },
                        { code: '000858', name: '五粮液', price: 168.50, change: 1.35, changePercent: 0.8, volume: '2300万', amount: '38.8亿' },
                        { code: '600519', name: '贵州茅台', price: 1680.00, change: 20.00, changePercent: 1.2, volume: '350万', amount: '588亿' },
                        { code: '000333', name: '美的集团', price: 58.30, change: -0.50, changePercent: -0.85, volume: '5600万', amount: '32.6亿' }
                    ],
                    
                    // 订单表单
                    orderForm: {
                        code: '',
                        name: '',
                        type: 'buy',
                        price: 0,
                        quantity: 100
                    },
                    
                    // 持仓数据
                    positions: [
                        { code: '000001', name: '平安银行', quantity: 1000, cost: 10.20, current: 10.50, profit: 2.94 },
                        { code: '000858', name: '五粮液', quantity: 200, cost: 165.00, current: 168.50, profit: 2.12 },
                        { code: '600519', name: '贵州茅台', quantity: 10, cost: 1650.00, current: 1680.00, profit: 1.82 }
                    ]
                }
            },
            
            computed: {
                filteredStocks() {
                    if (!this.searchStock) return this.stockList;
                    return this.stockList.filter(stock => 
                        stock.code.includes(this.searchStock) || 
                        stock.name.includes(this.searchStock)
                    );
                },
                
                orderAmount() {
                    return (this.orderForm.price * this.orderForm.quantity).toFixed(2);
                }
            },
            
            methods: {
                handleMenuSelect(index) {
                    this.currentPage = index;
                    const titles = {
                        'dashboard': '仪表盘',
                        'market': '市场行情',
                        'trading': '交易中心',
                        'strategy': '策略中心',
                        'risk': '风险管理',
                        'portfolio': '投资组合'
                    };
                    this.pageTitle = titles[index] || '未知页面';
                },
                
                handleBuy(stock) {
                    this.currentPage = 'trading';
                    this.pageTitle = '交易中心';
                    this.orderForm.code = stock.code;
                    this.orderForm.name = stock.name;
                    this.orderForm.price = stock.price;
                    this.orderForm.type = 'buy';
                },
                
                handleSell(stock) {
                    this.currentPage = 'trading';
                    this.pageTitle = '交易中心';
                    this.orderForm.code = stock.code;
                    this.orderForm.name = stock.name;
                    this.orderForm.price = stock.price;
                    this.orderForm.type = 'sell';
                },
                
                submitOrder() {
                    const typeText = this.orderForm.type === 'buy' ? '买入' : '卖出';
                    ElMessage.success(`${typeText}订单提交成功（模拟）`);
                    this.resetOrder();
                },
                
                resetOrder() {
                    this.orderForm = {
                        code: '',
                        name: '',
                        type: 'buy',
                        price: 0,
                        quantity: 100
                    };
                },
                
                handleLogout() {
                    ElMessage.info('已退出登录（模拟）');
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
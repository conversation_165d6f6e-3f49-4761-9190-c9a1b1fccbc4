@echo off
echo ========================================
echo 启动修复后的量化交易平台
echo ========================================

echo.
echo 第1步: 检查目录...
if not exist "frontend" (
    echo 错误: 前端目录不存在!
    pause
    exit /b 1
)
if not exist "backend" (
    echo 错误: 后端目录不存在!
    pause
    exit /b 1
)

echo 目录检查完成.

echo.
echo 第2步: 启动简化后端服务器 (端口8001)...
start "后端服务器" cmd /k "cd backend && python simple_http_server.py"
timeout /t 3 /nobreak >nul

echo.
echo 第3步: 启动前端开发服务器 (端口5173)...
start "前端服务器" cmd /k "cd frontend && pnpm run dev"
timeout /t 5 /nobreak >nul

echo.
echo 第4步: 打开浏览器...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo ========================================
echo 平台启动完成!
echo 前端: http://localhost:5173
echo 后端: http://localhost:8001
echo 后端健康检查: http://localhost:8001/health
echo ========================================
echo.
echo 两个服务器将在单独的窗口中运行
echo 等待几秒钟让服务器完全启动
echo ========================================
pause

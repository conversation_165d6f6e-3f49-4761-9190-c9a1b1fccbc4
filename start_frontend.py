#!/usr/bin/env python3
"""
前端服务启动脚本
"""

import subprocess
import sys
import os
import time

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    # 切换到前端目录
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print("❌ 前端目录不存在")
        return False
    
    try:
        # 启动前端开发服务器
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=True
        )
        
        print("✅ 前端服务启动中...")
        print("📍 服务地址: http://localhost:5173")
        print("⏳ 等待服务启动...")
        
        # 等待几秒让服务启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 前端服务启动成功")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 前端服务启动失败")
            print(f"输出: {stdout}")
            print(f"错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动前端服务时发生错误: {e}")
        return False

if __name__ == "__main__":
    start_frontend()

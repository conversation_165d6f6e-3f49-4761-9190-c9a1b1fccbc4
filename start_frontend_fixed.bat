@echo off
echo ========================================
echo    量化投资平台 - 前端修复版启动
echo ========================================
echo.

cd /d "%~dp0\frontend"

echo [1/3] 检查Node.js环境...
node -v >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)
echo ✅ Node.js已安装

echo.
echo [2/3] 安装依赖...
if not exist "node_modules" (
    echo 正在安装依赖包...
    call npm install
) else (
    echo ✅ 依赖已安装
)

echo.
echo [3/3] 启动前端服务...
echo.
echo ========================================
echo 前端地址: http://localhost:5173
echo API文档: http://localhost:8000/docs
echo ========================================
echo.
echo 按 Ctrl+C 停止服务
echo.

:: 使用修复后的入口文件启动
call npx vite --host

pause
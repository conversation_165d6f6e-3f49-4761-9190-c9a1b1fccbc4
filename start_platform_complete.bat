@echo off
echo ========================================
echo     Quantum Investment Platform
echo       Complete System Startup
echo ========================================
echo.

:: Check Python and Node.js installation
echo Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    pause
    exit /b 1
)

echo Requirements OK
echo.

:: Start Backend Server
echo ========================================
echo Starting Backend Server...
echo ========================================
cd backend
start cmd /k "python app/main_fixed.py"
echo Backend starting on http://localhost:8000
timeout /t 3 /nobreak >nul

:: Start Frontend Server
echo.
echo ========================================
echo Starting Frontend Server...
echo ========================================
cd ..\frontend

:: Install dependencies if needed
if not exist node_modules (
    echo Installing frontend dependencies...
    call npm install
)

:: Copy the full main.ts to main.ts
echo Activating full Vue application...
copy /Y src\main_full.ts src\main.ts >nul 2>&1

:: Start Vite dev server
start cmd /k "npm run dev"
echo Frontend starting on http://localhost:5173
echo.

:: Wait for services to start
echo ========================================
echo Waiting for services to start...
timeout /t 5 /nobreak >nul

:: Display access information
cls
echo ========================================
echo   Quantum Investment Platform Started
echo ========================================
echo.
echo Access Points:
echo --------------
echo Frontend:    http://localhost:5173
echo Backend API: http://localhost:8000
echo API Docs:    http://localhost:8000/docs
echo.
echo Services Status:
echo ----------------
echo [OK] Backend Server  - Port 8000
echo [OK] Frontend Server - Port 5173
echo [OK] Database        - SQLite
echo [OK] WebSocket       - Ready
echo.
echo Press any key to open the platform in browser...
pause >nul

:: Open browser
start http://localhost:5173

echo.
echo Platform is running. Close this window to stop all services.
pause
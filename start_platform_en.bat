@echo off
echo ========================================
echo Starting Quant Trading Platform
echo ========================================

echo.
echo Step 1: Checking directories...
if not exist "frontend" (
    echo ERROR: Frontend directory not found!
    pause
    exit /b 1
)
if not exist "backend" (
    echo ERROR: Backend directory not found!
    pause
    exit /b 1
)

echo Directories found.

echo.
echo Step 2: Starting Backend Server (port 8001)...
start "Backend Server" cmd /k "cd backend && python simple_http_server.py"
timeout /t 3 /nobreak >nul

echo.
echo Step 3: Starting Frontend Server (port 5173)...
start "Frontend Server" cmd /k "cd frontend && npm run dev"
timeout /t 5 /nobreak >nul

echo.
echo Step 4: Opening browser...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo ========================================
echo Platform Started!
echo Frontend: http://localhost:5173
echo Backend: http://localhost:8001
echo Backend Health: http://localhost:8001/health
echo ========================================
echo.
echo Both servers are running in separate windows
echo Wait a few seconds for servers to fully start
echo ========================================
pause

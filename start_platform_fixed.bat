@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ===================================
echo 量化投资平台 - 修复版启动脚本
echo ===================================
echo.

:: 检查环境
echo [1/4] 检查运行环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请安装 Python 3.9+
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请安装 Node.js 16+
    pause
    exit /b 1
)

echo ✅ 环境检查通过

:: 停止现有服务
echo.
echo [2/4] 停止现有服务...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

:: 启动后端
echo.
echo [3/4] 启动后端服务...
cd backend
start /min /b python -m uvicorn main_fixed:app --host 0.0.0.0 --port 8000 --reload

:: 等待后端启动
echo 等待后端服务启动...
set /a count=0
:wait_backend
timeout /t 2 >nul
set /a count+=1
curl -f http://localhost:8000/api/health >nul 2>&1
if errorlevel 1 (
    if !count! geq 10 (
        echo ❌ 后端服务启动失败
        pause
        exit /b 1
    )
    echo .
    goto wait_backend
)
echo ✅ 后端服务启动成功

:: 启动前端
cd ..\frontend
echo.
echo [4/4] 启动前端服务...
start /min /b npm run dev

:: 等待前端启动
echo 等待前端服务启动...
set /a count=0
:wait_frontend
timeout /t 3 >nul
set /a count+=1
curl -f http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    if !count! geq 15 (
        echo ❌ 前端服务启动失败
        pause
        exit /b 1
    )
    echo .
    goto wait_frontend
)
echo ✅ 前端服务启动成功

:: 显示完成信息
cd ..
echo.
echo =====================================
echo ✅ 量化投资平台启动完成！
echo =====================================
echo.
echo 📊 服务地址:
echo    前端应用: http://localhost:5173
echo    后端API: http://localhost:8000
echo    API文档: http://localhost:8000/docs
echo    健康检查: http://localhost:8000/api/health
echo.
echo 📊 测试页面:
echo    主页: http://localhost:5173
echo    交易终端: http://localhost:5173/trading-terminal.html
echo    市场数据: http://localhost:5173/market-charts.html
echo    API测试: http://localhost:5173/api-test.html
echo.
echo 🔧 管理:
echo    本窗口可以关闭，服务在后台运行
echo    要停止服务，请运行 stop_platform.bat
echo.

:: 自动打开浏览器
timeout /t 3 >nul
start http://localhost:5173

echo 按任意键关闭此窗口...
pause >nul
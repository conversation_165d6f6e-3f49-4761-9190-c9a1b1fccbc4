#!/usr/bin/env python3
"""
简单的前端启动脚本
"""

import subprocess
import os
import time
import sys

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    # 确保在正确的目录
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print("❌ 前端目录不存在")
        return False
    
    # 切换到前端目录
    os.chdir(frontend_dir)
    print(f"📂 当前目录: {os.getcwd()}")
    
    # 检查package.json
    if not os.path.exists("package.json"):
        print("❌ package.json不存在")
        return False
    
    print("✅ package.json存在")
    
    # 检查node_modules
    if not os.path.exists("node_modules"):
        print("⚠️ node_modules不存在，正在安装依赖...")
        try:
            subprocess.run(["npm", "install"], check=True, shell=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False
    
    print("✅ node_modules存在")
    
    # 启动开发服务器
    try:
        print("🔄 启动Vite开发服务器...")
        
        # 使用subprocess.Popen以便可以监控输出
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            shell=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("⏳ 等待服务启动...")
        
        # 监控输出，查找启动成功的标志
        startup_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < startup_timeout:
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                print(f"❌ 进程意外退出")
                print(f"输出: {stdout}")
                return False
            
            # 检查是否有输出
            try:
                line = process.stdout.readline()
                if line:
                    print(f"📝 {line.strip()}")
                    
                    # 检查启动成功的标志
                    if "Local:" in line and "5173" in line:
                        print("✅ 前端服务启动成功!")
                        print(f"🌐 访问地址: http://localhost:5173")
                        return True
                    elif "ready in" in line.lower():
                        print("✅ 前端服务启动成功!")
                        return True
                        
            except:
                pass
            
            time.sleep(0.5)
        
        print("❌ 启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    success = start_frontend()
    if success:
        print("🎉 前端服务启动成功")
        print("💡 请保持此窗口打开")
        input("按Enter键退出...")
    else:
        print("❌ 前端服务启动失败")
        sys.exit(1)

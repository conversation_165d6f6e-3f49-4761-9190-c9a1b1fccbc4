@echo off
echo Starting Trading Center Platform...
echo ===========================================

echo.
echo Step 1: Checking directories...
if not exist "frontend" (
    echo ERROR: Frontend directory not found!
    pause
    exit /b 1
)
if not exist "backend" (
    echo ERROR: Backend directory not found!
    pause
    exit /b 1
)

echo Frontend and backend directories found.

echo.
echo Step 2: Starting Backend Server...
start "Trading Center Backend" cmd /k "cd backend && python app/main.py"
timeout /t 5 /nobreak >nul

echo.
echo Step 3: Starting Frontend Server...
start "Trading Center Frontend" cmd /k "cd frontend && npm run dev"
timeout /t 3 /nobreak >nul

echo.
echo Step 4: Opening browser...
timeout /t 8 /nobreak >nul
start http://localhost:5173

echo.
echo ===========================================
echo Trading Center Platform Starting...
echo Frontend: http://localhost:5173
echo Backend: http://localhost:8000
echo.
echo Both servers should be starting in separate windows.
echo Wait a few moments for the servers to fully start.
echo ===========================================
pause
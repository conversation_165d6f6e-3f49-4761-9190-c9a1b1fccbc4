@echo off
echo ================================================================================
echo                     STOPPING QUANTUM INVESTMENT PLATFORM
echo ================================================================================
echo.

echo Stopping services...

:: Kill Python processes (backend)
taskkill /F /IM python.exe /T >nul 2>&1
if errorlevel 1 (
    echo No Python processes to stop
) else (
    echo [OK] Backend services stopped
)

:: Kill Node processes (frontend)
taskkill /F /IM node.exe /T >nul 2>&1
if errorlevel 1 (
    echo No Node processes to stop
) else (
    echo [OK] Frontend services stopped
)

echo.
echo All services have been stopped.
echo.
pause
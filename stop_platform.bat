@echo off
chcp 65001 >nul

echo ===================================
echo 量化投资平台 - 停止服务
echo ===================================
echo.

echo 正在停止所有服务...

:: 停止Python进程（后端）
taskkill /f /im python.exe >nul 2>&1
echo ✅ 后端服务已停止

:: 停止Node.js进程（前端）
taskkill /f /im node.exe >nul 2>&1
echo ✅ 前端服务已停止

:: 停止可能的uvicorn进程
tasklist | findstr uvicorn >nul 2>&1
if not errorlevel 1 (
    taskkill /f /im uvicorn.exe >nul 2>&1
)

echo.
echo ===================================
echo ✅ 所有服务已停止
echo ===================================
echo.
pause
@echo off
chcp 65001 >nul
echo 量化投资平台 - 环境管理测试

echo.
echo 测试配置验证...
python scripts\validate-config.py

echo.
echo 测试 Docker Compose 配置语法...
docker compose -f docker\compose\local\docker-compose.yml config >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 本地环境配置语法正确
) else (
    echo ❌ 本地环境配置语法错误
)

docker compose -f docker\compose\staging\docker-compose.yml config >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 测试环境配置语法正确
) else (
    echo ❌ 测试环境配置语法错误
)

docker compose -f docker\compose\production\docker-compose.yml config >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 生产环境配置语法正确
) else (
    echo ❌ 生产环境配置语法错误
)

echo.
echo 检查环境变量文件...
if exist "docker\compose\local\.env.local" (
    echo ✅ 本地环境变量模板存在
) else (
    echo ❌ 本地环境变量模板缺失
)

if exist "docker\compose\staging\.env.staging.example" (
    echo ✅ 测试环境变量模板存在
) else (
    echo ❌ 测试环境变量模板缺失
)

if exist "docker\compose\production\.env.prod.example" (
    echo ✅ 生产环境变量模板存在
) else (
    echo ❌ 生产环境变量模板缺失
)

echo.
echo 检查 Nginx 配置文件...
if exist "docker\nginx\local\default.conf" (
    echo ✅ 本地 Nginx 配置存在
) else (
    echo ❌ 本地 Nginx 配置缺失
)

if exist "docker\nginx\staging\default.conf" (
    echo ✅ 测试 Nginx 配置存在
) else (
    echo ❌ 测试 Nginx 配置缺失
)

if exist "docker\nginx\production\default.conf" (
    echo ✅ 生产 Nginx 配置存在
) else (
    echo ❌ 生产 Nginx 配置缺失
)

if exist "docker\nginx\templates\frontend.conf.j2" (
    echo ✅ Nginx 模板存在
) else (
    echo ❌ Nginx 模板缺失
)

echo.
echo 检查管理工具...
if exist "scripts\env-switch.sh" (
    echo ✅ 环境切换脚本存在
) else (
    echo ❌ 环境切换脚本缺失
)

if exist "scripts\validate-config.py" (
    echo ✅ 配置验证脚本存在
) else (
    echo ❌ 配置验证脚本缺失
)

if exist "scripts\migrate-docker-configs.py" (
    echo ✅ 配置迁移脚本存在
) else (
    echo ❌ 配置迁移脚本缺失
)

if exist "Makefile" (
    echo ✅ Makefile 存在
) else (
    echo ❌ Makefile 缺失
)

if exist "docs\deployment\STANDARD_SETUP.md" (
    echo ✅ 标准化部署文档存在
) else (
    echo ❌ 标准化部署文档缺失
)

echo.
echo 🎉 Docker Compose 与 Nginx 配置统一检查完成！
pause

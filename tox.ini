[tox]
envlist = py310, py311, py312-experimental
skipsdist = true
isolated_build = false

[testenv]
deps = 
    pytest
    pytest-cov
    pytest-asyncio
    -r{toxinidir}/backend/requirements.txt

commands = 
    python scripts/check_python.py
    pytest backend/app/tests/ -v --cov=backend/app --cov-report=term-missing

[testenv:py310]
# Python 3.10 - 推荐生产版本
basepython = python3.10
deps = 
    {[testenv]deps}
    # 所有依赖都支持
    vnpy==3.9.1
    TA-Lib==0.4.28

[testenv:py311]
# Python 3.11 - 支持版本
basepython = python3.11
deps = 
    {[testenv]deps}
    # 关键依赖仍然支持
    vnpy==3.9.1
    TA-Lib==0.4.28

[testenv:py312-experimental]
# Python 3.12 - 实验性支持
basepython = python3.12
deps = 
    {[testenv]deps}
    # 跳过不兼容的依赖
    # vnpy - 不支持 3.12
    # TA-Lib - 可能需要源码编译
    pandas-ta==0.3.14b0  # TA-Lib 替代

commands = 
    python scripts/check_python.py
    # 运行不依赖 vnpy/TA-Lib 的测试
    pytest backend/app/tests/ -v -k "not (vnpy or talib)" --cov=backend/app --cov-report=term-missing

[testenv:compatibility-check]
# 兼容性检查环境
deps = 
    pip-audit
    safety
    pipdeptree

commands = 
    python scripts/verify_python_compatibility.py
    pip-audit --desc --ignore-vuln "PYSEC-2024-999"
    safety check --json
    pipdeptree --warn silence

[testenv:lint]
# 代码质量检查
deps = 
    flake8
    mypy
    black
    isort

commands = 
    flake8 backend/app --count --select=E9,F63,F7,F82 --show-source --statistics
    mypy backend/app --ignore-missing-imports
    black --check backend/app
    isort --check-only backend/app

[flake8]
max-line-length = 127
exclude = 
    .git,
    __pycache__,
    .tox,
    .venv,
    venv,
    backend/venv,
    frontend/node_modules,
    frontend/dist

[coverage:run]
source = backend/app
omit = 
    */tests/*
    */venv/*
    */__pycache__/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError

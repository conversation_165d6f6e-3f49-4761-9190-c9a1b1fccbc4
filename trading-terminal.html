<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易终端 - 量化投资平台</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
        }
        
        .trading-terminal {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #409eff;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            padding: 10px;
            gap: 10px;
        }
        
        .left-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .center-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .right-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        
        .order-form {
            margin-top: 20px;
        }
        
        .market-data {
            margin-bottom: 20px;
        }
        
        .price-display {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            text-align: center;
            margin: 10px 0;
        }
        
        .price-up {
            color: #e74c3c;
        }
        
        .price-down {
            color: #27ae60;
        }
        
        .order-book {
            margin-top: 20px;
        }
        
        .order-book table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .order-book th,
        .order-book td {
            padding: 5px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .bid {
            color: #27ae60;
        }
        
        .ask {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="trading-terminal">
            <!-- 头部 -->
            <div class="header">
                <h2>交易终端</h2>
                <div>
                    <el-button type="primary" size="small" @click="connectMarket">
                        {{ connected ? '已连接' : '连接市场' }}
                    </el-button>
                    <el-button size="small" @click="goBack">返回主页</el-button>
                </div>
            </div>
            
            <!-- 主要内容 -->
            <div class="main-content">
                <!-- 左侧面板 - 股票搜索和订单表单 -->
                <div class="left-panel">
                    <h3>股票搜索</h3>
                    <el-input
                        v-model="searchKeyword"
                        placeholder="输入股票代码或名称"
                        @input="searchStocks"
                        clearable
                    >
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                    
                    <div v-if="searchResults.length > 0" style="margin-top: 10px;">
                        <el-card v-for="stock in searchResults" :key="stock.code" 
                                 class="stock-item" @click="selectStock(stock)"
                                 style="margin-bottom: 5px; cursor: pointer;">
                            <div>{{ stock.code }} - {{ stock.name }}</div>
                            <div style="color: #666; font-size: 12px;">{{ stock.price }}</div>
                        </el-card>
                    </div>
                    
                    <!-- 订单表单 -->
                    <div class="order-form" v-if="selectedStock">
                        <h3>下单</h3>
                        <el-form :model="orderForm" label-width="80px">
                            <el-form-item label="股票">
                                <el-input v-model="selectedStock.code" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="方向">
                                <el-radio-group v-model="orderForm.direction">
                                    <el-radio label="buy">买入</el-radio>
                                    <el-radio label="sell">卖出</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="类型">
                                <el-select v-model="orderForm.type" style="width: 100%">
                                    <el-option label="限价单" value="limit"></el-option>
                                    <el-option label="市价单" value="market"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="价格" v-if="orderForm.type === 'limit'">
                                <el-input-number v-model="orderForm.price" :precision="2" style="width: 100%"></el-input-number>
                            </el-form-item>
                            <el-form-item label="数量">
                                <el-input-number v-model="orderForm.quantity" :min="100" :step="100" style="width: 100%"></el-input-number>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="submitOrder" :loading="submitting">
                                    {{ orderForm.direction === 'buy' ? '买入' : '卖出' }}
                                </el-button>
                                <el-button @click="resetForm">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
                
                <!-- 中间面板 - K线图 -->
                <div class="center-panel">
                    <h3>{{ selectedStock ? selectedStock.name : '请选择股票' }}</h3>
                    <div v-if="selectedStock" class="price-display" :class="priceClass">
                        ¥{{ currentPrice }}
                        <span style="font-size: 14px; margin-left: 10px;">
                            {{ priceChange > 0 ? '+' : '' }}{{ priceChange.toFixed(2) }} ({{ priceChangePercent.toFixed(2) }}%)
                        </span>
                    </div>
                    <div id="kline-chart" class="chart-container"></div>
                </div>
                
                <!-- 右侧面板 - 五档行情 -->
                <div class="right-panel">
                    <h3>五档行情</h3>
                    <div class="market-data" v-if="selectedStock">
                        <div class="order-book">
                            <table>
                                <thead>
                                    <tr>
                                        <th>档位</th>
                                        <th>卖价</th>
                                        <th>卖量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(item, index) in askData" :key="'ask-' + index">
                                        <td>卖{{ 5 - index }}</td>
                                        <td class="ask">{{ item.price }}</td>
                                        <td>{{ item.volume }}</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <div style="text-align: center; padding: 10px; background: #f5f5f5; margin: 10px 0;">
                                当前价: {{ currentPrice }}
                            </div>
                            
                            <table>
                                <thead>
                                    <tr>
                                        <th>档位</th>
                                        <th>买价</th>
                                        <th>买量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(item, index) in bidData" :key="'bid-' + index">
                                        <td>买{{ index + 1 }}</td>
                                        <td class="bid">{{ item.price }}</td>
                                        <td>{{ item.volume }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 最近成交 -->
                    <div v-if="selectedStock">
                        <h4>最近成交</h4>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <div v-for="trade in recentTrades" :key="trade.id" 
                                 style="display: flex; justify-content: space-between; padding: 2px 0; font-size: 12px;">
                                <span>{{ trade.time }}</span>
                                <span :class="trade.direction === 'buy' ? 'price-up' : 'price-down'">
                                    {{ trade.price }}
                                </span>
                                <span>{{ trade.volume }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    connected: false,
                    searchKeyword: '',
                    searchResults: [],
                    selectedStock: null,
                    currentPrice: 0,
                    priceChange: 0,
                    priceChangePercent: 0,
                    orderForm: {
                        direction: 'buy',
                        type: 'limit',
                        price: 0,
                        quantity: 100
                    },
                    submitting: false,
                    askData: [],
                    bidData: [],
                    recentTrades: [],
                    chart: null
                }
            },
            computed: {
                priceClass() {
                    return this.priceChange >= 0 ? 'price-up' : 'price-down';
                }
            },
            methods: {
                connectMarket() {
                    this.connected = !this.connected;
                    ElMessage.success(this.connected ? '市场连接成功' : '已断开连接');
                },
                
                goBack() {
                    window.location.href = '/';
                },
                
                searchStocks() {
                    if (this.searchKeyword.length < 2) {
                        this.searchResults = [];
                        return;
                    }
                    
                    // 模拟搜索结果
                    this.searchResults = [
                        { code: '000001', name: '平安银行', price: '13.20' },
                        { code: '000002', name: '万科A', price: '18.50' },
                        { code: '600036', name: '招商银行', price: '42.80' },
                        { code: '600519', name: '贵州茅台', price: '1680.00' }
                    ].filter(stock => 
                        stock.code.includes(this.searchKeyword) || 
                        stock.name.includes(this.searchKeyword)
                    );
                },
                
                selectStock(stock) {
                    this.selectedStock = stock;
                    this.currentPrice = parseFloat(stock.price);
                    this.orderForm.price = this.currentPrice;
                    this.generateMarketData();
                    this.initChart();
                    ElMessage.success(`已选择 ${stock.name}`);
                },
                
                generateMarketData() {
                    const basePrice = this.currentPrice;
                    
                    // 生成五档卖单
                    this.askData = [];
                    for (let i = 0; i < 5; i++) {
                        this.askData.push({
                            price: (basePrice + (i + 1) * 0.01).toFixed(2),
                            volume: Math.floor(Math.random() * 1000 + 100)
                        });
                    }
                    
                    // 生成五档买单
                    this.bidData = [];
                    for (let i = 0; i < 5; i++) {
                        this.bidData.push({
                            price: (basePrice - (i + 1) * 0.01).toFixed(2),
                            volume: Math.floor(Math.random() * 1000 + 100)
                        });
                    }
                    
                    // 生成最近成交
                    this.recentTrades = [];
                    for (let i = 0; i < 20; i++) {
                        this.recentTrades.push({
                            id: i,
                            time: new Date(Date.now() - i * 60000).toLocaleTimeString(),
                            price: (basePrice + (Math.random() - 0.5) * 0.1).toFixed(2),
                            volume: Math.floor(Math.random() * 500 + 100),
                            direction: Math.random() > 0.5 ? 'buy' : 'sell'
                        });
                    }
                    
                    this.priceChange = (Math.random() - 0.5) * 2;
                    this.priceChangePercent = (this.priceChange / basePrice) * 100;
                },
                
                initChart() {
                    if (this.chart) {
                        this.chart.dispose();
                    }
                    
                    const chartDom = document.getElementById('kline-chart');
                    this.chart = echarts.init(chartDom);
                    
                    // 生成模拟K线数据
                    const data = [];
                    const basePrice = this.currentPrice;
                    for (let i = 0; i < 100; i++) {
                        const open = basePrice + (Math.random() - 0.5) * 2;
                        const close = open + (Math.random() - 0.5) * 1;
                        const high = Math.max(open, close) + Math.random() * 0.5;
                        const low = Math.min(open, close) - Math.random() * 0.5;
                        data.push([open, close, low, high]);
                    }
                    
                    const option = {
                        title: {
                            text: this.selectedStock.name + ' K线图'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: Array.from({length: 100}, (_, i) => i + 1)
                        },
                        yAxis: {
                            scale: true
                        },
                        series: [{
                            type: 'candlestick',
                            data: data,
                            itemStyle: {
                                color: '#ef232a',
                                color0: '#14b143',
                                borderColor: '#ef232a',
                                borderColor0: '#14b143'
                            }
                        }]
                    };
                    
                    this.chart.setOption(option);
                },
                
                submitOrder() {
                    if (!this.selectedStock) {
                        ElMessage.error('请先选择股票');
                        return;
                    }
                    
                    this.submitting = true;
                    
                    // 模拟提交订单
                    setTimeout(() => {
                        this.submitting = false;
                        ElMessage.success('订单提交成功');
                        this.resetForm();
                    }, 1000);
                },
                
                resetForm() {
                    this.orderForm = {
                        direction: 'buy',
                        type: 'limit',
                        price: this.currentPrice,
                        quantity: 100
                    };
                }
            },
            
            mounted() {
                // 初始化搜索结果
                this.searchStocks();
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>

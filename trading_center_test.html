<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资交易中心 - 功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
        }
        
        .status-card.error {
            border-left-color: #f44336;
        }
        
        .status-card.warning {
            border-left-color: #ff9800;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .nav-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
            cursor: pointer;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .nav-item h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .trading-form {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }
        
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 量化投资交易中心</h1>
            <p>专业的量化交易解决方案 - 功能测试页面</p>
        </div>

        <div class="status-grid">
            <div class="status-card" id="frontend-status">
                <h3>🌐 前端服务</h3>
                <p>状态: <span id="frontend-status-text">✅ 测试页面运行中</span></p>
                <p>地址: 当前页面</p>
            </div>
            <div class="status-card" id="backend-status">
                <h3>🔧 后端服务</h3>
                <p>状态: <span id="backend-status-text"><span class="loading"></span> 检查中...</span></p>
                <p>地址: http://localhost:8000</p>
            </div>
            <div class="status-card" id="websocket-status">
                <h3>📡 WebSocket</h3>
                <p>状态: <span id="websocket-status-text">⏳ 未连接</span></p>
                <p>实时数据连接</p>
            </div>
        </div>

        <div class="nav-grid">
            <div class="nav-item" onclick="testPage('dashboard')">
                <h3>📊 仪表盘</h3>
                <p>数据概览和监控</p>
            </div>
            <div class="nav-item" onclick="testPage('market')">
                <h3>📈 市场数据</h3>
                <p>实时行情和历史数据</p>
            </div>
            <div class="nav-item" onclick="testPage('trading')">
                <h3>💼 交易终端</h3>
                <p>模拟和实盘交易</p>
            </div>
            <div class="nav-item" onclick="testPage('portfolio')">
                <h3>📋 投资组合</h3>
                <p>资产管理和分析</p>
            </div>
            <div class="nav-item" onclick="testPage('strategy')">
                <h3>🎯 策略中心</h3>
                <p>策略创建和管理</p>
            </div>
            <div class="nav-item" onclick="testPage('risk')">
                <h3>🛡️ 风险管理</h3>
                <p>风险监控和控制</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 系统测试</h3>
            <div class="test-buttons">
                <button class="btn" onclick="testBackendConnection()">🔗 测试后端连接</button>
                <button class="btn secondary" onclick="testMarketData()">📊 测试市场数据</button>
                <button class="btn secondary" onclick="testTradingAPI()">💼 测试交易API</button>
                <button class="btn secondary" onclick="testWebSocket()">📡 测试WebSocket</button>
                <button class="btn" onclick="runAllTests()">🚀 运行全部测试</button>
                <button class="btn danger" onclick="clearResults()">🗑️ 清空结果</button>
            </div>
            <div class="results" id="test-results">
🎯 量化投资交易中心测试页面已加载
⏳ 等待测试指令...

💡 提示：
- 点击上方按钮测试各项功能
- 查看测试结果和错误信息
- 确保后端服务正在运行 (http://localhost:8000)
            </div>
        </div>

        <div class="test-section">
            <h3>💼 模拟交易测试</h3>
            <div class="trading-form">
                <div class="form-group">
                    <label for="stock-code">股票代码</label>
                    <input type="text" id="stock-code" placeholder="例如: 000001" value="000001">
                </div>
                <div class="form-group">
                    <label for="trade-type">交易类型</label>
                    <select id="trade-type">
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="price">价格</label>
                    <input type="number" id="price" placeholder="例如: 10.50" value="10.50" step="0.01">
                </div>
                <div class="form-group">
                    <label for="quantity">数量</label>
                    <input type="number" id="quantity" placeholder="例如: 100" value="100">
                </div>
                <button class="btn" onclick="submitOrder()">📝 提交订单 (模拟)</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 图表展示区域</h3>
            <div class="chart-container" id="chart-container">
                <div style="text-align: center;">
                    <div class="loading"></div>
                    <p style="margin-top: 10px;">图表加载中...</p>
                    <p style="font-size: 14px; opacity: 0.7;">点击"测试市场数据"加载图表</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testCount = 0;

        function log(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            results.textContent += `[${timestamp}] ${icon} ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, isOnline, status) {
            const statusCard = document.getElementById(elementId);
            const statusText = document.getElementById(elementId + '-text');
            
            if (isOnline) {
                statusCard.classList.remove('error', 'warning');
                statusText.innerHTML = `✅ ${status}`;
            } else {
                statusCard.classList.add('error');
                statusText.innerHTML = `❌ ${status}`;
            }
        }

        async function testBackendConnection() {
            log('🔍 测试后端连接...', 'info');
            try {
                const response = await fetch('http://localhost:8000/api/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`后端连接成功: ${data.message}`, 'success');
                    updateStatus('backend', true, data.data.status);
                    return true;
                } else {
                    log(`后端连接失败: HTTP ${response.status}`, 'error');
                    updateStatus('backend', false, 'HTTP错误');
                    return false;
                }
            } catch (error) {
                log(`后端连接错误: ${error.message}`, 'error');
                updateStatus('backend', false, '连接失败');
                return false;
            }
        }

        async function testMarketData() {
            log('📊 测试市场数据API...', 'info');
            try {
                const response = await fetch('http://localhost:8000/api/market/stocks');
                if (response.ok) {
                    const data = await response.json();
                    log(`市场数据获取成功，股票数量: ${data.data?.length || 0}`, 'success');
                    
                    // 模拟图表更新
                    const chartContainer = document.getElementById('chart-container');
                    chartContainer.innerHTML = `
                        <div style="text-align: center;">
                            <h4>📈 市场数据图表</h4>
                            <p>股票数量: ${data.data?.length || 0}</p>
                            <p>数据更新时间: ${new Date().toLocaleTimeString()}</p>
                            <div style="margin: 20px 0; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                                <p>📊 模拟K线图</p>
                                <p>📈 模拟趋势图</p>
                                <p>📉 模拟成交量</p>
                            </div>
                        </div>
                    `;
                    return true;
                } else {
                    log(`市场数据获取失败: HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`市场数据错误: ${error.message}`, 'error');
                return false;
            }
        }

        async function testTradingAPI() {
            log('💼 测试交易API...', 'info');
            try {
                const response = await fetch('http://localhost:8000/api/trading/account');
                if (response.ok) {
                    const data = await response.json();
                    log(`交易API连接成功`, 'success');
                    return true;
                } else {
                    log(`交易API失败: HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`交易API错误: ${error.message}`, 'error');
                return false;
            }
        }

        async function testWebSocket() {
            log('📡 测试WebSocket连接...', 'info');
            try {
                // 模拟WebSocket测试
                log('WebSocket功能模拟测试', 'warning');
                updateStatus('websocket', true, '模拟连接');
                return true;
            } catch (error) {
                log(`WebSocket错误: ${error.message}`, 'error');
                updateStatus('websocket', false, '连接失败');
                return false;
            }
        }

        async function runAllTests() {
            log('🚀 开始运行全部测试...', 'info');
            testCount++;
            
            const tests = [
                { name: '后端连接', func: testBackendConnection },
                { name: '市场数据', func: testMarketData },
                { name: '交易API', func: testTradingAPI },
                { name: 'WebSocket', func: testWebSocket }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                const result = await test.func();
                if (result) passedTests++;
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log(`测试完成 (${testCount}): ${passedTests}/${tests.length} 通过`, 
                 passedTests === tests.length ? 'success' : 'warning');
        }

        function testPage(page) {
            log(`🔗 模拟导航到: ${page}`, 'info');
            
            const pageInfo = {
                dashboard: '仪表盘 - 数据概览和监控',
                market: '市场数据 - 实时行情和历史数据',
                trading: '交易终端 - 模拟和实盘交易',
                portfolio: '投资组合 - 资产管理和分析',
                strategy: '策略中心 - 策略创建和管理',
                risk: '风险管理 - 风险监控和控制'
            };
            
            log(`页面功能: ${pageInfo[page] || '未知页面'}`, 'info');
        }

        function submitOrder() {
            const stockCode = document.getElementById('stock-code').value;
            const tradeType = document.getElementById('trade-type').value;
            const price = document.getElementById('price').value;
            const quantity = document.getElementById('quantity').value;
            
            log(`📝 模拟订单提交:`, 'info');
            log(`   股票代码: ${stockCode}`, 'info');
            log(`   交易类型: ${tradeType === 'buy' ? '买入' : '卖出'}`, 'info');
            log(`   价格: ¥${price}`, 'info');
            log(`   数量: ${quantity}股`, 'info');
            log(`   总金额: ¥${(parseFloat(price) * parseInt(quantity)).toFixed(2)}`, 'info');
            log(`✅ 模拟订单提交成功 (仅测试)`, 'success');
        }

        function clearResults() {
            document.getElementById('test-results').textContent = '🗑️ 测试结果已清空\n⏳ 等待新的测试...\n';
        }

        // 页面加载完成后自动检查服务状态
        window.addEventListener('load', function() {
            log('🎯 量化投资交易中心测试页面已加载', 'success');
            log('🔄 自动检查服务状态...', 'info');
            setTimeout(testBackendConnection, 1000);
        });

        // 定期检查服务状态
        setInterval(() => {
            testBackendConnection();
        }, 30000);
    </script>
</body>
</html>

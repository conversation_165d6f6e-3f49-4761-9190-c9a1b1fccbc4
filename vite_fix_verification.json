{"timestamp": "2025-08-11T14:09:06.741022", "vite_version": "5.4.8", "results": {"前端服务器状态": {"status": "success", "code": 200, "message": "前端服务器正常运行"}, "页面内容加载": {"status": "partial", "checks": {"Vue应用挂载点": true, "主JavaScript文件": true, "Vite模块加载": true, "标题": false}, "content_length": 1953}, "静态资源加载": {"status": "success", "assets": [{"asset": "/src/assets/logo.svg", "status": 200, "loaded": true}, {"asset": "/src/assets/main.css", "status": 200, "loaded": true}]}, "API代理功能": {"status": "success", "code": 500, "message": "API代理正常工作"}}, "summary": {"total_tests": 4, "passed": 3, "partial": 1, "failed": 0}}